apply plugin: 'com.android.library'

android {
    compileSdkVersion 28
    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 28
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        rn069 {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    lintOptions {
        abortOnError false
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation ('com.github.bumptech.glide:glide:3.8.0') {
        exclude group: "com.android.support"
    }
    implementation("com.cmcm.launcher:GLEye:1.0.2"){
        exclude group: "com.badlogicgames.gdx",module: 'gdx'
    }
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
}
