/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.emoji;

import android.util.Log;

/**
 * <AUTHOR>
 */
public class Emoji {

    private static final String TAG = "Emoji";

    public enum EMOJI_TYPE {
        TYPE_DEPRESS("depress", "depress.task"),
        IDLE_SAD("depress", "depress.task"),
        SMILE_NEW("smile_new", "smile.task"),
        SONG("song", "song_loop.task"),
        COCO("coco", "coco_loop.task"),
        SLEEP("sleep", "sleep_loop.task"),
        BOOK("book", "book.task"),
        CROWN("crown", "crown.task"),
        TYPE_ACQUAINTANC<PERSON>("acquaintance", "crown.task"),
        SING_2("eyeSing2", "sing2.task"),
        SING_4("eyeSing4", "sing4.task"),
        FLOWER("flower", "flower_loop.task"),
        EYE_ROLL("eye_roll", "supercilious.task"),
        IDLE_WELCOME("idle_welcome", "default.task") {
            @Override
            public boolean canIdle() {
                return true;
            }
        },
        /**
         * 后续问题小动作结束
         */
        LOOK_AROUND("look_around", "look_left_right.task"),
        LOOK_AROUND_TWO("look_up_left_right", "look_up_left_right.task"),
        DANCE_ONE("dance_one", "dance_one.task"),
        DANCE_TWO("dance_two", "dance_two.task"),
        /**
         * RN 调用
         */
        BLINK_NEW("blink_new", "default.task") {
            @Override
            public boolean canIdle() {
                return true;
            }
        },
        //未实现的表情
        SMILE_SAY("disable", "smile_say"),
        TYPE_SAY1("disable", "say1"),
        LOOK_LEFT("disable", "look_left"),
        LOOK_RIGHT("disable", "look_right"),
        LEFT_UP("disable", "left_up"),
        RIGHT_UP("disable", "right_up"),
        SING_1("disable", "eyeSing1"),
        SING_3("disable", "eyeSing3"),
        FACE_END("disable", "face_end"),
        LEFT_UP_RIGHT_UP("disable", "left_up_right_up"),
        IDLE_RUNNING("disable", "running"),
        NORMAL_NEW("disable", "normal_new");

        private String name;
        private String task;

        EMOJI_TYPE(String name, String task) {
            this.name = name;
            this.task = task;
        }

        public String getName() {
            return this.name;
        }

        public String getTaskName() {
            return this.task;
        }

        public EMOJI_TYPE getEmojiType() {
            return typeOfName(this.name);
        }

        public static EMOJI_TYPE typeOfName(String name) {
            EMOJI_TYPE[] types = values();
            for (EMOJI_TYPE type : types) {
                if (type.name.equals(name)) {
                    return type;
                }
            }
            Log.w(TAG, "have no this enum: " + name);
            return IDLE_WELCOME;
        }

        public static String validTask(String task) {
            EMOJI_TYPE[] types = values();
            for (EMOJI_TYPE type : types) {
                if (type.task.equals(task)) {
                    return type.getTaskName();
                }
            }
            Log.w(TAG, "have no this enum: " + task);
            return IDLE_WELCOME.getTaskName();
        }

        public boolean canIdle() {
            return false;
        }
    }
}
