package com.ainirobot.emoji.common;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;

import com.ainirobot.emoji.BasicEmojiLayout;
import com.ainirobot.emoji.SwitchEmojiManager;
import com.ainirobot.emoji.config.EmojiConfig;
import com.ainirobot.emoji.util.Utils;

public class CommonBigEmojiLayout extends BasicEmojiLayout {

    private static final String TAG = "CommonBigEmojiLayout";

    public CommonBigEmojiLayout(Context context) {
        super(context, TAG);
    }

    public CommonBigEmojiLayout(Context context, AttributeSet attrs) {
        super(context, attrs, TAG);
    }

    @Override
    protected void showDefault() {
        SwitchEmojiManager.getInstance().enterBigEye();
    }

    public void resetBigBackground(int delay) {
        resetBackground(delay);
        Utils.setBigBackgroundRes(TAG, null);
    }

    @Override
    public String getBackgroundUrl() {
        return Utils.getBigBackgroundRes(TAG);
    }

    @Override
    protected int getDefaultRes() {
        int resId = EmojiConfig.getDefaultBigBackgroundRes();
        Log.d(TAG, "Get default res : " + resId);
        if (resId != 0) {
            return resId;
        }
        throw new IllegalArgumentException("getDefaultBigBackgroundRes can not null");
    }

    @Override
    public void updateBackgroundUrl(String backgoundUrl) {
        Log.i(TAG, "updateBackgroundUrl: " + backgoundUrl);
        if (!TextUtils.equals(Utils.getBigBackgroundRes(TAG), backgoundUrl)) {
            super.updateBackgroundUrl(backgoundUrl);
            Utils.setBigBackgroundRes(TAG, backgoundUrl);
        }
    }

    @Override
    public void show() {
        Log.i(TAG, "show ");
        compareUrlNeedUpdate();
        if (CommonBigEmojiLayout.this.getBackground() != null) {
            CommonBigEmojiLayout.this.getBackground().setAlpha(255);
        }
        SwitchEmojiManager.getInstance().addFaceView(TAG, CommonBigEmojiLayout.this);
        SwitchEmojiManager.getInstance().enterBigEye();
    }

    @Override
    public void hide() {
        super.hide();
        this.getBackground().setAlpha(0);
    }


    @Override
    public void show(float positionX, float positionY, boolean bnned) {
        Log.i(TAG, "show: positionX :" + positionX + " positionY = " + positionY + " bnned = " + bnned);
    }
}
