package com.ainirobot.emoji.common;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;

import com.ainirobot.emoji.BasicEmojiLayout;
import com.ainirobot.emoji.Emoji;
import com.ainirobot.emoji.SwitchEmojiManager;
import com.ainirobot.emoji.config.EmojiConfig;
import com.ainirobot.emoji.util.Utils;

import java.util.ArrayList;

public class CommonSmallEmojiLayout extends BasicEmojiLayout {

    private static final String TAG = "CommonSmallEmojiLayout";

    protected float positionX = 329;
    private float positionY = Utils.getScreenHeightPixels() - 1733;
    private boolean needAnimation = EmojiConfig.isNeedAnimation();

    public CommonSmallEmojiLayout(Context context) {
        super(context, TAG);
    }

    public CommonSmallEmojiLayout(Context context, AttributeSet attrs) {
        super(context, attrs, TAG);
    }

    @Override
    protected void showDefault() {
        SwitchEmojiManager.getInstance().enterSmallEye(this.positionX, this.positionY, needAnimation);
    }

    @Override
    public void setDefaultEmojis(ArrayList<String> types) {

    }

    @Override
    public void updateBackgroundUrl(String backgoundUrl) {
        Log.i(TAG, "updateBackgroundUrl: " + backgoundUrl);
        if (!TextUtils.equals(Utils.getSmallBackgroundRes(TAG), backgoundUrl)) {
            super.updateBackgroundUrl(backgoundUrl);
            Utils.setSmallBackgroundRes(TAG, backgoundUrl);
        }
    }

    public void resetBigBackground(int delay){
        resetBackground(delay);
        Utils.setSmallBackgroundRes(TAG,null);
    }

    @Override
    public String getBackgroundUrl() {
        return Utils.getSmallBackgroundRes(TAG);
    }

    @Override
    protected int getDefaultRes() {
        int resId = EmojiConfig.getDefaultSmallBackgroundRes();
        if(resId != 0){
            return resId;
        }
        throw new IllegalArgumentException("getDefaultSmallBackgroundRes can not null");
    }

    @Override
    public void show() {
        Log.i(TAG, "show :");
        compareUrlNeedUpdate();
        SwitchEmojiManager.getInstance().addFaceView(TAG, CommonSmallEmojiLayout.this);
        SwitchEmojiManager.getInstance().enterSmallEye(this.positionX, this.positionY, needAnimation);
    }

    @Override
    public void show(float positionX, float positionY, boolean bnned) {
        Log.i(TAG, "show: positionX :" + positionX + " positionY = " + positionY + " bnned = " + bnned);
        this.positionX = positionX;
        this.positionY = positionY;
        this.needAnimation = bnned;
        compareUrlNeedUpdate();
        if (TextUtils.equals(SwitchEmojiManager.getInstance().getCurrentTag(), TAG)) {
            SwitchEmojiManager.getInstance().enterSmallEye(positionX, positionY, bnned);
        }
    }
}
