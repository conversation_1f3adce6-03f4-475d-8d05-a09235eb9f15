package com.ainirobot.emoji.module;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;

import com.ainirobot.emoji.BasicEmojiLayout;
import com.ainirobot.emoji.SwitchEmojiManager;
import com.ainirobot.emoji.config.EmojiConfig;
import com.ainirobot.emoji.util.Utils;

public class BigEmojiLayout extends BasicEmojiLayout {

    private static final String TAG = "BigEmojiLayout";

    public BigEmojiLayout(Context context) {
        super(context, TAG);
    }

    public BigEmojiLayout(Context context, AttributeSet attrs) {
        super(context, attrs, TAG);
    }

    @Override
    protected void showDefault() {
        SwitchEmojiManager.getInstance().enterBigEye();
    }

    @Override
    public void show() {
        compareUrlNeedUpdate();
        SwitchEmojiManager.getInstance().addFaceView(TAG, BigEmojiLayout.this);
        SwitchEmojiManager.getInstance().enterBigEye();
    }

    public void resetBigBackground(int delay) {
        resetBackground(delay);
        Utils.setBigBackgroundRes(TAG, null);
    }

    @Override
    public String getBackgroundUrl() {
        return Utils.getBigBackgroundRes(TAG);
    }

    @Override
    protected int getDefaultRes() {
        int resId = EmojiConfig.getDefaultBigBackgroundRes();
        if (resId != 0) {
            return resId;
        }
        throw new IllegalArgumentException("getDefaultBigBackgroundRes can not null");
    }

    @Override
    public void updateBackgroundUrl(String backgoundUrl) {
        Log.i(TAG, "updateBackgroundUrl: " + backgoundUrl);
        if (!TextUtils.equals(Utils.getBigBackgroundRes(TAG), backgoundUrl)) {
            super.updateBackgroundUrl(backgoundUrl);
            Utils.setBigBackgroundRes(TAG, backgoundUrl);
        }
    }
}
