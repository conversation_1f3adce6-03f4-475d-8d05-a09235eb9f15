package com.ainirobot.emoji;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;


import com.ainirobot.emoji.config.EmojiConfig;
import com.ainirobot.emoji.module.BigEmojiLayout;
import com.ainirobot.emoji.util.Utils;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.Queue;

public abstract class BasicEmojiLayout extends FrameLayout implements IBigEmoji, SwitchEmojiManager.OnActivePushListener {

    private String TAG = null;
    private Context mContext;
    private String mDefaultEmojiType = Emoji.EMOJI_TYPE.IDLE_WELCOME.getTaskName();
    private ArrayList<String> mDefaultEmojiTypes;
    private Queue<String> emojiQueue;
    private OnEmojiCompleteListener mCompleteListener;

    public void setOnEmojiCompleteListener(OnEmojiCompleteListener listener) {
        this.mCompleteListener = listener;
    }

    public BasicEmojiLayout(Context context, String tag) {
        super(context);
        this.TAG = tag;
        init(context);
    }

    public BasicEmojiLayout(Context context, AttributeSet attrs, String tag) {
        super(context, attrs);
        this.TAG = tag;
        init(context);
    }

    private void init(Context context) {
        mContext = context;
        emojiQueue = new LinkedList<>();
        SwitchEmojiManager.getInstance().init(context);
        setLayoutParams(new FrameLayout.LayoutParams(Utils.getScreenWidthPixels(), Utils.getScreenHeightPixels()));
        if (EmojiConfig.isShowDefault()) {
            SwitchEmojiManager.getInstance().addFaceView(this.TAG, this);
            showDefault();
        }
        SwitchEmojiManager.getInstance().registerActivePushListener(this);
        initBackground();
    }

    @Override
    public void setFaceOffset(float x, float y, boolean isAnimator) {
        SwitchEmojiManager.getInstance().setFaceOffset(x, y, isAnimator);
    }

    protected abstract void showDefault();

    protected void initBackground() {
        if (EmojiConfig.isEnableBg()) {
            Utils.loadViewBackground(this, getBackgroundUrl(), getDefaultRes());
        }
    }

    public abstract String getBackgroundUrl();

    protected abstract int getDefaultRes();

    public void updateBackgroundUrl(String backgoundUrl) {
        Log.i(TAG, "updateBackgroundUrl: " + backgoundUrl);
        if (EmojiConfig.isEnableBg()) {
            Utils.loadViewBackground(this, backgoundUrl, getDefaultRes());
        }
    }

    public void resetBackground(int delay) {
        postDelayed(new Runnable() {
            @Override
            public void run() {
                Utils.loadViewBackground(BasicEmojiLayout.this, null, getDefaultRes());
            }
        }, delay);
    }

    @Override
    public void setDefaultEmoji(String type) {
        Log.i(TAG, "setDefaultEmoji :" + type);
        mDefaultEmojiType = type;
    }

    @Override
    public void setDefaultEmojis(ArrayList<String> types) {
        Log.i(TAG, "setDefaultEmoji :" + types);
        mDefaultEmojiTypes = types;
    }

    @Override
    protected void onVisibilityChanged(View changedView, int visibility) {
        super.onVisibilityChanged(changedView, visibility);
        Log.i(TAG, "onVisibilityChanged :" + changedView + " visibility:" + visibility);
        if (changedView instanceof BigEmojiLayout) {
            if (visibility == View.VISIBLE) {

            } else {
                SwitchEmojiManager.getInstance().removeView(TAG);
                clearQueue();
            }
        }
    }

    @Override
    public void hide() {
        Log.i(TAG, "hide :");
        SwitchEmojiManager.getInstance().removeView(TAG);
        clearQueue();
    }

    @Override
    public void show(float positionX, float positionY, boolean bnned) {
        this.show();
    }

    protected void compareUrlNeedUpdate() {
        Log.v(TAG, " previous url =" + getBackgroundUrl());
        if (EmojiConfig.isEnableBg() && !TextUtils.equals(getBackgroundUrl(), String.valueOf(this.getTag()))) {
            Utils.loadViewBackground(this, getBackgroundUrl(), getDefaultRes());
        }
    }

    @Override
    public void speak() {
        Log.i(TAG, "speak :");
        SwitchEmojiManager.getInstance().speak();
    }

    @Override
    public void speakEnd() {
        Log.i(TAG, "speakEnd :");
        SwitchEmojiManager.getInstance().speakEnd();
    }

    @Override
    public void playEmojiType(String emojiType, final OnEmojiListener listener) {
        if (emojiType != null && isShow()) {
            Log.i(TAG, "playEmojiType :" + emojiType);
            SwitchEmojiManager.getInstance().playTask(emojiType, new SwitchEmojiManager.OnEmojiListener() {
                @Override
                public void onEmojiEnd(String task) {
                    if (listener != null) {
                        listener.onEmojiEnd(task);
                    }
                }
            });
        }
    }

    private boolean isShow() {
        return this.getVisibility() == View.VISIBLE;
    }

    @Override
    public void playEmojiType(ArrayList<String> types) {
        clearQueue();
        if (types != null && types.size() > 0) {
            for (String emojiType : types) {
                emojiQueue.offer(emojiType);
            }
        }
        playNext();
    }

    private void playNext() {
        if (emojiQueue != null && emojiQueue.size() > 0) {
            String emojiType = emojiQueue.poll();
            Log.i(TAG, "playEmojiType :" + emojiType);
            if (!TextUtils.isEmpty(emojiType) && isShow()) {
                SwitchEmojiManager.getInstance().playTask(emojiType, new SwitchEmojiManager.OnEmojiListener() {
                    @Override
                    public void onEmojiEnd(String task) {
                        doEmojiPlayEnd(task);
                    }
                });
            }
        } else {
            if (mCompleteListener != null) {
                mCompleteListener.onEmojiComplete();
            }
            if (mDefaultEmojiType != null) {
                SwitchEmojiManager.getInstance().playTask(mDefaultEmojiType, new SwitchEmojiManager.OnEmojiListener() {
                    @Override
                    public void onEmojiEnd(String task) {
                        doEmojiPlayEnd(task);
                    }
                });
            }

            if (mDefaultEmojiTypes != null) {
                playEmojiType(mDefaultEmojiTypes);
            }
        }
    }

    private void clearQueue() {
        if (!emojiQueue.isEmpty()) {
            emojiQueue.clear();
        }
    }

    @Override
    public void activePush(String tag) {
        if (TextUtils.equals(tag, TAG) && this.getVisibility() == View.VISIBLE) {
            Log.i(TAG, "activePush :" + tag);
            show();
        }
    }

    private void doEmojiPlayEnd(String taskName) {
        Log.i(TAG, "doEmojiPlayEnd :" + taskName);
        playNext();
    }

    @Override
    public void destroy() {
        Log.i(TAG, "destroy :");
        clearQueue();
        SwitchEmojiManager.getInstance().resetTag();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        SwitchEmojiManager.getInstance().unRegisterActivePushListener(this);
    }

    public interface OnEmojiListener {
        void onEmojiEnd(String task);
    }

    public interface OnEmojiCompleteListener {
        void onEmojiComplete();
    }
}
