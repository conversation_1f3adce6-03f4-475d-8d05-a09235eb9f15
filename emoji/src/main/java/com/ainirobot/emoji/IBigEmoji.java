package com.ainirobot.emoji;

import java.util.ArrayList;

public interface IBigEmoji {

    /**
     * 销毁布局
     */
    void destroy();

    /**
     * 隐藏表情
     */
    void hide();

    /**
     * 显示表情
     */
    void show();

    /**
     * 显示表情
     *
     * @param positionX
     * @param positionY
     * @param bnned
     */
    void show(float positionX, float positionY, boolean bnned);

    /**
     * 说话
     */
    void speak();

    /**
     * 停止说话
     */
    void speakEnd();

    /**
     * 播放表情类型
     */
    void playEmojiType(ArrayList<String> taskTypes);

    void playEmojiType(String emojiType, final BasicEmojiLayout.OnEmojiListener listener);

    /**
     * 设置默认表情类型
     */
    void setDefaultEmoji(String type);

    void setDefaultEmojis(ArrayList<String> types);

    /**
     * 设置Face整体偏移
     * @param x 左右偏移
     * @param y 上下偏移
     * @param isAnimator 是否需要过渡动画
     */
    void setFaceOffset(float x, float y, boolean isAnimator);
}
