package com.ainirobot.emoji.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;

import com.ainirobot.emoji.config.EmojiConfig;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.SimpleTarget;

public class Utils {

    private static final String TAG = "Utils";
    private static final String SMALL_BACKGROUND_RES = "smallBackgroundRes";
    private static final String BIG_BACKGROUND_RES = "bigBackgroundRes";

    public static void setSmallBackgroundRes(String tag,String url) {
        PreferenceUtil.getInstance().putValue(tag.concat("_").concat(SMALL_BACKGROUND_RES), url);
    }

    public static void setBigBackgroundRes(String tag,String url) {
        PreferenceUtil.getInstance().putValue(tag.concat("_").concat(BIG_BACKGROUND_RES), url);
    }

    public static String getSmallBackgroundRes(String tag) {
        return PreferenceUtil.getInstance().getStringValue(tag.concat("_").concat(SMALL_BACKGROUND_RES));
    }

    public static String getBigBackgroundRes(String tag) {
        return PreferenceUtil.getInstance().getStringValue(tag.concat("_").concat(BIG_BACKGROUND_RES));
    }

    public static void loadViewBackground(final View view, String url, final int failImageRes) {
        loadViewBackground(view, url, failImageRes, null);
    }


    private static void loadViewBackground(final View view, String url, final int failImageRes,
                                           final SimpleTarget<Bitmap> simpleTarget) {
        Log.i(TAG, "loadViewBackground url = " + url +" isEnableBg =" + EmojiConfig.isEnableBg());
        if (TextUtils.isEmpty(url)) {
            if (failImageRes != 0) {
                view.setTag(String.valueOf(failImageRes));
                view.setBackgroundResource(failImageRes);

                {
                    Drawable drawable = view.getBackground();
                    if(drawable != null) {
                        Log.i(TAG, "View: " + view.getClass().getSimpleName() + ", bg alpha: " + drawable.getAlpha());
                    }
                }

                Log.i(TAG, "loadViewBackground url is empty, use fail image res : " + failImageRes);
            }
            return;
        }
        view.setTag(url);
        Glide.with(view.getContext())
                .load(url)
                .asBitmap()
                .format(DecodeFormat.PREFER_ARGB_8888)
                .override(getScreenWidthPixels(), getScreenHeightPixels())
                .into(new SimpleTarget<Bitmap>() {
                    @Override
                    public void onResourceReady(Bitmap resource, GlideAnimation<? super Bitmap> glideAnimation) {
                        if (simpleTarget != null) {
                            simpleTarget.onResourceReady(resource, glideAnimation);
                        } else {
                            Log.i(TAG, "load resource " + resource.toString());
                            view.setBackground(new BitmapDrawable(resource));
                        }
                    }

                    @Override
                    public void onLoadFailed(Exception e, Drawable errorDrawable) {
                        super.onLoadFailed(e, errorDrawable);
                        Log.i(TAG, "loadViewBackground " + e.getMessage());
                        if (failImageRes != 0) {
                            view.setBackgroundResource(failImageRes);
                        }
                    }
                });
    }

    private static DisplayMetrics mMetrics = null;

    private static DisplayMetrics getDisplayMetrics() {
        if (mMetrics != null) {
            return mMetrics;
        }
        if (EmojiConfig.get().getContext() != null && EmojiConfig.get().getContext().getResources() != null) {
            mMetrics = EmojiConfig.get().getContext().getResources().getDisplayMetrics();
        }
        return mMetrics;
    }

    public static int getScreenWidthPixels() {
        if (mMetrics == null) {
            getDisplayMetrics();
        }
        WindowManager windowMgr = (WindowManager) EmojiConfig.get().getContext().getSystemService(Context.WINDOW_SERVICE);
        windowMgr.getDefaultDisplay().getRealMetrics(mMetrics);
        return mMetrics.widthPixels;
    }

    public static int getScreenHeightPixels() {
        if (mMetrics == null) {
            getDisplayMetrics();
        }
        WindowManager windowMgr = (WindowManager) EmojiConfig.get().getContext().getSystemService(Context.WINDOW_SERVICE);
        windowMgr.getDefaultDisplay().getRealMetrics(mMetrics);
        return mMetrics.heightPixels;
    }
}