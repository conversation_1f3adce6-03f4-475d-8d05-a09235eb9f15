package com.ainirobot.emoji.util;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

public class PreferenceUtil {

    private static final String PERF_NAME = "local_preference";

    private static final int CURRENT_VERSION_CODE = 1;
    private volatile static PreferenceUtil instance;
    private SharedPreferences preferences;

    private PreferenceUtil() {
    }

    public void init(Context context){
        preferences = context.getSharedPreferences(PERF_NAME, Context.MODE_PRIVATE);
        checkPrefVersion();
    }

    public static PreferenceUtil getInstance() {
        if (instance == null) {
            synchronized (PreferenceUtil.class) {
                if (instance == null) {
                    instance = new PreferenceUtil();
                }
            }
        }
        return instance;
    }

    @SuppressLint("ApplySharedPref")
    public final void putValue(String key, String value) {
        preferences.edit().putString(key, value).commit();
    }

    @SuppressLint("ApplySharedPref")
    public final void putValue(String key, int value) {
        preferences.edit().putInt(key, value).commit();
    }

    public final String getStringValue(String key) {
        checkIsLegal(key);
        return preferences.getString(key, "");
    }

    public final int getIntValue(String key) {
        checkIsLegal(key);
        return preferences.getInt(key, 0);
    }

    @SuppressLint("ApplySharedPref")
    public final void deleteValue(String key) {
        checkIsLegal(key);
        preferences.edit().remove(key).commit();
    }

    @SuppressLint("ApplySharedPref")
    public final void clear() {
        preferences.edit().clear().commit();
    }

    private void checkIsLegal(String key) {
        if (TextUtils.isEmpty(key)) {
            throw new IllegalArgumentException("This parameter is illegal,key : " + key);
        }
    }

    private void checkPrefVersion() {
        final int oldVersion = preferences.getInt(PERF_NAME, 0);
        if (oldVersion < CURRENT_VERSION_CODE) {
            preferences.edit()
                    .clear()
                    .putInt(PERF_NAME, CURRENT_VERSION_CODE).apply();
        }
    }


}
