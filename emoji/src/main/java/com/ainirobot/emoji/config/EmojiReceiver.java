/*
 *
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
package com.ainirobot.emoji.config;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.ainirobot.emoji.SwitchEmojiManager;

public class EmojiReceiver extends BroadcastReceiver {

    private static final int DEFAULT_FRAME_RATE = 30;
    private static final int DEFAULT_LOG_STATE = 0;

    public static final String SETTING_EMOJI_RATE = "com.ainirobot.emoji.CHAMGE_FRAME_RATE";

    private static final String TAG = EmojiReceiver.class.getSimpleName();
    private static final String FRAME_RATE = "frame";
    private static final String ENABLE_LOG = "log";

    @Override
    public void onReceive(Context context, Intent intent) {

        if (intent != null) {
            int rate = intent.getIntExtra(FRAME_RATE, DEFAULT_FRAME_RATE);
            int logState = intent.getIntExtra(ENABLE_LOG, DEFAULT_LOG_STATE);
            if (rate > 0) {
                Log.i(TAG, "onReceive : rate :" + rate);
                SwitchEmojiManager.getInstance().setFrameRate(rate);
                SwitchEmojiManager.getInstance().setLogState(logState);
            }
        }
    }
}
