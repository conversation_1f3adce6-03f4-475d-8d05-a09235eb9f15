package com.ainirobot.emoji.config;

import android.content.Context;
import android.content.IntentFilter;
import android.text.TextUtils;

import com.ainirobot.emoji.SwitchEmojiManager;
import com.ainirobot.emoji.util.PreferenceUtil;

import java.lang.ref.WeakReference;

public class EmojiConfig {

    private static final String DEFAULT_BIG_BACKGROUND_RES = "default_big_background_res";
    private static final String DEFAULT_SMALL_BACKGROUND_RES = "default_small_background_res";

    private static final String ENABLE_LOG = "enableLog";
    private static final String FRAME_RATE = "frameRate";
    private static final String ASSERT_EMOJI_PATH = "assertEmojiPath";

    private WeakReference<Context> mReference;
    private static boolean enableBg = true;
    private static boolean needAnimation = false;
    private static boolean showDefault = true;
    private static EmojiConfig mConfig;

    public static EmojiConfig get(){
        return mConfig;
    }

    private EmojiConfig(Context context) {
        mReference = new WeakReference<>(context);
    }

    public static void init(Context context) {
        init(context, true, false);
    }

    public static void init(Context context, boolean enableBg) {
        init(context, enableBg, false);
    }

    public static void init(Context context, boolean enableBg, boolean needAnimation) {
        mConfig = new EmojiConfig(context);
        EmojiConfig.enableBg = enableBg;
        EmojiConfig.needAnimation = needAnimation;
        registerEmojiReceiver(context);
        PreferenceUtil.getInstance().init(context);
    }

    private static void registerEmojiReceiver(Context context) {
        IntentFilter filter = new IntentFilter();
        filter.addAction(EmojiReceiver.SETTING_EMOJI_RATE);
        EmojiReceiver emojiReceiver = new EmojiReceiver();
        context.registerReceiver(emojiReceiver, filter);
    }

    public static void setDefaultSmallBackgroundRes(int res) {
        PreferenceUtil.getInstance().putValue(DEFAULT_SMALL_BACKGROUND_RES, res);
    }

    public static void setDefaultBigBackgroundRes(int res) {
        PreferenceUtil.getInstance().putValue(DEFAULT_BIG_BACKGROUND_RES, res);
    }

    public static void setFrameRate(int frame) {
        PreferenceUtil.getInstance().putValue(FRAME_RATE, frame);
    }

    public static void setEnableLog(boolean enable) {
        PreferenceUtil.getInstance().putValue(ENABLE_LOG, enable ? 1 : 0);
    }

    public static void setAssetsEmojiPath(String path) {
        PreferenceUtil.getInstance().putValue(ASSERT_EMOJI_PATH, path);
    }

    public static void setShowDefault(boolean enable) {
        showDefault = enable;
    }

    public static int getDefaultSmallBackgroundRes() {
        return PreferenceUtil.getInstance().getIntValue(DEFAULT_SMALL_BACKGROUND_RES);
    }

    public static int getDefaultBigBackgroundRes() {
        return PreferenceUtil.getInstance().getIntValue(DEFAULT_BIG_BACKGROUND_RES);
    }

    public static boolean isEnableLog() {
        return PreferenceUtil.getInstance().getIntValue(ENABLE_LOG) == 1;
    }

    public static int getFrameRate() {
        int result = PreferenceUtil.getInstance().getIntValue(FRAME_RATE);
        return result != 0 ? result : 30;
    }

    public static String getAssertEmojiPath() {
        String path = PreferenceUtil.getInstance().getStringValue(ASSERT_EMOJI_PATH);
        if (TextUtils.isEmpty(path)) {
            path = SwitchEmojiManager.ASSETS_EMOJI_PATH;
        }
        return path;
    }

    public Context getContext() {
        return mReference.get();
    }

    public static boolean isEnableBg() {
        return enableBg;
    }

    public static boolean isShowDefault() {
        return showDefault;
    }

    public static boolean isNeedAnimation() {
        return needAnimation;
    }
}