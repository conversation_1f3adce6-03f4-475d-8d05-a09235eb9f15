package com.ainirobot.emoji;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;

import com.ainirobot.emoji.config.EmojiConfig;
import com.ainirobot.emoji.util.Utils;
import com.cmcm.gl.engine.gc.GCManager;
import com.cmcm.gleye.IO3DFace;
import com.cmcm.gleye.O3DFace;

import java.util.ArrayList;
import java.util.List;
import java.util.Stack;

public class SwitchEmojiManager {

    private static final String TAG = "SwitchEmojiManager";
    public static final String ASSETS_EMOJI_PATH = "baoxiaomi-v1.cmt";

    private IO3DFace mFace;
    private Handler mMainHandler;
    private String currentTag;
    private List<OnActivePushListener> mPushListeners;
    private Stack previousTags;
    private boolean isInit = false;

    private SwitchEmojiManager() {
        mMainHandler = new Handler(Looper.getMainLooper());
        mPushListeners = new ArrayList<>();
        previousTags = new Stack();
    }

    public String getCurrentTag() {
        return this.currentTag;
    }

    public void resetTag() {
        removeFaceParent();
        currentTag = null;
    }

    private static class SwitchEmojiManagerInner {
        private static SwitchEmojiManager mInstance = new SwitchEmojiManager();
    }

    public static SwitchEmojiManager getInstance() {
        return SwitchEmojiManagerInner.mInstance;
    }

    public synchronized void init(Context context) {
        Log.d(TAG, "SwitchEmojiManager init start : " + this.isInit);
        if (isInit) {
            return;
        }
        mFace = O3DFace.init(context, O3DFace.EngineType.texture, EmojiConfig.getAssertEmojiPath()
                , true, false);
        mFace.setO3DFaceLogEnable(EmojiConfig.isEnableLog());
        // 横屏缩小适配
        int orientation = context.getResources().getConfiguration().orientation;
        Log.d(TAG, "SwitchEmojiManager init " + orientation);
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            Log.d(TAG, "init 0.82f");
            mFace.setInitScale(0.82f);
        }
        mFace.setFrameRate(EmojiConfig.getFrameRate());
        this.isInit = true;
        Log.i(TAG, "init emojiRate :" + EmojiConfig.getFrameRate() + " enableLog:" + EmojiConfig.isEnableLog());
    }

    public void setFrameRate(int frameRate) {
        if (mFace != null) {
            mFace.setFrameRate(frameRate);
            EmojiConfig.setFrameRate(frameRate);
        }
    }

    public void setLogState(int logState) {
        if (mFace != null) {
            mFace.setO3DFaceLogEnable(logState == 1);
            EmojiConfig.setEnableLog(logState == 1);
        }
    }

    public void setFaceOffset(float x, float y, boolean isAnimator) {
        if (mFace != null) {
            mFace.setFaceOffset(x, y, isAnimator);
        }
    }

    /**
     * 单个view版本，用来显示全局的View，包含TextureView和SurfaceView
     * 双View的版本，只是用来进行截图，但是截屏的方法太消耗性能，建议简单实现，不做截屏处理
     */
    public View getRendererView() {
        if (mFace != null) {
            return mFace.getRendererView();
        }
        return null;
    }

    public void addFaceView(String tag, FrameLayout layout) {
        Log.i(TAG, "addFaceView tag: " + tag + " engineView tag: " + currentTag);
        if (TextUtils.equals(tag, currentTag)) {
            return;
        }
        Log.i(TAG, "addFaceView previousTags: " + tag);
        previousTags.push(tag);
        View engineView = getRendererView();
        if (!TextUtils.isEmpty(currentTag)) {
            removeFaceParent();
        }
        currentTag = tag;
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(Utils.getScreenWidthPixels(), Utils.getScreenHeightPixels());
        layout.addView(engineView, params);
        engineView.requestLayout();
        Log.i(TAG, "addFaceView currentTag: " + currentTag);
        mFace.onResume();
        resetFace();
    }

    public void onResume() {
        if (mFace != null) {
            mFace.onResume();
        }
    }

    public void onPause() {
        if (mFace != null) {
            mFace.onPause();
        }
    }

    private void resetFace() {
        playTask(Emoji.EMOJI_TYPE.IDLE_WELCOME.getTaskName(), null);
        speakEnd();
    }

    public void removeView(String tag) {
        Log.i(TAG, "removeView tag: " + tag + " engineView tag: " + currentTag);
        if (TextUtils.equals(tag, currentTag)) {
            removeFaceParent();
            String tmp = (String) previousTags.pop();
            Log.i(TAG, "removeView previousTags: " + tmp);
            if (TextUtils.equals(tmp, tag) && !previousTags.empty()) {
                String previousTag = (String) previousTags.peek();
                Log.i(TAG, "removeView previousTags: notifyActivePush: " + previousTag);
                notifyActivePush(previousTag);
            }
        }
    }

    private void removeFaceParent() {
        View engineView = getRendererView();
        if (engineView.getParent() != null) {
            Log.i(TAG, "removeFaceParent engineView tag: " + currentTag);
            FrameLayout faceview = (FrameLayout) engineView.getParent();
            faceview.removeView(engineView);
            currentTag = null;
            GCManager.resetEGLContext();
        }
    }

    public void playTask(final String task, final OnEmojiListener listener) {
        if (mFace != null) {
            Log.i(TAG, "playTask: " + task);
            mFace.playTask(task, new Runnable() {
                @Override
                public void run() {
                    mMainHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            if (listener != null) {
                                listener.onEmojiEnd(task);
                            }
                        }
                    });
                }
            });
        }
    }

    public void enterBigEye() {
        if (mFace != null) {
            mFace.enterFullScreenMode(false);
        }
    }

    public void enterSmallEye(float x, float y, boolean needAnimation) {
        if (mFace != null) {
            mFace.exitFullScreenMode(0.38f, x, y, needAnimation);
        }
    }

    public void speak() {
        if (mFace != null) {
            mFace.autoSpeak(true);
        }
    }

    public void speakEnd() {
        if (mFace != null) {
            mFace.autoSpeak(false);
        }
    }

    public void destory() {
        if (mFace != null) {
            mFace.onPause();
        }
    }

    public interface OnEmojiListener {
        void onEmojiEnd(String task);
    }


    public void registerActivePushListener(OnActivePushListener listener) {
        if (mPushListeners != null) {
            if (!mPushListeners.contains(listener)) {
                mPushListeners.add(listener);
            }
        }
    }

    public void unRegisterActivePushListener(OnActivePushListener listener) {
        if (mPushListeners != null && mPushListeners.size() > 0) {
            mPushListeners.remove(listener);
        }
    }

    public void notifyActivePush(String tag) {
        if (mPushListeners != null && mPushListeners.size() > 0) {
            for (OnActivePushListener listener : mPushListeners) {
                listener.activePush(tag);
            }
        }
    }

    public interface OnActivePushListener {
        void activePush(String tag);
    }
}
