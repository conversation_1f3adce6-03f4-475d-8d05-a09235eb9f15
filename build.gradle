// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        //versionCode align with versionName
        //[10000 - 19999] <-> [1.00.00 - 1.99.99]
        currentVersionCode = 13600
        currentVersionName = "1.36.0"
        buildToolsVersion = "28.0.3"
        minSdkVersion = 23
        compileSdkVersion = 28
        targetSdkVersion = 28
        supportLibVersion = "25.3.1"
    }

    repositories {
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin/' }
        jcenter()
        google()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.6.10"
        classpath files('libraries/plugins/react-native-gradle-plugin.jar')
        // : Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

subprojects {
    afterEvaluate { project ->
        if (project.hasProperty("android")) {
            android {
                compileSdkVersion rootProject.ext.compileSdkVersion
                buildToolsVersion rootProject.ext.buildToolsVersion
            }
        }
    }
}

allprojects {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin/' }
        jcenter()
        google()
        mavenCentral()
        maven {
            allowInsecureProtocol true
            url "https://oss.sonatype.org/content/repositories/snapshots/"
        }
        maven {
            allowInsecureProtocol true
            url "http://nexus.ainirobot.com:8081/nexus/content/repositories/securitymastersnapshot"
        }
        maven {
            allowInsecureProtocol true
            credentials {
                username 'launcher_maven'
                password 'kingsoft' }
            url "http://nexus.ainirobot.com:8081/nexus/content/repositories/launchersdksnapshot"
        }
        maven {
            allowInsecureProtocol true
            credentials {
                username 'launcher_maven'
                password 'kingsoft' }
            url "http://nexus.ainirobot.com:8081/nexus/content/repositories/launchersdkrel"
        }

        maven { url "https://jitpack.io" }

        flatDir {
            dirs rootProject.file("libraries/3rdlibs")
            dirs rootProject.file("platform/libs")
            dirs rootProject.file("libraries/3rdlibs").listFiles()
        }

        configurations.all {
            resolutionStrategy.eachDependency { details ->
                if (details.requested.group == 'com.android.support'
                        && !details.requested.name.contains('multidex')) {
                    details.useVersion "${supportLibVersion}"
                }

                if (details.requested.group == 'com.facebook.fresco'
                        && details.requested.name.contains('fresco')) {
                    details.useVersion "2.5.0"
                }
            }
        }
    }
}
