/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.moduleapp.data;

import android.content.ContentProvider;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.Log;

import com.ainirobot.moduleapp.bean.Resource;

public class DBProvider extends ContentProvider {

    private static final String TAG = "DBProvider";
//    public static final String authority = BuildConfig.DBProviderAuthority;
    public static final String authority = "com.ainirobot.moduleapp";
    private DBHelper dbHelper;
    private static final UriMatcher URI_MATCHER = new UriMatcher(UriMatcher.NO_MATCH);
    private static final int CODE_RESOURCE = 8;

    static {
        URI_MATCHER.addURI(authority, Resource.DBField.TABLE_NAME, CODE_RESOURCE);
    }

    @Override
    public boolean onCreate() {
        Log.i(TAG, "onCreate");
        this.dbHelper = new DBHelper(this.getContext(), DBHelper.DB_NAME, null,
                DBHelper.DATABASE_VERSION);
        //remotecontrol 触发数据库创建
        return false;
    }

    @Nullable
    @Override
    public Cursor query(@NonNull Uri uri, @Nullable String[] projection, @Nullable String selection,
                        @Nullable String[] selectionArgs, @Nullable String sortOrder) {
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        switch (URI_MATCHER.match(uri)) {
            case CODE_RESOURCE:
                return db.query(Resource.DBField.TABLE_NAME, projection, selection, selectionArgs,
                    null, null, sortOrder);

            default:
                break;
        }
        return null;
    }

    @Nullable
    @Override
    public String getType(@NonNull Uri uri) {
        return null;
    }

    @Nullable
    @Override
    public Uri insert(@NonNull Uri uri, @Nullable ContentValues values) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        long rowId = 0;
        Uri insertUri = null;
        switch (URI_MATCHER.match(uri)) {
            case CODE_RESOURCE:
                rowId = db.insert(Resource.DBField.TABLE_NAME, null, values);
                break;

            default:
                break;
        }
        Log.d(TAG, "insert rowID:" + rowId);
        if (rowId > 0) {
            insertUri = ContentUris.withAppendedId(uri, rowId);
        }
        Log.i(TAG, "inserturi: " + insertUri);
        return insertUri;
    }

    @Override
    public int delete(@NonNull Uri uri, @Nullable String selection, @Nullable String[]
            selectionArgs) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        int count = 0;
        switch (URI_MATCHER.match(uri)) {
            case CODE_RESOURCE:
                count = db.delete(Resource.DBField.TABLE_NAME, selection, selectionArgs);
                break;


            default:
                break;
        }
        Log.d(TAG, "delete count:" + count);
        return count;
    }

    @Override
    public int update(@NonNull Uri uri, @Nullable ContentValues values, @Nullable String selection,
                      @Nullable String[] selectionArgs) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        int count = 0;
        switch (URI_MATCHER.match(uri)) {
            case CODE_RESOURCE:
                count = db.update(Resource.DBField.TABLE_NAME, values, selection, selectionArgs);
                break;
            default:
                break;
        }
        return count;
    }
}
