package com.ainirobot.moduleapp.data.table;

import static com.ainirobot.moduleapp.bean.Resource.DBField.FILED_DOWNLOAD_PRIORITY;
import static com.ainirobot.moduleapp.bean.Resource.DBField.FILED_MODULE_FEATURE;
import static com.ainirobot.moduleapp.bean.Resource.DBField.FILED_NAME;
import static com.ainirobot.moduleapp.bean.Resource.DBField.FILED_SWITCH;
import static com.ainirobot.moduleapp.bean.Resource.DBField.TABLE_NAME;

public class TableResource {

    public static final String RESOURCE__DROP = "DROP TABLE IF EXISTS " + TABLE_NAME;

    public static final String RESOURCE_INSERT = "INSERT INTO " + TABLE_NAME + " (" +
        FILED_NAME + ", " + FILED_DOWNLOAD_PRIORITY + ", " + FILED_SWITCH + ", " + FILED_MODULE_FEATURE+ ") VALUES " +
        "('module_skill_home',            1,                         1,                      2)," +
        "('module_guide_home',            1,                         1,                      2)," +
        "('module_welcome',               1,                         1,                      2)," +
        "('module_remote_greet',          1,                         1,                      2)," +
        "('module_reception',             2,                         1,                      12)," +
        "('module_guide',                 2,                         1,                      23)," +
        "('module_qa',                    3,                         1,                      32)," +
        "('module_ride',                  1,                         1,                      2)," +
        "('module_cate_ads',              1,                         1,                      2),"+
        "('module_cafe_menu',             1,                         1,                      2),"+
        "('module_bxd_blessing',          1,                         1,                      2),"+
        "('module_bxd_deliver',           1,                         1,                      2)," +
        "('module_bxd_default_broadcast', 1,                         1,                      2)," +
        "('module_pushtask_message',             1,                         1,                      2)," +
        "('module_ads',                   3,                         1,                      30);";

}
