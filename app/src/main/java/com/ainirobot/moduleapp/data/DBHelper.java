/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.moduleapp.data;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.ainirobot.moduleapp.data.table.TableResource;

public class DB<PERSON>elper extends SQLiteOpenHelper {

    private static final String TAG = "DBHelper";

    public static final int DATABASE_VERSION = 110;

    public static final String DB_NAME = "module_db.db";

    public DBHelper(Context context, String name, SQLiteDatabase.CursorFactory factory, int
            version) {
        super(context, name, factory, version);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        Log.i(TAG, "onCreate");
        createDB(db);
        //copyDataToDataBase(context, db);
        initDataBase(db);
    }

    private void initDataBase(SQLiteDatabase db) {
        Log.d(TAG, "init database success");
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.i(TAG, "Upgrading database from version " + oldVersion
                + " to " + newVersion + ".");
        upgradeIntentAndFeatureTables(db);

        Log.i(TAG, "init download db");
        initDownloadDB(db);
        initModuleResource(db);
    }

    @Override
    public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.i(TAG, "Downgrading database from version " + oldVersion
                + " to " + newVersion + ".");
        upgradeIntentAndFeatureTables(db);

        Log.i(TAG, "init download db");
        initDownloadDB(db);
        initModuleResource(db);
    }

    private void upgradeIntentAndFeatureTables(SQLiteDatabase db) {
        initDataBase(db);
    }

    private void initDownloadDB(SQLiteDatabase db) {
        db.execSQL(" DROP TABLE IF EXISTS download");
        String downloadSql = "CREATE TABLE download(_id integer primary key autoincrement, "+
                "url text, " +
                "date integer,"+
                "path text,"+
                "state text, "+
                "break_continue integer, "+
                "length integer"+
                ")";
        db.execSQL(downloadSql);
    }

    private void initModuleResource(SQLiteDatabase db) {
        db.execSQL(TableResource.RESOURCE__DROP);
        String createResourceSql = "CREATE TABLE resource(_id integer primary key autoincrement, " +
            "resource_name text DEFAULT ''," +
            "resource_download_priority integer DEFAULT 3," +
            "resource_switch integer DEFAULT 1," +
            "module_feature integer," +
            "FOREIGN KEY (module_feature) REFERENCES feature(module_feature)" +
            ")";
        db.execSQL(createResourceSql);
        db.execSQL(TableResource.RESOURCE_INSERT);

    }

    private void createDB(SQLiteDatabase db) {
        String featureSql = "CREATE TABLE feature(_id integer primary key autoincrement," +
                "module_feature integer UNIQUE," +
                "module_name text," +
                "module_priority integer," +
                "module_background integer" +
                ")";

        String intentSql = "CREATE TABLE intent(_id integer primary key autoincrement, " +
                "module_intent varchar(50)," +
                "module_feature integer," +
                "module_pattern text," +
                "module_pattern_type integer"
                + ")";

        String testCaseSql = "CREATE TABLE testcase(_id integer primary key autoincrement, " +
                "case_id integer UNIQUE," +
                "case_name text," +
                "starttime integer," +
                "endtime integer"
                + ")";

        String testDataSql = "CREATE TABLE testdata(_id integer primary key autoincrement, " +
                "data_text text," +
                "module_intent varchar(50)," +
                "case_id integer," +
                "data_time integer"
                + ")";

        String routeLayoutSql = "CREATE TABLE route(_id integer primary key autoincrement, " +
                "route_name text," +
                "map_name text," +
                "interval_time integer," +
                "pause_time integer," +
                "dock_index_array text," +
                "coordinate text" +
                ")";

        String personSql = "CREATE TABLE person(_id integer primary key autoincrement, " +
                "remote_id text," +
                "person_name text," +
                "person_identity integer," +
                "register_type integer," +
                "register_time text," +
                "book_time text," +
                "invitation_time text," +
                "leave_time text" +
                ")";

        db.execSQL(featureSql);
        db.execSQL(intentSql);
        db.execSQL(testCaseSql);
        db.execSQL(testDataSql);
        db.execSQL(routeLayoutSql);
        db.execSQL(personSql);

        initDownloadDB(db);
        initModuleResource(db);
    }
}
