package com.ainirobot.moduleapp;

import static android.view.WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;
import static android.view.WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
import static android.view.WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.PixelFormat;
import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.PowerManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.ainirobot.moduleapp.bi.BiRelocationFailReport;
import com.ainirobot.moduleapp.test.TestActivity;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.BuildConfig;

public class MainActivity extends Activity {

    private static final String TAG = "Platform_"+MainActivity.class.getSimpleName();

    private AnimationDrawable mAnimationDrawable;
    private ImageView mEveLoadingImage;
    private TextView mEveTipText;
    private ImageView mEveRebootButtom;
    private Handler mHandler;
    private BroadcastReceiver broadcastReceiver;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.i(TAG, "onCreate");
        setContentView(R.layout.activity_main);
        initWindowsParam();
        new Handler().post(() -> {
            Log.i(TAG, "onCreate delay invoke base for something");
            BaseApplication.getApplication().onActivityCreate();
        });
        try {
            mAnimationDrawable = (AnimationDrawable) getDrawable(R.drawable.anim_eve_loading);
            mEveLoadingImage = (ImageView) findViewById(R.id.eve_loading);
            mEveTipText = (TextView) findViewById(R.id.eve_tip);
            mEveRebootButtom = (ImageView) findViewById(R.id.eve_reboot);
            mEveRebootButtom.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Log.d(TAG, "onClick: " + BuildConfig.DEBUG);
                    try {
                        if (BuildConfig.DEBUG) {
                            startActivity(new Intent(MainActivity.this, TestActivity.class));
                        } else {
                            Log.i(TAG, "click reboot!");
                            showRebooting();
                            new Handler().postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    reboot("99999");
                                }
                            }, 2 * 1000);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
            mEveLoadingImage.setImageDrawable(mAnimationDrawable);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        } finally {

        }
        initTestViewReceiver();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        new Handler().post(() -> {
            Log.i(TAG, "onNewIntent delay invoke base for something");
            BaseApplication.getApplication().onActivityCreate();
        });
    }

    private void initWindowsParam() {
        WindowManager.LayoutParams wmParams = new WindowManager.LayoutParams();
        wmParams.type = WindowManager.LayoutParams.TYPE_APPLICATION;
        wmParams.format = PixelFormat.RGBA_8888;
        wmParams.flags = FLAG_LAYOUT_IN_SCREEN | FLAG_NOT_TOUCH_MODAL | FLAG_NOT_FOCUSABLE;
        wmParams.gravity = Gravity.CENTER_HORIZONTAL | Gravity.TOP;
        wmParams.width = 100;
        wmParams.height = 100;
    }

    @Override
    protected void onStart() {
        Log.i(TAG, "onStart");
        super.onStart();
    }

    @Override
    protected void onStop() {
        Log.i(TAG, "onStop");
        super.onStop();
    }

    @Override
    protected void onResume() {
        Log.i(TAG, "onResume");
        super.onResume();
        findViewById(R.id.eve_loading_layout).setVisibility(View.GONE);
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                resumeEveLoading();
            }
        }, 2 * 1000);
    }

    @Override
    protected void onPause() {
        Log.i(TAG, "onPause");
        pauseEveLoading();
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        Log.i(TAG, "onDestroy");
        unregisterReceiver(broadcastReceiver);
        super.onDestroy();
    }

    private void resumeEveLoading() {
        try {
            findViewById(R.id.eve_loading_layout).setVisibility(View.VISIBLE);
            mEveRebootButtom.setVisibility(View.GONE);
            mEveLoadingImage.setImageDrawable(mAnimationDrawable);
            mAnimationDrawable.start();
            mEveTipText.setText(R.string.eve_loading_tip);
            mHandler = new Handler();
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    showRebootButton();
                }
            }, 5 * 1000);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        } finally {

        }
    }

    private void showRebooting() {
        reportReboot();
        mEveRebootButtom.setVisibility(View.GONE);
        mEveLoadingImage.setImageDrawable(mAnimationDrawable);
        mAnimationDrawable.start();
        mEveTipText.setText(R.string.eve_rebooting);
    }

    private void showRebootButton() {
        try {
            mAnimationDrawable.stop();
            mEveLoadingImage.setImageResource(R.drawable.ic_ok);
            mEveTipText.setText(R.string.eve_loading_reboot);
            mEveRebootButtom.setVisibility(View.VISIBLE);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        } finally {

        }
    }

    private void pauseEveLoading() {
        try {
            findViewById(R.id.eve_loading_layout).setVisibility(View.GONE);
            mEveRebootButtom.setVisibility(View.GONE);
            mAnimationDrawable.stop();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        } finally {

        }
    }

    private void reboot(String reason) {
        PowerManager powerManager = (PowerManager) BaseApplication.getContext()
                .getSystemService(Context.POWER_SERVICE);
        if (powerManager != null) {
            try {
                powerManager.reboot(reason);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void reportReboot() {
        BiRelocationFailReport report = new BiRelocationFailReport(getBaseContext());
        report.addActionType("eve_black_click_reboot");
        report.report();
    }

    private void initTestViewReceiver() {
        IntentFilter intentFilter = new IntentFilter("platform_test");
        broadcastReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (intent == null) {
                    Toast.makeText(MainActivity.this, "Please add StringExtra 'fragment' ", Toast.LENGTH_SHORT).show();
                    return;
                }

                String fragment = intent.getStringExtra("fragment");
                Log.d(TAG, "onTestReceive: " + fragment);
                Intent test = new Intent(MainActivity.this, TestActivity.class);
                //adb shell am broadcast -a platform_test --es fragment "fragment_elevator"
                if (TextUtils.isEmpty(fragment) || TestActivity.FRAGMENT_ELEVATOR.equals(fragment)) {
                    test.putExtra("fragment_extra", TestActivity.FRAGMENT_ELEVATOR);
                    //adb shell am broadcast -a platform_test --es fragment "fragment_uvc"
                } else if (TestActivity.FRAGMENT_UVC.equals(fragment)) {
                    test.putExtra("fragment_extra", TestActivity.FRAGMENT_UVC);
                }
                context.startActivity(test);
            }
        };
        registerReceiver(broadcastReceiver, intentFilter);
    }
}
