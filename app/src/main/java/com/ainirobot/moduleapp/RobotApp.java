package com.ainirobot.moduleapp;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.PlatformInit;
import com.ainirobot.platform.utils.LocalUtils;
import com.ainirobot.platform.utils.SystemUtils;

public class RobotApp extends BaseApplication {

    private static final String TAG = RobotApp.class.getSimpleName();

    @Override
    public void onCreate() {
        super.onCreate();
        if (SystemUtils.isMainProcess(this)) {
            LocalUtils.storage2SystemSettings(this, Definition.VERSION_MODULE_APP, BuildConfig.VERSION_NAME);
            PlatformInit.getInstance().init();
        }
    }

    @Override
    public void initPlatform() {
        Log.i(TAG, "initPlatform");
    }

    @Override
    public boolean getDefaultReactNativeAutoSwitch() {
        return true;
    }

    @Override
    public String getDefaultReactNativeAppId() {
        return super.getDefaultReactNativeAppId();
    }

    @Override
    public boolean isForceSwitchRomOpk() {
        return true;
    }
}
