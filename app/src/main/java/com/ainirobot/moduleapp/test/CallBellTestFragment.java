package com.ainirobot.moduleapp.test;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.moduleapp.R;
import com.ainirobot.platform.callbutton.CallButtonManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CallBellTestFragment extends Fragment implements View.OnClickListener, PlaceListAdapter.ItemClickListener {
    private static final String TAG = "CallBellTestFragment";
    private View mContentView;
    private PlaceListAdapter mRoomAdapter;
    private TextView mTvLog;
    private EditText mServerIp;
    private EditText mInputBtnId;

    private Map<String, String> buttonMap = new HashMap<>();
    private String curSelectPosName;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mContentView = LayoutInflater.from(getActivity()).inflate(R.layout.fragment_test_call_bell, container, false);
        mContentView.findViewById(R.id.btn_connect).setOnClickListener(this);
        mContentView.findViewById(R.id.btn_config_finish).setOnClickListener(this);
        mContentView.findViewById(R.id.btn_item_finish).setOnClickListener(this);

        mRoomAdapter = new PlaceListAdapter(getContext());
        RecyclerView roomList = mContentView.findViewById(R.id.place_list);
        roomList.setLayoutManager(new GridLayoutManager(getContext(), 3));
        roomList.setAdapter(mRoomAdapter);
        mRoomAdapter.setClickListener(this);


        mTvLog = mContentView.findViewById(R.id.button_map_log);
        mServerIp = mContentView.findViewById(R.id.server_ip);
        mServerIp.setImeOptions(EditorInfo.IME_ACTION_DONE);
        mInputBtnId = mContentView.findViewById(R.id.input_btn_id);
        mInputBtnId.setImeOptions(EditorInfo.IME_ACTION_DONE);
        return mContentView;
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.getPointList();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_connect:
                Log.d(TAG, "mServerIp=" + mServerIp.getText().toString());
                CallButtonManager.getInstance().connect(mServerIp.getText().toString());
                break;

            case R.id.btn_config_finish:
                CallButtonManager.getInstance().syncButtonMapping(buttonMap);
                break;

            case R.id.btn_item_finish:
                if (!TextUtils.isEmpty(mInputBtnId.getText().toString()) && !TextUtils.isEmpty(this.curSelectPosName)) {
                    Log.d(TAG, "mInputBtnId=" + mInputBtnId.getText().toString() +
                            " this.curSelectPosName=" + this.curSelectPosName);
                    buttonMap.put(mInputBtnId.getText().toString(), this.curSelectPosName);
                    mInputBtnId.setText("");
                    mTvLog.setText(buttonMap.toString());
                }
                break;
        }
    }

    /**
     * 获取当前地图下所有地点
     */
    private void getPointList() {
        List<Pose> poseList = RobotApi.getInstance().getPlaceList();
        Log.d(TAG, "poseList=" + poseList);
        mRoomAdapter.setData(poseList);
    }

    @Override
    public void onItemClick(Pose pose) {
        this.curSelectPosName = pose.getName();
    }
}
