package com.ainirobot.moduleapp.test;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TextView;

import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.moduleapp.R;
import com.ainirobot.platform.component.ProTrayLEDComponent;
import com.ainirobot.platform.component.UvcCameraComponent;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.light.ProTrayLedBean;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

public class UvcTestFragment extends Fragment implements View.OnClickListener {

    private static final String TAG = UvcTestFragment.class.getSimpleName();
    private MyCommandListener singleListener;
    private MyCommandListener conListener;
    private StatusListener uvcStatusListener;
    private View mContentView;
    private TextView mTvLog;
    private ScrollView mScrollView;
    private UiHandler uiHandler;

    private static final int MSG_START_SINGLE = 1;
    private static final int MSG_START_CONTINUE = 2;
    private static final int MSG_CONTINUE_DATA = 3;
    private static final int MSG_STOP_CONTINUE = 4;
    private UvcCameraComponent UVCcomponent;;
    private ProTrayLEDComponent LedComponent;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mContentView = LayoutInflater.from(getActivity()).inflate(R.layout.fragment_test_uvc, container, false);
        mContentView.findViewById(R.id.btn_single).setOnClickListener(this);
        mContentView.findViewById(R.id.btn_continue).setOnClickListener(this);
        mContentView.findViewById(R.id.btn_stop_continue).setOnClickListener(this);

        mTvLog = mContentView.findViewById(R.id.log);
        mScrollView = mContentView.findViewById(R.id.sv_log);
        return mContentView;
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        uiHandler = new UiHandler(Looper.getMainLooper());
        UVCcomponent = new UvcCameraComponent(UvcCameraComponent.class.getSimpleName(),
                Looper.getMainLooper());
        LedComponent = new ProTrayLEDComponent(ProTrayLEDComponent.class.getSimpleName(),
                Looper.getMainLooper());
        super.onViewCreated(view, savedInstanceState);
    }

    private void testCmpSingle(){

        try {
            JSONObject json = new JSONObject();
            json.put(ComponentParams.UvcCamera.PARAM_START_CLASSIFY_TYPE, "single");
            UVCcomponent.start(json.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void testCmpContinue(){
        try {
            JSONObject json = new JSONObject();
            json.put(ComponentParams.UvcCamera.PARAM_START_CLASSIFY_TYPE, "continue");
            UVCcomponent.start(json.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void testCmpContinueStop(){
        UVCcomponent.stop(50);
    }

    private void testTrayLed(){
        try {
            ArrayList<ProTrayLedBean> beans = new ArrayList<>();
            beans.add(new ProTrayLedBean(1,false));
            beans.add(new ProTrayLedBean(2,false));
            beans.add(new ProTrayLedBean(3,false));

            JSONObject json = new JSONObject();
            json.put(ComponentParams.ProTrayLED.PARAM_TRAY_LED_FLOOR, new Gson().toJson(beans));
            json.put(ComponentParams.ProTrayLED.PARAM_TRAY_LED_ALL, false);
            LedComponent.start(json.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }




    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_single:
                testCmpSingle();
                break;
            case R.id.btn_continue:
                testCmpContinue();
                break;
            case R.id.btn_stop_continue:
                testCmpContinueStop();
                testTrayLed();
                break;
        }
    }

    class MyCommandListener extends CommandListener {

        private int mode = 0;

        MyCommandListener(int mode) {
            this.mode = mode;
        }


        @Override
        public void onResult(int result, String message, String extraData) {
            super.onResult(result, message, extraData);
            Log.d(TAG, "Mode :" + mode + " onResult result : " + result + ",message : " + message + ", extraData : " + extraData);
            Message obtain = Message.obtain();
            obtain.what = mode;
            obtain.obj = message;
            uiHandler.sendMessage(obtain);
        }

        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            super.onStatusUpdate(status, data, extraData);
            Log.d(TAG, "Mode :" + mode + " onStatusUpdate status : " + status + ",message : " + data + ", extraData : " + extraData);
        }

        @Override
        public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
            super.onError(errorCode, errorString, extraData);
            Log.d(TAG, "Mode :" + mode + " onError errorCode : " + errorCode + ",message : " + errorString + ", extraData : " + extraData);
        }
    }

    private final class UiHandler extends Handler {

        public UiHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {

            String message = (String) msg.obj;
            Log.d(TAG, "message : " + message);
            switch (msg.what) {
                case MSG_START_SINGLE:
                    print(message, false, true);
                case MSG_START_CONTINUE:
                    print(message, false, false);
                    break;
                case MSG_CONTINUE_DATA:
                    print(message, false, false);
                    break;
            }
        }
    }

    private void print(final String msg, final boolean highlight, final boolean clearPrint) {
        if (clearPrint) {
            mTvLog.setText("");
        }
        mTvLog.append(msg + "\n");
        mScrollView.fullScroll(ScrollView.FOCUS_DOWN);
    }
}
