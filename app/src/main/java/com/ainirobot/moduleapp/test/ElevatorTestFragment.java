package com.ainirobot.moduleapp.test;

import static com.ainirobot.platform.component.definition.ComponentResult.RESULT_NAVIGATION_ARRIVED;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.IBinder;
import android.os.RemoteException;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.coreservice.utils.ZipUtils;
import com.ainirobot.moduleapp.R;
import com.ainirobot.moduleapp.bean.TestPlaceBean;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.react.EveActivity;
import com.ainirobot.platform.react.server.control.ComponentManager;
import com.ainirobot.platform.react.server.utils.ComponentUtils;
import com.ainirobot.platform.rn.listener.IRNFinishListener;
import com.ainirobot.platform.rn.listener.IRNStatusListener;
import com.ainirobot.platform.speech.SpeechApi;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class ElevatorTestFragment extends Fragment {
    private static final String TAG = "ElevatorTestFragment";

    private View mContentView;
    private Context mContext;
    private TextView selectButton;
    private TextView chargeButton;
    private TextView tvTitle;
    private AppCompatTextView tvMsg;
    private TextView tvInfo;
    private FrameLayout mLeadContainer;
    private List<TestPlaceBean> placeList;
    //导航类型 0：高级导航  1：原NavigationComponent
    private int naviType = 0;
    private int naviCount = 0;
    private int MAX_RETRY_COUNT = 100;
    private long naviStartTime;
    private DebugToolUtils debugToolUtils;

    //测试异常中断，在上报near destination时重启一次组件
    private boolean isStopped = false;

    private View leadTitleDes;
    private TextView leadTitleRoom, leadConfirmBtn, leadCancelBtn, leadDefaultBtn;

    private SelectDestinationDialog mSelectRoomDialog;

    private List<MultiFloorInfo> mDatas;
    private ElevatorNavigationModule mElevatorNavigationModule;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        mContentView = LayoutInflater.from(getActivity()).inflate(R.layout.fragment_elevator_test, container, false);
        initData();
        initView();
        return mContentView;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        debugToolUtils.stop();
    }

    private void initData() {
        Log.d(TAG, "initData");
        SpeechApi.getInstance().playText(getString(R.string.lead_tts_input_room_manual), null);
        initAllMapInfo();

        mElevatorNavigationModule = new ElevatorNavigationModule();
    }

    private void initAllMapInfo() {
        RobotApi.getInstance().getMultiFloorConfigAndPose(Definition.DEBUG_REQ_ID, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Type type = new TypeToken<ArrayList<MultiFloorInfo>>() {
                }.getType();
                try {
                    Log.d(TAG, "initAllMapInfo: " + result + " msg:" + message);
                    Gson gson = new Gson();
                    message = ZipUtils.unzipMapData(gson, message);
                    mDatas = gson.fromJson(message, type);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void initView() {
        mContext = getContext();
        mContentView.findViewById(R.id.lead_title).setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                backToEventActivity();
                return false;
            }
        });
        mLeadContainer = mContentView.findViewById(R.id.multi_storey_lead_container);
        showLeadEntry();
    }

    private void backToEventActivity() {
        Intent intentEve = new Intent();
        intentEve.setClass(getContext(), EveActivity.class);
        intentEve.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        getContext().startActivity(intentEve);
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mSelectRoomDialog != null) {
            mSelectRoomDialog.dismiss();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private void showLeadEntry() {
        mLeadContainer.removeAllViews();
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.fragment_lead_prepare_entry, mLeadContainer);
        selectButton = rootView.findViewById(R.id.delivery_card_expand_select_button);
        selectButton.setVisibility(View.VISIBLE);
        selectButton.setOnClickListener(view -> {
            showSelectDest();
        });
        chargeButton = rootView.findViewById(R.id.delivery_auto_charge_button);
        chargeButton.setVisibility(View.VISIBLE);
        chargeButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showChargeDest("回充点");
            }
        });
        leadTitleDes = rootView.findViewById(R.id.lead_title_des_layout);
        leadTitleDes.setVisibility(View.VISIBLE);
        leadTitleRoom = rootView.findViewById(R.id.lead_title_room);
        leadTitleRoom.setVisibility(View.GONE);

        initDebugView(rootView);
    }

    private void showChargeDest(final String dest) {
        mLeadContainer.removeAllViews();
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.fragment_lead_prepare_confirm, mLeadContainer);
        tvTitle = rootView.findViewById(R.id.tvDestination);
        tvMsg = rootView.findViewById(R.id.tvDestinationDesc);
        tvTitle.setText(dest);
        leadCancelBtn = rootView.findViewById(R.id.lead_cancel);
        leadCancelBtn.setOnClickListener(view -> {
            leadConfirmBtn.setEnabled(true);
            leadConfirmBtn.setVisibility(View.VISIBLE);
            cancelMultiCharge();
            showLeadEntry();
            SpeechApi.getInstance().playText(getString(R.string.lead_tts_confirm_room_cancel), null);
        });
        leadConfirmBtn = rootView.findViewById(R.id.lead_confirm);
        leadConfirmBtn.setOnClickListener(view -> {
            multiCharge();
            leadConfirmBtn.setEnabled(false);
            leadConfirmBtn.setVisibility(View.GONE);
        });
        SpeechApi.getInstance().playText(getString(R.string.lead_tts_confirm_room, dest), null);
    }

    public void multiCharge() {
        ComponentManager.getInstance().start(ComponentUtils.COMPONENT_CHARGE_ELEVATOR, TAG, "");

        ComponentManager.getInstance().setComponentListener(ComponentUtils.COMPONENT_CHARGE_ELEVATOR, TAG, new IRNFinishListener() {
            @Override
            public void onFinish(int result, String message, String extraData) throws RemoteException {
                eventListener.onFinish(result, message, extraData);
            }

            @Override
            public IBinder asBinder() {
                return null;
            }
        });

        ComponentManager.getInstance().setStatusListener(ComponentUtils.COMPONENT_CHARGE_ELEVATOR, TAG, new IRNStatusListener() {
            @Override
            public void onStatusUpdate(int status, String data, String extraData) throws RemoteException {
                eventListener.onStatusUpdate(status, data, extraData);
            }

            @Override
            public IBinder asBinder() {
                return null;
            }
        });
    }

    public void cancelMultiCharge() {
        ComponentManager.getInstance().stop(ComponentUtils.COMPONENT_CHARGE_ELEVATOR, TAG, 500);
    }


    private void initDebugView(View rootView) {
        debugToolUtils = new DebugToolUtils(getContext());
        List<DebugToolUtils.TestMethod> testMethods = debugToolUtils.initDebugMethod(getContext(), rootView);

        RecyclerView recyclerView = rootView.findViewById(R.id.list_container);
        recyclerView.setLayoutManager(new StaggeredGridLayoutManager(3, StaggeredGridLayoutManager.VERTICAL));
        recyclerView.setAdapter(new RecyclerView.Adapter<DebugToolUtils.TestViewHolder>() {
            @Override
            public DebugToolUtils.TestViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
                View view = LayoutInflater.from(parent.getContext()).inflate(android.R.layout.simple_list_item_1, null, false);
                return new DebugToolUtils.TestViewHolder(view);
            }

            @Override
            public void onBindViewHolder(DebugToolUtils.TestViewHolder holder, int position) {
                holder.name.setText(testMethods.get(position).title);

                Log.d(TAG, "initDebugView: " +
                        "title: " + testMethods.get(position).title +
                        ",method:" + testMethods.get(position).method +
                        ",position: " + position);

                holder.name.setOnClickListener(v -> {
                    try {
                        DebugToolUtils.class.getDeclaredMethod(testMethods.get(position).method, DebugToolUtils.TestMethod.class)
                                .invoke(debugToolUtils, testMethods.get(position));
                    } catch (Exception e) {
                        e.printStackTrace();
                        Toast.makeText(mContext, "没有找到:" + testMethods.get(position).title
                                + "的方法:" + testMethods.get(position).method, Toast.LENGTH_SHORT).show();
                    }
                });
            }

            @Override
            public int getItemCount() {
                return testMethods.size();
            }
        });

    }

    private void showSelectDest() {
        if (null == mDatas) {
            Toast.makeText(mContext, "正在加载地图", Toast.LENGTH_SHORT).show();
            return;
        }
        if (mDatas.isEmpty()) {
            Toast.makeText(mContext, "多层地图为空，请配置", Toast.LENGTH_SHORT).show();
            return;
        }
        mSelectRoomDialog = new SelectDestinationDialog(mContext, mContentView, mDatas);
        mSelectRoomDialog.setSelectListener(new SelectDestinationDialog.SelectRoomListener() {
            @Override
            public void onStart() {

            }

            @Override
            public void onStop() {
                initAllMapInfo();
            }

            @Override
            public void onConfirm(List<TestPlaceBean> placeBeans, String loopCount) {
                Log.d(TAG, "lead places=" + placeBeans + "  loopCount: " + loopCount);
                placeList = placeBeans;
                mSelectRoomDialog.dismiss();
                try {
                    int count = Integer.getInteger(loopCount);
                    if (count >= 1) {
                        MAX_RETRY_COUNT = count;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (placeBeans != null && placeBeans.size() > 0) {
                    showConfirmDest(placeBeans);
                }
            }
        });
        mSelectRoomDialog.show();
    }


    private void showConfirmDest(List<TestPlaceBean> placeBeans) {
        mLeadContainer.removeAllViews();
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.fragment_lead_prepare_confirm, mLeadContainer);
        tvTitle = rootView.findViewById(R.id.tvDestination);
        tvMsg = rootView.findViewById(R.id.tvDestinationDesc);
        tvInfo = rootView.findViewById(R.id.tvInfo);

        updateTitle(placeBeans);

        String dest = placeBeans.get(0).getPlaceName();
        int floor = placeBeans.get(0).getFloorIndex();
        leadCancelBtn = rootView.findViewById(R.id.lead_cancel);
        leadCancelBtn.setOnClickListener(view -> {
            leadConfirmBtn.setEnabled(true);
            leadConfirmBtn.setVisibility(View.VISIBLE);
            // 重设状态
            stopNavi();
            showLeadEntry();
            SpeechApi.getInstance().playText(getString(R.string.lead_tts_confirm_room_cancel), null);
        });
        leadConfirmBtn = rootView.findViewById(R.id.lead_confirm);
        leadConfirmBtn.setOnClickListener(view -> {
            naviType = 0;   //高级导航
            startNavigation(dest, floor);
            leadConfirmBtn.setEnabled(false);
            leadConfirmBtn.setVisibility(View.GONE);
            leadDefaultBtn.setVisibility(View.GONE);
        });

        leadDefaultBtn = rootView.findViewById(R.id.lead_default);
        leadDefaultBtn.setVisibility(View.VISIBLE);
        leadDefaultBtn.setOnClickListener(view -> {
            naviType = 1;   //NavigationComponent组件
            startNavigation(dest, floor);
            leadConfirmBtn.setEnabled(false);
            leadConfirmBtn.setVisibility(View.GONE);
            leadDefaultBtn.setVisibility(View.GONE);
        });
    }

    /**
     * 启动导航
     *
     * @param destination 目的地
     * @param targetFloor 目标楼层
     */
    private void startNavigation(String destination, int targetFloor) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(ComponentParams.Navigation.PARAM_DESTINATION, destination);
            jsonObject.put(ComponentParams.Navigation.PARAM_TARGET_FLOOR, targetFloor);
            jsonObject.put(ComponentParams.Navigation.PARAM_NAVIGATION_TYPE, naviType);
        } catch (Exception e) {
            Log.e(TAG, "startNavigation: " + e.getMessage());
        }

        Log.d(TAG, "startNavigation: " + jsonObject.toString());
        mElevatorNavigationModule.setViewEventListener(eventListener);
        mElevatorNavigationModule.startNavigation(jsonObject.toString());
    }

    private void stopNavi() {
        naviType = 0;
        naviCount = 0;
        DelayTask.cancel(TAG);
        placeList = new ArrayList<>();
        mElevatorNavigationModule.stopNavigation();
    }

    private final ElevatorNavigationModule.NavigationEventListener eventListener = new ElevatorNavigationModule.NavigationEventListener() {
        @Override
        public void onStatusUpdate(int result, String message, String extraData) {
            updateText(message, extraData);
        }

        @Override
        public void onFinish(int result, String message, String extraData) {
            Log.e(TAG, "onFinish: " + result + " msg:" + message + " extraData:" + extraData);
            if (result == ComponentError.ERROR_NAVIGATION_ALREADY_IN_DESTINATION || result == RESULT_NAVIGATION_ARRIVED) {
                updateText("到达", extraData);
                startNextPlace();
            } else {
                updateText(String.valueOf(result), message + "\n" + extraData);
            }
        }
    };

    //2s后执行下一个点
    private void startNextPlace() {
        if (placeList == null || placeList.size() <= 1) {
            return;
        }
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {

                naviCount++;

                if (MAX_RETRY_COUNT != -1 && naviCount >= MAX_RETRY_COUNT) {
                    Log.i(TAG, " loop navi finish ! total count: " + naviCount);
                    updateTvInfo("循环导航结束 共执行" + naviCount + "次");
                    return;
                }

                TestPlaceBean placeBean = getNextPlace();
                if (placeBean != null) {
                    Log.d(TAG, "start next navi,nextPlace: " + placeBean);
                    updateTvInfo("循环导航 " + naviCount + "/" + MAX_RETRY_COUNT + "; 目的地：" + placeBean.getPlaceName());
                    startNavigation(placeBean.getPlaceName(), placeBean.getFloorIndex());

                    //中断测试
//                    if (!TextUtils.equals(placeBean.getPlaceName(), "wlj")) {
//                        interruptAction(true);
//                    }
                }



            }
        });
    }

    private void interruptAction(boolean enable) {
        isStopped = enable;
        if (isStopped) {
            DelayTask.cancel(MAX_RETRY_COUNT);
            DelayTask.submit(MAX_RETRY_COUNT, new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "interrupt ! ");
                    stopNavi();
                    TestPlaceBean placeBean = getNextPlace();
                    if (placeBean != null) {
                        startNavigation(placeBean.getPlaceName(), placeBean.getFloorIndex());
                    }
                }
            }, 70);
        }
    }

    private TestPlaceBean getNextPlace() {
        try {
            return placeList.get(naviCount % placeList.size());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void updateTvInfo(String info) {
        if (null != tvInfo) {
            FragmentActivity activity = getActivity();
            if (null == activity) {
                return;
            }
            activity.runOnUiThread(() -> {
                tvInfo.setText(info);
            });
        }
    }


    private void updateTitle(List<TestPlaceBean> placeBeans) {
        if (placeBeans.size() == 1) {
            tvTitle.setText(placeBeans.get(0).getPlaceName());
            SpeechApi.getInstance().playText(getString(R.string.lead_tts_confirm_room, placeBeans.get(0).getPlaceName()), null);
        } else {
            tvTitle.setText("循环执行");
            StringBuilder places = new StringBuilder();
            for (int i = 0; i < placeBeans.size(); i++) {
                places.append(placeBeans.get(i).getPlaceName());
                if (i != placeBeans.size() - 1) {
                    places.append(" - ");
                }
            }
            tvMsg.setText(places);
            naviStartTime = System.currentTimeMillis();
        }
    }

    private void updateText(String title, String msg) {
        FragmentActivity activity = getActivity();
        if (null == activity) {
            return;
        }
        activity.runOnUiThread(() -> {
            if (null != tvTitle) {
                tvTitle.setText(title);
            }
            if (null != tvMsg) {
                tvMsg.setText(msg);
            }
        });
    }
}
