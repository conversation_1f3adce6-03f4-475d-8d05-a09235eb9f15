package com.ainirobot.moduleapp.test;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.moduleapp.R;

import java.util.ArrayList;
import java.util.List;

public class PlaceListAdapter extends RecyclerView.Adapter<PlaceListAdapter.RoomViewHolder> {
    private Context mContext;
    private List<Pose> mDataList = new ArrayList<>();

    private int mSelectPosition = -1;
    private ItemClickListener mClickListener;

    public interface ItemClickListener {
        void onItemClick(Pose pose);
    }

    public PlaceListAdapter(Context context) {
        mContext = context;
    }

    public void setData(List<Pose> dataList) {
        mDataList.clear();
        mDataList.addAll(dataList);
    }

    public void setClickListener(ItemClickListener clickListener) {
        mClickListener = clickListener;
    }

    public void setSelectPosition(int position) {
        mSelectPosition = position;
    }

    @Override
    public RoomViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.place_list_item,
                parent, false);
        return new RoomViewHolder(view);
    }

    @Override
    public void onBindViewHolder(RoomViewHolder holder, int position) {
        Pose data = mDataList.get(position);
        holder.tvDestination.setText(data.getName());

        if (mSelectPosition == position) {
            holder.tvDestination.setTextColor(mContext.getColor(R.color.color_58c3fe));
        } else {
            holder.tvDestination.setTextColor(mContext.getColor(R.color.black));
        }

        holder.itemView.setOnClickListener(v -> {
            int selectPosition = position;
            mSelectPosition = selectPosition;
            notifyDataSetChanged();

            if (mClickListener != null) {
                mClickListener.onItemClick(data);
            }
        });
    }

    @Override
    public int getItemCount() {
        return mDataList == null ? 0 : mDataList.size();
    }

    static class RoomViewHolder extends RecyclerView.ViewHolder {
        View layoutBg;
        TextView tvDestination;

        public RoomViewHolder(View itemView) {
            super(itemView);
            layoutBg = itemView.findViewById(R.id.place_item_bg);
            tvDestination = itemView.findViewById(R.id.place_item_text);
        }
    }
}
