package com.ainirobot.moduleapp.test;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;

import com.ainirobot.coreservice.client.actionbean.Pose;

import java.math.BigDecimal;

/**
 * Data: 2022/9/1 14:13
 * Author: wanglijing
 * Description: ElevatorRangeView
 */
public class ElevatorRangeView extends View {
    private String TAG = ElevatorRangeView.class.getSimpleName();
    private Point mCenterPoint;
    private ElevatorLocationUtils.RangeInfo mInfo;
    private PointInfo mPointInfo;
    private final float resolution = 200;    //分辨率
    private float screenW;
    private float screenH;
    private boolean isSameToScreen;

    public ElevatorRangeView(Context context) {
        this(context, null);
    }

    public ElevatorRangeView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ElevatorRangeView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        screenW = getWidth();
        screenH = getHeight();
        mCenterPoint = new Point(floatToInt(screenW / 2), floatToInt(screenH / 2));

        if (null == mPointInfo) {
            Log.e(TAG, "onDraw mPointInfo is null");
            return;
        }

        Log.d(TAG, "onDraw \ncenterPose: " + mCenterPoint + " \n" + mPointInfo);


        if (isSameToScreen) {
            canvas.rotate((float) (Math.toDegrees(0 - mInfo.getCenterPose().getTheta() + 90)),
                    mCenterPoint.x,
                    mCenterPoint.y);
        }

        drawElevatorRangeLine(canvas);
        drawElevatorRangePoint(canvas);
        drawCurrentPoint(canvas);
    }


    private void drawElevatorRangeLine(Canvas canvas) {
        Paint paint =new Paint();
        paint.setStrokeWidth(5);
        paint.setColor(mInfo.isInRange()
                ? Color.rgb(0, 238, 255)
                : Color.GRAY);
        paint.setStyle(Paint.Style.FILL_AND_STROKE);

        Path path = new Path();
        path.moveTo(mPointInfo.getTopLeftPoint().x, mPointInfo.getTopLeftPoint().y);
        path.lineTo(mPointInfo.getTopRightPoint().x, mPointInfo.getTopRightPoint().y);
        path.lineTo(mPointInfo.getBottomRightPoint().x, mPointInfo.getBottomRightPoint().y);
        path.lineTo(mPointInfo.getBottomLeftPoint().x, mPointInfo.getBottomLeftPoint().y);

        canvas.drawPath(path,paint);
    }


    private void drawCurrentPoint(Canvas canvas) {

        Paint paint = new Paint();
        paint.setStrokeWidth(20);
        paint.setColor(Color.RED);

        drawRangePoint(canvas, mPointInfo.currentPoint, paint,
                getDrawHint("Robot", mInfo.getCurrentPose()));

    }

    private void drawElevatorRangePoint(Canvas canvas) {
        Paint paint = new Paint();
        paint.setStrokeWidth(10);
        paint.setColor(Color.BLUE);

        drawRangePoint(canvas, mPointInfo.topLeftPoint, paint,
                getDrawHint("TL", mInfo.getTopLeftPose()));

        drawRangePoint(canvas, mPointInfo.topRightPoint, paint,
                getDrawHint("TR", mInfo.getTopRightPose()));

        drawRangePoint(canvas, mPointInfo.bottomLeftPoint, paint,
                getDrawHint("BL", mInfo.getBottomLeftPose()));

        drawRangePoint(canvas, mPointInfo.bottomRightPoint, paint,
                getDrawHint("BR", mInfo.getBottomRightPose()));

        paint.setStrokeWidth(15);
        paint.setColor(Color.GREEN);

        drawRangePoint(canvas, mCenterPoint, paint,
                getDrawHint("CT", mInfo.getCenterPose()));
    }

    private void drawRangePoint(Canvas canvas, Point point, Paint paint, String hint) {
        int x = point.x;
        int y = point.y;

        canvas.drawPoint(x, y, paint);
        canvas.drawText(hint, x + 5, y - 5, paint);
    }

    private String getDrawHint(String poseName, Pose pose) {
        return poseName + "(" + pose.getX() + "," + pose.getY() + ")";
    }

    public int floatToInt(float f) {
        BigDecimal bigDecimal = new BigDecimal(f);
        return bigDecimal.intValue();
    }

    public void updateRange(ElevatorLocationUtils.RangeInfo info) {
        try {
            if (null == info) {
                Log.e(TAG, "updateRange info is null");
                return;
            }
            this.mInfo = info;
            Pose centerPose = info.getCenterPose();
            float x = mCenterPoint.x - (centerPose.getX() * resolution);
            float y = mCenterPoint.y - (centerPose.getY() * resolution);
            Log.d(TAG, "updateRange x: " + x + "  y:" + y);

            mPointInfo = new PointInfo();
            mPointInfo.setCurrentPoint(getPixelByRoverMap(x, y, info.getCurrentPose()));
            mPointInfo.setTopLeftPoint(getPixelByRoverMap(x, y, info.getTopLeftPose()));
            mPointInfo.setTopRightPoint(getPixelByRoverMap(x, y, info.getTopRightPose()));
            mPointInfo.setBottomLeftPoint(getPixelByRoverMap(x, y, info.getBottomLeftPose()));
            mPointInfo.setBottomRightPoint(getPixelByRoverMap(x, y, info.getBottomRightPose()));


            invalidate();

        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "updateRange error " + e.getMessage());
        }
    }

    /**
     * 地图坐标 转 屏幕坐标
     *
     * @param x
     * @param y
     * @param pose 地图坐标
     */
    private Point getPixelByRoverMap(float x, float y, Pose pose) {
        int pointX = floatToInt(x + (pose.getX() * resolution));
        int pointY = floatToInt(y + (pose.getY() * resolution));

        if (pointX > screenW) {
            pointX = floatToInt(screenW);
        }
        if (pointX < 0) {
            pointX = 0;
        }

        if (pointY > screenH) {
            pointY = floatToInt(screenH);
        }
        if (pointY < 0) {
            pointY = 10;
        }

        return new Point(pointX, pointY);
    }

    public void setAngle(boolean sameToScreen){
        this.isSameToScreen = sameToScreen;
    }

    public boolean isSameToScreen(){
        return isSameToScreen;
    }
    private static class PointInfo {
        Point currentPoint;
        Point topLeftPoint;
        Point topRightPoint;
        Point bottomLeftPoint;
        Point bottomRightPoint;

        public Point getCurrentPoint() {
            return currentPoint;
        }

        public void setCurrentPoint(Point currentPoint) {
            this.currentPoint = currentPoint;
        }

        public Point getTopLeftPoint() {
            return topLeftPoint;
        }

        public void setTopLeftPoint(Point topLeftPoint) {
            this.topLeftPoint = topLeftPoint;
        }

        public Point getTopRightPoint() {
            return topRightPoint;
        }

        public void setTopRightPoint(Point topRightPoint) {
            this.topRightPoint = topRightPoint;
        }

        public Point getBottomLeftPoint() {
            return bottomLeftPoint;
        }

        public void setBottomLeftPoint(Point bottomLeftPoint) {
            this.bottomLeftPoint = bottomLeftPoint;
        }

        public Point getBottomRightPoint() {
            return bottomRightPoint;
        }

        public void setBottomRightPoint(Point bottomRightPoint) {
            this.bottomRightPoint = bottomRightPoint;
        }

        @Override
        public String toString() {
            return "PointInfo{" +
                    ", currentPoint=" + currentPoint +
                    ", topLeftPoint=" + topLeftPoint +
                    ", topRightPoint=" + topRightPoint +
                    ", bottomLeftPoint=" + bottomLeftPoint +
                    ", bottomRightPoint=" + bottomRightPoint +
                    '}';
        }
    }

}
