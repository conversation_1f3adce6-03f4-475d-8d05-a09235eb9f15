package com.ainirobot.moduleapp.test;

import static android.view.WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;
import static android.view.WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
import static android.view.WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL;

import android.content.Intent;
import android.graphics.PixelFormat;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.ainirobot.moduleapp.MainActivity;
import com.ainirobot.moduleapp.R;


public class TestActivity extends AppCompatActivity {

    private static final String TAG = MainActivity.class.getSimpleName();
    public static final String FRAGMENT_ELEVATOR = "fragment_elevator";
    public static final String FRAGMENT_UVC = "fragment_uvc";
    FragmentManager fragmentManager;
    FragmentTransaction fragmentTransaction;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.i(TAG, "onCreate");
        setContentView(R.layout.activity_test_main);
        initData();
        initWindowsParam();
        fragmentManager = getSupportFragmentManager();
    }

    private void initData() {
    }

    private void initWindowsParam() {
        WindowManager.LayoutParams wmParams = new WindowManager.LayoutParams();
        wmParams.type = WindowManager.LayoutParams.TYPE_APPLICATION;
        wmParams.format = PixelFormat.RGBA_8888;
        wmParams.flags = FLAG_LAYOUT_IN_SCREEN | FLAG_NOT_TOUCH_MODAL | FLAG_NOT_FOCUSABLE;
        wmParams.gravity = Gravity.CENTER_HORIZONTAL | Gravity.TOP;
        wmParams.width = 100;
        wmParams.height = 100;
    }

    @Override
    protected void onResume() {
        Log.i(TAG, "onCreate");
        super.onResume();
        Intent intent = getIntent();
        if (intent == null){
            Toast.makeText(this,"Need to add intent fragment_extra , when start TestActivity",Toast.LENGTH_SHORT).show();
            return;
        }
        if (TextUtils.isEmpty(intent.getStringExtra("fragment_extra"))
                || FRAGMENT_ELEVATOR.equals(intent.getStringExtra("fragment_extra"))) {
//            startFragment(new ElevatorTestFragment());
            startFragment(new CallBellTestFragment());
        } else if (FRAGMENT_UVC.equals(intent.getStringExtra("fragment_extra"))) {
            startFragment(new UvcTestFragment());
        }
    }

    private void startFragment(Fragment fragment){
        fragmentTransaction = fragmentManager.beginTransaction();
        fragmentTransaction.replace(R.id.fragment_content,fragment);
        fragmentTransaction.commit();
        fragmentTransaction.show(fragment);
    }
}

