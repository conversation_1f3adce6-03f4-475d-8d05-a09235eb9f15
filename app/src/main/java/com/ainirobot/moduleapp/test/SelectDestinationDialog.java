package com.ainirobot.moduleapp.test;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.drawable.BitmapDrawable;
import android.os.Handler;
import android.os.Looper;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.moduleapp.R;
import com.ainirobot.moduleapp.bean.TestPlaceBean;
import com.ainirobot.platform.utils.CommonUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class SelectDestinationDialog implements View.OnClickListener,
        SelectDestinationAdapter.ItemClickListener, SelectFloorAdapter.FloorClickListener {
    private static final String TAG = "SelectDestinationDialog";
    private Dialog mDialog;

    private List<MultiFloorInfo> mDatas;

    private Pose mCurrentSelectRoom = null;
    private int mCurrentSelectFloor = 0;

    private SelectFloorAdapter mFloorAdapter;
    private SelectDestinationAdapter mRoomAdapter;

    private SelectRoomListener mListener;
    private TextView mTitle;
    private LinearLayout layoutLoop;
    private EditText editLoopCount;
    private TextView tvSelected;
    private List<TestPlaceBean> placeList;

    private Handler mMainHandler = new Handler(Looper.getMainLooper());

    public interface SelectRoomListener {
        void onStart();

        void onStop();

        void onConfirm(List<TestPlaceBean> placeBeans, String loopCount);
    }

    public SelectDestinationDialog(Context context, View rootView, List<MultiFloorInfo> dataMap) {
        this.mDatas = dataMap;
        placeList = new ArrayList<>();
        mDialog = createFullscreenDialog(rootView);

        View view = View.inflate(context, R.layout.dialog_select_destination, null);

        mFloorAdapter = new SelectFloorAdapter(context);
        mFloorAdapter.setClickListener(this);

        mRoomAdapter = new SelectDestinationAdapter(context);
        mRoomAdapter.setClickListener(this);

        mTitle = view.findViewById(R.id.delivery_select_dialog_title);
        layoutLoop = view.findViewById(R.id.layout_loop_count);
        editLoopCount = view.findViewById(R.id.edit_loop_count);
        editLoopCount.requestFocus();
        editLoopCount.setOnClickListener(this);
        tvSelected = view.findViewById(R.id.tv_selected_place);

        RecyclerView roomList = view.findViewById(R.id.delivery_select_dialog_list);
        RecyclerView floorList = view.findViewById(R.id.delivery_select_dialog_layer);

        LinearLayout.LayoutParams floorListLp = (LinearLayout.LayoutParams) floorList.getLayoutParams();
//        floorListLp.width = (int) (DimenUtils.getScreenWidthPixels() * 0.16);
        floorList.setLayoutParams(floorListLp);

        LinearLayout.LayoutParams roomListLp = (LinearLayout.LayoutParams) roomList.getLayoutParams();
//        roomListLp.width = (int) (DimenUtils.getScreenWidthPixels() * 0.72);
        roomList.setLayoutParams(roomListLp);

        roomList.setLayoutManager(new GridLayoutManager(context, 3));
        roomList.setAdapter(mRoomAdapter);

        floorList.setLayoutManager(new GridLayoutManager(context, 4));
        floorList.setAdapter(mFloorAdapter);

        view.findViewById(R.id.delivery_select_dialog_close).setOnClickListener(this);
        view.findViewById(R.id.delivery_select_dialog_add).setOnClickListener(this);
        view.findViewById(R.id.delivery_select_dialog_ok).setOnClickListener(this);
        mDialog.setContentView(view);

        Collections.sort(mDatas, (s1, s2) -> Integer.compare(s1.getFloorIndex(), s2.getFloorIndex()));

        Log.d(TAG, "Dialog init");
        mMainHandler.post(() -> {
            if (mFloorAdapter != null) {
                mFloorAdapter.setData(mDatas);
                mFloorAdapter.notifyDataSetChanged();
            }
            MultiFloorInfo multiFloorInfo = this.mDatas.get(mCurrentSelectFloor);
            if (multiFloorInfo != null && mRoomAdapter != null) {
                mRoomAdapter.setData(multiFloorInfo.getPoseList());
                mRoomAdapter.notifyDataSetChanged();
            }
        });
    }

    public void show() {
        if (mDialog != null && !mDialog.isShowing()) {
            mDialog.show();
//            layoutLoop.setVisibility(View.GONE);
//            editLoopCount.setHint("100");
            if (mListener != null) {
                mListener.onStart();
            }
        }
    }

    public void dismiss() {
        if (mDialog != null && mDialog.isShowing()) {
            if (mListener != null) {
                mListener.onStop();
            }
            mMainHandler.removeCallbacksAndMessages(null);
            mDialog.dismiss();
        }
    }

    public void setSelectListener(SelectRoomListener listener) {
        mListener = listener;
    }

    @Override
    public void onItemClick(Pose pose) {
        mCurrentSelectRoom = pose;
    }

    @Override
    public void onFloorClick(int position) {
        if (position < 0 || position >= mDatas.size()) {
            return;
        }
        mCurrentSelectFloor = position;
        List<Pose> currentRooms = mDatas.get(mCurrentSelectFloor).getPoseList();
        if (currentRooms == null) {
            return;
        }
        if (mRoomAdapter != null) {
            mRoomAdapter.setData(currentRooms);
            int roomPosition = currentRooms.indexOf(mCurrentSelectRoom);
            if (roomPosition >= 0) {
                mRoomAdapter.setSelectPosition(roomPosition);
            } else {
                mCurrentSelectRoom = null;
                mRoomAdapter.setSelectPosition(-1);
            }
            mRoomAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.edit_loop_count:
                InputMethodManager imm = (InputMethodManager) v.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.showSoftInput(editLoopCount, InputMethodManager.SHOW_IMPLICIT);
                break;
            case R.id.delivery_select_dialog_close:
                dismiss();
                break;
            case R.id.delivery_select_dialog_add:
                if (null == mCurrentSelectRoom) {
                    Toast.makeText(v.getContext(), "请选择房间号", Toast.LENGTH_SHORT).show();
                    return;
                }
                Toast.makeText(v.getContext(), "已添加", Toast.LENGTH_SHORT).show();
                addSelectList(mCurrentSelectRoom.getName(), mDatas.get(mCurrentSelectFloor).getFloorIndex());
//                layoutLoop.setVisibility(View.VISIBLE);
                break;
            case R.id.delivery_select_dialog_ok:
                if (null == placeList || placeList.size() <= 0) {
                    if (mCurrentSelectRoom == null) {
                        Toast.makeText(v.getContext(), "请选择房间号", Toast.LENGTH_SHORT).show();
                        return;
                    } else {
                        addSelectList(mCurrentSelectRoom.getName(), mDatas.get(mCurrentSelectFloor).getFloorIndex());
                    }
                }
                if (mListener != null) {
                    mListener.onConfirm(placeList, editLoopCount.getText().toString());
                }
                break;
        }
    }

    private void addSelectList(String dest, int floorIndex) {
        if (placeList == null) {
            placeList = new ArrayList<>();
        }
        placeList.add(new TestPlaceBean(dest, floorIndex));

        updateSelected();
    }

    private void updateSelected() {
        if (tvSelected != null) {
            StringBuilder title = new StringBuilder("已选择：");
            for (TestPlaceBean placeBean : placeList) {
                title.append(placeBean.getPlaceName()).append(" - ");
            }
            tvSelected.setText(title);
        }
    }

    private Dialog createFullscreenDialog(View rootView) {
        Context context = rootView.getContext();
        View view = ((Activity) context).findViewById(R.id.activity_main);
        Bitmap bitmap = Bitmap.createBitmap(view.getWidth(), view.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        view.draw(canvas);
        // 模糊处理并保存
        Bitmap blurBg = CommonUtil.blurBitmap(
                context,
                bitmap,
                25
        );

        Dialog dialog = new Dialog(context, R.style.FullScreenDialog);
        Window window = dialog.getWindow();
        if (window != null) {
            // 设置成dialog的背景
            window.setBackgroundDrawable(new BitmapDrawable(context.getResources(), blurBg));
            window.setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        }
        bitmap.recycle();
        final Bitmap finalBlurBg = blurBg;
        dialog.setOnDismissListener(dialog1 -> {
            // 对话框取消时释放背景图bitmap
            if (finalBlurBg != null && !finalBlurBg.isRecycled()) {
                finalBlurBg.recycle();
            }
        });
        return dialog;
    }
}
