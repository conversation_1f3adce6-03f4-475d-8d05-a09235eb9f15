package com.ainirobot.moduleapp.test;

import android.app.AlertDialog;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;

import com.ainirobot.moduleapp.R;

public class DialogElevatorRange extends AlertDialog {
    private static final String TAG = DialogElevatorRange.class.getSimpleName();
    private Context mContext;
    private ElevatorRangeView rangeView;
    private OnFinishListener mListener;
    private ElevatorLocationUtils.RangeInfo info;
    private Button btnSwitchModule;
    private Button btnAngle;

    public DialogElevatorRange(Context context, OnFinishListener listener) {
        super(context);
        this.mContext = context;
        this.mListener = listener;
    }

    public void updateInfo(ElevatorLocationUtils.RangeInfo info) {
        Log.d(TAG, "RangeInfo: " + info);
        this.info = info;
        updateView();
    }


    @Override
    protected void onCreate(final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        View view = LayoutInflater.from(mContext).inflate(R.layout.elevator_range_view, null);
        setContentView(view);

        initView();
        updateView();
    }

    private void initView() {
        Window win = getWindow();
        WindowManager.LayoutParams lp = win.getAttributes();
        lp.height = WindowManager.LayoutParams.MATCH_PARENT;
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        lp.gravity = Gravity.CENTER;
        win.setDimAmount(0.7f);
        setCanceledOnTouchOutside(true);


        rangeView = findViewById(R.id.range_view);

        findViewById(R.id.tv_finish).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (null != mListener) {
                    mListener.onCancel();
                }
            }
        });

        btnSwitchModule = findViewById(R.id.btn_switch_enter);
        btnSwitchModule.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (null != mListener) {
                    btnSwitchModule.setText(mListener.onChangeMode()
                            ? "进梯模式"
                            : "出梯模式");
                }
            }
        });

        btnAngle = findViewById(R.id.btn_angle);
        btnAngle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (null != rangeView) {
                    boolean isSameAngle = !rangeView.isSameToScreen();
                    updateAngle(isSameAngle);
                    btnAngle.setText(isSameAngle
                            ? "与屏幕方向一致"
                            : "与实际方向一致");
                }
            }
        });
    }

    public void updateView() {
        if (null != rangeView) {
            rangeView.updateRange(info);
        }
    }

    public void updateAngle(boolean isSameToScreen) {
        if (null != rangeView) {
            rangeView.setAngle(isSameToScreen);
        }
    }


    public interface OnFinishListener {
        void onCancel();

        boolean onChangeMode();
    }


}
