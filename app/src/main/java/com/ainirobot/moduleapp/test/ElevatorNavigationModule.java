/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.moduleapp.test;


import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.ainirobot.platform.react.server.control.ComponentManager;
import com.ainirobot.platform.react.server.utils.ComponentUtils;
import com.ainirobot.platform.rn.listener.IRNFinishListener;
import com.ainirobot.platform.rn.listener.IRNStatusListener;

import org.json.JSONObject;

public class ElevatorNavigationModule {
    private static final String TAG = ElevatorNavigationModule.class.getSimpleName();
    private String componentName = ComponentUtils.COMPONENT_NAVIGATION_ELEVATOR;

    private NavigationEventListener mViewEventListener;

    public ElevatorNavigationModule() {

    }

    public void startNavigation(String params) {
        try {
            if (TextUtils.isEmpty(params)) {
                Log.i(TAG, "startNavigation: fail params is null");
                return;
            }
            JSONObject object = new JSONObject(params);
            //导航类型，0：高级导航 1：NavigationComponent，默认0
            int naviType = object.optInt(ComponentParams.Navigation.PARAM_NAVIGATION_TYPE,0);
            if (naviType == 1) {
                //普通导航组件
                componentName = ComponentUtils.COMPONENT_NAVIGATION;
            } else {
                //高级导航
                componentName = ComponentUtils.COMPONENT_NAVIGATION_ELEVATOR;
            }

            ComponentManager.getInstance().start(componentName, TAG, params);

            ComponentManager.getInstance().setComponentListener(componentName, TAG, new IRNFinishListener() {
                @Override
                public void onFinish(int result, String message, String extraData) throws RemoteException {
                    mViewEventListener.onFinish(result, message, extraData);
                }

                @Override
                public IBinder asBinder() {
                    return null;
                }
            });
            ComponentManager.getInstance().setStatusListener(componentName, TAG, new IRNStatusListener() {
                @Override
                public void onStatusUpdate(int status, String data, String extraData) throws RemoteException {
                    Log.e(TAG, "onStatusUpdate: " + status + " msg:" + data + " extraData:" + extraData);
                    String title = "";
                    switch (status) {
                        case ComponentStatus.STATUS_DISTANCE_WITH_DESTINATION:
                            title = "导航";
                            break;
                        case ComponentStatus.STATUS_NAVIGATION_AVOID:
                            title = "障碍物";
                            break;
                        case ComponentStatus.STATUS_ELEVATOR_FLOOR_INFO:
                            title = "电梯信息";
                            break;
                        case ComponentStatus.STATUS_ARRIVED_ELEVATOR_GATE:
                            title = "到达电梯口";
                            break;
                        case ComponentStatus.STATUS_ARRIVED_ELEVATOR_CENTER:
                            title = "到达电梯中心";
                            break;
                        case ComponentStatus.STATUS_CALL_ELEVATOR_AND_WAIT:
                            title = "等待电梯";
                            break;
                        case ComponentStatus.STATUS_UPDATE_SWITCH_MAP_START:
                            title = "开始切图";
                            break;
                        case ComponentStatus.STATUS_UPDATE_SWITCH_MAP_SUCCESS:
                            title = "切图成功";
                            break;
                        case ComponentStatus.STATUS_TAKE_ELEVATOR_TO_TARGET_FLOOR_SUCCESS:
                            title = "乘梯成功";
                            break;
                        case ComponentStatus.STATUS_START_BACK_TO_ELEVATOR_GATE:
                            title = "返回电梯口";
                            break;
                        case Definition.STATUS_UPDATE_RELOCATION_SUC:
                            title = "重定位成功";
                            break;

                        default:
                            break;
                    }
                    mViewEventListener.onStatusUpdate(status, title, data + "\n" + extraData);
                }

                @Override
                public IBinder asBinder() {
                    return null;
                }
            });
        } catch (Exception e) {
            Log.i(TAG, Log.getStackTraceString(e));
        }
    }

    public void stopNavigation() {
        ComponentManager.getInstance().stop(componentName, TAG, 500);
    }

    public void setViewEventListener(NavigationEventListener listener) {
        mViewEventListener = listener;
    }

    interface NavigationEventListener {
        void onStatusUpdate(int result, String message, String extraData);

        void onFinish(int result, String message, String extraData);
    }
}
