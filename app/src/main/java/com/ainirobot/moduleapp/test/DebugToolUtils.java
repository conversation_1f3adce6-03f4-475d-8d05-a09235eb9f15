package com.ainirobot.moduleapp.test;

import android.app.AlertDialog;
import android.content.Context;
import android.graphics.Color;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.ainirobot.coreservice.bean.ElevatorRangeInfo;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.moduleapp.R;
import com.ainirobot.moduleapp.RobotApp;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.data.RobotInfo;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class DebugToolUtils {

    private static final String TAG = "DebugToolUtils";
    private final Context context;
    private View editDialogView;
    private AlertDialog dialogBuilder;
    private final Gson mGson = new Gson();
    private DialogElevatorRange rangeDialog;

    public DebugToolUtils(Context context) {
        this.context = context;
    }

    public List<TestMethod> initDebugMethod(Context context, View rootView) {
        List<TestMethod> testMethods = new ArrayList<>();
        testMethods.add(new TestMethod("获得电梯状态", "getElevatorState"));
        testMethods.add(new TestMethod("呼叫电梯", "callElevator", "5"));
        testMethods.add(new TestMethod("释放电梯", "releaseElevator"));
        testMethods.add(new TestMethod("电梯开门", "openElevatorDoor"));
        testMethods.add(new TestMethod("电梯关门", "closeElevatorDoor"));
        testMethods.add(new TestMethod("前方是否有障碍物", "checkIfHasObstacle", "25.093603040871724"));
        testMethods.add(new TestMethod("关闭自动重启", "closeAutoShutDown"));
        testMethods.add(new TestMethod("关闭警报", "closeWarning"));
        testMethods.add(new TestMethod("电梯范围判断", "elevatorRangeListener","1"));
        testMethods.add(new TestMethod("取消电梯范围判断", "cancelElevatorRangeListener"));
        testMethods.add(new TestMethod("重置PresetRomVersion", "resetRomVersion"));


        return testMethods;
    }

    public void checkIfHasObstacle(TestMethod testMethod) {
        showEditDialog(testMethod, new EditListener() {
            @Override
            public void onFinished(String text) {
                RobotApi.getInstance().checkIfHasObstacle(Definition.DEBUG_REQ_ID, -1d, 1d, Double.parseDouble(text), getCommandListener(testMethod));
            }
        });
    }

    public void resetRomVersion(TestMethod testMethod) {
        RobotSettings.storage2SystemSettings(context, "PresetRomVersion", "");
        Toast.makeText(context,"reseted",Toast.LENGTH_SHORT).show();
    }

    public void getElevatorState(TestMethod testMethod) {
        long startTime = System.currentTimeMillis();
        RobotApi.getInstance().getElevatorStatus(Definition.DEBUG_REQ_ID, getCommandListener(testMethod));
    }

    public void callElevator(TestMethod testMethod) {
        showEditDialog(testMethod, new EditListener() {
            @Override
            public void onFinished(String text) {
                RobotApi.getInstance().callElevatorToTargetFloor(Definition.DEBUG_REQ_ID, Integer.parseInt(text), getCommandListener(testMethod));
            }
        });
    }

    public void releaseElevator(TestMethod testMethod) {
        RobotApi.getInstance().releaseElevator(Definition.DEBUG_REQ_ID, getCommandListener(testMethod));
    }

    public void openElevatorDoor(TestMethod testMethod) {
        RobotApi.getInstance().openElevatorDoor(Definition.DEBUG_REQ_ID, getCommandListener(testMethod));
    }

    public void closeElevatorDoor(TestMethod testMethod) {
        RobotApi.getInstance().closeElevatorDoor(Definition.DEBUG_REQ_ID, getCommandListener(testMethod));
    }

    public void closeAutoShutDown(TestMethod testMethod) {
        SettingsUtil.putString(BaseApplication.getContext(), SettingsUtil.ROBOT_SETTING_SHUTDOWN_SWITCH, "0");
    }

    public void closeWarning(TestMethod testMethod) {
        SettingsUtil.putInt(BaseApplication.getContext(), Definition.ROBOT_SETTING_MAP_OUTSIDE_WARNING, 0);
        Toast.makeText(context,"closed",Toast.LENGTH_SHORT).show();
    }

    // ----------------- 监听是否在电梯范围内 ----------------- //

    public void cancelElevatorRangeListener(TestMethod testMethod){
        DelayTask.cancel(TAG);
    }

    public void elevatorRangeListener(TestMethod testMethod){
        //set range update listener
        ElevatorLocationUtils.getInstance().setListener(new ElevatorLocationUtils.RangeListener() {
            @Override
            public void onUpdate(ElevatorLocationUtils.RangeInfo rangeInfo) {
                updateRangeDialog(rangeInfo);
            }
        });

        initRangeView(false);


    }

    private void initRangeView(boolean isEnter) {
        try {
            showRangeDialog();  //showDialog


            Pose centerPose = RobotInfo.getPose("默认电梯中心");
            ElevatorRangeInfo info = getElevatorRangeByName("默认");

            getIsInRange(isEnter,
                    centerPose,
                    info);

        } catch (Exception e) {
            e.printStackTrace();
            Log.e("ElevatorLocationUtils", "onFinished: " + e.getMessage());
            DelayTask.cancel(TAG);
        }
    }

    private void getIsInRange(boolean isEnterElevator,Pose centerPose, ElevatorRangeInfo info) {
        DelayTask.cancel(TAG);
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {
                Pose currentPose = RobotInfo.getCurrentPose();
                ElevatorLocationUtils.getInstance().relativeElevatorPosition(isEnterElevator,
                        currentPose,
                        centerPose,
                        info);
                getIsInRange(isEnterElevator, centerPose, info);
            }
        }, 1000);
    }

    /**
     * 获得某电梯的范围信息
     * @param elevatorName  电梯名
     * @return  ElevatorRangeInfo
     */
    private ElevatorRangeInfo getElevatorRangeByName(String elevatorName) {
        String rangeInfo = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTING_ELEVATOR_RANGE);
        try {
            Type type = new TypeToken<List<ElevatorRangeInfo>>() {
            }.getType();
            List<ElevatorRangeInfo> rangeInfoList = mGson.fromJson(rangeInfo, type);
            for (ElevatorRangeInfo info : rangeInfoList) {
                if (TextUtils.equals(info.getElevatorName(), elevatorName)) {
                    return info;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    private void showRangeDialog() {
        if (null != rangeDialog && rangeDialog.isShowing()) {
            return;
        }
        rangeDialog = new DialogElevatorRange(context, new DialogElevatorRange.OnFinishListener() {
            @Override
            public void onCancel() {
                cancelElevatorRangeListener(null);
                rangeDialog.dismiss();
            }

            @Override
            public boolean onChangeMode() {
                boolean isEnter = !ElevatorLocationUtils.getInstance().isEnterElevator();
                initRangeView(isEnter);
                return isEnter;
            }
        });
        rangeDialog.show();
    }

    private void updateRangeDialog(ElevatorLocationUtils.RangeInfo info) {
        if (null != rangeDialog) {
            rangeDialog.updateInfo(info);
        }
    }

    private CommandListener getCommandListener(TestMethod testMethod) {
        long startTime = System.currentTimeMillis();
        return new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                try {
                    long time = System.currentTimeMillis() - startTime;
                    Log.d(TAG, "getElevatorStatus result:" + result + " message:" + message + " time:" + time);
                    String text = testMethod.method + ":result=" + result + ",message:" + message + "(" + time + "ms)";
                    Toast.makeText(RobotApp.getContext(), text, Toast.LENGTH_SHORT).show();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        };
    }

    private void showEditDialog(TestMethod testMethod, EditListener listener) {
        View editView = getEditView();
        EditText editText = (EditText) editView.findViewById(R.id.edt_comment);
        ((TextView) editView.findViewById(R.id.textView)).setText(testMethod.title);
        editText.setText(testMethod.defaultInput);
        editView.findViewById(R.id.buttonCancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dialogBuilder.dismiss();
            }
        });
        editView.findViewById(R.id.buttonSubmit).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                listener.onFinished(editText.getText().toString());
                dialogBuilder.dismiss();
            }
        });
        showEditDialog(editView);
    }

    private void showEditDialog(View editView) {
        if (null == dialogBuilder) {
            dialogBuilder = new AlertDialog.Builder(context).create();
        }
        dialogBuilder.show();
        dialogBuilder.setContentView(editView);
        dialogBuilder.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
    }

    private View getEditView() {
        if (null == editDialogView) {
            editDialogView = LayoutInflater.from(context).inflate(R.layout.elevator_test_dialog, null);
        }
        return editDialogView;
    }

    public interface EditListener {
        void onFinished(String text);
    }

    public static class TestMethod {
        public String title;
        public String method;
        public String defaultInput;

        public TestMethod(String title, String method) {
            this(title, method, "");
        }

        public TestMethod(String title, String method, String defaultInput) {
            this.title = title;
            this.method = method;
            this.defaultInput = defaultInput;
        }

        public void setDefaultInput(String defaultInput) {
            this.defaultInput = defaultInput;
        }
    }

    public static class TestViewHolder extends RecyclerView.ViewHolder {
        public TextView name;

        public TestViewHolder(View view) {
            super(view);
            name = view.findViewById(android.R.id.text1);
            name.setTextColor(Color.RED);
        }
    }
    public void stop(){
        cancelElevatorRangeListener(null);
    }
}
