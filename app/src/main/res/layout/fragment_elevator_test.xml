<?xml version="1.0" encoding="utf-8"?><!--
  Copyright (C) 2017 OrionStar Technology Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/lead_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="22dp"
        android:layout_marginTop="18dp"
        android:layout_marginBottom="10dp"
        android:drawablePadding="15dp"
        android:text="引领"
        android:textColor="#DEFFFFFF"
        android:textSize="27sp" />

    <FrameLayout
        android:id="@+id/multi_storey_lead_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>