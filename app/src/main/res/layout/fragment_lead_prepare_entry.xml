<?xml version="1.0" encoding="utf-8"?><!--
  Copyright (C) 2017 OrionStar Technology Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <LinearLayout
        android:id="@+id/lead_title_des_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/list_container"
        android:layout_marginBottom="70dp"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        >
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="您好，请问您要去哪？"
            android:textColor="#deffffff"
            android:textSize="30sp"
            android:textStyle="bold"
            />
        <TextView
            android:layout_width="305dp"
            android:layout_height="2dp"
            android:layout_marginTop="17dp"
            android:background="#33FFFFFF" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/list_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginVertical="10dp"
        android:paddingBottom="70dp"
        app:layout_constraintTop_toBottomOf="@id/lead_title_des_layout"
        app:layout_constraintBottom_toTopOf="@id/delivery_card_expand_select_button"
        app:layout_constraintLeft_toLeftOf="parent">

    </androidx.recyclerview.widget.RecyclerView>

    <TextView
        android:id="@+id/lead_title_room"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="45dp"
        android:textColor="#deffffff"
        android:textSize="80sp"
        android:visibility="gone"/>

    <TextView
        android:id="@+id/delivery_card_expand_select_button"
        android:layout_width="200dp"
        android:layout_height="50dp"
        android:layout_marginBottom="10dp"
        android:gravity="center"
        android:text="选择地点"
        android:textColor="#ffffffff"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/delivery_auto_charge_button"/>

    <TextView
        android:id="@+id/delivery_auto_charge_button"
        android:layout_width="200dp"
        android:layout_height="50dp"
        android:layout_marginBottom="10dp"
        android:gravity="center"
        android:text="跨层回充"
        android:textColor="#ffffffff"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="parent"
        app:layout_constraintLeft_toRightOf="@id/delivery_card_expand_select_button"
        app:layout_constraintRight_toRightOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>