<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="100dp"
    android:layout_height="30dp"
    android:layout_marginVertical="3dp"
    android:layout_marginRight="10dp">

    <FrameLayout
        android:id="@+id/place_item_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/place_list_item_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/place_item_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:paddingHorizontal="2dp"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>