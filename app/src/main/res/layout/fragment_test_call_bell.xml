<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/server_ip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="请输入IP" />
            <Button
                android:id="@+id/btn_connect"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:padding="5dp"
                android:text="连接" />
        </LinearLayout>
        <android.support.v7.widget.RecyclerView
            android:id="@+id/place_list"
            android:layout_width="330dp"
            android:layout_height="150dp"
            android:layout_marginHorizontal="10dp"
            android:layout_marginVertical="10dp"
            android:padding="10dp" />

        <LinearLayout
            android:layout_width="330dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/input_btn_id"
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/btn_item_finish"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:padding="5dp"
                android:text="添加" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="330dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_config_finish"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:padding="5dp"
                android:text="配置完成" />

        </LinearLayout>
    </LinearLayout>

    <FrameLayout
        android:layout_marginLeft="330dp"
        android:layout_marginTop="50dp"
        android:background="@color/black_alpha40"
        android:layout_width="200dp"
        android:layout_height="250dp">

        <ScrollView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/button_map_log"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:textSize="12sp" />
        </ScrollView>
    </FrameLayout>
</FrameLayout>