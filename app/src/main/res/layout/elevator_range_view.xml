<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <Button
        android:id="@+id/btn_switch_enter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="切换进出模式"
        android:paddingHorizontal="5dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btn_angle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="修正角度"
        android:paddingHorizontal="5dp"
        app:layout_constraintLeft_toRightOf="@id/btn_switch_enter"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_finish"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:text="X"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_marginEnd="10dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <com.ainirobot.moduleapp.test.ElevatorRangeView
        android:id="@+id/range_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_finish"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>