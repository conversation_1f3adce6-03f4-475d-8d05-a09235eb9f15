<?xml version="1.0" encoding="utf-8"?><!--
  Copyright (C) 2017 OrionStar Technology Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_a8b3b5"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <TextView
            android:id="@+id/delivery_select_dialog_close"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="45dp"
            android:scaleType="center"
            android:text="@string/cancel"
            android:textColor="@color/white" />

        <TextView
            android:id="@+id/delivery_select_dialog_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="选择地点"
            android:textColor="@color/white"
            android:textSize="36sp"
            android:textStyle="bold" />
    </FrameLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/delivery_select_dialog_layer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="10dp"
        android:layout_marginVertical="10dp"
        android:layout_marginTop="45dp"
        android:padding="10dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/delivery_select_dialog_list"
        android:layout_width="match_parent"
        android:layout_height="150dp"
        android:layout_marginHorizontal="10dp"
        android:layout_marginVertical="10dp"
        android:padding="10dp" />

    <LinearLayout
        android:id="@+id/layout_loop_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:visibility="gone"
        >
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="循环次数"
            android:layout_marginEnd="20dp"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            />

        <EditText
            android:id="@+id/edit_loop_count"
            android:layout_width="70dp"
            android:layout_height="40dp"
            android:gravity="center"
            android:inputType="number"/>

    </LinearLayout>
    <TextView
        android:id="@+id/tv_selected_place"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/white"
        android:layout_marginTop="20dp"
        android:textSize="13sp"/>
    <TextView
        android:id="@+id/delivery_select_dialog_add"
        android:layout_width="150dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:gravity="center"
        android:text="添加循环点位"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        android:background="@drawable/delivery_select_item_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/delivery_select_dialog_ok"
        android:layout_width="150dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="22dp"
        android:gravity="center"
        android:text="@string/confirm"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        android:background="@drawable/delivery_select_item_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</LinearLayout>