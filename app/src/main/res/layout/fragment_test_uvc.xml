<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@color/white">

    <Button
        android:id="@+id/btn_single"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_continue"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/gl_top"
        android:text="SINGLE"
        android:padding="5dp"/>
    <Button
        android:id="@+id/btn_continue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@+id/btn_single"
        app:layout_constraintEnd_toStartOf="@+id/btn_stop_continue"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/gl_top"
        android:text="CONTINUE"
        android:padding="5dp"/>
    <Button
        android:id="@+id/btn_stop_continue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@+id/btn_continue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/gl_top"
        android:text="STOP_CONTINUE"
        android:padding="5dp"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl_top"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.123"
        />
    <ScrollView
        android:id="@+id/sv_log"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@+id/gl_top"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="20dp"
        >

        <TextView
            android:id="@+id/log"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textIsSelectable="true"
            android:textSize="12sp" />
    </ScrollView>


</androidx.constraintlayout.widget.ConstraintLayout>