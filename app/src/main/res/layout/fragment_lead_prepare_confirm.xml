<?xml version="1.0" encoding="utf-8"?><!--
  Copyright (C) 2017 OrionStar Technology Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tvDestination"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="60dp"
        android:textColor="#FF85EDFF"
        android:textSize="50sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDestinationDesc"
        android:layout_width="match_parent"
        android:layout_height="350dp"
        android:layout_marginHorizontal="40dp"
        android:layout_marginTop="20dp"
        android:autoSizeMaxTextSize="30sp"
        android:autoSizeMinTextSize="15sp"
        android:autoSizeTextType="uniform"
        android:gravity="center_horizontal"
        android:maxLines="5"
        android:minWidth="40dp"
        android:text="确认要我带您过去吗？"
        android:textColor="#DEFFFFFF"
        android:textSize="30sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvDestination" />

    <TextView
        android:id="@+id/lead_cancel"
        android:layout_width="wrap_content"
        android:layout_height="75dp"
        android:layout_marginBottom="60dp"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="#deffffff"
        android:textSize="27sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/lead_confirm" />

    <TextView
        android:id="@+id/lead_confirm"
        android:layout_width="wrap_content"
        android:layout_height="75dp"
        android:layout_marginLeft="30dp"
        android:gravity="center"
        android:text="高级导航"
        android:textColor="#ff6de7f2"
        android:textSize="27sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/lead_cancel"
        app:layout_constraintLeft_toRightOf="@id/lead_cancel"
        app:layout_constraintRight_toLeftOf="@id/lead_default"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/lead_cancel" />

    <TextView
        android:id="@+id/lead_default"
        android:layout_width="wrap_content"
        android:layout_height="75dp"
        android:layout_marginBottom="60dp"
        android:gravity="center"
        android:visibility="gone"
        android:layout_marginLeft="30dp"
        android:text="默认导航"
        android:textColor="#deffffff"
        android:textSize="27sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toRightOf="@id/lead_confirm"
        app:layout_constraintRight_toRightOf="parent" />


    <TextView
        android:id="@+id/tvInfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:textSize="12sp"
        android:textColor="@color/white"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>

