<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.ainirobot.moduleapp"
    android:sharedUserId="android.uid.system">

    <application
        android:allowBackup="true"
        android:icon="@drawable/icon_home"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:name="com.ainirobot.moduleapp.RobotApp"
        android:usesCleartextTraffic="true"
        android:hardwareAccelerated="true"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:theme="@style/AppTheme">
        <activity
            android:name="com.ainirobot.moduleapp.MainActivity"
            android:launchMode="singleTask"
            android:taskAffinity=".platform"
            android:configChanges="density|screenLayout|screenSize|keyboardHidden|keyboard|orientation|mcc|mnc">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.orion.meissa.videocall.rvms"/>

                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <intent-filter>
                <action android:name="action.orionstar.default.app"/>

                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

        <provider
            android:name=".data.DBProvider"
            android:authorities="com.ainirobot.moduleapp"
            android:exported="true"
            android:permission="com.ainirobot.moduleapp.permission_MODULEAPP_PROVIDER"/>
    </application>

</manifest>