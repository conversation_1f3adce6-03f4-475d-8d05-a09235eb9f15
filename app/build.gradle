apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'

def isOversea = project.hasProperty('isOversea') ? isOversea : false

android {
    compileSdkVersion 28
    buildToolsVersion "28.0.3"

    defaultConfig {
        applicationId "com.ainirobot.moduleapp"
        minSdkVersion 23
        //noinspection ExpiredTargetSdkVersion
        targetSdkVersion 28
        versionCode 21100
        versionName "2.11.0"
        multiDexEnabled true

        ndk {
            abiFilters 'armeabi-v7a'
        }
    }

    signingConfigs {
        signConfig {
            keyAlias 'platform'
            keyPassword 'android'
            storeFile file('debug.keystore')
            storePassword 'android'
        }
        signConfig_845 {
            keyAlias 'AiniBox'
            keyPassword 'AiniRobot@9102'
            storeFile file('platform.keystore')
            storePassword 'AiniRobot@9102'
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.signConfig_845
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        debug {
            signingConfig signingConfigs.signConfig_845
            debuggable true
        }

        rn069 {
            signingConfig signingConfigs.signConfig_845
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility 1.8
        targetCompatibility 1.8
    }

    dataBinding {
        enabled = true
    }

    sourceSets {
        main {
            if (isOversea) {
                jni.srcDirs = []
                java.srcDirs = ['src/main/java', 'src/rn/java']
                manifest.srcFile 'src/main/oversea/AndroidManifest.xml'
            } else {
                jni.srcDirs = []
                java.srcDirs = ['src/main/java', 'src/rn/java']
                manifest.srcFile 'src/main/domestic/AndroidManifest.xml'
            }
        }
    }

    if (isOversea) {
        println(':app is oversea')
    } else {
        println(':app is domestic')
    }

    lintOptions {
        abortOnError false
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(":platform")
    implementation 'com.alibaba:fastjson:1.2.70'
    implementation project(":libraries:baidumap")
}
repositories {
    mavenCentral()
}
