#coding: utf-8
# This is a sample Python script.

# Press ⌃R to execute it or replace it with your code.
# Press Double ⇧ to search everywhere for classes, files, tool windows, actions, and settings.
import matplotlib.pyplot as plot
import sys
import os
import subprocess


def copyFileFromSdcard(dir):
    file=dir+"mem_summary.log"
    if os.path.exists(file):
        print('文件在指定目录中已存在')
        return
    sdcard_path='sdcard/meminfo/mem_summary.log'
    command='adb shell ls %s' % (sdcard_path)
    print(command)
    path = os.popen(command).read().strip()
    print(path)
    if path.find('No such file or directory')==1:
        print('No such file or directory')
        return
    print('开始sdcard中拉取文件到指定目录')
    cmd = "adb pull %s %s" % (sdcard_path,dir)
    result = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()

def readMem(heap,dir):
    time=[]
    java=[]
    native=[]
    graphics=[]
    other=[]
    total=[]
    print('开始在指定目录中读取文件')
    with open(dir+"mem_summary.log") as file:
        while True:
            line = file.readline()
            if not line:
                statistics(heap,time,java,native,graphics,other,total)
                file.close()
                break
            line = line.replace(' ', '')
            strs=line.split('|')
            scene=''
            if len(strs)==7:
                scene=strs[6].split('_')[2]
            time.append(strs[0].split('Time:')[1]+'\n'+scene)
            java.append(strs[1].split(':')[1])
            native.append(strs[2].split(':')[1])
            graphics.append(strs[3].split(':')[1])
            other.append(strs[4].split(':')[1])
            total.append(strs[5].split(':')[1])

def statistics(heap,time,java,native,graphics,other,total):
    print('开始统计指定内存类型的数据')
    plot.yticks(fontproperties='Times New Roman', size=8)
    plot.xticks(fontproperties='Times New Roman', size=8)
    plot.xlabel("time")  # X轴标签
    plot.ylabel("heap size")  # Y轴标签
    plot.title("RN process heap")  # 图标
    if heap=='java' or heap=='all' :
        plot.plot(time, java, label='jave heap')
    if heap=='native' or heap=='all':
        plot.plot(time, native, label='native heap')
    if heap=='graphics' or heap=='all':
        plot.plot(time, graphics, label='graphics')
    if heap=='other' or heap=='all':
        plot.plot(time, other, label='private other')
    if heap=='total' or heap=='all':
        plot.plot(time, total, label='total')
    plot.legend()
    plot.show()


# Press the green button in the gutter to run the script.
if __name__ == '__main__':
    args=sys.argv
    if len(args)<3:
        print('\n\033[1;35m 参数1--请指定查看进程以下内存类型： \033[0m\n')
        print('\033[1;35m         java|native|graphics|other|total \033[0m\n')
        print('\033[1;35m         或者all查看以上所有类型 \033[0m\n')
        print('\033[1;35m 参数2--请指定存放文件的路径 \033[0m\n')
        print('\033[1;35m 示例：python meminfo.py java /Users/<USER>/Downloads/ \033[0m\n ')
    else:
        copyFileFromSdcard(args[2])
        readMem(args[1],args[2])

# See PyCharm help at https://www.jetbrains.com/help/pycharm/
