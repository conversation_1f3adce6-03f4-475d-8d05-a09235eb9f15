package com.ainirobot.lib.shadowopk

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.util.Log
import com.ainirobot.platform.control.IAppMessenger
import org.json.JSONObject

interface RobotMessengerCallBack {
    fun onMessengerReady();
    fun onServiceDisconnected();
}

object RobotMessengerManager {
    private const val TAG = "RobotMessengerManager"

    private var callBack: RobotMessengerCallBack? = null
    private var messenger: IAppMessenger? = null

    private var deathRecipient: IBinder.DeathRecipient? = null
    private var robotMessenger: AbsRobotMessenger? = null

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceDisconnected(name: ComponentName?) {
            Log.i(TAG, "onServiceDisconnected ${name.toString()}")
            messenger = null
            deathRecipient = null
            callBack?.onServiceDisconnected()
        }

        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Log.i(TAG, "onServiceConnected ${name.toString()}")
            service?.apply {
                <EMAIL> =
                        IAppMessenger.Stub.asInterface(service)
                callBack?.onMessengerReady()

                deathRecipient = IBinder.DeathRecipient {
                    Log.e(TAG, "服务已经死亡")
                    messenger = null
                    deathRecipient = null
                    callBack?.onServiceDisconnected()
                }
                try {
                    service.linkToDeath(deathRecipient, 0)
                } catch (e: Exception) {
                    Log.e(TAG, "无法注册linkToDeath监听", e)
                }
            }
        }
    }

    /**
     * 连接机器人核心服务
     * @param context Context 用于启动服务
     */
    fun connectRobot(context: Context, callBack: RobotMessengerCallBack) {
        this.callBack = callBack
        val intent = Intent("com.ainirobot.platform.APP_MESSENGER")
        intent.putExtra("package", context.packageName)
        intent.component = ComponentName(
                "com.ainirobot.moduleapp",
                "com.ainirobot.platform.control.AppMessengerService"
        )
        context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
    }

    /**
     * 断开连接机器人核心服务
     * @param context Context 用于断开服务
     */
    fun disConnectRobot(context: Context) {
        try {
            this.messenger?.asBinder()?.unlinkToDeath(deathRecipient, 0)
            // 其他解绑操作
        } catch (e: Exception) {
            Log.e(TAG, "无法解除linkToDeath监听", e)
        } finally {
            deathRecipient = null
        }
        context.unbindService(serviceConnection)
        this.messenger = null
        this.callBack = null
        this.robotMessenger = null;
    }

    /**
     * 向 OPK 发送命令，命令格式可自行定义，在影子 OPK 内按照格式解析
     * @param command 命令
     */
    fun triggerCommand(command: String) {
        if (!isServiceConnected()){
            Log.e(TAG, "triggerCommand: messenger is not connected")
            val disConnected = JSONObject()
            disConnected.put("command", "service_disconnected")
            this.robotMessenger?.onRobotMessage(disConnected.toString())
            return
        }
        this.messenger?.onTriggerCommand(command)
    }

    /**
     * 设置接收影子 OPK 消息传递者
     * @param robotMessenger 影子 OPK 消息的接收器
     */
    fun setRobotMessenger(robotMessenger: AbsRobotMessenger) {
        this.robotMessenger = robotMessenger
        this.messenger?.setRobotMessenger(robotMessenger)
    }

    /**
     * 重置接收影子 OPK 消息传递者
     */
    fun resetRobotMessenger() {
        this.messenger?.setRobotMessenger(null)
    }

    /**
     * 获取服务连接状态
     */
    fun isServiceConnected(): Boolean {
        return messenger != null
    }
}