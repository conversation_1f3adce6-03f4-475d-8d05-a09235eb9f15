package com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager;

import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.DrivingRouteOverlay;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.IndoorPoiOverlay;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.IndoorRouteOverlay;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.PoiOverlay;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.TransitRouteOverlay;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.WalkingRouteOverlay;
import com.baidu.mapapi.search.poi.PoiResult;
import com.baidu.mapapi.search.route.DrivingRouteResult;
import com.baidu.mapapi.search.route.TransitRouteResult;
import com.baidu.mapapi.search.route.WalkingRouteResult;

public class MapViewTAG {
    PoiOverlay poiOverlay;
    WalkingRouteOverlay walkingRouteOverlay;
    DrivingRouteOverlay drivingRouteOverlay;
    TransitRouteOverlay transitRouteOverlay;
    IndoorPoiOverlay indoorPoiOverlay;
    IndoorRouteOverlay indoorRouteOverlay;
    WalkingRouteResult walkingRouteResult= null;
    DrivingRouteResult drivingRouteResult = null;
    TransitRouteResult transitRouteResult = null;
    PoiResult poiResult = null;

    public MapViewTAG(PoiOverlay poiOverlay, WalkingRouteOverlay walkingRouteOverlay, DrivingRouteOverlay drivingRouteOverlay, TransitRouteOverlay transitRouteOverlay, IndoorPoiOverlay indoorPoiOverlay, IndoorRouteOverlay indoorRouteOverlay) {
        this.poiOverlay = poiOverlay;
        this.walkingRouteOverlay = walkingRouteOverlay;
        this.drivingRouteOverlay = drivingRouteOverlay;
        this.transitRouteOverlay = transitRouteOverlay;
        this.indoorPoiOverlay = indoorPoiOverlay;
        this.indoorRouteOverlay = indoorRouteOverlay;
    }

    public PoiOverlay getPoiOverlay() {
        return poiOverlay;
    }

    public WalkingRouteOverlay getWalkingRouteOverlay() {
        return walkingRouteOverlay;
    }

    public DrivingRouteOverlay getDrivingRouteOverlay() {
        return drivingRouteOverlay;
    }

    public TransitRouteOverlay getTransitRouteOverlay() {
        return transitRouteOverlay;
    }

    public IndoorPoiOverlay getIndoorPoiOverlay() {
        return indoorPoiOverlay;
    }

    public IndoorRouteOverlay getIndoorRouteOverlay() {
        return indoorRouteOverlay;
    }

    public WalkingRouteResult getWalkingRouteResult() {
        return walkingRouteResult;
    }

    public void setWalkingRouteResult(WalkingRouteResult walkingRouteResult) {
        this.walkingRouteResult = walkingRouteResult;
    }

    public DrivingRouteResult getDrivingRouteResult() {
        return drivingRouteResult;
    }

    public void setDrivingRouteResult(DrivingRouteResult drivingRouteResult) {
        this.drivingRouteResult = drivingRouteResult;
    }

    public TransitRouteResult getTransitRouteResult() {
        return transitRouteResult;
    }

    public void setTransitRouteResult(TransitRouteResult transitRouteResult) {
        this.transitRouteResult = transitRouteResult;
    }

    public PoiResult getPoiResult() {
        return poiResult;
    }

    public void setPoiResult(PoiResult poiResult) {
        this.poiResult = poiResult;
    }
}
