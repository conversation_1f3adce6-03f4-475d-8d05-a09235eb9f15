package com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.util;

import android.util.Log;

public class DebugUtil {

    private static final int MAX_LENGTH = 3000;

    /**
     * 分段打印较长的文本
     * @param tag 标志
     * @param content 内容
     */
    public static void debugLarge(String tag, String content) {
        if (content.length() > MAX_LENGTH) {
            String part = content.substring(0, MAX_LENGTH);
            Log.d(tag, part);

            part = content.substring(MAX_LENGTH, content.length());
            if ((content.length() - MAX_LENGTH) > MAX_LENGTH) {
                debugLarge(tag, part);
            } else {
                Log.d(tag, part);
            }
        } else {
            Log.d(tag, content);
        }
    }

}