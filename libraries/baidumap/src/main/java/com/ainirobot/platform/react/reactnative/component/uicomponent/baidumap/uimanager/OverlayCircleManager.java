/**
 * Copyright (c) 2016-present, lovebing.org.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager;

import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.util.ColorUtil;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.util.LatLngUtil;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.util.StrokeUtil;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.view.OverlayCircle;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.annotations.ReactProp;

/**
 * <AUTHOR> Created on Dec 09, 2018
 */
public class OverlayCircleManager extends SimpleViewManager<OverlayCircle> {

    @Override
    public String getName() {
        return "BaiduMapOverlayCircle";
    }

    @Override
    protected OverlayCircle createViewInstance(ThemedReactContext reactContext) {
        return new OverlayCircle(reactContext);
    }

    @ReactProp(name = "radius")
    public void setRadius(OverlayCircle overlayCircle, int radius) {
        overlayCircle.setRadius(radius);
    }

    @ReactProp(name = "center")
    public void setCenter(OverlayCircle overlayCircle, ReadableMap center) {
        overlayCircle.setCenter(LatLngUtil.fromReadableMap(center));
    }

    @ReactProp(name = "fillColor")
    public void setFillColor(OverlayCircle overlayCircle, String fillColor) {
        overlayCircle.setFillColor(ColorUtil.fromString(fillColor));
    }

    @ReactProp(name = "stroke")
    public void setStroke(OverlayCircle overlayCircle, ReadableMap stroke) {
        overlayCircle.setStroke(StrokeUtil.fromReadableMap(stroke));
    }

    @ReactProp(name = "visible")
    public void setVisibility(OverlayCircle overlayCircle, boolean visible) {
        overlayCircle.setVisible(visible);
        if (visible) {
            if (!overlayCircle.hasAdded() &&
                    MapViewManager.staticMapView != null &&
                    MapViewManager.staticMapView.getMap() != null) {

                overlayCircle.addTopMap(MapViewManager.staticMapView.getMap());
            }
        } else {
            overlayCircle.remove();
        }
    }
}
