package com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.util;

import androidx.annotation.Nullable;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.uimanager.events.RCTEventEmitter;

public class EventUtils {
    public static  void sendEvent(ReactContext reactContext, int viewId, String eventName, @Nullable WritableMap params) {
        WritableMap event = Arguments.createMap();
        event.putMap("params", params);
        event.putString("type", eventName);
        reactContext
                .getJSModule(RCTEventEmitter.class)
                .receiveEvent(viewId,
                        "topChange",
                        event);
    }
    public static  void sendEventForStringParam(ReactContext reactContext, int viewId, String eventName, String param) {
        WritableMap event = Arguments.createMap();
        event.putString("params", param);
        event.putString("type", eventName);
        reactContext
                .getJSModule(RCTEventEmitter.class)
                .receiveEvent(viewId,
                        "topChange",
                        event);
    }
}
