/**
 * Copyright (c) 2016-present, lovebing.org.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module;

import android.content.Context;
import android.util.Log;

import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.PoiOverlay;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.TransitRouteOverlay;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.WalkingRouteOverlay;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager.MapViewTAG;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.util.DebugUtil;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.util.LatLngUtil;
import com.alibaba.fastjson.JSONObject;
import com.baidu.mapapi.map.MapStatus;
import com.baidu.mapapi.map.MapStatusUpdate;
import com.baidu.mapapi.map.MapStatusUpdateFactory;
import com.baidu.mapapi.map.MapView;
import com.baidu.mapapi.model.LatLng;
import com.baidu.mapapi.search.core.PoiInfo;
import com.baidu.mapapi.search.core.RouteNode;
import com.baidu.mapapi.search.core.SearchResult;
import com.baidu.mapapi.search.poi.OnGetPoiSearchResultListener;
import com.baidu.mapapi.search.poi.PoiDetailResult;
import com.baidu.mapapi.search.poi.PoiDetailSearchResult;
import com.baidu.mapapi.search.poi.PoiIndoorResult;
import com.baidu.mapapi.search.poi.PoiNearbySearchOption;
import com.baidu.mapapi.search.poi.PoiResult;
import com.baidu.mapapi.search.poi.PoiSearch;
import com.baidu.mapapi.search.poi.PoiSortType;
import com.baidu.mapapi.search.route.BikingRouteResult;
import com.baidu.mapapi.search.route.DrivingRouteLine;
import com.baidu.mapapi.search.route.DrivingRoutePlanOption;
import com.baidu.mapapi.search.route.DrivingRouteResult;
import com.baidu.mapapi.search.route.IndoorPlanNode;
import com.baidu.mapapi.search.route.IndoorRouteLine;
import com.baidu.mapapi.search.route.IndoorRoutePlanOption;
import com.baidu.mapapi.search.route.IndoorRouteResult;
import com.baidu.mapapi.search.route.MassTransitRouteResult;
import com.baidu.mapapi.search.route.OnGetRoutePlanResultListener;
import com.baidu.mapapi.search.route.PlanNode;
import com.baidu.mapapi.search.route.RoutePlanSearch;
import com.baidu.mapapi.search.route.TransitRouteLine;
import com.baidu.mapapi.search.route.TransitRoutePlanOption;
import com.baidu.mapapi.search.route.TransitRouteResult;
import com.baidu.mapapi.search.route.WalkingRouteLine;
import com.baidu.mapapi.search.route.WalkingRoutePlanOption;
import com.baidu.mapapi.search.route.WalkingRouteResult;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.WritableNativeArray;
import com.facebook.react.bridge.WritableNativeMap;
import com.facebook.react.uimanager.UIManagerModule;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Created on Dec 09, 2018
 */
public class MapAppModule extends BaseModule {

    private Context context;
    RoutePlanSearch search = RoutePlanSearch.newInstance();
    PoiSearch mPoiSearch = PoiSearch.newInstance();

    public MapAppModule(ReactApplicationContext reactContext) {
        super(reactContext);
        context = reactContext;
    }

    @Override
    public String getName() {
        return "BaiduMapAppModule";
    }

    @ReactMethod
    public void openBaiduMapIndoorRoute(int viewTag, ReadableMap start, String startFloor, ReadableMap end, String endFloor, Promise promise) {
        Log.d("MapAppModule", "openBaiduMapIndoorRoute" );
        try {
            OnGetRoutePlanResultListener listener = new OnGetRoutePlanResultListener() {
                @Override
                public void onGetWalkingRouteResult(WalkingRouteResult walkingRouteResult) {

                }

                @Override
                public void onGetTransitRouteResult(TransitRouteResult transitRouteResult) {

                }

                @Override
                public void onGetMassTransitRouteResult(MassTransitRouteResult massTransitRouteResult) {

                }

                @Override
                public void onGetDrivingRouteResult(DrivingRouteResult drivingRouteResult) {

                }

                @Override
                public void onGetIndoorRouteResult(IndoorRouteResult indoorRouteResult) {
                    Log.e("MapAppModule", "onGetIndoorRouteResult" );
                    //创建IndoorRouteOverlay实例
//                    UIManagerModule uiManager = getReactApplicationContext().getNativeModule(UIManagerModule.class);
//                    uiManager.addUIBlock(nativeViewHierarchyManager -> {
//                        MapView baiduMapView = (MapView) nativeViewHierarchyManager.resolveView(viewTag);
//                        IndoorRouteOverlay overlay = new IndoorRouteOverlay(baiduMapView.getMap());
//                        if (indoorRouteResult.getRouteLines() != null && indoorRouteResult.getRouteLines().size() > 0) {
//                            //获取室内路径规划数据（以返回的第一条路线为例）
//                            //为IndoorRouteOverlay实例设置数据
//                            IndoorRouteLine indoorRouteLine = indoorRouteResult.getRouteLines().get(0);
//                            overlay.setData(indoorRouteLine);
//                            //在地图上绘制IndoorRouteOverlay
//                            overlay.addToMap();
//                            search.destroy();
//                        }
//                    });

                    try {
                        if (indoorRouteResult != null && indoorRouteResult.getRouteLines() != null && indoorRouteResult.getRouteLines().size() > 0) {
                            WritableNativeMap routeResult = new WritableNativeMap();
                            WritableNativeArray routeArr = new WritableNativeArray();
                            for(IndoorRouteLine routeLine : indoorRouteResult.getRouteLines()) {
                                routeArr.pushMap(transformRouteLine2JsObj(routeLine));
                            }
                            routeResult.putArray("routeLines", routeArr);
                            promise.resolve(routeResult);
                        }else {
                            promise.reject("indoorRouteResult or getRouteLines are empty or null");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        promise.reject(e);
                    } finally {
                    }
                }

                @Override
                public void onGetBikingRouteResult(BikingRouteResult bikingRouteResult) {

                }
            };
            search.setOnGetRoutePlanResultListener(listener);

            LatLng startPoi = LatLngUtil.fromReadableMap(start);
            LatLng endPoi = LatLngUtil.fromReadableMap(end);

            IndoorPlanNode startNode = new IndoorPlanNode(new LatLng(startPoi.latitude, startPoi.longitude), startFloor);
            IndoorPlanNode endNode = new IndoorPlanNode(new LatLng(endPoi.latitude, endPoi.longitude), endFloor);

            search.walkingIndoorSearch(new IndoorRoutePlanOption()
                    .from(startNode)
                    .to(endNode));
        } catch (Exception e) {
            Log.e("BaiduMapError", "openBaiduMapIndoorRoute:" + e.getMessage());
            e.printStackTrace();
        }
    }

    @ReactMethod
    public void drawIndoorRouteLine(int viewTag, ReadableMap param) {
        Log.d("MapAppModule", "drawIndoorRouteLine" );
        try {
            IndoorRouteLine routeLine = null;

            if (param != null) {

                routeLine = new IndoorRouteLine();

                routeLine.setDistance(param.getInt("distance"));
                routeLine.setDuration(param.getInt("duration"));
                routeLine.setTitle(param.getString("title"));

                RouteNode startingNode = new RouteNode();
                startingNode.setTitle(param.getMap("starting").getString("title"));
                startingNode.setUid(param.getMap("starting").getString("uid"));
                startingNode.setLocation(new LatLng(param.getMap("starting").getDouble("latitude"), param.getMap("starting").getDouble("longitude")));
                routeLine.setStarting(startingNode);

                RouteNode terminalNode = new RouteNode();
                terminalNode.setTitle(param.getMap("terminal").getString("title"));
                terminalNode.setUid(param.getMap("terminal").getString("uid"));
                terminalNode.setLocation(new LatLng(param.getMap("terminal").getDouble("latitude"), param.getMap("terminal").getDouble("longitude")));
                routeLine.setTerminal(terminalNode);

                List<IndoorRouteLine.IndoorRouteStep> stepList = new ArrayList<>();

                ReadableArray steps = param.getArray("steps");
                for(int i = 0; i < steps.size(); i++) {
                    IndoorRouteLine.IndoorRouteStep step = new IndoorRouteLine.IndoorRouteStep();

                    ReadableMap stepMap = steps.getMap(i);

                    step.setInstructions(stepMap.getString("instructions"));
                    step.setBuildingId(stepMap.getString("buildingId"));
                    step.setFloorId(stepMap.getString("floorId"));
                    step.setDistance(stepMap.getInt("distance"));
                    step.setDuration(stepMap.getInt("duration"));
                    step.setName(stepMap.getString("name"));

                    RouteNode entraceNode = new RouteNode();
                    entraceNode.setTitle(stepMap.getMap("entrace").getString("title"));
                    entraceNode.setUid(stepMap.getMap("entrace").getString("uid"));
                    entraceNode.setLocation(new LatLng(stepMap.getMap("entrace").getDouble("latitude"), stepMap.getMap("entrace").getDouble("longitude")));
                    step.setEntrace(entraceNode);

                    RouteNode exitNode = new RouteNode();
                    exitNode.setTitle(stepMap.getMap("exit").getString("title"));
                    exitNode.setUid(stepMap.getMap("exit").getString("uid"));
                    exitNode.setLocation(new LatLng(stepMap.getMap("exit").getDouble("latitude"), stepMap.getMap("exit").getDouble("longitude")));
                    step.setExit(exitNode);


                    ReadableArray stepNodes = stepMap.getArray("stepNodes");
                    if (stepNodes != null) {
                        List<IndoorRouteLine.IndoorRouteStep.IndoorStepNode> stepNodeList = new ArrayList<>();
                        for(int j = 0; stepNodes != null && j < stepNodes.size(); j++) {
                            IndoorRouteLine.IndoorRouteStep.IndoorStepNode stepNode = new IndoorRouteLine.IndoorRouteStep.IndoorStepNode();
                            stepNode.setDetail(stepNodes.getMap(j).getString("detail"));
                            stepNode.setName(stepNodes.getMap(j).getString("name"));
                            stepNode.setType(stepNodes.getMap(j).getInt("type"));
                            stepNode.setLocation(new LatLng(stepNodes.getMap(j).getDouble("latitude"), stepNodes.getMap(j).getDouble("longitude")));
                            stepNodeList.add(stepNode);
                        }
                        step.setStepNodes(stepNodeList);
                    }

                    List<Double> latLngList = new ArrayList<>();
                    List<LatLng> wayPoints = new ArrayList<>();
                    ReadableArray latLngs = stepMap.getArray("wayPoints");
                    for(int j = 0; latLngs != null && j < latLngs.size(); j++) {
                        latLngList.add(latLngs.getMap(j).getDouble("latitude"));
                        latLngList.add(latLngs.getMap(j).getDouble("longitude"));
                        wayPoints.add(new LatLng(latLngs.getMap(j).getDouble("latitude"), latLngs.getMap(j).getDouble("longitude")));
                    }
                    step.setPath(latLngList);
                    step.setWayPoints(wayPoints);

                    stepList.add(step);
                }

                routeLine.setSteps(stepList);
            }

            UIManagerModule uiManager = getReactApplicationContext().getNativeModule(UIManagerModule.class);
            IndoorRouteLine finalRouteLine = routeLine;
            uiManager.addUIBlock(nativeViewHierarchyManager -> {
                try {
                    MapView baiduMapView = (MapView) nativeViewHierarchyManager.resolveView(viewTag);
                    if (finalRouteLine != null) {
                        IndoorRouteOverlay overlay = new IndoorRouteOverlay(baiduMapView.getMap());
                        //获取室内路径规划数据（以返回的第一条路线为例）
                        //为IndoorRouteOverlay实例设置数据
                        overlay.setData(finalRouteLine);
                        //在地图上绘制IndoorRouteOverlay
                        overlay.addToMap();
                    } else {
                        baiduMapView.getMap().clear();
                    }
                } catch (Exception e) {
                    Log.e("BaiduMapError", "drawIndoorRouteLine:" + e.getMessage());
                    e.printStackTrace();
                }

            });
        } catch (Exception e) {
            Log.e("BaiduMapError", "drawIndoorRouteLine:" + e.getMessage());
            e.printStackTrace();
        }
    }


    @ReactMethod
    public void openBaiduMapWalkingRoute(int viewTag, ReadableMap start,ReadableMap end, Promise promise) {
        Log.d("MapAppModule", "openBaiduMapWalkingRoute" );
        try {
            OnGetRoutePlanResultListener listener = new OnGetRoutePlanResultListener() {
                @Override
                public void onGetWalkingRouteResult(WalkingRouteResult walkingRouteResult) {
                    Log.d("MapAppModule", "onGetWalkingRouteResult" );
                    try {
                        if (walkingRouteResult != null && walkingRouteResult.getRouteLines() != null && walkingRouteResult.getRouteLines().size() > 0) {
                            String walkingRouteResultJson =  JSONObject.toJSONString(walkingRouteResult);
                            DebugUtil.debugLarge("MapAppModule","walkingRouteResultJson = "+walkingRouteResultJson);
                            processMapView(viewTag, new ProcessMapViewCallBack() {
                                @Override
                                public void process(MapView mapView) {
                                    ((MapViewTAG)mapView.getTag()).setWalkingRouteResult(walkingRouteResult);
                                }
                            });
                            promise.resolve(walkingRouteResultJson);
                        }else {
                            processMapView(viewTag, new ProcessMapViewCallBack() {
                                @Override
                                public void process(MapView mapView) {
                                    ((MapViewTAG)mapView.getTag()).setWalkingRouteResult(null);
                                }
                            });
                            promise.reject("walkingRouteResult or getRouteLines are empty or null");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        processMapView(viewTag, new ProcessMapViewCallBack() {
                            @Override
                            public void process(MapView mapView) {
                                ((MapViewTAG)mapView.getTag()).setWalkingRouteResult(null);
                            }
                        });
                        promise.reject(e);
                    } finally {
                    }
                }

                @Override
                public void onGetTransitRouteResult(TransitRouteResult transitRouteResult) {

                }

                @Override
                public void onGetMassTransitRouteResult(MassTransitRouteResult massTransitRouteResult) {

                }

                @Override
                public void onGetDrivingRouteResult(DrivingRouteResult drivingRouteResult) {

                }

                @Override
                public void onGetIndoorRouteResult(IndoorRouteResult indoorRouteResult) {

                }

                @Override
                public void onGetBikingRouteResult(BikingRouteResult bikingRouteResult) {

                }
            };
            search.setOnGetRoutePlanResultListener(listener);

            LatLng startPoi = LatLngUtil.fromReadableMap(start);
            LatLng endPoi = LatLngUtil.fromReadableMap(end);

            PlanNode startNode = PlanNode.withLocation(startPoi);
            PlanNode endNode = PlanNode.withLocation(endPoi);

            search.walkingSearch(new WalkingRoutePlanOption()
                    .from(startNode)
                    .to(endNode));
        } catch (Exception e) {
            Log.e("BaiduMapError", "openBaiduMapWalkingRoute:" + e.getMessage());
            e.printStackTrace();
        }
    }

    @ReactMethod
    public void drawWalkingRouteLine(int viewTag, int left,int right,int top,int bottom,int num) {
        Log.d("MapAppModule", "drawWalkingRouteLine" );
        try {
            UIManagerModule uiManager = getReactApplicationContext().getNativeModule(UIManagerModule.class);
            uiManager.addUIBlock(nativeViewHierarchyManager -> {
                try {
                    WalkingRouteLine routeLine = null;
                    MapView baiduMapView = (MapView) nativeViewHierarchyManager.resolveView(viewTag);
                    MapViewTAG mapViewTAG = (MapViewTAG)baiduMapView.getTag();
                    if(mapViewTAG!=null && mapViewTAG.getWalkingRouteResult()!=null&&mapViewTAG.getWalkingRouteResult().getRouteLines().size()>num&&num>=0){
                        routeLine = mapViewTAG.getWalkingRouteResult().getRouteLines().get(num);
                    }
                    WalkingRouteOverlay overlay = ((MapViewTAG)(baiduMapView.getTag())).getWalkingRouteOverlay();
                    if (routeLine != null) {
                        overlay.removeFromMap();
                        overlay.setData(routeLine);
                        overlay.addToMap();
                        overlay.zoomToSpan(left, right, top, bottom);
                    } else {
                        overlay.removeFromMap();
                    }
                } catch (Exception e) {
                    Log.e("BaiduMapError", "drawWalkingRouteLine:" + e.getMessage());
                    e.printStackTrace();
                }

            });
        } catch (Exception e) {
            Log.e("BaiduMapError", "drawWalkingRouteLine:" + e.getMessage());
            e.printStackTrace();
        }
    }


    @ReactMethod
    public void openBaiduMapDrivingRoute(int viewTag, ReadableMap start,ReadableMap end, Promise promise) {
        Log.d("MapAppModule", "openBaiduMapDrivingRoute" );
        try {
            OnGetRoutePlanResultListener listener = new OnGetRoutePlanResultListener() {
                @Override
                public void onGetWalkingRouteResult(WalkingRouteResult walkingRouteResult) {

                }

                @Override
                public void onGetTransitRouteResult(TransitRouteResult transitRouteResult) {

                }

                @Override
                public void onGetMassTransitRouteResult(MassTransitRouteResult massTransitRouteResult) {

                }

                @Override
                public void onGetDrivingRouteResult(DrivingRouteResult drivingRouteResult) {
                    Log.d("MapAppModule", "onGetDrivingRouteResult" );
                    try {
                        if (drivingRouteResult != null && drivingRouteResult.getRouteLines() != null && drivingRouteResult.getRouteLines().size() > 0) {
                            String drivingRouteResultJson = JSONObject.toJSONString(drivingRouteResult);
                            DebugUtil.debugLarge("MapAppModule","drivingRouteResultJson = "+drivingRouteResultJson);
                            processMapView(viewTag, new ProcessMapViewCallBack() {
                                @Override
                                public void process(MapView mapView) {
                                    ((MapViewTAG)mapView.getTag()).setDrivingRouteResult(drivingRouteResult);
                                }
                            });
                            promise.resolve(drivingRouteResultJson);
                        }else {
                            processMapView(viewTag, new ProcessMapViewCallBack() {
                                @Override
                                public void process(MapView mapView) {
                                    ((MapViewTAG)mapView.getTag()).setDrivingRouteResult(null);
                                }
                            });
                            promise.reject("drivingRouteResult or getRouteLines are empty or null");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        processMapView(viewTag, new ProcessMapViewCallBack() {
                            @Override
                            public void process(MapView mapView) {
                                ((MapViewTAG)mapView.getTag()).setDrivingRouteResult(null);
                            }
                        });
                        promise.reject(e);
                    } finally {
                    }
                }

                @Override
                public void onGetIndoorRouteResult(IndoorRouteResult indoorRouteResult) {

                }

                @Override
                public void onGetBikingRouteResult(BikingRouteResult bikingRouteResult) {

                }
            };
            search.setOnGetRoutePlanResultListener(listener);

            LatLng startPoi = LatLngUtil.fromReadableMap(start);
            LatLng endPoi = LatLngUtil.fromReadableMap(end);

            PlanNode startNode = PlanNode.withLocation(startPoi);
            PlanNode endNode = PlanNode.withLocation(endPoi);

            search.drivingSearch(new DrivingRoutePlanOption()
                    .from(startNode)
                    .to(endNode));
        } catch (Exception e) {
            Log.e("BaiduMapError", "openBaiduMapDrivingRoute:" + e.getMessage());
            e.printStackTrace();
        }
    }

    @ReactMethod
    public void drawDrivingRouteLine(int viewTag,int left,int right,int top,int bottom, int num) {
        Log.d("MapAppModule", "drawDrivingRouteLine" );
        try {
            UIManagerModule uiManager = getReactApplicationContext().getNativeModule(UIManagerModule.class);
            uiManager.addUIBlock(nativeViewHierarchyManager -> {
                try {
                    DrivingRouteLine routeLine = null;
                    MapView baiduMapView = (MapView) nativeViewHierarchyManager.resolveView(viewTag);
                    MapViewTAG mapViewTAG = (MapViewTAG)baiduMapView.getTag();
                    if (mapViewTAG != null && mapViewTAG.getDrivingRouteResult() != null && mapViewTAG.getDrivingRouteResult().getRouteLines().size() > num && num >= 0) {
                        routeLine = mapViewTAG.getDrivingRouteResult().getRouteLines().get(num);
                    }
                    DrivingRouteOverlay overlay = ((MapViewTAG)(baiduMapView.getTag())).getDrivingRouteOverlay();
                    if (routeLine != null) {
                        overlay.removeFromMap();
                        overlay.setData(routeLine);
                        overlay.addToMap();
                        overlay.zoomToSpan(left, right, top, bottom);
                    } else {
                        overlay.removeFromMap();
                    }
                } catch (Exception e) {
                    Log.e("BaiduMapError", "drawDrivingRouteLine:" + e.getMessage());
                    e.printStackTrace();
                }

            });
        } catch (Exception e) {
            Log.e("BaiduMapError", "drawDrivingRouteLine:" + e.getMessage());
            e.printStackTrace();
        }
    }

    @ReactMethod
    public void openBaiduMapTransitRoute(int viewTag, ReadableMap start,ReadableMap end,String city, Promise promise) {
        Log.d("MapAppModule", "openBaiduMapTransitRoute" );
        try {
            OnGetRoutePlanResultListener listener = new OnGetRoutePlanResultListener() {
                @Override
                public void onGetWalkingRouteResult(WalkingRouteResult walkingRouteResult) {

                }

                @Override
                public void onGetTransitRouteResult(TransitRouteResult transitRouteResult) {
                    Log.d("MapAppModule", "onGetTransitRouteResult" );
                    try {
                        if (transitRouteResult != null && transitRouteResult.getRouteLines() != null && transitRouteResult.getRouteLines().size() > 0) {
                            String transitRouteResultJson =  JSONObject.toJSONString(transitRouteResult);
                            DebugUtil.debugLarge("MapAppModule","transitRouteResultJson = "+transitRouteResultJson);
                            processMapView(viewTag, new ProcessMapViewCallBack() {
                                @Override
                                public void process(MapView mapView) {
                                    ((MapViewTAG)mapView.getTag()).setTransitRouteResult(transitRouteResult);
                                }
                            });
                            promise.resolve(transitRouteResultJson);
                        }else {
                            processMapView(viewTag, new ProcessMapViewCallBack() {
                                @Override
                                public void process(MapView mapView) {
                                    ((MapViewTAG)mapView.getTag()).setTransitRouteResult(null);
                                }
                            });
                            promise.reject("transitRouteResult or getRouteLines are empty or null");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        processMapView(viewTag, new ProcessMapViewCallBack() {
                            @Override
                            public void process(MapView mapView) {
                                ((MapViewTAG)mapView.getTag()).setTransitRouteResult(null);
                            }
                        });
                        promise.reject(e);
                    } finally {
                    }
                }

                @Override
                public void onGetMassTransitRouteResult(MassTransitRouteResult massTransitRouteResult) {

                }

                @Override
                public void onGetDrivingRouteResult(DrivingRouteResult drivingRouteResult) {

                }

                @Override
                public void onGetIndoorRouteResult(IndoorRouteResult indoorRouteResult) {

                }

                @Override
                public void onGetBikingRouteResult(BikingRouteResult bikingRouteResult) {

                }
            };
            search.setOnGetRoutePlanResultListener(listener);

            LatLng startPoi = LatLngUtil.fromReadableMap(start);
            LatLng endPoi = LatLngUtil.fromReadableMap(end);

            PlanNode startNode = PlanNode.withLocation(startPoi);
            PlanNode endNode = PlanNode.withLocation(endPoi);

            search.transitSearch(new TransitRoutePlanOption()
                    .from(startNode)
                    .to(endNode).city(city));
        } catch (Exception e) {
            Log.e("BaiduMapError", "openBaiduMapTransitRoute:" + e.getMessage());
            e.printStackTrace();
        }
    }


    @ReactMethod
    public void drawTransitRouteLine(int viewTag,int left,int right,int top,int bottom, int num) {
        Log.d("MapAppModule", "drawTransitRouteLine" );
        try {
            UIManagerModule uiManager = getReactApplicationContext().getNativeModule(UIManagerModule.class);
            uiManager.addUIBlock(nativeViewHierarchyManager -> {
                try {
                    TransitRouteLine routeLine = null;
                    MapView baiduMapView = (MapView) nativeViewHierarchyManager.resolveView(viewTag);
                    MapViewTAG mapViewTAG = (MapViewTAG)baiduMapView.getTag();
                    if(mapViewTAG!=null && mapViewTAG.getTransitRouteResult()!=null&&mapViewTAG.getTransitRouteResult().getRouteLines().size()>num&&num>=0){
                        routeLine = mapViewTAG.getTransitRouteResult().getRouteLines().get(num);
                    }
                    TransitRouteOverlay overlay = ((MapViewTAG)(baiduMapView.getTag())).getTransitRouteOverlay();
                    if (routeLine != null) {
                        overlay.removeFromMap();
                        overlay.setData(routeLine);
                        overlay.addToMap();
                        overlay.zoomToSpan(left, right, top, bottom);
                    } else {
                        overlay.removeFromMap();
                    }
                } catch (Exception e) {
                    Log.e("BaiduMapError", "drawTransitRouteLine:" + e.getMessage());
                    e.printStackTrace();
                }

            });
        } catch (Exception e) {
            Log.e("BaiduMapError", "drawTransitRouteLine:" + e.getMessage());
            e.printStackTrace();
        }
    }


    @ReactMethod
    public void openBaiduMapSearchPoi(int viewTag,String keyword, ReadableMap center,int radius, Promise promise) {
        Log.d("MapAppModule", "openBaiduMapSearchPoi" );
        try {
            OnGetPoiSearchResultListener listener = new OnGetPoiSearchResultListener() {
                @Override
                public void onGetPoiResult(PoiResult poiResult) {
                    Log.d("MapAppModule", "onGetPoiResult" );
                    try {
                        if (poiResult != null && poiResult.error == SearchResult.ERRORNO.NO_ERROR ) {
                            String poiResultJson =  JSONObject.toJSONString(poiResult);
                            DebugUtil.debugLarge("MapAppModule","poiResultJson = "+poiResultJson);
                            processMapView(viewTag, new ProcessMapViewCallBack() {
                                @Override
                                public void process(MapView mapView) {
                                    ((MapViewTAG)mapView.getTag()).setPoiResult(poiResult);
                                }
                            });
                            promise.resolve(poiResultJson);
                        }else {
                            processMapView(viewTag, new ProcessMapViewCallBack() {
                                @Override
                                public void process(MapView mapView) {
                                    ((MapViewTAG)mapView.getTag()).setPoiResult(null);
                                }
                            });
                            if(poiResult == null){
                                Log.d("MapAppModule", "poiResult = null" );
                                promise.reject("poiResult = null");
                            }else if(poiResult.error.equals(SearchResult.ERRORNO.RESULT_NOT_FOUND)){
                                Log.d("MapAppModule", "RESULT_NOT_FOUND");
                                promise.resolve("");
                            } else{
                                Log.d("MapAppModule", "poiResult error = "+poiResult.error );
                                promise.reject("poiResult error = "+poiResult.error);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        processMapView(viewTag, new ProcessMapViewCallBack() {
                            @Override
                            public void process(MapView mapView) {
                                ((MapViewTAG)mapView.getTag()).setPoiResult(null);
                            }
                        });
                        promise.reject(e);
                    } finally {
                    }
                }

                @Override
                public void onGetPoiDetailResult(PoiDetailResult poiDetailResult) {

                }

                @Override
                public void onGetPoiDetailResult(PoiDetailSearchResult poiDetailSearchResult) {

                }

                @Override
                public void onGetPoiIndoorResult(PoiIndoorResult poiIndoorResult) {

                }
            };
            mPoiSearch.setOnGetPoiSearchResultListener(listener);

            LatLng centerLatLng = LatLngUtil.fromReadableMap(center);

            mPoiSearch.searchNearby(new PoiNearbySearchOption()
                    .keyword(keyword)
                    .sortType(PoiSortType.distance_from_near_to_far)
                    .location(centerLatLng)
                    .radius(radius)
                    .pageNum(0)
                    .scope(2));
        } catch (Exception e) {
            Log.e("BaiduMapError", "openBaiduMapSearchPoi:" + e.getMessage());
            e.printStackTrace();
        }
    }

    @ReactMethod
    public void openSearchPoiOutSideBaidumap(String keyword, ReadableMap center,int radius, Promise promise) {
        Log.d("MapAppModule", "openSearchPoiOutSideBaidumap" );
        try {
            OnGetPoiSearchResultListener listener = new OnGetPoiSearchResultListener() {
                @Override
                public void onGetPoiResult(PoiResult poiResult) {
                    Log.d("MapAppModule", "onGetPoiResult" );
                    try {
                        if (poiResult != null && poiResult.error == SearchResult.ERRORNO.NO_ERROR ) {
                            String poiResultJson =  JSONObject.toJSONString(poiResult);
                            DebugUtil.debugLarge("MapAppModule","poiResultJson = "+poiResultJson);
                            promise.resolve(poiResultJson);
                        }else {
                            if(poiResult == null){
                                Log.d("MapAppModule", "poiResult = null" );
                            }else{
                                Log.d("MapAppModule", "poiResult error = "+poiResult.error );
                            }
                            promise.reject("poiResult null or poiResult error");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        promise.reject(e);
                    } finally {
                    }
                }

                @Override
                public void onGetPoiDetailResult(PoiDetailResult poiDetailResult) {

                }

                @Override
                public void onGetPoiDetailResult(PoiDetailSearchResult poiDetailSearchResult) {

                }

                @Override
                public void onGetPoiIndoorResult(PoiIndoorResult poiIndoorResult) {

                }
            };
            mPoiSearch.setOnGetPoiSearchResultListener(listener);

            LatLng centerLatLng = LatLngUtil.fromReadableMap(center);

            mPoiSearch.searchNearby(new PoiNearbySearchOption()
                    .keyword(keyword)
                    .sortType(PoiSortType.distance_from_near_to_far)
                    .location(centerLatLng)
                    .radius(radius)
                    .pageNum(0)
                    .scope(2));
        } catch (Exception e) {
            Log.e("BaiduMapError", "openSearchPoiOutSideBaidumap:" + e.getMessage());
            e.printStackTrace();
        }
    }


    @ReactMethod
    public void drawPoiPoint(int viewTag,int left,int right,int top,int bottom,ReadableArray poiIds) {
        Log.d("MapAppModule", "drawPoiPoint" );
        List<String> poiIdList = poiIds == null? null: (List)(poiIds.toArrayList());
        try {
            UIManagerModule uiManager = getReactApplicationContext().getNativeModule(UIManagerModule.class);
            uiManager.addUIBlock(nativeViewHierarchyManager -> {
                try {
                    PoiResult poiResult = null;
                    MapView baiduMapView = (MapView) nativeViewHierarchyManager.resolveView(viewTag);
                    MapViewTAG mapViewTAG = (MapViewTAG)baiduMapView.getTag();
                    poiResult = mapViewTAG.getPoiResult();
                    PoiOverlay overlay = ((MapViewTAG)(baiduMapView.getTag())).getPoiOverlay();
                    if (poiResult != null&&poiIdList!=null&&poiIdList.size()>0) {
                        List<PoiInfo> poiInfos = new ArrayList<PoiInfo>();
                        for(PoiInfo poiInfo: poiResult.getAllPoi()){
                            String uid = poiInfo.getUid();
                            if(poiIdList.contains(uid)){
                                poiInfos.add(poiInfo);
                            }
                        }
                        if(poiInfos!=null && poiInfos.size()>0){
                            overlay.removeFromMap();
                            overlay.setData(poiInfos);
                            overlay.addToMap();
                            overlay.zoomToSpan(left, right, top, bottom);
                            baiduMapView.getMap().setOnMarkerClickListener(overlay);
                        }else {
                            overlay.removeFromMap();
                        }

                    } else {
                        overlay.removeFromMap();
                    }
                } catch (Exception e) {
                    Log.e("BaiduMapError", "drawPoiPoint:" + e.getMessage());
                    e.printStackTrace();
                }

            });
        } catch (Exception e) {
            Log.e("BaiduMapError", "drawPoiPoint:" + e.getMessage());
            e.printStackTrace();
        }
    }
    @ReactMethod
    public void setMapCenter(int viewTag,ReadableMap position){
        processMapView(viewTag, new ProcessMapViewCallBack() {
            @Override
            public void process(MapView mapView) {
                if(position != null) {
                    LatLng point = LatLngUtil.fromReadableMap(position);
                    MapStatus mapStatus = new MapStatus.Builder()
                            .target(point)
                            .build();
                    MapStatusUpdate mapStatusUpdate = MapStatusUpdateFactory.newMapStatus(mapStatus);
                    mapView.getMap().setMapStatus(mapStatusUpdate);
                }
            }
        });
    }

    @ReactMethod
    public void clickPoiOverLay(int viewTag,String uid){
        processMapView(viewTag, new ProcessMapViewCallBack() {
            @Override
            public void process(MapView mapView) {
                MapViewTAG mapViewTAG = (MapViewTAG)mapView.getTag();
                List<PoiInfo> infos = null;
                PoiOverlay poiOverlay = mapViewTAG.getPoiOverlay();
                if(poiOverlay!=null){
                    infos = poiOverlay.getPoiInfo();
                }
                if(infos!=null){
                    for(int index = 0;index<infos.size();index++){
                        if(infos.get(index).getUid().equals(uid)){
                            poiOverlay.onPoiClick(index);
                        }
                    }
                }
            }
        });
    }

    public static abstract class ProcessMapViewCallBack{
        public abstract void process(MapView mapView);
    }

    public void processMapView(int viewTag,ProcessMapViewCallBack callBack){
        UIManagerModule uiManager = getReactApplicationContext().getNativeModule(UIManagerModule.class);
        uiManager.addUIBlock(nativeViewHierarchyManager -> {
            try {
                MapView baiduMapView = (MapView) nativeViewHierarchyManager.resolveView(viewTag);
                callBack.process(baiduMapView);
            } catch (Exception e) {
                Log.e("BaiduMapError", "processMapView:" + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    private WritableMap transformRouteLine2JsObj(IndoorRouteLine routeLine) {
        if (routeLine == null) {
            return null;
        }

        List<IndoorRouteLine.IndoorRouteStep> stepList = routeLine.getAllStep();

        WritableNativeMap stepMap = new WritableNativeMap();

        stepMap.putInt("distance", routeLine.getDistance());
        stepMap.putInt("duration", routeLine.getDuration());
        stepMap.putString("title", routeLine.getTitle());

        WritableNativeMap startingMap = new WritableNativeMap();
        startingMap.putDouble("latitude", routeLine.getStarting().getLocation().latitude);
        startingMap.putDouble("longitude", routeLine.getStarting().getLocation().longitude);
        startingMap.putString("uid", routeLine.getStarting().getUid());
        startingMap.putString("title", routeLine.getStarting().getTitle());
        stepMap.putMap("starting", startingMap);

        WritableNativeMap terminalMap = new WritableNativeMap();
        terminalMap.putDouble("latitude", routeLine.getTerminal().getLocation().latitude);
        terminalMap.putDouble("longitude", routeLine.getTerminal().getLocation().longitude);
        terminalMap.putString("uid", routeLine.getTerminal().getUid());
        terminalMap.putString("title", routeLine.getTerminal().getTitle());
        stepMap.putMap("terminal", terminalMap);

        WritableNativeArray stepArr = new WritableNativeArray();

        for(IndoorRouteLine.IndoorRouteStep routeStep : stepList) {
            WritableNativeMap step = new WritableNativeMap();
            step.putInt("distance", routeStep.getDistance());
            step.putInt("duration", routeStep.getDuration());
            step.putString("name", routeStep.getName());
            step.putString("floorId", routeStep.getFloorId());
            step.putString("buildingId", routeStep.getBuildingId());
            step.putMap("entrace", transformRouteNode(routeStep.getEntrace()));
            step.putMap("exit", transformRouteNode(routeStep.getExit()));
            step.putString("instructions", routeStep.getInstructions());
            step.putArray("stepNodes", transformStepNode(routeStep.getStepNodes()));
            step.putArray("wayPoints", transformsLatLng(routeStep.getWayPoints()));

            stepArr.pushMap(step);
        }

        stepMap.putArray("steps", stepArr);

        return stepMap;
    }

    private WritableMap transformRouteNode(RouteNode routeNode) {
        if (routeNode == null) {
            return null;
        }

        WritableNativeMap node = new WritableNativeMap();
        node.putString("title", routeNode.getTitle());
        node.putString("uid", routeNode.getUid());
        node.putDouble("latitude", routeNode.getLocation().latitude);
        node.putDouble("longitude", routeNode.getLocation().longitude);

        return node;
    }

    private WritableArray transformStepNode(List<IndoorRouteLine.IndoorRouteStep.IndoorStepNode> indoorStepNodes) {
        if (indoorStepNodes == null) {
            return null;
        }

        WritableNativeArray stepNodes = new WritableNativeArray();
        for (IndoorRouteLine.IndoorRouteStep.IndoorStepNode node : indoorStepNodes) {
            WritableNativeMap map = new WritableNativeMap();
            map.putString("name", node.getName());
            map.putInt("type", node.getType());
            map.putDouble("latitude", node.getLocation().latitude);
            map.putDouble("longitude", node.getLocation().longitude);
            map.putString("detail", node.getDetail());

            stepNodes.pushMap(map);
        }
        return stepNodes;
    }

    private WritableArray transformsLatLng(List<LatLng> latLngs) {
        if (latLngs == null) {
            return null;
        }

        WritableNativeArray latLngArr = new WritableNativeArray();
        for (LatLng latLng : latLngs) {
            WritableNativeMap map = new WritableNativeMap();
            map.putDouble("latitude", latLng.latitude);
            map.putDouble("longitude", latLng.longitude);

            latLngArr.pushMap(map);
        }
        return latLngArr;
    }
}
