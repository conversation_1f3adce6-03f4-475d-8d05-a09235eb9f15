/**
 * Copyright (c) 2016-present, lovebing.org.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.view;

import android.annotation.TargetApi;
import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;

import com.baidu.mapapi.map.BaiduMap;
import com.baidu.mapapi.map.Polygon;
import com.baidu.mapapi.map.PolygonOptions;
import com.baidu.mapapi.map.Stroke;
import com.baidu.mapapi.model.LatLng;

import java.util.List;

/**
 * <AUTHOR> Created on Dec 9, 2018
 */
public class OverlayPolygon extends View implements com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.view.OverlayView {

    private Polygon polygon;
    private List<LatLng> points;
    private int fillColor = 0xAAFFFF00;
    private Stroke stroke = new Stroke(5, 0xAA00FF00);
    private boolean visible = true;

    public OverlayPolygon(Context context) {
        super(context);
    }

    public OverlayPolygon(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public OverlayPolygon(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @TargetApi(21)
    public OverlayPolygon(Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }


    public List<LatLng> getPoints() {
        return points;
    }

    public void setPoints(List<LatLng> points) {
        this.points = points;
        if (polygon != null) {
            polygon.setPoints(points);
        }
    }

    public int getFillColor() {
        return fillColor;
    }

    public void setFillColor(int fillColor) {
        this.fillColor = fillColor;
        if (polygon != null) {
            polygon.setFillColor(fillColor);
        }
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public Stroke getStroke() {
        return stroke;
    }

    public void setStroke(Stroke stroke) {
        this.stroke = stroke;
        if (polygon != null) {
            polygon.setStroke(stroke);
        }
    }

    @Override
    public void addTopMap(BaiduMap baiduMap) {
        if (!isVisible()) return;

        PolygonOptions options = new PolygonOptions()
                .points(points)
                .stroke(stroke)
                .fillColor(fillColor);
        polygon = (Polygon) baiduMap.addOverlay(options);
    }

    @Override
    public boolean hasAdded() {
        return polygon != null;
    }

    @Override
    public void remove() {
        if (polygon != null) {
            polygon.remove();
            polygon = null;
        }
    }
}
