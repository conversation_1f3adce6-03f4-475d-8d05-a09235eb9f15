/**
 * Copyright (c) 2016-present, lovebing.org.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.util;

import com.baidu.mapapi.map.Stroke;
import com.facebook.react.bridge.ReadableMap;

/**
 * <AUTHOR> Created on Dec 09, 2018
 */
public class StrokeUtil {

    public static Stroke fromReadableMap(ReadableMap stroke) {
        return new Stroke(stroke.getInt("width"),
                com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.util.ColorUtil.fromString(stroke.getString("color")));
    }
}
