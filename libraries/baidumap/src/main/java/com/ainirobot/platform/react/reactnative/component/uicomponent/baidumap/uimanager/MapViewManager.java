/**
 * Copyright (c) 2016-present, lovebing.org.
 * <p>
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager;

import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.BaiduMapPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.listener.MapListener;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.DrivingRouteOverlay;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.IndoorPoiOverlay;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.IndoorRouteOverlay;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.PoiOverlay;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.TransitRouteOverlay;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.WalkingRouteOverlay;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.util.LatLngUtil;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.view.OverlayView;
import com.baidu.mapapi.map.BaiduMap;
import com.baidu.mapapi.map.LogoPosition;
import com.baidu.mapapi.map.MapStatus;
import com.baidu.mapapi.map.MapStatusUpdate;
import com.baidu.mapapi.map.MapStatusUpdateFactory;
import com.baidu.mapapi.map.MapView;
import com.baidu.mapapi.model.LatLng;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.ViewGroupManager;
import com.facebook.react.uimanager.annotations.ReactProp;

import javax.annotation.Nonnull;

public class MapViewManager extends ViewGroupManager<MapView> {

    public static MapView staticMapView = null;

    @Override
    public String getName() {
        return "BaiduMapView";
    }

    @Override
    public void addView(MapView parent, View child, int index) {
        staticMapView = parent;
        staticMapView.setLogoPosition(LogoPosition.logoPostionRightBottom);
        staticMapView.setPadding(0, 0, 20, 20);
        ViewGroup p = (ViewGroup) child.getParent();
        if (p != null) {
//            p.removeView(child);
        }
        if (child instanceof OverlayView) {
            ((OverlayView) child).addTopMap(parent.getMap());
        }
    }

    @Override
    protected MapView createViewInstance(ThemedReactContext themedReactContext) {
        BaiduMapPackage.setCusstomPath();
        MapView mapView = new MapView(themedReactContext);
        BaiduMap map = mapView.getMap();
        map.getUiSettings().setRotateGesturesEnabled(false);
        map.getUiSettings().setOverlookingGesturesEnabled(false);

        MapListener listener = new MapListener(mapView, themedReactContext);
        map.setOnMapStatusChangeListener(listener);
        map.setOnMapLoadedCallback(listener);
        map.setOnMapClickListener(listener);
        map.setOnMapDoubleClickListener(listener);
        map.setOnMarkerClickListener(listener);
        map.setOnBaseIndoorMapListener(listener);
        PoiOverlay poiOverlay = new PoiOverlay(map, mapView);
        WalkingRouteOverlay walkingRouteOverlay = new WalkingRouteOverlay(map);
        DrivingRouteOverlay drivingRouteOverlay = new DrivingRouteOverlay(map);
        TransitRouteOverlay transitRouteOverlay = new TransitRouteOverlay(map);
        IndoorPoiOverlay indoorPoiOverlay = new IndoorPoiOverlay(map);
        IndoorRouteOverlay indoorRouteOverlay = new IndoorRouteOverlay(map);
        com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager.MapViewTAG tag = new com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager.MapViewTAG(poiOverlay, walkingRouteOverlay, drivingRouteOverlay, transitRouteOverlay, indoorPoiOverlay, indoorRouteOverlay);
        mapView.setTag(tag);
        return mapView;
    }

    @Override
    public void onDropViewInstance(@Nonnull MapView view) {
        super.onDropViewInstance(view);
        BaiduMap map = view.getMap();
        map.setOnMapStatusChangeListener(null);
        map.setOnMapLoadedCallback(null);
        map.setOnMapClickListener(null);
        map.setOnMapDoubleClickListener(null);
        map.setOnMarkerClickListener(null);
        map.setOnBaseIndoorMapListener(null);
        map.clear();
        MapView.setMapCustomEnable(false);
        view.removeAllViews();
        view.onDestroy();
        view = null;
        staticMapView = null;
    }

    @ReactProp(name = "customEnable")
    public void setMapCustomEnable(MapView mapView, boolean enable) {
        Log.d("MapViewManager", "setMapCustomEnable " + enable);
        MapView.setMapCustomEnable(enable);
    }

    @ReactProp(name = "zoomControlsVisible")
    public void setZoomControlsVisible(MapView mapView, boolean zoomControlsVisible) {
        Log.d("MapViewManager", "setZoomControlsVisible " + zoomControlsVisible);
        mapView.showZoomControls(zoomControlsVisible);
    }

    @ReactProp(name = "trafficEnabled")
    public void setTrafficEnabled(MapView mapView, boolean trafficEnabled) {
        Log.d("MapViewManager", "trafficEnabled " + trafficEnabled);
        mapView.getMap().setTrafficEnabled(trafficEnabled);
    }

    @ReactProp(name = "indoorEnabled")
    public void setIndoorEnabled(MapView mapView, boolean indoorEnabled) {
        Log.d("MapViewManager", "indoorEnabled " + indoorEnabled);
        mapView.getMap().setIndoorEnable(indoorEnabled);
    }

    @ReactProp(name = "baiduHeatMapEnabled")
    public void setBaiduHeatMapEnabled(MapView mapView, boolean baiduHeatMapEnabled) {
        Log.d("MapViewManager", "setBaiduHeatMapEnabled " + baiduHeatMapEnabled);
        mapView.getMap().setBaiduHeatMapEnabled(baiduHeatMapEnabled);
    }

    @ReactProp(name = "mapType")
    public void setMapType(MapView mapView, int mapType) {
        Log.d("MapViewManager", "setMapType " + mapType);
        mapView.getMap().setMapType(mapType);
    }

    @ReactProp(name = "zoom")
    public void setZoom(MapView mapView, float zoom) {
        Log.d("MapViewManager", "setZoom " + zoom);
        MapStatus mapStatus = new MapStatus.Builder().zoom(zoom).build();
        MapStatusUpdate mapStatusUpdate = MapStatusUpdateFactory.newMapStatus(mapStatus);
        mapView.getMap().setMapStatus(mapStatusUpdate);
    }

    @ReactProp(name = "center")
    public void setCenter(MapView mapView, ReadableMap position) {
        Log.d("MapViewManager", "setCenter " + position);
        if (position != null) {
            LatLng point = LatLngUtil.fromReadableMap(position);
            MapStatus mapStatus = new MapStatus.Builder()
                    .target(point)
                    .build();
            MapStatusUpdate mapStatusUpdate = MapStatusUpdateFactory.newMapStatus(mapStatus);
            mapView.getMap().setMapStatus(mapStatusUpdate);
        }
    }

    @ReactProp(name = "zoomLevel")
    public void setMaxAndMinZoomLevel(MapView mapView, ReadableMap zoomLevel) {
        if (zoomLevel != null) {
            double max = zoomLevel.getDouble("max");
            double min = zoomLevel.getDouble("min");
            Log.d("MapViewManager", "max = " + mapView.getMap().getMaxZoomLevel() + ", min = " + mapView.getMap().getMinZoomLevel());
            Log.d("MapViewManager", "max = " + max + ", min = " + min);
            mapView.getMap().setMaxAndMinZoomLevel((float) max, (float) min);
        }
    }

}
