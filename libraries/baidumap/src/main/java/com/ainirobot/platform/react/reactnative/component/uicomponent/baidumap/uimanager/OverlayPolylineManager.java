/**
 * Copyright (c) 2016-present, lovebing.org.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager;

import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.util.ColorUtil;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.util.LatLngUtil;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.view.OverlayPolyline;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.annotations.ReactProp;


/**
 * <AUTHOR> Created on Dec 09, 2018
 */
public class OverlayPolylineManager extends SimpleViewManager<OverlayPolyline> {

    @Override
    public String getName() {
        return "BaiduMapOverlayPolyline";
    }

    @Override
    protected OverlayPolyline createViewInstance(ThemedReactContext reactContext) {
        return new OverlayPolyline(reactContext);
    }

    @ReactProp(name = "points")
    public void setPoints(OverlayPolyline overlayPolyline, ReadableArray points) {
        overlayPolyline.setPoints(LatLngUtil.fromReadableArray(points));
    }

    @ReactProp(name = "color")
    public void setColor(OverlayPolyline overlayPolyline, String color) {
        overlayPolyline.setColor(ColorUtil.fromString(color));
    }

    @ReactProp(name = "visible")
    public void setVisibility(OverlayPolyline overlayPolyline, boolean visible) {
        overlayPolyline.setVisible(visible);
        if (visible) {
            if (!overlayPolyline.hasAdded() &&
                    com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager.MapViewManager.staticMapView != null &&
                    com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager.MapViewManager.staticMapView.getMap() != null) {

                overlayPolyline.addTopMap(com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager.MapViewManager.staticMapView.getMap());
            }
        } else {
            overlayPolyline.remove();
        }
    }
}
