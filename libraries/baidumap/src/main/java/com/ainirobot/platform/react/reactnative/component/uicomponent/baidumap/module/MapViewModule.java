package com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module;


import android.util.Log;

import com.baidu.mapapi.map.MapBaseIndoorMapInfo;
import com.baidu.mapapi.map.MapView;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.uimanager.UIManagerModule;


public class MapViewModule extends BaseModule {

    public MapViewModule(ReactApplicationContext reactContext) {
        super(reactContext);
        context = reactContext;
    }

    @Override
    public String getName() {
        return "BaiduMapViewModule";
    }


    @ReactMethod
    public void switchBaseIndoorMapFloor(int viewTag, String floorName, String floorId, Promise promise) {
        try {
            Log.i("BaiduMapViewModule", "switchBaseIndoorMapFloor floorName:" + floorName + "floorId:" + floorId);
            UIManagerModule uiManager = context.getNativeModule(UIManagerModule.class);
            uiManager.addUIBlock(nativeViewHierarchyManager -> {
                try {
                    MapView baiduMapView = (MapView) nativeViewHierarchyManager.resolveView(viewTag);
                    if (baiduMapView.getMap() != null) {
                        MapBaseIndoorMapInfo.SwitchFloorError resultCode = baiduMapView.getMap().switchBaseIndoorMapFloor(floorName, floorId);
                        promise.resolve(resultCode.ordinal());
                    } else {
                        Log.e("BaiduMapViewModule", "baidumap not exist!");
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }

            });
        } catch (Exception e) {
            Log.e("BaiduMapError", "switchBaseIndoorMapFloor:" + e.getMessage());
            e.printStackTrace();
            promise.reject(e);
        }
    }
}
