/**
 * Copyright (c) 2016-present, lovebing.org.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap;

import android.content.Context;

import androidx.annotation.MainThread;
import android.util.Log;

import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.BaiduMapSearchModule;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.GeolocationModule;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.MapAppModule;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.MapViewModule;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.OfflineMapModule;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module.RoutePlanSearchModule;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager.MapViewManager;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager.OverlayArcManager;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager.OverlayCircleManager;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager.OverlayMarkerManager;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager.OverlayOverlayInfoWindowManager;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager.OverlayPolygonManager;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager.OverlayPolylineManager;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.uimanager.OverlayTextManager;
import com.baidu.mapapi.map.MapView;
import com.facebook.react.ReactPackage;
import com.facebook.react.bridge.NativeModule;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.uimanager.ViewManager;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;


/**
 * Created by lovebing on 4/17/16.
 */
public class BaiduMapPackage implements ReactPackage {

    static String moduleName = null;
    static String filename = null;

    @Override
    public List<NativeModule> createNativeModules(ReactApplicationContext reactContext) {
        List<NativeModule> modules = new ArrayList<>();
        modules.add(new GeolocationModule(reactContext));
        modules.add(new MapAppModule(reactContext));
        modules.add(new MapViewModule(reactContext));
        modules.add(new BaiduMapSearchModule(reactContext));
        modules.add(new OfflineMapModule(reactContext));
        modules.add(new RoutePlanSearchModule(reactContext));
        return modules;
    }

    @Override
    public List<ViewManager> createViewManagers(
            ReactApplicationContext reactContext) {
        init(reactContext);
        List<ViewManager> managers = new ArrayList<>();
        managers.add(new MapViewManager());
        managers.add(new OverlayMarkerManager());
        managers.add(new OverlayOverlayInfoWindowManager());
        managers.add(new OverlayArcManager());
        managers.add(new OverlayCircleManager());
        managers.add(new OverlayPolygonManager());
        managers.add(new OverlayPolylineManager());
        managers.add(new OverlayTextManager());
        return managers;
    }

    @MainThread
    protected void init(ReactApplicationContext reactContext) {
//        Looper.prepare();
 //       SDKInitializer.initialize(reactContext.getApplicationContext());
        setMapCustomFile(reactContext, "custom_config_dark.json");
    }

    private void setMapCustomFile(Context context, String fileName) {
        InputStream inputStream = null;
        FileOutputStream fileOutputStream = null;
        String moduleName = null;
        try {
            inputStream = context.getAssets().open("customConfigdir/" + fileName);
            byte[] b = new byte[inputStream.available()];
            inputStream.read(b);
            moduleName = context.getFilesDir().getAbsolutePath();
            File file = new File(moduleName + "/" + fileName);
            if (file.exists()) file.delete();
            file.createNewFile();
            fileOutputStream = new FileOutputStream(file);
            //将自定义样式文件写入本地
            fileOutputStream.write(b);
        } catch (IOException e) {
            Log.e("BaiduMapError", "setMapCustomFile" + e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (fileOutputStream != null) {
                    fileOutputStream.close();
                }
            } catch (IOException e) {
                Log.e("BaiduMapError", "setMapCustomFile" + e.getMessage());
                e.printStackTrace();
            }
        }

        Log.i("MapViewManager", "setCustomMapStylePath " + (moduleName + "/" + fileName));

        BaiduMapPackage.moduleName = moduleName;
        filename = fileName;

        setCusstomPath();
    }

    public static boolean setCusstomPath() {
        File file = new File(moduleName + "/" + filename);
        if (file != null && file.exists()) {
            //设置自定义样式文件
            MapView.setCustomMapStylePath(moduleName + "/" + filename);
            return true;
        } else {
            Log.i("MapViewManager", "do not exist " + (moduleName + "/" + filename));
            return false;
        }
    }
}
