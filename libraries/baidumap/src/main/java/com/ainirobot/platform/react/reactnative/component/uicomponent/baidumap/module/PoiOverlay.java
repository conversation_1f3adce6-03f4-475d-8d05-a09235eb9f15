package com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.os.Bundle;
import android.util.Log;

import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.util.BitMapUtils;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.util.EventUtils;
import com.alibaba.fastjson.JSONObject;
import com.baidu.mapapi.map.BaiduMap;
import com.baidu.mapapi.map.BitmapDescriptor;
import com.baidu.mapapi.map.BitmapDescriptorFactory;
import com.baidu.mapapi.map.MapView;
import com.baidu.mapapi.map.Marker;
import com.baidu.mapapi.map.MarkerOptions;
import com.baidu.mapapi.map.Overlay;
import com.baidu.mapapi.map.OverlayOptions;
import com.baidu.mapapi.map.Polyline;
import com.baidu.mapapi.search.core.PoiInfo;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.WritableMap;

import java.util.ArrayList;
import java.util.List;

/**
 * 用于显示poi的overly
 */
public class PoiOverlay extends OverlayManager {

    private static final String TAG = "PoiOverlay";
    private static final int MAX_POI_SIZE = 10;
    private MapView mapView = null;
    private List<PoiInfo> mPoiInfos = null;

    /**
     * 构造函数
     * 
     * @param baiduMap   该 PoiOverlay 引用的 BaiduMap 对象
     */
    public PoiOverlay(BaiduMap baiduMap, MapView mapView) {
        super(baiduMap);
        this.mapView = mapView;
    }

    /**
     * 设置POI数据
     * 
     * @param poiInfos    设置POI数据
     */
    public void setData(List<PoiInfo> poiInfos) {
        this.mPoiInfos = poiInfos;
    }

    @Override
    public final List<OverlayOptions> getOverlayOptions() {
        if (mPoiInfos == null) {
            return null;
        }

        List<OverlayOptions> markerList = new ArrayList<>();
        int markerSize = 0;

        for (int i = 0; i < mPoiInfos.size() && markerSize < MAX_POI_SIZE; i++) {
            if (mPoiInfos.get(i).location == null) {
                continue;
            }

            markerSize++;
            Bundle bundle = new Bundle();
            bundle.putInt("index", i);
            markerList.add(new MarkerOptions()
                .icon(BitmapDescriptorFactory.fromAssetWithDpi("baidumap/Icon_mark" + markerSize + ".png"))
                .extraInfo(bundle)
                .position(mPoiInfos.get(i).location));
            
        }

        return markerList;
    }


    /**
     * 覆写此方法以改变默认点击行为
     * 
     * @param index    被点击的poi在
     *             {@link com.baidu.mapapi.search.poi.PoiResult#getAllPoi()} 中的索引
     * @return     true--事件已经处理，false--事件未处理
     */
    public boolean onPoiClick(int index) {
        Log.d(TAG,"onPoiClick index = "+index);
        Log.d(TAG,"onPoiClick size = "+mOverlayList.size());
        for(int i = 0;i<mOverlayList.size();i++){
            Marker marker = (Marker)(mOverlayList.get(i));
            BitmapDescriptor bitmapDescriptor = BitmapDescriptorFactory.fromAssetWithDpi("baidumap/Icon_mark" + (i+1) + ".png");
            if(i!=index){
                Log.d(TAG,"onPoiClick normal i = "+index);
                marker.setIcon(bitmapDescriptor);
            }else {
                Log.d(TAG,"onPoiClick scale i = "+index);
                Bitmap bitmap = bitmapDescriptor.getBitmap();
                bitmap = BitMapUtils.scaleBitmap(bitmap,1.7f);
                BitmapDescriptor scaleBitmapDescriptor = BitmapDescriptorFactory.fromBitmap(bitmap);
                marker.setIcon(scaleBitmapDescriptor);
            }
        }
        PoiInfo poiInfo = mPoiInfos.get(index);
        String poiInfoJson = JSONObject.toJSONString(poiInfo);
        EventUtils.sendEventForStringParam((ReactContext) (this.mapView.getContext()),mapView.getId(),"onPoiMarkClick",poiInfoJson);
        return false;
    }

    @Override
    public final boolean onMarkerClick(Marker marker) {
        if (!mOverlayList.contains(marker)) {
            return false;
        }

        if (marker.getExtraInfo() != null) {
            return onPoiClick(marker.getExtraInfo().getInt("index"));
        }

        return false;
    }

    @Override
    public boolean onPolylineClick(Polyline polyline) {
        return false;
    }

    public List<PoiInfo> getPoiInfo(){
        return mPoiInfos;
    }
}
