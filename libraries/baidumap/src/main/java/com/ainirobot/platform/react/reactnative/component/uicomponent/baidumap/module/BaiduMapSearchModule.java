package com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module;


import android.util.Log;

import com.baidu.mapapi.model.LatLng;
import com.baidu.mapapi.search.core.PoiInfo;
import com.baidu.mapapi.search.core.SearchResult;
import com.baidu.mapapi.search.poi.OnGetPoiSearchResultListener;
import com.baidu.mapapi.search.poi.PoiCitySearchOption;
import com.baidu.mapapi.search.poi.PoiDetailResult;
import com.baidu.mapapi.search.poi.PoiDetailSearchResult;
import com.baidu.mapapi.search.poi.PoiIndoorResult;
import com.baidu.mapapi.search.poi.PoiNearbySearchOption;
import com.baidu.mapapi.search.poi.PoiResult;
import com.baidu.mapapi.search.poi.PoiSearch;
import com.baidu.mapapi.search.poi.PoiSortType;
import com.baidu.mapapi.search.sug.OnGetSuggestionResultListener;
import com.baidu.mapapi.search.sug.SuggestionResult;
import com.baidu.mapapi.search.sug.SuggestionSearch;
import com.baidu.mapapi.search.sug.SuggestionSearchOption;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;

import java.util.List;

/**
 * Created by sujialong on 2019/7/10.
 */

public class BaiduMapSearchModule extends ReactContextBaseJavaModule implements OnGetPoiSearchResultListener, OnGetSuggestionResultListener {

    private Promise searchPromise = null;
    private Promise poiSearchPromise = null;
    private ReactApplicationContext mReactContext;
    private PoiSearch mPoiSearch = null;
    private SuggestionSearch mSuggestionSearch = null;
    private int type = 0;

    public BaiduMapSearchModule(ReactApplicationContext reactContext) {
        super(reactContext);
        mReactContext = reactContext;
    }

    @Override
    public String getName() {
        return "BaiduMapSearch";
    }

    /**
     * POI城市内检索（关键字检索）
     *
     * @param city
     * @param keyword
     * @param pageNum
     */
    @ReactMethod
    public void searchInCity(String city, String keyword, int pageNum, Promise promise) {
        try {
            poiSearchPromise = promise;
            if (mPoiSearch != null) {
                mPoiSearch.destroy();
            }
            mPoiSearch = PoiSearch.newInstance();
            mPoiSearch.setOnGetPoiSearchResultListener(this);
            type = 0;
            mPoiSearch.searchInCity(new PoiCitySearchOption()
                    .city(city) //必填
                    .keyword(keyword) //必填
                    .pageNum(pageNum));
        } catch (Exception e) {
            if (promise != null) {
                promise.reject("-1", "搜索失败" + e.getMessage());
            } else {
                Log.e("BaiduMapError", "searchInCity:" + e.getMessage());
            }
        }
    }

    /**
     * 周边检索
     *
     * @param options
     */
    @ReactMethod
    public void searchNearby(ReadableMap options, Promise promise) {
        try {
            poiSearchPromise = promise;
            if (mPoiSearch != null) {
                mPoiSearch.destroy();
            }
            mPoiSearch = PoiSearch.newInstance();
            mPoiSearch.setOnGetPoiSearchResultListener(this);
            double lat = options.getDouble("latitude");
            double lng = options.getDouble("longitude");
            String keyword = options.getString("keyword");
            int radius = 1000;
            int pageNum = 0;
            int pageCapacity = 20;
            type = 1;
            if (options.hasKey("radius")) {
                radius = options.getInt("radius");
            }
            if (options.hasKey("pageNum")) {
                pageNum = options.getInt("pageNum");
            }
            if (options.hasKey("pageCapacity")) {
                pageCapacity = options.getInt("pageCapacity");
            }
            mPoiSearch.searchNearby(new PoiNearbySearchOption()
                    .location(new LatLng(lat, lng)) //必填
                    .sortType(PoiSortType.distance_from_near_to_far)
                    .radius(radius)
                    .keyword(keyword)
                    .scope(2)
                    .pageCapacity(pageCapacity)
                    .pageNum(pageNum));
        } catch (Exception e) {
            if (promise != null) {
                promise.reject("-1", "搜索失败1" + e.toString());
            } else {
                Log.e("BaiduMapError", "searchNearby:" + e.getMessage());
            }
        }
    }

    /**
     * 输入提示检索
     *
     * @param city
     * @param keyword
     */
    @ReactMethod
    public void requestSuggestion(String city, String keyword, Promise promise) {
        try {
            searchPromise = promise;
            if (mSuggestionSearch != null) {
                mSuggestionSearch.destroy();
            }
            mSuggestionSearch = SuggestionSearch.newInstance();
            mSuggestionSearch.setOnGetSuggestionResultListener(this);
            mSuggestionSearch.requestSuggestion(new SuggestionSearchOption()
                    .city(city)
                    .keyword(keyword));
        } catch (Exception e) {
            if (promise != null) {
                promise.reject("-1", "搜索失败" + e.getMessage());
            } else {
                Log.e("BaiduMapError", "requestSuggestion" + e.getMessage());
            }
        }
    }

    public void onSendEvent(String eventName, WritableMap writableMap) {
        mReactContext.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                .emit(eventName, writableMap);
    }

    @Override
    public void onGetPoiResult(PoiResult poiResult) {
        try {
            WritableArray data = Arguments.createArray();
            WritableMap writableMap = Arguments.createMap();

            if (poiResult.error == SearchResult.ERRORNO.NO_ERROR) {
                List<PoiInfo> poiList = poiResult.getAllPoi();
                for (PoiInfo info : poiList) {
                    WritableMap attr = Arguments.createMap();
                    LatLng latLng = info.location;
                    if (latLng != null) {
                        attr.putString("name", info.name);
                        attr.putString("address", info.address);
                        attr.putString("city", info.city);
                        attr.putString("province", info.province);
                        attr.putString("uid", info.uid);
                        attr.putDouble("latitude", latLng.latitude);
                        attr.putDouble("longitude", latLng.longitude);
                        data.pushMap(attr);
                    }
                }
                writableMap.putInt("type", type);
                writableMap.putInt("code", 1000);
                writableMap.putArray("poiList", data);
                poiSearchPromise.resolve(writableMap);
            } else {
                poiSearchPromise.reject("-1", "搜索失败");
            }
        } catch (Exception e) {
            if (poiSearchPromise != null) {
                poiSearchPromise.reject("-1", "搜索失败" + e.getMessage());
            } else {
                Log.e("BaiduMapError", "requestSuggestion:" + e.getMessage());
            }
        }
    }

    @Override
    public void onGetPoiDetailResult(PoiDetailResult poiDetailResult) {
        if (poiSearchPromise == null) {
            return;
        }
    }

    @Override
    public void onGetPoiDetailResult(PoiDetailSearchResult poiDetailSearchResult) {
        if (poiSearchPromise == null) {
            return;
        }
    }

    @Override
    public void onGetPoiIndoorResult(PoiIndoorResult poiIndoorResult) {
        if (poiSearchPromise == null) {
            return;
        }
    }

    @Override
    public void onGetSuggestionResult(SuggestionResult suggestionResult) {
        if (searchPromise == null) {
            return;
        }
        try {
            WritableArray data = Arguments.createArray();
            WritableMap writableMap = Arguments.createMap();

            if (suggestionResult.error == SearchResult.ERRORNO.NO_ERROR) {
                List<SuggestionResult.SuggestionInfo> poiLIst = suggestionResult.getAllSuggestions();
                for (SuggestionResult.SuggestionInfo info : poiLIst) {
                    WritableMap attr = Arguments.createMap();
                    LatLng latLng = info.getPt();
                    if (latLng != null) {
                        attr.putString("address", info.getAddress());
                        attr.putString("city", info.getCity());
                        attr.putString("district", info.getDistrict());
                        attr.putString("uid", info.getUid());
                        attr.putString("key", info.getKey());
                        attr.putString("name", info.getKey());
                        attr.putDouble("latitude", latLng.latitude);
                        attr.putDouble("longitude", latLng.longitude);
                        data.pushMap(attr);
                    }
                }
                writableMap.putInt("type", type);
                writableMap.putInt("code", 1000);
                writableMap.putArray("poiList", data);
                searchPromise.resolve(writableMap);
            } else {
                searchPromise.reject("-1", "搜索失败");
            }
        } catch (Exception e) {
            if (searchPromise != null) {
                searchPromise.reject("-1", "搜索失败2" + e.toString());
            } else {
                Log.e("BaiduMapError", "requestSuggestion" + e.getMessage());
            }
        }
    }
}
