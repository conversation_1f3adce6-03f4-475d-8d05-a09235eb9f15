/**
 * Copyright (c) 2016-present, lovebing.org.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.module;

import android.util.Log;

import com.baidu.location.BDLocation;
import com.baidu.location.BDLocationListener;
import com.baidu.location.LocationClient;
import com.baidu.location.LocationClientOption;
import com.baidu.location.LocationClientOption.LocationMode;
import com.baidu.mapapi.model.LatLng;
import com.baidu.mapapi.search.core.PoiInfo;
import com.baidu.mapapi.search.core.SearchResult;
import com.baidu.mapapi.search.geocode.GeoCodeOption;
import com.baidu.mapapi.search.geocode.GeoCodeResult;
import com.baidu.mapapi.search.geocode.GeoCoder;
import com.baidu.mapapi.search.geocode.OnGetGeoCoderResultListener;
import com.baidu.mapapi.search.geocode.ReverseGeoCodeOption;
import com.baidu.mapapi.search.geocode.ReverseGeoCodeResult;
import com.baidu.mapapi.search.poi.OnGetPoiSearchResultListener;
import com.baidu.mapapi.search.poi.PoiDetailResult;
import com.baidu.mapapi.search.poi.PoiDetailSearchResult;
import com.baidu.mapapi.search.poi.PoiIndoorInfo;
import com.baidu.mapapi.search.poi.PoiIndoorOption;
import com.baidu.mapapi.search.poi.PoiIndoorResult;
import com.baidu.mapapi.search.poi.PoiResult;
import com.baidu.mapapi.search.poi.PoiSearch;
import com.baidu.mapapi.utils.CoordinateConverter;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;

import java.util.List;

/**
 * Created by lovebing on 2016/10/28.
 */
public class GeolocationModule extends BaseModule
        implements BDLocationListener, OnGetGeoCoderResultListener {

    private LocationClient locationClient;
    private static GeoCoder geoCoder;
    private int mLocationTimes = 0;
    private static final int RELOCATION_MAX_TIMES = 10;
    private static final int RELOCATION_INTERVAL = 1000;

    public GeolocationModule(ReactApplicationContext reactContext) {
        super(reactContext);
        context = reactContext;
    }

    public String getName() {
        return "BaiduGeolocationModule";
    }

    private void initLocationClient() {
        LocationClientOption option = new LocationClientOption();
        option.setLocationMode(LocationMode.Hight_Accuracy);
        option.setScanSpan(RELOCATION_INTERVAL);
        option.setCoorType("bd09ll");
        option.setIsNeedAddress(true);
        option.setIsNeedAltitude(true);
        option.setIsNeedLocationDescribe(true);
        option.setOpenGps(false);
        locationClient = new LocationClient(context.getApplicationContext());
        locationClient.setLocOption(option);
        Log.i("locationClient", "locationClient");
        locationClient.registerLocationListener(this);
    }
    /**
     *
     * @return
     */
    protected GeoCoder getGeoCoder() {
        if(geoCoder != null) {
            geoCoder.destroy();
        }
        geoCoder = GeoCoder.newInstance();
        geoCoder.setOnGetGeoCodeResultListener(this);
        return geoCoder;
    }

    /**
     *
     * @param sourceLatLng
     * @return
     */
    protected LatLng getBaiduCoorFromGPSCoor(LatLng sourceLatLng) {
        CoordinateConverter converter = new CoordinateConverter();
        converter.from(CoordinateConverter.CoordType.GPS);
        converter.coord(sourceLatLng);
        LatLng desLatLng = converter.convert();
        return desLatLng;

    }

    @ReactMethod
    public void getPoiSearchResult(String bid, String wd) {
        final PoiSearch poiSearch = PoiSearch.newInstance();
        try {
            poiSearch.setOnGetPoiSearchResultListener(new OnGetPoiSearchResultListener() {

                @Override
                public void onGetPoiResult(PoiResult poiResult) {

                }

                @Override
                public void onGetPoiDetailResult(PoiDetailResult poiDetailResult) {

                }

                @Override
                public void onGetPoiDetailResult(PoiDetailSearchResult poiDetailSearchResult) {

                }

                @Override
                public void onGetPoiIndoorResult(PoiIndoorResult poiIndoorResult) {
                    WritableMap params = Arguments.createMap();
                    params.putDouble("pageNum", poiIndoorResult.getPageNum());
                    params.putDouble("poiNum", poiIndoorResult.getPoiNum());

                    WritableArray array = Arguments.createArray();
                    if (poiIndoorResult.getmArrayPoiInfo() != null) {
                        for(PoiIndoorInfo info: poiIndoorResult.getmArrayPoiInfo()) {

                            WritableMap infoParams = Arguments.createMap();
                            infoParams.putString("address", info.address);
                            infoParams.putString("bid", info.bid);
                            infoParams.putInt("cid", info.cid);
                            infoParams.putInt("discount", info.discount);
                            infoParams.putString("floor", info.floor);
                            infoParams.putString("name", info.name);
                            infoParams.putString("phone", info.phone);
                            infoParams.putDouble("price", info.price);
                            infoParams.putInt("starLevel", info.starLevel);
                            infoParams.putBoolean("isGroup", info.isGroup);
                            infoParams.putBoolean("isTakeOut", info.isTakeOut);
                            infoParams.putBoolean("isWaited", info.isWaited);
                            infoParams.putString("uid", info.uid);
                            infoParams.putString("tag", info.tag);
                            infoParams.putInt("groupNum", info.groupNum);

                            WritableMap locationInfo = Arguments.createMap();
                            locationInfo.putDouble("latitude", info.latLng.latitude);
                            locationInfo.putDouble("longitude", info.latLng.longitude);
                            infoParams.putMap("locationInfo", locationInfo);

                            array.pushMap(infoParams);
                        }
                    }
                    params.putArray("poiIndoorInfos", array);

                    sendEvent("onGetPoiSearchResult", params);
                    poiSearch.destroy();
                }
            } );
            PoiIndoorOption option = new PoiIndoorOption().poiIndoorBid(bid).poiIndoorWd(wd);
            poiSearch.searchPoiIndoor(option);
        } catch (Exception e) {
            Log.e("BaiduMapError", "getPoiSearchResult:" + e.getMessage());
            poiSearch.destroy();
            Log.e("GeolocationModule", "getPoiSearchResult -> error " + e.toString());
        }
    }

    @ReactMethod
    public void convertGPSCoor(double lat, double lng, Promise promise) {
        try {
            Log.i("convertGPSCoor", "convertGPSCoor");
            LatLng latLng = getBaiduCoorFromGPSCoor(new LatLng(lat, lng));
            WritableMap map = Arguments.createMap();
            map.putDouble("latitude", latLng.latitude);
            map.putDouble("longitude", latLng.longitude);
            promise.resolve(map);
        } catch (Exception e) {
            Log.e("BaiduMapError", "convertGPSCoor:" + e.getMessage());
            e.printStackTrace();
            promise.reject(e);
        }
    }

    @ReactMethod
    public void getCurrentPosition() {
        mLocationTimes = 0;
        try {
            if(locationClient == null) {
                initLocationClient();
            }
            Log.i("BaiduMap position", "getCurrentPosition: mLocationTimes=" + mLocationTimes);
            if (!locationClient.isStarted()) {
                locationClient.start();
                Log.i("BaiduMap position", "getCurrentPosition: start");
            } else {
                locationClient.restart();
                Log.i("BaiduMap position", "getCurrentPosition: restart");
            }
        } catch (Exception e) {
            Log.e("BaiduMapError", "getCurrentPosition:" + e.getMessage());
            e.printStackTrace();
        }
    }

    @ReactMethod
    public void geocode(String city, String addr) {
        try {
            getGeoCoder().geocode(new GeoCodeOption()
                    .city(city).address(addr));
        } catch (Exception e) {
            Log.e("BaiduMapError", "geocode:" + e.getMessage());
            e.printStackTrace();
        }
    }

    @ReactMethod
    public void reverseGeoCode(double lat, double lng) {
        try {
            getGeoCoder().reverseGeoCode(new ReverseGeoCodeOption()
                    .location(new LatLng(lat, lng)));
        } catch (Exception e) {
            Log.e("BaiduMapError", "reverseGeoCode:" + e.getMessage());
            e.printStackTrace();
        }
    }

    @ReactMethod
    public void reverseGeoCodeGPS(double lat, double lng) {
        try {
            getGeoCoder().reverseGeoCode(new ReverseGeoCodeOption()
                    .location(getBaiduCoorFromGPSCoor(new LatLng(lat, lng))));
        } catch (Exception e) {
            Log.e("BaiduMapError", "reverseGeoCodeGPS:" + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onReceiveLocation(BDLocation bdLocation) {
        mLocationTimes++;
        Log.i("onReceiveLocation", " bdLocation=" + bdLocation + " mLocationTimes=" + mLocationTimes);
        if (bdLocation == null
                || bdLocation.getLocType() == BDLocation.TypeServerError
                || bdLocation.getLocType() == BDLocation.TypeOffLineLocationFail
                || bdLocation.getLocType() == BDLocation.TypeCriteriaException
                || bdLocation.getLocType() == BDLocation.TypeOffLineLocation) {
            if (mLocationTimes < RELOCATION_MAX_TIMES) {
                Log.i("onReceiveLocation", " BDLocation error and retry!");
                return;
            }
        }

        WritableMap params = Arguments.createMap();
        try {
            params.putDouble("latitude", bdLocation.getLatitude());
            params.putDouble("longitude", bdLocation.getLongitude());
            params.putDouble("direction", bdLocation.getDirection());
            params.putDouble("altitude", bdLocation.getAltitude());
            params.putDouble("radius", bdLocation.getRadius());
            params.putString("address", bdLocation.getAddrStr());
            params.putString("countryCode", bdLocation.getCountryCode());
            params.putString("country", bdLocation.getCountry());
            params.putString("province", bdLocation.getProvince());
            params.putString("cityCode", bdLocation.getCityCode());
            params.putString("city", bdLocation.getCity());
            params.putString("district", bdLocation.getDistrict());
            params.putString("street", bdLocation.getStreet());
            params.putString("streetNumber", bdLocation.getStreetNumber());
            params.putString("buildingId", bdLocation.getBuildingID());
            params.putString("buildingName", bdLocation.getBuildingName());
            Log.i("onReceiveLocation", "onGetCurrentLocationPosition");
        } catch (Exception e) {
            Log.e("BaiduMapError", "onReceiveLocation" + e.getMessage());
            e.printStackTrace();
        }

        sendEvent("onGetCurrentLocationPosition", params);
        locationClient.stop();
    }

    @Override
    public void onGetGeoCodeResult(GeoCodeResult result) {
        WritableMap params = Arguments.createMap();
        try {
            if (result == null || result.error != SearchResult.ERRORNO.NO_ERROR) {
                params.putInt("errcode", -1);
            }
            else {
                params.putDouble("latitude",  result.getLocation().latitude);
                params.putDouble("longitude",  result.getLocation().longitude);
            }
        } catch (Exception e) {
            Log.e("BaiduMapError", "onGetGeoCodeResult:" + e.getMessage());
            e.printStackTrace();
        }
        sendEvent("onGetGeoCodeResult", params);
    }

    @Override
    public void onGetReverseGeoCodeResult(ReverseGeoCodeResult result) {
        WritableMap params = Arguments.createMap();
        try {
            if (result == null || result.error != SearchResult.ERRORNO.NO_ERROR) {
                params.putInt("errcode", -1);
            }
            else {
                ReverseGeoCodeResult.AddressComponent addressComponent = result.getAddressDetail();
                params.putString("address", result.getAddress());
                params.putString("province", addressComponent.province);
                params.putString("city", addressComponent.city);
                params.putString("district", addressComponent.district);
                params.putString("street", addressComponent.street);
                params.putString("streetNumber", addressComponent.streetNumber);

                WritableArray list = Arguments.createArray();
                List<PoiInfo> poiList = result.getPoiList();
                for (PoiInfo info: poiList) {
                    WritableMap attr = Arguments.createMap();
                    attr.putString("name", info.name);
                    attr.putString("address", info.address);
                    attr.putString("city", info.city);
                    attr.putDouble("latitude", info.location.latitude);
                    attr.putDouble("longitude", info.location.longitude);
                    list.pushMap(attr);
                }
                params.putArray("poiList", list);
            }
        } catch (Exception e) {
            Log.e("BaiduMapError", "onGetReverseGeoCodeResult:" + e.getMessage());
            e.printStackTrace();
        }
        sendEvent("onGetReverseGeoCodeResult", params);
    }
}
