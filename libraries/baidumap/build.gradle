apply plugin: 'com.android.library'

def isOversea = project.hasProperty('isOversea') ? isOversea : false

android {
    compileSdkVersion rootProject.ext.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        debug {
            dependencies {
                fileRN('059') { name ->
                    debugCompileOnly(name: name, ext: 'aar')
                }
            }
        }

        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            dependencies {
                fileRN('059') { name ->
                    releaseCompileOnly(name: name, ext: 'aar')
                }
            }
        }

        rn069 {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            dependencies {
                fileRN('069') { name ->
                    rn069CompileOnly(name: name, ext: 'aar')
                }
            }
        }
    }

    lintOptions {
        abortOnError false
    }
    compileOptions {
        sourceCompatibility 1.8
        targetCompatibility 1.8
    }

    sourceSets {
        main {
            if (isOversea) {
                manifest.srcFile 'src/main/oversea/AndroidManifest.xml'
            } else {
                manifest.srcFile 'src/main/domestic/AndroidManifest.xml'
            }
            jniLibs.srcDir file('jniLibs/')
        }
    }

    if (isOversea) {
        println(':libraries:baidumap is oversea')
    } else {
        println(':libraries:baidumap is domestic')
    }
}

def fileRN(String dir, def addDepend) {
    def thirdLibs = rootProject.file("libraries/3rdlibs/$dir")
    thirdLibs.traverse(nameFilter: ~/orionos-react-native.*\.aar/) {
        def name = it.getName().replace('.aar', '')
        addDepend(name)
    }
}

dependencies {
    compileOnly fileTree(dir: 'libs', include: ['*.jar'])
    compileOnly 'androidx.appcompat:appcompat:1.3.1'
    compileOnly "com.facebook.infer.annotation:infer-annotation:0.11.2"
    compileOnly "com.facebook.fresco:fresco:1.10.0"
    compileOnly 'com.alibaba:fastjson:1.2.70'
}
