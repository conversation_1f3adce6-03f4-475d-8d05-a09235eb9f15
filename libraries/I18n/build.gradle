apply plugin: 'com.android.library'

def _ext = rootProject.ext
def _compileSdkVersion = _ext.has('compileSdkVersion') ? _ext.compileSdkVersion : 28
def _buildToolsVersion = _ext.has('buildToolsVersion') ? _ext.buildToolsVersion : '28.0.3'
def _targetSdkVersion = _ext.has('targetSdkVersion') ? _ext.targetSdkVersion : 25

android {
    compileSdkVersion _compileSdkVersion
    buildToolsVersion _buildToolsVersion

    defaultConfig {
        targetSdkVersion _targetSdkVersion
    }

    lintOptions {
        abortOnError false
    }

    buildTypes {
        debug {
            dependencies {
                fileRN('059') { name ->
                    debugCompileOnly(name: name, ext: 'aar')
                }
            }
        }

        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            dependencies {
                fileRN('059') { name ->
                    releaseCompileOnly(name: name, ext: 'aar')
                }
            }
        }

        rn069 {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            dependencies {
                fileRN('069') { name ->
                    rn069CompileOnly(name: name, ext: 'aar')
                }
            }
        }
    }
}

def fileRN(String dir, def addDepend) {
    def thirdLibs = rootProject.file("libraries/3rdlibs/$dir")
    thirdLibs.traverse(nameFilter: ~/orionos-react-native.*\.aar/) {
        def name = it.getName().replace('.aar', '')
        addDepend(name)
    }
}

dependencies {

}
