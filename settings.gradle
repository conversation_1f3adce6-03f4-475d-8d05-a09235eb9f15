enableFeaturePreview("VERSION_CATALOGS")
dependencyResolutionManagement {
    versionCatalogs {
        //共用库
        common {
            //AndroidX
            alias('appcompat').to('androidx.appcompat:appcompat:1.3.1')
            alias('appcompat-resources').to('androidx.appcompat:appcompat-resources:1.3.1')
            alias('cardview').to('androidx.cardview:cardview:1.0.0')
            alias('autofill').to('androidx.autofill:autofill:1.1.0')
            alias('annotation').to('androidx.annotation:annotation:1.4.0')
            alias('core-ktx').to('androidx.core:core-ktx:1.6.0')
            alias('media').to('androidx.media:media:1.3.0')
            alias('constraintlayout').to('androidx.constraintlayout:constraintlayout:2.1.4')
            alias('swiperefreshlayout').to('androidx.swiperefreshlayout:swiperefreshlayout:1.0.0')
            alias('recyclerview').to('androidx.recyclerview:recyclerview:1.2.1')
            alias('dynamicanimation').to('androidx.dynamicanimation:dynamicanimation:1.0.0')
            alias('exifinterface').to('androidx.exifinterface:exifinterface:1.3.6')
            alias('material').to('com.google.android.material:material:1.4.0')
            bundle('androidx', ['appcompat', 'appcompat-resources', 'cardview', 'annotation',
                                'media', 'constraintlayout', 'swiperefreshlayout', 'recyclerview',
                                'dynamicanimation', 'exifinterface', 'material', 'autofill'])

            //Kotlin
            version('kt-version', '1.6.10')
            alias('kt-stdlib').to('org.jetbrains.kotlin', 'kotlin-stdlib-jdk7').versionRef('kt-version')
            alias('kt-reflect').to('org.jetbrains.kotlin', 'kotlin-reflect').versionRef('kt-version')
            alias('kt-coroutines').to('org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4')
            bundle('kotlin', ['kt-stdlib', 'kt-reflect', 'kt-coroutines'])

            //Fresco
            version('fresco-version', '2.5.0')
            alias('fresco').to('com.facebook.fresco', 'fresco').versionRef('fresco-version')
            alias('imagepipeline-okhttp3').to('com.facebook.fresco', 'imagepipeline-okhttp3').versionRef('fresco-version')
            alias('ui-common').to('com.facebook.fresco', 'ui-common').versionRef('fresco-version')
            alias('animated-gif').to('com.facebook.fresco', 'animated-gif').versionRef('fresco-version')
            alias('animated-webp').to('com.facebook.fresco', 'animated-webp').versionRef('fresco-version')
            alias('webpsupport').to('com.facebook.fresco', 'webpsupport').versionRef('fresco-version')
            bundle('fresco', ['fresco', 'imagepipeline-okhttp3', 'ui-common', 'animated-gif', 'animated-webp', 'webpsupport'])

            //OkHttp
            version('okhttp-version', '4.9.2')
            alias('okhttp').to('com.squareup.okhttp3', 'okhttp').versionRef('okhttp-version')
            alias('okhttp-urlconnection').to('com.squareup.okhttp3', 'okhttp-urlconnection').versionRef('okhttp-version')
            alias('logging-interceptor').to('com.squareup.okhttp3', 'logging-interceptor').versionRef('okhttp-version')
            alias('okio').to('com.squareup.okio:okio:2.9.0')
            bundle('okhttp', ['okhttp', 'okhttp-urlconnection', 'logging-interceptor', 'okio'])

            //其他基础库
            alias('glide').to('com.github.bumptech.glide:glide:3.8.0')
            alias('glide-transformations').to('jp.wasabeef:glide-transformations:2.0.1')
            alias('gson').to('com.google.code.gson:gson:2.7')
            alias('eventbus').to('org.greenrobot:eventbus:3.1.1')
            alias('liulishuo').to('com.liulishuo.filedownloader:library:1.7.6')
            alias('retrofit').to('com.squareup.retrofit2:retrofit:2.6.0')
            alias('android-eventbus').to('org.simple:androideventbus:1.0.5')
            alias('joml').to("org.joml:joml-android:1.9.3-SNAPSHOT")
            bundle('base', ['glide', 'glide-transformations', 'gson', 'eventbus', 'liulishuo',
                            'retrofit', 'android-eventbus', 'joml'])
        }

        //RN059版本依赖库
        rn059Libs {
            //RN框架依赖
            alias('infer-annotation').to("com.facebook.infer.annotation:infer-annotation:0.11.2")
            alias('javax-inject').to("javax.inject:javax.inject:1")
            alias('jsr305').to('com.google.code.findbugs:jsr305:3.0.2')
            alias('soloader').to("com.facebook.soloader:soloader:0.6.0")
            bundle('rn', ['soloader', 'infer-annotation', 'javax-inject', 'jsr305'])

            //默认集成的三方库的依赖
            alias('lottie').to('com.airbnb.android:lottie:2.6.1')
            alias('jobdispatcher').to('com.firebase:firebase-jobdispatcher:0.7.0')
            alias('rxandroidble').to("com.polidea.rxandroidble:rxandroidble:1.7.0")
            alias('metadata-extractor').to("com.drewnoakes:metadata-extractor:2.11.0")
            alias('gms-play-services').to("com.google.android.gms:play-services-vision:17.0.2")
            alias('spongycastle-core').to('com.madgag.spongycastle:core:********')
            alias('spongycastle-prov').to('com.madgag.spongycastle:prov:********')
            alias('spongycastle-bcpkix-jdk15on').to('com.madgag.spongycastle:bcpkix-jdk15on:********')
            alias('exoplayer').to('com.google.android.exoplayer:exoplayer:2.9.3')
            alias('extension-okhttp').to('com.google.android.exoplayer:extension-okhttp:2.9.3')
            alias('zip4j').to("net.lingala:zip4j:1.3.3")
            bundle('thirdepends', ['lottie', 'jobdispatcher', 'rxandroidble', 'metadata-extractor',
                                 'gms-play-services', 'spongycastle-core', 'spongycastle-prov',
                                 'spongycastle-bcpkix-jdk15on', 'exoplayer', 'extension-okhttp', 'zip4j'])
        }

        rn069Libs {
            //RN框架依赖
            alias('infer-annotation').to("com.facebook.infer.annotation:infer-annotation:0.18.0")
            alias('proguard-annotations').to("com.facebook.yoga:proguard-annotations:1.19.0")
            alias('javax-inject').to("javax.inject:javax.inject:1")
            alias('jsr305').to('com.google.code.findbugs:jsr305:3.0.2')
            alias('soloader').to("com.facebook.soloader:soloader:0.10.3")
            alias('fbjni').to("com.facebook.fbjni:fbjni-java-only:0.2.2")
            bundle('rn', ['soloader', 'fbjni', 'proguard-annotations', 'infer-annotation',
                          'javax-inject', 'jsr305'])

            //默认集成的三方库的依赖
            alias('lottie').to('com.airbnb.android:lottie:5.1.1')
            alias('jobdispatcher').to('com.firebase:firebase-jobdispatcher:0.8.5')
            alias('rxandroidble').to("com.github.Polidea:MultiPlatformBleAdapter:b0272c0fae040cc90f790481c54e8f2d45304339")
            alias('metadata-extractor').to("com.drewnoakes:metadata-extractor:2.11.0")
            alias('gms-play-services').to("com.google.android.gms:play-services-vision:17.0.2")
            alias('spongycastle-core').to('com.madgag.spongycastle:core:********')
            alias('spongycastle-prov').to('com.madgag.spongycastle:prov:********')
            alias('spongycastle-bcpkix-jdk15on').to('com.madgag.spongycastle:bcpkix-jdk15on:********')
            alias('exoplayer').to('com.google.android.exoplayer:exoplayer:2.13.3')
            alias('extension-okhttp').to('com.google.android.exoplayer:extension-okhttp:2.13.3')
            alias('zip4j').to("net.lingala:zip4j:1.3.3")
            bundle('thirdepends', ['lottie', 'jobdispatcher', 'rxandroidble', 'metadata-extractor',
                                 'gms-play-services', 'spongycastle-core', 'spongycastle-prov',
                                 'spongycastle-bcpkix-jdk15on', 'exoplayer', 'extension-okhttp', 'zip4j'])
        }
    }
}

include ':emoji', ':platform', ':libraries:I18n', ':app', ':libraries:baidumap', ':libraries:shadowopk'