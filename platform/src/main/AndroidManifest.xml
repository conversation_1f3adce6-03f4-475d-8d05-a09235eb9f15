<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools"
          package="com.ainirobot.platform">

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.BROADCAST_STICKY"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.Manifest.permission.MODIFY_PHONE_STATE"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.Manifest.permission.SET_ACTIVITY_WATCHER"/>

    <uses-feature android:name="android.hardware.camera"/>

    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="android.permission.SET_PREFERRED_APPLICATIONS"/>
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS"/>

    <permission android:name="com.ainirobot.permission.WAKEUP_ID_RECEIVER"/>
    <permission android:name="com.ainirobot.moduleapp.permission_MODULEAPP_PROVIDER"/>
    <permission android:name="com.ainirobot.permission.CHANGE_MODULE_RECEIVER"/>

    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.REBOOT"/>

    <uses-permission android:name="com.ainirobot.account_provider.READ"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>

    <uses-permission android:name="android.Manifest.permission.INJECT_EVENTS"/>
    <uses-permission android:name="android.permission.GET_TASKS"/>
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES"/>
    <uses-permission android:name="android.permission.REAL_GET_TASKS"/>
    <uses-permission android:name="android.permission.REMOVE_TASKS"/>
    <uses-permission android:name="android.permission.REORDER_TASKS"/>

    <permission android:name="com.ainirobot.platform.msgprovider"/>
    <application>
        <service
            android:name="com.ainirobot.platform.control.SkillService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.ainirobot.platform.SERVICE"/>
            </intent-filter>
        </service>

        <!--react native process begin-->
        <activity
            android:name=".react.EveActivity"
            android:configChanges="density|screenLayout|screenSize|keyboardHidden|keyboard|orientation|mcc|mnc|touchscreen|navigation"
            android:exported="true"
            android:launchMode="singleTask"
            android:process=":sandbox"
            android:screenOrientation="fullSensor"
            android:taskAffinity=".platform"
            android:theme="@style/EvaAppTheme"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
            </intent-filter>
        </activity>

        <service
            android:name=".react.server.control.RNServerService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.ainirobot.platform.rn.server.SERVICE"/>
            </intent-filter>
        </service>

        <activity
            android:name="com.facebook.react.devsupport.DevSettingsActivity"
            android:process=":sandbox"
            android:screenOrientation="fullSensor"/>
        <activity
            android:name=".react.FlashActivity"
            android:process=":sandbox" />

        <receiver
            android:name=".react.reactnative.RNDebugReceiver"
            android:exported="true"
            android:process=":sandbox">
            <intent-filter>
                <action android:name="com.ainirobot.moduleapp.debug"/>
                <action android:name="com.ainirobot.moduleapp.delete.bundle"/>
<!--                <action android:name="com.ainirobot.moduleapp.remote.debug"/>-->
            </intent-filter>
        </receiver>

        <receiver
            android:name=".react.reactnative.RNDumpReceiver"
            android:exported="true"
            android:process=":sandbox"
            >
            <intent-filter>
                <action android:name="com.ainirobot.moduleapp.take.snapshot"/>
                <action android:name="com.ainirobot.moduleapp.stop.snapshot"/>
                <action android:name="com.ainirobot.moduleapp.dump.meminfo"/>
                <action android:name="com.ainirobot.moduleapp.stop.dump"/>
            </intent-filter>
        </receiver>

        <service
            android:name="com.pilloxa.backgroundjob.BackgroundJob"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":sandbox"
            tools:node="replace"/>

        <service
            android:name="com.pilloxa.backgroundjob.ExactJob"
            android:enabled="true"
            android:process=":sandbox"
            tools:node="replace"/>

        <service
            android:name="com.pilloxa.backgroundjob.ReactNativeEventStarter$MyHeadlessJsTaskService"
            android:enabled="true"
            android:process=":sandbox"
            tools:node="replace"/>

        <service
            android:name=".control.AppMessengerService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.ainirobot.platform.APP_MESSENGER"></action>
            </intent-filter>
        </service>
        <!--react native process end-->

        <!--main process begin-->
        <!--        <receiver-->
        <!--            android:name=".react.CharacterReceiver"-->
        <!--            android:exported="true">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="com.ainirobot.moduleapp.SWITCH_CHARACTER"/>-->
        <!--            </intent-filter>-->
        <!--        </receiver>-->

        <!--        <receiver-->
        <!--            android:name=".react.InstallCharacterReceiver"-->
        <!--            android:exported="true">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="com.ainirobot.remotecontrolservice.rninstallfinish"/>-->
        <!--            </intent-filter>-->
        <!--        </receiver>-->

        <!--        <receiver-->
        <!--            android:name=".react.InstallPluginReceiver"-->
        <!--            android:exported="true">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="com.ainirobot.install.plugin"/>-->
        <!--                <action android:name="com.ainirobot.install.host"/>-->
        <!--                <action android:name="com.ainirobot.uninstall.plugin"/>-->
        <!--            </intent-filter>-->
        <!--        </receiver>-->


        <!--        <receiver-->
        <!--            android:name=".react.RomOpkUpdateReceiver"-->
        <!--            android:exported="true">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="com.ainirobot.moduleapp.rom_opk_update"/>-->
        <!--            </intent-filter>-->
        <!--        </receiver>-->

        <provider
            android:name=".data.provider.DataProvider"
            android:authorities="com.ainirobot.robotplatform.dataprovider"
            android:exported="true"/>

        <provider
            android:name=".msg.data.MsgProvider"
            android:authorities="com.ainirobot.platform.msgprovider"
            android:exported="true"
            android:permission="com.ainirobot.platform.msgprovider"/>
    </application>
</manifest>
