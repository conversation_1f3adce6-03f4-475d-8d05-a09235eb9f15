package com.ainirobot.platform;

import android.content.Intent;
import android.os.HandlerThread;
import android.util.Log;

import com.ainirobot.coreservice.client.ApiListener;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.module.ModuleCallbackApi;
import com.ainirobot.platform.appmanager.ConfigManager;
import com.ainirobot.platform.callbutton.CallButtonManager;
import com.ainirobot.platform.character.CharacterManager;
import com.ainirobot.platform.control.ControlManager;
import com.ainirobot.platform.control.ModuleCallback;
import com.ainirobot.platform.control.SkillService;
import com.ainirobot.platform.data.FeatureConfig;
import com.ainirobot.platform.data.NetworkManager;
import com.ainirobot.platform.data.RobotInfo;
import com.ainirobot.platform.data.TestManager;
import com.ainirobot.platform.msg.MsgController;
import com.ainirobot.platform.react.server.control.RNServerService;
import com.ainirobot.platform.utils.observe.SystemObserves;

public class PlatformInit {

    private static final String TAG = "Platform";

    private static volatile PlatformInit sPlatformInit;
    private ModuleCallbackApi mModuleCallback;
    private HandlerThread mApiCallbackThread;

    private PlatformInit() {
        mModuleCallback = new ModuleCallback(BaseApplication.getContext());
        mApiCallbackThread = new HandlerThread("ModuleApp");
        mApiCallbackThread.start();
    }

    public static PlatformInit getInstance() {
        if (sPlatformInit == null) {
            synchronized (PlatformInit.class) {
                if (sPlatformInit == null) {
                    sPlatformInit = new PlatformInit();
                }
            }
        }
        return sPlatformInit;
    }

    public void init() {
        initRobotApi();
    }

    private void addApiCallBack() {
        Log.d(TAG, "CoreService connected ");
        RobotApi.getInstance().setCallback(mModuleCallback);
        RobotApi.getInstance().setResponseThread(mApiCallbackThread);
    }

    private void initRobotApi() {
        RobotApi.getInstance().connectServer(BaseApplication.getContext(), new ApiListener() {
            @Override
            public void handleApiDisabled() {
                Log.d(TAG, "handleApiDisabled");
                ControlManager.handleApiDisconnection();
            }

            @Override
            public void handleApiConnected() {
                Log.d(TAG, "handleApiConnected");
                addApiCallBack();
                CharacterManager.getInstance().loadCharacterInfo();
                BaseApplication.setIsAlreadyOpenAsr(true);
                startRNServerService();
                startSkillService();
                RobotInfo.init();
                TestManager.getInstance();
                ConfigManager.getInstance();
                NetworkManager.getInstance().init();
                ControlManager.handleApiConnection();
                MsgController.getController().init();
                if (FeatureConfig.isAllowChargingChat()
                        || FeatureConfig.isAllowChargingNavi()) {
                    Log.d(TAG, "handleApiConnected: disableBattery");
                    RobotApi.getInstance().disableBattery();
                }

                SystemObserves.INSTANCE.startObserver();
                CallButtonManager.getInstance().init();
            }

            @Override
            public void handleApiDisconnected() {
                Log.d(TAG, "handleApiDisconnected");
            }
        });
    }

    private void startSkillService() {
        BaseApplication.getContext().startService(new Intent(BaseApplication.getContext(),
                SkillService.class));
    }

    private void startRNServerService() {
        BaseApplication.getContext().startService(new Intent(BaseApplication.getContext(),
                RNServerService.class));
    }
}
