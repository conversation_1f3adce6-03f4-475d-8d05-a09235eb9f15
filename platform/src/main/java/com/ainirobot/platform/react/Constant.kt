package com.ainirobot.platform.react

/***
 ** <AUTHOR>
 ** @email <EMAIL>
 ** @date 2019-06-17
 **/

object Constant {
    //切换角色时RELOAD
    const val EVENT_BUS_RELOAD_REACT_NATIVE_ENVIRONMENT = "event_bus_reload_react_native_environment"
    //控制DEBUG环境
    const val EVENT_BUS_DEBUG_CHARACTER_ENVIRONMENT = "event_bus_debug_character_environment"

    //删除debug模式下产生的临时bundle
    const val EVENT_BUS_DELETE_BUNDLE_ENVIRONMENT = "event_bus_delete_bundle_environment"

    //切换到远程debug模式
    const val EVENT_BUS_REMOTE_DEBUG_ENVIRONMENT = "event_bus_remote_debug_environment"
}