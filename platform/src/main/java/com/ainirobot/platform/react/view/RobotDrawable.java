package com.ainirobot.platform.react.view;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Message;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.ImageView;

import com.ainirobot.platform.R;

import java.lang.ref.WeakReference;

import static com.ainirobot.platform.react.view.RobotDrawable.MyHandler.MESSAGE_EXECUTE_RESET;

public class RobotDrawable extends Drawable implements IRobotAnimation {

    private static final String TAG = "RobotDrawable";

    private float radius = 90;
    private Context context;
    private Bitmap mHeadBitmap;
    private Bitmap mBoadBitmap;
    private static final float DEGREES_SIZE = 14.0f;
    private static final int DEGREES_DURATION = 400 * 4;
    private float mHeadY = radius * 2;
    private float mHoardY = radius * 2;
    private float mDegrees = 0;
    private IRobotAnimation.RobotAnimationListener mListener;
    private boolean isFirstTextAnimation = false;
    private View alphaView;
    private boolean flag = false;
    private ImageView mImageView;
    private MyHandler mMyHandler;
    private Paint mPaint;
    private ValueAnimator mAnimator;

    public RobotDrawable(Context context, ImageView view) {
        this.context = context;
        this.mImageView = view;
        this.mMyHandler = new MyHandler(this);
        initBitmap();
        initPaint();
    }

    private void initPaint() {
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setStyle(Paint.Style.FILL);
    }

    private void initBitmap() {
        mHeadBitmap = BitmapFactory.decodeResource(context.getResources(), R.drawable.robot_head_img);
        mBoadBitmap = BitmapFactory.decodeResource(context.getResources(), R.drawable.robot_boad_img);
    }

    public void setRadius(int radius) {
        this.radius = radius;
    }

    private void reset() {
        mDegrees = 0;
        flag = true;
        mHeadY = radius * 2;
        mHoardY = radius * 2;
        releaseAnimation();
    }

    @Override
    public void draw(@NonNull Canvas canvas) {
        if (flag) {
            drawImage(canvas);
        }
    }

    private void drawImage(Canvas canvas) {
        //绘制身体
        float boardX = radius - mBoadBitmap.getWidth() / 2;
        canvas.drawBitmap(mBoadBitmap, boardX, mHoardY, mPaint);
        int save = canvas.save();
        //绘制头部
        float headX = radius - mHeadBitmap.getWidth() / 2;
        drawRotateBitmap(canvas, mHeadBitmap, mDegrees, headX, mHeadY);
        canvas.restoreToCount(save);

    }

    private void drawRotateBitmap(Canvas canvas, Bitmap bitmap,
                                  float rotation, float posX, float posY) {
        Matrix matrix = new Matrix();
        int offsetX = bitmap.getWidth() / 2;
        int offsetY = bitmap.getHeight();
        matrix.postTranslate(-offsetX, -offsetY);
        matrix.postRotate(rotation);
        matrix.postTranslate(posX + offsetX, posY + offsetY);
        canvas.drawBitmap(bitmap, matrix, null);
    }


    private void startHeadAnimation() {
        mDegrees = 0;
        isFirstTextAnimation = false;
        Log.v(TAG, "startHeadAnimation");
        ValueAnimator mValueAnimator = ValueAnimator.ofFloat(1.0f, 0.0f);
        mValueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                int alpha = (int) (animation.getAnimatedFraction() * 255);
                mPaint.setAlpha(alpha);
                mHeadY = Math.max((Float) animation.getAnimatedValue() * radius * 1.25f, radius - mHeadBitmap.getHeight() / 2);
                mHoardY = Math.max((Float) animation.getAnimatedValue() * radius * 1.25f + mHeadBitmap.getHeight() - 10, radius * 2 - mBoadBitmap.getHeight() - 20);
                invalidate();
                float animatedFraction = animation.getAnimatedFraction();
                if (alphaView != null && animatedFraction >= 0.43f && !isFirstTextAnimation) {
                    Log.v(TAG, "onAnimationUpdate: " + animatedFraction);
                    alphaView.setVisibility(View.VISIBLE);
                    textAnimation();
                    isFirstTextAnimation = true;
                }
            }
        });
        mValueAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                rotateHeadAnimation();
            }
        });
        mValueAnimator.setDuration(280);
        mValueAnimator.start();
    }

    private void textAnimation() {
        if (mListener != null) {
            mListener.enterAnimationEnd();
        }
        ObjectAnimator alphaAnimatorX = ObjectAnimator.ofFloat(alphaView, "alpha", 0.0f, 1.0f);
        alphaAnimatorX.setInterpolator(new AccelerateDecelerateInterpolator());
        alphaAnimatorX.setDuration(240);
        alphaAnimatorX.start();
    }

    private void rotateHeadAnimation() {
        Log.v(TAG, "rotateHeadAnimation");
        mAnimator = ValueAnimator.ofFloat(0.0f, DEGREES_SIZE, 0.0f, -DEGREES_SIZE, 0.0f);
        mAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                mDegrees = (Float) animation.getAnimatedValue();
                invalidate();
                if (animation.getAnimatedFraction() >= 1.0f) {
                    Log.v(TAG, "rotateHeadAnimation next");
                    mMyHandler.removeMessages(MESSAGE_EXECUTE_RESET);
                    mMyHandler.sendEmptyMessageDelayed(MESSAGE_EXECUTE_RESET, 500);
                }
            }
        });
        mAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
        mAnimator.setDuration(DEGREES_DURATION);
        mAnimator.start();
    }


    @Override
    public void setRobotAnimationListener(IRobotAnimation.RobotAnimationListener listener) {
        this.mListener = listener;
    }

    @Override
    public void setWithAlpha(View view) {
        this.alphaView = view;
    }

    @Override
    public void startDraw() {
        Log.v(TAG, String.format("startDraw :%b", flag));
        if (flag) {
            return;
        }
        if (mListener != null) {
            mListener.animationStart();
        }
        reset();
        startHeadAnimation();
        invalidate();
    }

    @Override
    public void stopDraw() {
        Log.v(TAG, "stopDraw");
        flag = false;
        endHeadAnimation();
        if (mMyHandler != null) {
            releaseAnimation();
        }
    }

    @Override
    public void releaseAnimation() {
        Log.v(TAG, "releaseAnimation");
        mMyHandler.removeCallbacksAndMessages(null);
        if (mAnimator != null) {
            mAnimator.end();
            mAnimator = null;
        }
    }

    private void endHeadAnimation() {
        AnimatorSet set = new AnimatorSet();
        set.setInterpolator(new AccelerateDecelerateInterpolator());
        ValueAnimator animator = ValueAnimator.ofFloat(0.0f, 1.0f);
        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                if (!flag) {
                    mHeadY = Math.max((Float) animation.getAnimatedValue() * radius * 2, radius - mHeadBitmap.getHeight() / 2);
                    mHoardY = Math.max((Float) animation.getAnimatedValue() * radius * 2 + mHeadBitmap.getHeight() - 20, radius * 2 - mBoadBitmap.getHeight());
                    invalidate();
                    float animatedFraction = animation.getAnimatedFraction();
                    if (mListener != null && animatedFraction == 1.0f) {
                        mListener.animationEnd();
                    }
                }
            }
        });
        set.setDuration(240);
        set.start();
    }

    private void invalidate() {
        mImageView.invalidate();
    }

    @Override
    public void setAlpha(int alpha) {

    }

    @Override
    public void setColorFilter(@Nullable ColorFilter colorFilter) {

    }

    @Override
    public int getOpacity() {
        return PixelFormat.TRANSLUCENT;
    }

    static class MyHandler extends Handler {

        public static final int MESSAGE_EXECUTE_RESET = 0x66;

        WeakReference<RobotDrawable> mReference;

        MyHandler(RobotDrawable drawable) {
            mReference = new WeakReference<RobotDrawable>(drawable);
        }

        @Override
        public void handleMessage(Message msg) {
            final RobotDrawable drawable = mReference.get();
            if (drawable == null) {
                return;
            }
            switch (msg.what) {
                case MESSAGE_EXECUTE_RESET:
                    drawable.rotateHeadAnimation();
                    break;
                default:
                    break;
            }
        }
    }
}
