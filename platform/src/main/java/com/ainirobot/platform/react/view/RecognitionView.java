/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.react.view;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ainirobot.platform.R;
import com.ainirobot.platform.bean.RecognitionAnimationData;
import com.ainirobot.platform.speech.SpeechRegister;
import com.ainirobot.platform.utils.AnimationUtil;

import java.lang.ref.WeakReference;
import java.util.List;

import static com.ainirobot.platform.react.view.RecognitionView.MyHandler.MESSAGE_EXECUTE_CLEAR_TEXT;
import static com.ainirobot.platform.react.view.RecognitionView.MyHandler.MESSAGE_EXECUTE_RESET_STATE;

public class RecognitionView extends FrameLayout implements IRecognizeText
        , GuideQueryManager.OnGuideQueryManagerListener, INotifyDisplayable, INotifyDisplayListener {

    private static final String TAG = RecognitionView.class.getSimpleName();

    private static final String DEFAULT_LISTEN_TEXT = "请继续,我在听";

    private View mContainer;
    private Context mContext;
    private TextView mRecognitionText;
    private MyHandler mHandler = new MyHandler(this);
    private BottomWaveManager mWaveManager;
    private GuideQueryManager mGuideQueryManager;
    private INotifyDisplayListener mNotifyDisplay;
    private RecognizeTextWrapper mRecognizeTextWrapper;
    private Handler mMainHandler;
    private RecognitionViewEventListener mEventListener;

    public RecognitionView(Context context) {
        this(context, null);
    }

    public RecognitionView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        initView();
        initListener();
    }

    public void setEventListener(RecognitionViewEventListener mEventListener) {
        this.mEventListener = mEventListener;
    }

    private void initView() {
        mContainer = LayoutInflater.from(mContext).inflate(R.layout.platform_fragment_recognition, this);
        setLayoutParams(new RelativeLayout.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
        mRecognitionText = (TextView) mContainer.findViewById(R.id.recognition_text);
        GuidedQueryView mRecognitionQuery = (GuidedQueryView) mContainer.findViewById(R.id.recognition_anim_text);
        mRecognitionQuery.setVisibility(View.INVISIBLE);
        mGuideQueryManager = new GuideQueryManager(mRecognitionQuery);
        mGuideQueryManager.setOnGuideQueryManagerListener(this);
        SurfaceAnimation mVoiceNewWaveLayout = (SurfaceAnimation) mContainer.findViewById(R.id.voice_new_wave);
        mWaveManager = new BottomWaveManager(mVoiceNewWaveLayout);
        mRecognizeTextWrapper = new RecognizeTextWrapper(mRecognizeText);
        mMainHandler = new Handler(Looper.getMainLooper());
    }

    private IRecognizeText mRecognizeText = new IRecognizeText() {
        @Override
        public void onRecognizeText(boolean isFinal, String result) {
            mWaveManager.startAnimation(Voice.VoiceType.SPEAK);
            Log.i(TAG, "isFinal =" + isFinal + " result = " + result);
            setRecognizeText(result);
            clearMessages();
        }
    };

    public void setQueryData(List<String> dataList) {
        Log.d(TAG, String.format("setQueryData : %d", dataList != null ? dataList.size() : 0));
        setQueryData(dataList, null);
    }

    public void setQueryData(List<String> dataList, String headerText) {
        Log.d(TAG, String.format("setQueryData : %d", dataList != null ? dataList.size() : 0));
        mGuideQueryManager.setQueryData(dataList, headerText);
    }

    public void showGuideAnimation(boolean isShow) {
        mGuideQueryManager.setVisibilityByUser(isShow);
        showGuideAnimationInternal(isShow);
    }

    private void showGuideAnimationInternal(boolean isShow) {
        Log.i(TAG, String.format("displayGuideAnimation : %b,mBottomWaveShow : %b,GuideQuery:%b", isShow, mWaveManager.isShowBottomWave(), mGuideQueryManager.isShowGuideState()));
        if (mGuideQueryManager.isShowGuideState()) {
            mRecognitionText.setVisibility(isShow ? INVISIBLE : VISIBLE);
            mGuideQueryManager.showGuideQueryView(isShow);
        } else {
            mRecognitionText.setVisibility(VISIBLE);
        }
    }

    @Override
    public void hideGuideQuery(boolean isShow) {
        Log.i(TAG, String.format("hideGuideQuery:%s", Boolean.valueOf(isShow).toString()));
        mRecognitionText.setVisibility(!isShow ? VISIBLE : INVISIBLE);
    }

    @Override
    public void onClick() {
        Log.i(TAG, "RecognitionView onClick");
        if (null != mEventListener) {
            mEventListener.onClick();
        }
    }


    @Override
    public void onRecognizeText(boolean isFinal, String result) {
        Log.i(TAG, String.format("onRecognizeText:isFinal=%s, result:%s", Boolean.valueOf(isFinal).toString(), result));
        mRecognizeTextWrapper.onRecognizeText(isFinal, result);
    }

    public void onClearRecognizeText() {
        if (mRecognitionText.isShown()) {
            Log.i(TAG, "onClearRecognizeText");
            mHandler.sendEmptyMessageDelayed(MESSAGE_EXECUTE_CLEAR_TEXT, 1000);
            mHandler.sendEmptyMessageDelayed(MESSAGE_EXECUTE_RESET_STATE, 3000);
        } else {
            Log.i(TAG, "onClearRecognizeText: isShown=false");
        }
    }

    private void clearMessages() {
        Log.i(TAG, "clearMessages");
        mHandler.removeMessages(MESSAGE_EXECUTE_CLEAR_TEXT);
        mHandler.removeMessages(MESSAGE_EXECUTE_RESET_STATE);
        mHandler.removeMessages(MyHandler.MESSAGE_EXECUTE_SHOW_TIPS);
    }

    public void stopBottom() {
        setRecognizeText("");
        if (mWaveManager.isShowBottomWave()) {
            mWaveManager.stopBottomWaveAnimation();
        }
        mHandler.removeMessages(MESSAGE_EXECUTE_CLEAR_TEXT);
        mHandler.removeMessages(MESSAGE_EXECUTE_RESET_STATE);
        mHandler.removeMessages(MyHandler.MESSAGE_EXECUTE_SHOW_TIPS);
    }

    public void setDefaultRecognizeText() {
        setRecognizeText(DEFAULT_LISTEN_TEXT);
    }

    public void animatorRecognizeText() {
        AnimationUtil.enterAnimator(mRecognitionText);
    }

    public void setRecognizeText(String text) {
        Log.i(TAG, "setRecognizeText");
        if (!TextUtils.isEmpty(text)) {
            Log.i(TAG, "setRecognizeText");
            showGuideAnimationInternal(false);
        }
        mRecognitionText.setText(text);
    }

    public void breakShowTips() {
        if (mHandler != null) {
            mHandler.removeMessages(MyHandler.MESSAGE_EXECUTE_SHOW_TIPS);
        }
    }

    public void resetWakeupState() {
        mWaveManager.resetWakeupState();
    }

    public void showBottomWaveAnimation() {
        mWaveManager.showBottomWave(true);
    }

    public boolean isShowBottomWaveAnimation() {
        return mWaveManager.isShowBottomWave();
    }

    public void startVoiceByType(Voice.VoiceType loading) {
        mWaveManager.startVoiceByType(loading);
    }

    public void hideBottomWaveAnimation() {
        mWaveManager.showBottomWave(false);
    }

    public void setGuideAnimationConfig(boolean isShow) {
        mGuideQueryManager.setGuideConfig(isShow);
    }

    public boolean getGuideAnimationState() {
        return mGuideQueryManager.isShowGuideState();
    }

    @Override
    public void registerNotifyDisplayListener(INotifyDisplayListener listener) {
        mNotifyDisplay = listener;
    }

    @Override
    public void onNotifyShow(Object view, boolean noAnimation, boolean postDelay) {
        Log.i(TAG, "onNotifyShow show recognition");
        mNotifyDisplay.onNotifyShow(view, noAnimation, postDelay);
    }

    @Override
    public void onNotifyShowDefault(boolean noAnimation, boolean postDelay) {
        Log.i(TAG, "onNotifyShowDefault hidden recognition");
        mNotifyDisplay.onNotifyShowDefault(noAnimation, postDelay);
    }

    static class MyHandler extends Handler {

        public static final int MESSAGE_EXECUTE_CLEAR_TEXT = 0x10;
        public static final int MESSAGE_EXECUTE_RESET_STATE = 0x11;
        public static final int MESSAGE_EXECUTE_SHOW_TIPS = 0x12;
        public static final int MESSAGE_EXECUTE_MSG_QUERY_END = 0x13;

        WeakReference<RecognitionView> mReference;

        MyHandler(RecognitionView view) {
            mReference = new WeakReference<RecognitionView>(view);
        }

        @Override
        public void handleMessage(Message msg) {
            Log.i(TAG, "handleMessage");
            final RecognitionView mView = mReference.get();
            if (mView == null) {
                Log.i(TAG, "handleMessage:mView is null");
                return;
            }
            switch (msg.what) {
                case MESSAGE_EXECUTE_CLEAR_TEXT:
                    Log.i(TAG, "MESSAGE_EXECUTE_CLEAR_TEXT");
                    removeMessages(MESSAGE_EXECUTE_SHOW_TIPS);
                    if (!mView.mGuideQueryManager.isShowVisibility() && mView.mGuideQueryManager.isVisibilityByUser()) {
                        sendEmptyMessageDelayed(MESSAGE_EXECUTE_SHOW_TIPS, 1600);
                    }
                    if (mView.mWaveManager.isShowBottomWave()) {
                        mView.mWaveManager.stopBottomWaveAnimation();
                    }
                    mView.setRecognizeText("");
                    break;
                case MESSAGE_EXECUTE_SHOW_TIPS:
                    Log.i(TAG, String.format("delayed show tips isShowVisibility:%b,isVisibilityByUser:%b", mView.mGuideQueryManager.isShowVisibility(), mView.mGuideQueryManager.isVisibilityByUser()));
                    if (!mView.mGuideQueryManager.isShowVisibility() && mView.mGuideQueryManager.isVisibilityByUser()) {
                        mView.showGuideAnimationInternal(true);
                    }
                    break;
                case MESSAGE_EXECUTE_RESET_STATE:
                    Log.i(TAG, "MESSAGE_EXECUTE_MSG_QUERY_END");
                    mView.mWaveManager.resetWakeupState();
                    break;
                case MESSAGE_EXECUTE_MSG_QUERY_END:
                    Log.i(TAG, "MESSAGE_EXECUTE_MSG_QUERY_END");
                    mView.dealClearRecognizeText();
                    break;
                default:
                    break;
            }
        }
    }

    public void setQueryDataForServer(RecognitionAnimationData data) {
        List<String> animationData = null;

        animationData = null;

        StringBuffer stringBuffer = new StringBuffer();
        for (String dataBuf : animationData) {
            stringBuffer.append(dataBuf);
        }
        Log.i(TAG, String.format("type:%s ,content :%s", data.getmQuery_Type(), stringBuffer.toString()));

        Log.d(TAG, "getGuideAnimationState() ==== " + getGuideAnimationState());

        if (getGuideAnimationState() && animationData != null) {

            Log.d(TAG, "mRecognizeView.setQueryData DataSize = " + animationData.size() + " Header_Text = " + data.getmHeader_Text());

            if (TextUtils.isEmpty(data.getmHeader_Text())) {
                setQueryData(animationData);
            } else {
                setQueryData(animationData, data.getmHeader_Text());
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        mNotifyDisplay = null;
        destroyListener();
        super.onDetachedFromWindow();
    }

    @Override
    protected void onVisibilityChanged(@NonNull View changedView, int visibility) {
        super.onVisibilityChanged(changedView, visibility);
        Log.i(TAG, "visible change " + changedView.toString() + "visibility =" + visibility);
        if (TextUtils.equals(changedView.getClass().getSimpleName(), "DecorView")) {
            Log.i(TAG, "ignore visibility from system ui ,visibility = " + visibility);
            return;
        }
        if (visibility == VISIBLE) {
            resetWakeupState();
        } else {
            if (allowSpeechState()) {
                stopBottom();
            }
        }
    }

    private void initListener() {
        SpeechRegister.getInstance().registerRecognitionListener(mSpeechRecognitionStopListener);
        SpeechRegister.getInstance().registerStatusListener(mSpeechStatusListener);
    }

    private void destroyListener() {
        SpeechRegister.getInstance().unRegisterRecognitionListener(mSpeechRecognitionStopListener);
        SpeechRegister.getInstance().unRegisterStatusListener(mSpeechStatusListener);
    }

    private boolean allowSpeechState() {
        return mContainer.getVisibility() == View.VISIBLE;
    }

    private SpeechRegister.SpeechRecognitionListener mSpeechRecognitionStopListener = new SpeechRegister.SpeechRecognitionListener() {
        @Override
        public void onSpeechRecognitionStop() {
            if (allowSpeechState() && isShowBottomWaveAnimation()) {
                mMainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        startVoiceByType(Voice.VoiceType.LOADING);
                    }
                });
            }
        }

        @Override
        public void onSpeechRecognitionStart() {
            if (allowSpeechState()) {
                mMainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        breakShowTips();
                    }
                });
            }
        }
    };

    private void dealClearRecognizeText() {
        mMainHandler.post(new Runnable() {
            @Override
            public void run() {
                onClearRecognizeText();
            }
        });
    }

    private void dealRecognizeText(final String result, final boolean isFinal) {
        if (allowSpeechState()) {
            mMainHandler.post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onRecognizeText = " + result);
                    onRecognizeText(isFinal, result);
                }
            });
        }
    }

    private SpeechRegister.SpeechStatusListener mSpeechStatusListener = new SpeechRegister.SpeechStatusListener() {

        @Override
        public void onSpeechParResult(String result) {
            dealRecognizeText(result, false);
        }

        @Override
        public void onQueryEnded(int type) {//返回最终结果，或是超时
            Log.i(TAG, "onQueryEnded() mDefaultSpeechState = " + allowSpeechState());
            mHandler.sendEmptyMessageDelayed(MyHandler.MESSAGE_EXECUTE_MSG_QUERY_END, 100);
        }

        @Override
        public void onAsrResult(String asrResult) {
            Log.i(TAG, "asrResult = " + asrResult);
            dealRecognizeText(asrResult, true);
        }
    };

    public interface RecognitionViewEventListener {
        void onClick();
    }
}
