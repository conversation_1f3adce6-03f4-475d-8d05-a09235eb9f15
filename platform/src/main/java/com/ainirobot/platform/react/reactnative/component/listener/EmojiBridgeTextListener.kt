package com.ainirobot.platform.react.reactnative.component.listener

import com.ainirobot.emoji.SwitchEmojiManager
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNTextListener
import com.ainirobot.platform.speech.SpeechApiCost
import com.ainirobot.platform.speech.SpeechRegister
import com.facebook.react.bridge.WritableNativeMap

class EmojiBridgeTextListener : IRNTextListener.Stub {

    companion object {
        fun obtain(callbackId:Int): EmojiBridgeTextListener? {
            return EmojiBridgeTextListener(callbackId)
        }
    }

    var id = -1

    private constructor(id: Int) {
        this.id = id
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
    }

    fun triggerEvent(event: String, streamSid: String = "", textSid: String = "") {
        if (id < 0) {
            return
        }
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putString("event", event)
        readableMap.putString("streamSid", streamSid)
        readableMap.putString("textSid", textSid)
        ReactNativeEventEmitter.triggerEvent("onPlayText", readableMap)
    }

    override fun onStart() {
        triggerEvent("onStart")
        playSpeakStart()
        SpeechRegister.getInstance().handleListeners(SpeechApiCost.MSG_SPEECH_START, 0, null)
    }

    override fun onStop() {
        triggerEvent("onStop")
        playSpeakEnd()
        SpeechRegister.getInstance().handleListeners(SpeechApiCost.MSG_SPEECH_STOP, 0, null)
    }

    override fun onError() {
        triggerEvent("onError")
        playSpeakEnd()
    }

    override fun onComplete() {
        triggerEvent("onComplete")
        playSpeakEnd()
        SpeechRegister.getInstance().handleListeners(SpeechApiCost.MSG_TTS_SPEECH_COMPLETE, 1, null)
    }

    override fun onStreamComplete(streamSid: String?, textSid: String?) {
        triggerEvent("onStreamComplete", streamSid ?: "", textSid ?: "")
        playSpeakEnd()
        SpeechRegister.getInstance().handleListeners(SpeechApiCost.MSG_TTS_SPEECH_COMPLETE, 1, null)
    }

    private fun playSpeakStart() {
        SwitchEmojiManager.getInstance().speak()
    }

    private fun playSpeakEnd() {
        SwitchEmojiManager.getInstance().speakEnd()
    }
}