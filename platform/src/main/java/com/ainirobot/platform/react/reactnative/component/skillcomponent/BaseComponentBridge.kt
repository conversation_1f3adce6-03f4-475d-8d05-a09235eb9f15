package com.ainirobot.platform.react.reactnative.component.skillcomponent

import android.text.TextUtils
import android.util.Log
import com.ainirobot.platform.component.Component
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.component.listener.BridgeComponentFinishListener
import com.ainirobot.platform.react.reactnative.component.listener.BridgeComponentStatusListener
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule

enum class ResouceType(type: String) {
    LightStrip("LightStrip"), BigScreen("BigScreen"),
    SmallScreen("SmallScreen"), Speech("Speech"),
    Mic("Mic"), Vision("Vision"), Navigation("Navigation"),
    HeadTurn("HeadTurn"), NetApi("NetApi")
}

/**
 * @date: 2019-04-11
 * @author: lumeng
 * @desc: 能力组件 base class,封装了所有组件的公共方法和抽象方法
 *        所有能力组件都需要继承 BaseComponentBridge，实现抽象方法，并在5个 action 方法上加上 {@link ReactMethod} 注解，
 *        分别调用对应的方法， {@link #_start} {@link #_stop} {@link #_setStatusListener}  {@link #_setFinishListener} {@link #_updateParams}
 *        最后需要在 {@link ComponentBridgeModuleReactPackage} 中注册
 */
abstract class BaseComponentBridge<T : Component>(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    protected val TAG by lazy {
        javaClass.name
    }

    /**
     * 是否独占硬件资源
     */
    abstract fun isHDMonopoly(): Boolean

    /**
     * 资源类型
     */
    abstract fun getResouceType(): Array<ResouceType>

    /**
     * 能力组件名称，会映射到 RN 环境中
     */
    abstract override fun getName(): String

    /**
     * 组件开始
     * @param uid 和 RN 层组件做映射
     * @param params 所需参数
     */
    abstract fun start(uid: String, param: String?)

    /**
     * 组件停止
     * @param uid 和 RN 层组件做映射
     * @param timeout 停止 timeout
     */
    abstract fun stop(uid: String, timeout: Int?)

    /**
     * 组件运行过程中更新参数，根据新参数执行
     * @param uid 和 RN 层组件做映射
     * @param intent 更新 intent
     * @param param  更新参数
     */
    abstract fun updateParams(uid: String, intent: String, param: String)

    /**
     * 设置 BridgeComponentStatusListener
     * @param statusListenerId BridgeComponentStatusListener 回调方法的id，需 > 0
     */
    abstract fun setStatusListener(uid: String, statusListenerId: Int)

    /**
     * 设置 BridgeComponentFinishListener
     * @param uid 和 RN 层组件做映射
     * @param finishListenerId BridgeComponentFinishListener 回调方法的id，需 > 0
     */
    abstract fun setFinishListener(uid: String, finishListenerId: Int)

    fun _start(uid: String, param: String?) {
        Log.i(TAG, "_start uid:$uid param:$param")
        try {
            var tempParam: String? = param
            if (TextUtils.isEmpty(tempParam)) {
                tempParam = "{}"
            }
            RNClientManager.instance?.componentManager?.start(name,uid,tempParam)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun _stop(uid: String, timeout: Int?) {
        Log.i(TAG, "_stop uid:$uid timeout:$timeout")
        try {
            val time: Long = timeout?.let {
                it.toLong()
            } ?: 500
            RNClientManager.instance?.componentManager?.stop(name , uid, time)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun _setStatusListener(uid: String, statusListenerId: Int) {
        Log.i(TAG, "_setStatusListener uid:$uid statusListenerId:$statusListenerId")
        try {
            if (statusListenerId > 0) {
                RNClientManager.instance?.componentManager?.setStatusListener(name,uid, BridgeComponentStatusListener.obtain(statusListenerId))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun _setFinishListener(uid: String, finishListenerId: Int) {
        Log.i(TAG, "_setComponentListener uid:$uid finishListenerId:$finishListenerId")
        try {
            if (finishListenerId > 0) {
                RNClientManager.instance?.componentManager?.setFinishListener(name,uid, BridgeComponentFinishListener.obtain(finishListenerId))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun _updateParams(uid: String, intent: String, param: String) {
        Log.i(TAG, "_updateParams uid:$uid intent:$intent param:$param")
        try {
            RNClientManager.instance?.componentManager?.updateParams(name, uid, intent, param)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onCatalystInstanceDestroy() {
        super.onCatalystInstanceDestroy()
        Log.i(TAG, "onCatalystInstanceDestroy name:$name")
        try {
            RNClientManager.instance?.componentManager?.onCatalystInstanceDestroy(name)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
}
