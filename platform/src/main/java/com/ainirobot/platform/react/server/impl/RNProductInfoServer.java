/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.react.server.impl;

import android.os.RemoteException;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.platform.rn.IProductInfoRegistry;

public class RNProductInfoServer extends IProductInfoRegistry.Stub {

    private static final String TAG = RNProductInfoServer.class.getSimpleName();


    @Override
    public boolean isMiniProduct() throws RemoteException {
        return ProductInfo.isMiniProduct();
    }

    @Override
    public boolean isDeliveryProduct() throws RemoteException {
        return ProductInfo.isDeliveryProduct();
    }

    @Override
    public boolean isOverSea() throws RemoteException {
        return ProductInfo.isOverSea();
    }

    @Override
    public boolean isDeliveryOverSea() throws RemoteException {
        return ProductInfo.isDeliveryOverSea();
    }

    @Override
    public boolean isBaseDeliveryOverSea() throws RemoteException {
        return ProductInfo.isBaseDeliveryOverSea();
    }

    @Override
    public boolean isLaraForSale() throws RemoteException {
        return ProductInfo.isLaraForSale();
    }

    @Override
    public boolean isMeissa() throws RemoteException {
        return ProductInfo.isMeissa();
    }

    @Override
    public boolean isMeissa1P5() throws RemoteException {
        return ProductInfo.isMeissa1P5();
    }

    @Override
    public boolean isMeissaPlus() throws RemoteException {
        return ProductInfo.isMeissaPlus();
    }

    @Override
    public boolean isMeissa2() throws RemoteException {
        return ProductInfo.isMeissa2();
    }

    @Override
    public boolean isSaiphXD() throws RemoteException {
        return ProductInfo.isSaiphXD();
    }

    @Override
    public boolean isSaiphBigScreen() throws RemoteException {
        return ProductInfo.isSaiphBigScreen();
    }

    @Override
    public boolean isSaiphXdOrBigScreen() throws RemoteException {
        return ProductInfo.isSaiphXdOrBigScreen();
    }

    @Override
    public boolean isMiniOverSea() throws RemoteException {
        return ProductInfo.isMiniOverSea();
    }

    @Override
    public boolean isSaiphChargeIr() throws RemoteException {
        return ProductInfo.isSaiphChargeIr();
    }

    @Override
    public boolean isSaiphRgbdFm1() throws RemoteException {
        return ProductInfo.isSaiphRgbdFm1();
    }

    @Override
    public boolean isSaiph() throws RemoteException {
        return ProductInfo.isSaiph();
    }

    @Override
    public boolean isSaiphMall() throws RemoteException {
        return ProductInfo.isSaiphMall();
    }

    @Override
    public boolean isSaiphPro() throws RemoteException {
        return ProductInfo.isSaiphPro();
    }

    @Override
    public boolean isAlnilamPro() throws RemoteException {
        return ProductInfo.isAlnilamPro();
    }

    @Override
    public boolean isChargeIrProduct() throws RemoteException {
        return ProductInfo.isChargeIrProduct();
    }

    @Override
    public boolean isElevatorCtrlProduct() throws RemoteException {
        return ProductInfo.isElevatorCtrlProduct();
    }

    @Override
    public boolean isMiniProductSupportMultiRobot() throws RemoteException {
        return ProductInfo.isMiniProductSupportMultiRobot();
    }

    @Override
    public boolean isCarryProduct() throws RemoteException {
        return ProductInfo.isCarryProduct();
    }

    @Override
    public boolean hasElectricDoor() throws RemoteException {
        return ProductInfo.hasElectricDoor();
    }
}
