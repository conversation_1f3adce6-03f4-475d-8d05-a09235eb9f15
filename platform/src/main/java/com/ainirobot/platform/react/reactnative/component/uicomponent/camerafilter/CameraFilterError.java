package com.ainirobot.platform.react.reactnative.component.uicomponent.camerafilter;

public class CameraFilterError {

    private static final int CAMERA_FILTER_ERROR_COMMON = -100;
    public static final int ERROR_COMMON_NOT_SUPPORT_WITHOUT_SHARE
            = CAMERA_FILTER_ERROR_COMMON - 1;

    private static final int CAMERA_FILTER_ERROR_SHARE = -200;
    public static final int ERROR_SHARE_GET_RESOLUTION_FAILED = CAMERA_FILTER_ERROR_SHARE - 1;
    public static final int ERROR_SHARE_CONNECT_SERVER_TIMEOUT = CAMERA_FILTER_ERROR_SHARE - 2;
    public static final int ERROR_SHARE_SERVER_EXIT_ABNORMALLY = CAMERA_FILTER_ERROR_SHARE - 3;
    public static final int ERROR_SHARE_SERVER_EXIT = CAMERA_FILTER_ERROR_SHARE - 4;
    public static final int ERROR_SHARE_SURFACE_SHARE_USED = CAMERA_FILTER_ERROR_SHARE - 5;
    public static final int ERROR_SHARE_SET_STREAM_SURFACE_FAILED = CAMERA_FILTER_ERROR_SHARE - 6;
    public static final int ERROR_SHARE_SURFACE_SHARE_PREEMPTED = CAMERA_FILTER_ERROR_SHARE - 7;

    private static final int CAMERA_FILTER_ERROR_CAMERA = -300;
}
