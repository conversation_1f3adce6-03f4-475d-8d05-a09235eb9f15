package com.ainirobot.platform.react.server.impl;

import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.platform.callbutton.CallButtonManager;
import com.ainirobot.platform.callbutton.MessageListener;
import com.ainirobot.platform.rn.IRNCallButtonManager;
import com.ainirobot.platform.rn.listener.IRNCallButtonListener;
import com.ainirobot.platform.utils.GsonUtil;

import java.util.Map;


public class RNCallButtonManagerServer extends IRNCallButtonManager.Stub {
    private static final String TAG = "RNCallButtonServer";

    @Override
    public void setEventListener(final IRNCallButtonListener listener) throws RemoteException {
        Log.d(TAG, "setEventListener : " + listener);

        CallButtonManager.getInstance().registerListener(new MessageListener() {
            @Override
            public void onMessageReceived(String eventType, String message) throws RemoteException {
                Log.d(TAG, eventType + " message=" + message);
                switch (eventType) {
                    case "callPrepare":
                        listener.callPrepare(message);
                        break;
                    case "call":
                        listener.call(message);
                        break;
                    case "cancel":
                        listener.cancel(message);
                        break;
                    case "buttonMappingChange":
                        listener.buttonMappingChange(message);
                        break;
                    case "buttonPress":
                        listener.buttonPress(message);
                        break;

                }
            }
        });
    }

    @Override
    public void replyCallPrepare(String result, String msg, String data) {
        CallButtonManager.getInstance().replyCallPrepare(result, msg, data);
    }

    @Override
    public void replyCall(String result, String msg) {
        CallButtonManager.getInstance().replyCall(result, msg);
    }

    @Override
    public void replyCancel(String result, String msg) {
        CallButtonManager.getInstance().replyCancel(result, msg);
    }

    @Override
    public void replyButtonMappingChange(String result, String msg) {
        CallButtonManager.getInstance().replyButtonMappingChange(result, msg);
    }

    @Override
    public void setRobotState(String state) {
        CallButtonManager.getInstance().setRobotState(state);
    }

    @Override
    public void connect(String serverIp) {
        CallButtonManager.getInstance().connect(serverIp);
    }

    @Override
    public void disconnect() {
        CallButtonManager.getInstance().disconnect();
    }

    @Override
    public void syncButtonMapping(String buttonMapJson) {
        Log.d(TAG, "syncButtonMapping " + buttonMapJson);
        Map buttonMap = GsonUtil.fromJson(buttonMapJson, Map.class);
        CallButtonManager.getInstance().syncButtonMapping(buttonMap);
    }

    @Override
    public String getCurrentDataMapping() throws RemoteException {
        return CallButtonManager.getInstance().getCurrentDataMapping();
    }

    @Override
    public String getMapName() throws RemoteException {
        return CallButtonManager.getInstance().getMapName();
    }

    @Override
    public void unbindButton(String buttonId, String locationName) throws RemoteException {
        Log.d(TAG, "unbindButton buttonId=" + buttonId + " locationName=" + locationName);
        CallButtonManager.getInstance().unbindButton(buttonId, locationName);
    }
}
