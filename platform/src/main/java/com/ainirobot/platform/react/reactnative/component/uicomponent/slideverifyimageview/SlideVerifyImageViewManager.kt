package com.ainirobot.platform.react.reactnative.component.uicomponent.slideverifyimageview

import com.facebook.react.common.MapBuilder
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.annotations.ReactProp

@ReactModule(name = SlideVerifyImageViewManager.REACT_CLASS)
class SlideVerifyImageViewManager: SimpleViewManager<SlideVerifyImageView>() {

    companion object {
        const val REACT_CLASS = "SlideVerifyImageView"
        const val TAG = REACT_CLASS

        enum class Events constructor(private val mName: String) {
            EVENT_ACCESS("_onVerifySuccess"),
            EVENT_FAILED("_onVerifyFailure"),
            EVENT_MAX_FAILED("_onVerifyMaxFailed"),
            EVENT_START_SLIDE("_onStartSlide");

            override fun toString(): String {
                return mName
            }
        }
    }

    override fun getName(): String {
        return REACT_CLASS
    }

    override fun createViewInstance(reactContext: ThemedReactContext): SlideVerifyImageView {
        return SlideVerifyImageView(reactContext)
    }

    @ReactProp(name = "srcImage")
    fun setSrcImage(view: SlideVerifyImageView, srcImage: String) {
        view.setSrcImg(srcImage)
    }

    override fun getExportedCustomDirectEventTypeConstants(): Map<String, Any>? {
        val builder = MapBuilder.builder<String, Any>()
        for (event in SlideVerifyImageViewManager.Companion.Events.values()) {
            builder.put(event.toString(), MapBuilder.of("registrationName", event.toString()))
        }
        return builder.build()
    }
}