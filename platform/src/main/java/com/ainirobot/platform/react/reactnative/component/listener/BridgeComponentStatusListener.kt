package com.ainirobot.platform.react.reactnative.component.listener

import android.annotation.SuppressLint
import android.util.Log
import com.ainirobot.platform.component.Component
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNStatusListener
import com.facebook.react.bridge.WritableNativeMap

/**
 * @date: 2019-03-28
 * @author: lumeng
 * @desc: RN bridge for  ComponentStatusListener
 */
class BridgeComponentStatusListener : Component.ComponentStatusListener, IRNStatusListener.Stub {

    companion object {
        const val EVENT_NAME = "onStatusUpdate"
        const val TAG = "BridgeComponentStatusListener"

        fun obtain(callbackId:Int): BridgeComponentStatusListener? {
            return if(callbackId < 0){
                null
            } else {
                BridgeComponentStatusListener(callbackId)
            }
        }
    }

    var id:Int = 0
    private constructor(id:Int){
        this.id = id
    }

    @SuppressLint("LongLogTag")
    override fun onStatusUpdate(status: Int, data: String?, extraData: String?) {
      //  Log.i(TAG, "onStatusUpdate status:$status,data:$data")
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putInt("status", status)
        data.apply {  readableMap.putString("data", data) }
        extraData?.apply {
            readableMap.putString("extraData", extraData)
        }
        ReactNativeEventEmitter.triggerEvent(EVENT_NAME, readableMap)
    }
}