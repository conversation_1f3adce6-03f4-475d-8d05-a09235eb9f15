package com.ainirobot.platform.react.server.impl;

import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.platform.appmanager.AppPackageManager;
import com.ainirobot.platform.appmanager.AppPackageManager.UpdateListener;
import com.ainirobot.platform.appmanager.ConfigManager;
import com.ainirobot.platform.appmanager.config.bean.ConfigItem;
import com.ainirobot.platform.appmanager.config.impl.ConfigType;
import com.ainirobot.platform.rn.IRNUpdateManager;
import com.ainirobot.platform.rn.listener.IRNUpdateListener;

public class RNUpdateManagerServer extends IRNUpdateManager.Stub {
    private static final String TAG = "RNUpdateManagerServer";

    private static IRNUpdateListener sUpdateListener;

    @Override
    public boolean isNeedUpdatePresetApp() throws RemoteException {
        Log.d(TAG, "isNeedUpdatePresetApp");
        return AppPackageManager.isNeedUpdatePresetApp();
    }

    @Override
    public boolean isInitInstall() throws RemoteException {
        Log.d(TAG, "isInitInstall");
        return AppPackageManager.isInitInstall();
    }

    @Override
    public boolean isUpdating() throws RemoteException {
        Log.d(TAG, "isInstalling");
        if (AppPackageManager.isInitInstall()) {
            return true;
        }
        return false;
//        return AppPackageManager.isUpdating();
    }

    @Override
    public boolean checkUpdate() throws RemoteException {
        Log.d(TAG, "Check update");
        return false;
//        return AppPackageManager.checkUpdate();
    }

    @Override
    public void setUpdateListener(final IRNUpdateListener listener) throws RemoteException {
        sUpdateListener = listener;
        Log.d(TAG, "Set update listener : " + sUpdateListener);
        sUpdateListener.asBinder().linkToDeath(new DeathRecipient() {
            @Override
            public void binderDied() {
                Log.d(TAG, "Set update listener dead : " + sUpdateListener);
                Log.d(TAG, "Set update enable : " + true);
                AppPackageManager.setUpdateEnable(true);
            }
        }, 0);
        AppPackageManager.setUpdateListener(new UpdateListener() {

            @Override
            public void onStart() {
                try {
                    sUpdateListener.onStart();
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onConfigUpdate(String appId, String content) {
                try {
                    sUpdateListener.onConfigUpdate(appId, content);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onProgressChange(int count, int index, String appName, String oper) {
                try {
                    sUpdateListener.onProgressUpdate(count, index, appName, oper);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFinish(boolean result, String message) {
                Log.d(TAG, "Update result : " + result + "  " + message);
                try {
                    sUpdateListener.onFinished(result, message);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        });
        Log.d(TAG, "Set update listener ##");
    }

    @Override
    public void setPresetAppListener(final IRNUpdateListener listener) throws RemoteException {
        Log.d(TAG, "Set update listener preset : " + listener);
        AppPackageManager.setPresetAppListener(new UpdateListener() {

            @Override
            public void onStart() {
                try {
                    listener.onStart();
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onConfigUpdate(String appId, String content) {
                try {
                    listener.onConfigUpdate(appId, content);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onProgressChange(int count, int index, String appName, String oper) {
                try {
                    listener.onProgressUpdate(count, index, appName, oper);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFinish(boolean result, String message) {
                try {
                    listener.onFinished(result, message);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        });

        Log.d(TAG, "Set update listener preset ##");
    }

    @Override
    public void retryUpdate() throws RemoteException {
        Log.d(TAG, "Retry update");
        AppPackageManager.retryUpdate();
    }

    @Override
    public void startUpdate() throws RemoteException {
        Log.d(TAG, "Start update : " + AppPackageManager.isUpdating());
        AppPackageManager.startUpdate();
    }

    @Override
    public void cancelUpdate() throws RemoteException {
        Log.d(TAG, "Cancel update");
        AppPackageManager.cancelUpdate();
    }

    @Override
    public void setUpdateEnable(boolean enable) throws RemoteException {
        Log.d(TAG, "Set update enable : " + enable);
        AppPackageManager.setUpdateEnable(enable);
    }

    @Override
    public void setAppConfig(String appId, String key, String value) {
        ConfigItem item = new ConfigItem(appId, ConfigType.UPDATE);
        item.setConfig(key, value);
        ConfigManager.getInstance().setConfig(item);
    }
}
