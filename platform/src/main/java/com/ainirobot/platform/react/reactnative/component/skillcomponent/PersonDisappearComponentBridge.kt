package com.ainirobot.platform.react.reactnative.component.skillcomponent

import com.ainirobot.platform.component.PersonDisappearComponent
import com.ainirobot.platform.react.server.utils.ComponentUtils
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactMethod

class PersonDisappearComponentBridge(reactContext: ReactApplicationContext) : BaseComponentBridge<PersonDisappearComponent>(reactContext) {

    override fun isHDMonopoly(): Boolean {
        return false
    }

    override fun getResouceType(): Array<ResouceType> {
        return arrayOf(ResouceType.Vision)
    }

    override fun getName(): String {
        return ComponentUtils.COMPONENT_PERSON_DISAPPEAR
    }

    @ReactMethod
    override fun start(uid: String, param: String?) {
        super._start(uid, param)
    }

    @ReactMethod
    override fun stop(uid: String, timeout: Int?) {
        super._stop(uid, timeout)
    }

    @ReactMethod
    override fun updateParams(uid: String, intent: String, param: String) {
        super._updateParams(uid, intent, param)
    }

    @ReactMethod
    override fun setStatusListener(uid: String, statusListenerId: Int) {
        super._setStatusListener(uid, statusListenerId)
    }

    @ReactMethod
    override fun setFinishListener(uid: String, finishListenerId: Int) {
        super._setFinishListener(uid, finishListenerId)
    }

}