package com.ainirobot.platform.react.reactnative.component.uicomponent.recognitionview
import android.util.Log
import com.facebook.react.bridge.Arguments
import com.facebook.react.uimanager.events.Event
import com.facebook.react.uimanager.events.RCTEventEmitter


class RNRecognitionViewEvent(viewId:Int) : Event<RNRecognitionViewEvent>(viewId) {

    companion object {
        const val EVENT_NAME = "_onClick"
    }

    override fun getEventName(): String {
        return EVENT_NAME
    }

    override fun dispatch(rctEventEmitter: RCTEventEmitter) {

        Log.i("RNRecognitionViewEvent", "dispatch")
        val map = Arguments.createMap()
        //map.putString("state",state.toString())
        rctEventEmitter.receiveEvent(viewTag, eventName, map)
    }
}
