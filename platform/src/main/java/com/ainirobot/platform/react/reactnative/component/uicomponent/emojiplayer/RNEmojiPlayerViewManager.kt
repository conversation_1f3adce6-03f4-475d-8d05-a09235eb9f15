package com.ainirobot.platform.react.reactnative.component.uicomponent.emojiplayer

import android.content.Context
import android.text.TextUtils
import android.util.Log
import android.view.View
import com.ainirobot.emoji.Emoji
import com.ainirobot.emoji.common.CommonBigEmojiLayout
import com.ainirobot.emoji.module.BigEmojiLayout
import com.ainirobot.platform.react.EveActivity
import com.ainirobot.platform.speech.SpeechRegister
import com.facebook.react.bridge.ReadableArray
import com.facebook.react.bridge.ReadableMap
import com.facebook.react.common.MapBuilder
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.annotations.ReactProp

/**
 * @date: 2019-04-08
 * @author: haohongwei
 * @desc: 封装大眼睛
 */

// js 层 EmojiPlayContent 定义

//export class EmojiPlayContent {
//    public loopTime?: number;
//    public emojiTypes?: string[];
//    public delayTimes?: number[];
//    public displayTimes?: number[];
//    public speeds?: number[];
//
//    public constructor(
//            loopTime?: number,
//            emojiTypes?: string[],
//    delayTimes?: number[],
//    displayTimes?: number[],
//    speeds?: number[]
//    ) {
//        this.speeds = speeds;
//        this.displayTimes = displayTimes;
//        this.delayTimes = delayTimes;
//        this.emojiTypes = emojiTypes;
//        this.loopTime = loopTime;
//        this.loopTime = _.isNumber(loopTime) ? loopTime : 1;
//        if (this.loopTime < 1) {
//            this.loopTime = 1;
//        }
//    }
//}

@ReactModule(name = RNEmojiPlayerViewManager.REACT_CLASS)
class RNEmojiPlayerViewManager : SimpleViewManager<CommonBigEmojiLayout>() {

    private var emojiPlayContents: ArrayList<String>? = null
    private var idleEmojiPlayContents: ArrayList<String>? = null
    private var mPlayerView: CommonBigEmojiLayout? = null

    companion object {
        const val COMMAND_PLAYRESTYPE = 1
        const val COMMAND_STOP = 2
        const val COMMAND_UNMOUNT = 3
        const val REACT_CLASS = "RNEmojiPlayerView2"
    }

    override fun getName(): String {
        return REACT_CLASS
    }

    override fun createViewInstance(reactContext: ThemedReactContext): CommonBigEmojiLayout {
        Log.i(REACT_CLASS, "createViewInstance")
        var activity = reactContext.currentActivity
        if (activity == null && EveActivity.getActivity().get() != null) {// 防止进入rn调用init方法时activity还没有onHostResume
            activity = EveActivity.getActivity().get()
        }
        if (mPlayerView != null) {
            mPlayerView?.destroy()
        }
        val bigEmojiLayout = CommonBigEmojiLayout(activity as Context)
        mPlayerView = bigEmojiLayout

        bigEmojiLayout.setDefaultEmoji(Emoji.EMOJI_TYPE.IDLE_WELCOME.taskName)
        SpeechRegister.getInstance().registerTTSStatusListener(mTtsStatusListener)
        return bigEmojiLayout
    }


    override fun onDropViewInstance(view: CommonBigEmojiLayout) {
        super.onDropViewInstance(view)
        Log.i(REACT_CLASS, "onDropViewInstance")
        try {
            SpeechRegister.getInstance().unRegisterTTSStatusListener(mTtsStatusListener)
            mPlayerView?.speakEnd()
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    @ReactProp(name = "emojiType")
    fun setEmojiType(view: View, emojiTypeString: String?) {
        Log.i(REACT_CLASS, "setEmojiType emojiType:$emojiTypeString")
        if (!TextUtils.isEmpty(emojiTypeString)) {
            playEmojiTypeImmediately(emojiTypeString!!)
        } else {
            mPlayerView?.playEmojiType(Emoji.EMOJI_TYPE.IDLE_WELCOME.taskName, null);
        }
    }

    @ReactProp(name = "bgSource")
    fun setBgSource(view: CommonBigEmojiLayout, url: String) {
        Log.i(REACT_CLASS, "setBgSource  : " + url)
        view.updateBackgroundUrl(url)
    }

    @ReactProp(name = "show")
    fun setShow(view: CommonBigEmojiLayout, show: Boolean) {
        Log.i(REACT_CLASS, "setShow show:$show")
        try {
            if (show) {
                Log.i(REACT_CLASS, "onAfterUpdateTransaction show")
                view.visibility = View.VISIBLE
                SpeechRegister.getInstance().registerTTSStatusListener(mTtsStatusListener)
                mPlayerView?.show()
            } else {
                Log.i(REACT_CLASS, "onAfterUpdateTransaction hide")
                SpeechRegister.getInstance().unRegisterTTSStatusListener(mTtsStatusListener)
                mPlayerView?.hide()
                view.visibility = View.GONE
            }
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    @ReactProp(name = "idleEmojiPlayContents")
    fun setIdleEmojiPlayContents(view: View, idleEmojiPlayContentsArray: ReadableArray?) {
        Log.i(REACT_CLASS, "setIdleEmojiPlayContent idleEmojiPlayContentsArray $idleEmojiPlayContentsArray")
        if (idleEmojiPlayContentsArray == null || idleEmojiPlayContentsArray.size() <= 0) {
            idleEmojiPlayContents = null
        } else {
            idleEmojiPlayContents = parsePlayContents(idleEmojiPlayContentsArray)
        }
        if (idleEmojiPlayContents != null && idleEmojiPlayContents!!.isNotEmpty()) {
            mPlayerView!!.setDefaultEmojis(idleEmojiPlayContents!!)
        } else {
            mPlayerView!!.setDefaultEmoji(Emoji.EMOJI_TYPE.IDLE_WELCOME.taskName)
        }
    }

    @ReactProp(name = "emojiPlayContents")
    fun setEmojiPlayContents(view: View, emojiPlayContentsArray: ReadableArray?) {
        Log.i(REACT_CLASS, "setEmojiPlayContent emojiPlayContentsArray:$emojiPlayContentsArray")
        try {
            if (emojiPlayContentsArray == null || emojiPlayContentsArray.size() <= 0) {
                emojiPlayContents = null
            } else {
                emojiPlayContents = parsePlayContents(emojiPlayContentsArray)
            }
        } catch (e: Throwable) {
            e.printStackTrace()
            emojiPlayContents = null
        }
        if (emojiPlayContents.isNullOrEmpty()) {
            mPlayerView?.playEmojiType(Emoji.EMOJI_TYPE.IDLE_WELCOME.taskName, null)
        } else {
            mPlayerView?.playEmojiType(emojiPlayContents!!)
        }
    }

    @ReactProp(name = "talk")
    fun setEmjiPlayTalk(view: View, talk: Boolean) {
        Log.i(REACT_CLASS, "setEmjiPlayTalk talk:$talk")
        startEmojiTalk(talk)
    }

    @ReactProp(name = "faceOffset")
    fun setFaceOffset(view: View, faceOffset: ReadableMap?) {
        Log.i(REACT_CLASS, "setFaceOffset faceOffset:$faceOffset")
        faceOffset?.let {
            mPlayerView?.setFaceOffset(it.getDouble("x").toFloat(), it.getDouble("y").toFloat(), it.getBoolean("isAnimator"))
        }
    }

    private fun startEmojiTalk(talk: Boolean) {
        try {
            if (talk) {
                Log.i(REACT_CLASS, "startTalk")
                mPlayerView?.speak()
            } else {
                Log.i(REACT_CLASS, "stopTalk")
                mPlayerView?.speakEnd()
            }
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    override fun onAfterUpdateTransaction(view: CommonBigEmojiLayout) {
        super.onAfterUpdateTransaction(view)
        Log.i(REACT_CLASS, "onAfterUpdateTransaction idleEmojiPlayContents:$idleEmojiPlayContents ,idleEmojiPlayContents:$idleEmojiPlayContents,emojiPlayContents:$emojiPlayContents")
    }

    private fun parsePlayContents(playContentsArray: ReadableArray): ArrayList<String>? {
        for (i in 0 until playContentsArray.size()) {
            val playContentItemMap = playContentsArray.getMap(i)
            playContentItemMap?.apply {
                if (playContentItemMap.hasKey("emojiTypes")) {
                    val emojisArray = playContentItemMap.getArray("emojiTypes")
                    emojisArray?.apply {
                        var emojiList: ArrayList<String?> = arrayListOf()
                        (this.toArrayList() as ArrayList<String>).forEachIndexed { index, s ->
                            try {
                                emojiList.add(Emoji.EMOJI_TYPE.typeOfName(s).taskName)
                            } catch (e: Throwable) {
                                e.printStackTrace()
                            }
                        }
                        return emojiList as ArrayList<String>
                    }
                }
            }
        }
        return null
    }

    override fun getCommandsMap(): Map<String, Int>? {
        return MapBuilder.of(
                "playResType", COMMAND_PLAYRESTYPE,
                "stop", COMMAND_STOP,
                "unmount", COMMAND_UNMOUNT)
    }

    override fun receiveCommand(root: CommonBigEmojiLayout, commandId: Int, args: ReadableArray?) {
        Log.e(REACT_CLASS, "command $commandId ,args:$args")
        when (commandId) {
            COMMAND_PLAYRESTYPE -> {
                var typeString = args?.getString(0)
                if (typeString != null) {
                    playEmojiType(typeString)
                }
            }
            COMMAND_STOP -> {
                var notify = args?.getBoolean(0)
                var playDefault = args?.getBoolean(1)
                if ((notify != null) && (playDefault != null)) {
                    //stop(notify, playDefault)
                }
            }
            COMMAND_UNMOUNT -> {
                mPlayerView?.destroy()
                mPlayerView = null
            }
            else -> Log.e(REACT_CLASS, "unsupported command $commandId")
        }
    }

    fun playEmojiTypeImmediately(typeName: String) {
        mPlayerView?.playEmojiType(Emoji.EMOJI_TYPE.typeOfName(typeName).taskName, null)
    }

    fun playEmojiType(typeName: String) {
        playEmojiTypeImmediately(typeName)
    }

    private val mTtsStatusListener = object : SpeechRegister.TTSSpeechStatueListener {
        override fun onStartTTS() {
            Log.i(REACT_CLASS, "onStartTTS")
            mPlayerView?.speak()
        }

        override fun onStopTTS(isComplete: Boolean) {
            Log.i(REACT_CLASS, "onSpeechCompleteTTS")
            mPlayerView?.speakEnd()
        }

        override fun onSpeechCompleteTTS(isComplete: Boolean) {
            Log.i(REACT_CLASS, "onSpeechCompleteTTS")
            mPlayerView?.speakEnd()
        }
    }

}
