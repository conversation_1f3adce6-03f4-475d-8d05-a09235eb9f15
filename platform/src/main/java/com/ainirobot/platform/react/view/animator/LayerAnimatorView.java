/*
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.ainirobot.platform.react.view.animator;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Shader;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.widget.ImageView;

import com.ainirobot.platform.PlatformDef;
import com.ainirobot.platform.react.view.animator.bean.BackgroundBean;
import com.ainirobot.platform.react.view.animator.bean.DrawableBean;
import com.ainirobot.platform.react.view.animator.bean.LayerBean;
import com.ainirobot.platform.utils.imageloader.ImageLoader;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.stream.JsonReader;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 通过自定义的 AnimatorDrawable 画出多层画布的叠加效果
 *
 * @FileName: com.ainirobot.platform.ui.animator.LayerAnimatorView.java
 * @author: Orion
 * @date: 2018-10-29 11:34
 */
@SuppressLint("AppCompatCustomView")
public class LayerAnimatorView extends ImageView implements AnimatorDrawable.AnimatorDrawableListener {
    private static final String TAG = LayerAnimatorView.class.getSimpleName();

    private Context mContext;
    private Handler mParseHandler;
    private OnJsonAnimListener mListener;
    private BitmapDrawable bgDrawable;
    private AnimatorDrawable mAnimatorDrawable;
    private Drawable mDefautBgDrawable;

    private static final int MSG_WHAT_CREATE = 111;
    private static final int MSG_WHAT_NOTIFY_END = 112;
    private static final int MSG_WHAT_NOTIFY_ERROR = 113;

    private Handler mHandler;

    public LayerAnimatorView(@NonNull Context context) {
        this(context, null);
    }

    public LayerAnimatorView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LayerAnimatorView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        mContext = context;
        mDefautBgDrawable = getBackground();
        init();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        cancelAnimation();
    }

    @Override
    public void onPrepared() {
        mHandler.removeMessages(MSG_WHAT_CREATE);
        mHandler.sendEmptyMessage(MSG_WHAT_CREATE);
    }

    @Override
    public void onEnd() {
        cancelAnimation();
        mHandler.removeMessages(MSG_WHAT_NOTIFY_END);
        mHandler.sendEmptyMessage(MSG_WHAT_NOTIFY_END);
    }

    @Override
    public void onError(String msg) {
        mHandler.removeMessages(MSG_WHAT_NOTIFY_ERROR);
        mHandler.sendMessage(mHandler.obtainMessage(MSG_WHAT_NOTIFY_ERROR, msg));
    }

    public void setDataSource(@NonNull final String jsonFile) throws FileNotFoundException {
        if (TextUtils.isEmpty(jsonFile) || !isValidFile(jsonFile)) {
            throw new FileNotFoundException("json file path is null in " + this.getClass().getName());
        }

        clean();

        mParseHandler.post(new Runnable() {
            @Override
            public void run() {

                DrawableBean drawableBean;

                if (jsonFile.startsWith(PlatformDef.ASSETS_PRE)) {
                    drawableBean = parserAssets(jsonFile);
                } else {
                    drawableBean = parserFile(new File(jsonFile));
                }

                if (null != drawableBean) {
                    // 背景[Building]
                    bgDrawable = buildBackgroundDrawable(drawableBean.getBackground());
                    try {
                        mAnimatorDrawable = AnimatorDrawable.create(drawableBean, LayerAnimatorView.this);
                    } catch (IllegalArgumentException e) {
                        Log.e(TAG, Log.getStackTraceString(e));
                        mHandler.removeMessages(MSG_WHAT_NOTIFY_ERROR);
                        mHandler.sendMessage(mHandler.obtainMessage(MSG_WHAT_NOTIFY_ERROR, e.getMessage()));
                    }
                }
            }
        });
    }


    /**
     * 设置动画监听
     *
     * @callback onPrepared(): 容器内每层动画都是通过解析 json文件获取动画属性参数的,所以需要准备时间. 准备好后就可以开始动画了
     * @callback onEnd(): 动画结束
     * @callback onError(String msg): 在动画资源解析 或 播放过程中有一层动画出错都回调该方法
     */
    public void setAnimationListener(OnJsonAnimListener l) {
        mListener = l;
    }

    /**
     * 开始 动画
     */
    public void startAnimation() {
        if (!this.isAttachedToWindow()) {
            Log.w(TAG, "start error! " + this.getClass().getName() + " haven't attached!");
            return;
        }

        if (null != mAnimatorDrawable) {
            mAnimatorDrawable.start();
        } else {
            Log.w(TAG, "start error! AnimatorDrawable is null!");
        }
    }

    /**
     * 结束/清除 动画
     */
    public void cancelAnimation() {
        if (!this.isAttachedToWindow()) {
            Log.w(TAG, "start error! " + this.getClass().getName() + " haven't attached!");
            return;
        }

        clean();

        if (null != mAnimatorDrawable) {
            mAnimatorDrawable.cancel();
        } else {
            Log.w(TAG, "cancel error! AnimatorDrawable is null!");
        }
    }

    /**
     * 暂停动画
     */
    public void pauseAnimation() {
        if (!this.isAttachedToWindow()) {
            Log.w(TAG, "pause error! " + this.getClass().getName() + " haven't attached!");
            return;
        }

        if (null != mAnimatorDrawable) {
            mAnimatorDrawable.pause();
        } else {
            Log.w(TAG, "pause error! AnimatorDrawable is null!");
        }
    }

    /**
     * 恢复动画
     */
    public void resumeAnimation() {
        if (!this.isAttachedToWindow()) {
            Log.w(TAG, "resume error! " + this.getClass().getName() + " haven't attached!");
            return;
        }

        if (null != mAnimatorDrawable) {
            mAnimatorDrawable.resume();
        } else {
            Log.w(TAG, "resume error! AnimatorDrawable is null!");
        }
    }

    /**
     * # mark ------------private function
     */
    private void clean() {
        if (null != mHandler) {
            mHandler.removeMessages(MSG_WHAT_CREATE);
            mHandler.removeMessages(MSG_WHAT_NOTIFY_END);
            mHandler.removeMessages(MSG_WHAT_NOTIFY_ERROR);
        }
    }

    private void init() {
        HandlerThread thread = new HandlerThread(this.getClass().getName());
        thread.start();
        mParseHandler = new Handler(thread.getLooper());

        mHandler = new Handler(Looper.getMainLooper()) {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                switch (msg.what) {
                    case MSG_WHAT_CREATE: {
                        if (null == bgDrawable) {
                            setBackground(mDefautBgDrawable);
                        } else {
                            setBackground(bgDrawable);
                        }
                        setImageDrawable(mAnimatorDrawable);
                        mListener.onPrepared(LayerAnimatorView.this);
                        break;
                    }
                    case MSG_WHAT_NOTIFY_END: {
                        if (null != mListener) {
                            mListener.onEnd(LayerAnimatorView.this);
                        }
                        break;
                    }
                    case MSG_WHAT_NOTIFY_ERROR: {
                        if (null != mListener) {
                            mListener.onError(LayerAnimatorView.this, (String) msg.obj);
                        }
                        break;
                    }
                    default: {
                        Log.e(TAG, "msg.what: " + msg.what);
                        break;
                    }
                }
            }
        };
    }

    private DrawableBean parserAssets(@NonNull final String jsonFile) {
        DrawableBean drawableBean = null;
        InputStream inputStream = null;
        try {
            inputStream = getContext().getAssets().open(jsonFile.replace(PlatformDef.ASSETS_PRE, ""));
            drawableBean = parser(inputStream, DrawableBean.class);
        } catch (Exception ex) {
            Log.e(getClass().getSimpleName(), Log.getStackTraceString(ex));
        } finally {
            if (null != inputStream) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    Log.w(getClass().getSimpleName(), Log.getStackTraceString(e));
                }
            }

            if (null == drawableBean) {
                mHandler.removeMessages(MSG_WHAT_NOTIFY_ERROR);
                mHandler.sendMessage(mHandler.obtainMessage(MSG_WHAT_NOTIFY_ERROR, "parse json file error!"));
                return null;
            }

            ArrayList<LayerBean> layers = drawableBean.getLayers();
            if (null == layers || layers.isEmpty()) {
                mHandler.removeMessages(MSG_WHAT_NOTIFY_ERROR);
                mHandler.sendMessage(mHandler.obtainMessage(MSG_WHAT_NOTIFY_ERROR, "layers is empty!"));
                return null;
            }

            return drawableBean;
        }
    }

    private DrawableBean parserFile(@NonNull final File file) {

        DrawableBean drawableBean = null;
        FileInputStream inputStream = null;
        try {
            inputStream = new FileInputStream(file);
            drawableBean = parser(inputStream, DrawableBean.class);
        } catch (Exception ex) {
            Log.e(getClass().getSimpleName(), Log.getStackTraceString(ex));
        } finally {
            if (null != inputStream) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    Log.w(getClass().getSimpleName(), Log.getStackTraceString(e));
                }
            }

            if (null == drawableBean) {
                mHandler.removeMessages(MSG_WHAT_NOTIFY_ERROR);
                mHandler.sendMessage(mHandler.obtainMessage(MSG_WHAT_NOTIFY_ERROR, "parse json file error!"));
                return null;
            }

            ArrayList<LayerBean> layers = drawableBean.getLayers();
            if (null == layers || layers.isEmpty()) {
                mHandler.removeMessages(MSG_WHAT_NOTIFY_ERROR);
                mHandler.sendMessage(mHandler.obtainMessage(MSG_WHAT_NOTIFY_ERROR, "layers is empty!"));
                return null;
            }

            String dir = file.getParent();
            // background path [Building]
            BackgroundBean bgBean = drawableBean.getBackground();
            if (null != bgBean && !TextUtils.isEmpty(bgBean.getRes())) {
                bgBean.setRes(dir + File.separatorChar + bgBean.getRes());
            }
            // layer path [Building]
            for (LayerBean bean : layers) {
                String[] ress = bean.getRess();
                if (null == ress || 0 >= ress.length) {
                    mHandler.removeMessages(MSG_WHAT_NOTIFY_ERROR);
                    mHandler.sendMessage(mHandler.obtainMessage(MSG_WHAT_NOTIFY_ERROR, bean.getType() + " have no res!"));
                    return null;
                }

                for (int i = 0; i < ress.length; i++) {
                    ress[i] = dir + File.separatorChar + ress[i];
                }

                bean.setRess(ress);
            }
        }

        return drawableBean;
    }

    private BitmapDrawable buildBackgroundDrawable(BackgroundBean bgBean) {
        BitmapDrawable drawable = null;
        if (null != bgBean && !TextUtils.isEmpty(bgBean.getRes())) {

            switch (AnimatorConfig.Properties.valueOf(bgBean.getScan())) {
                case horizontal: {
                    Bitmap bmp = ImageLoader.getInstance().getBitmap(bgBean.getRes(), 0, getHeight());
                    drawable = new BitmapDrawable(getResources(), bmp);
                    drawable.setTargetDensity(bmp.getDensity());
                    drawable.setTileModeX(Shader.TileMode.REPEAT);
                    break;
                }
                case vertical: {
                    Bitmap bmp = ImageLoader.getInstance().getBitmap(bgBean.getRes(), getWidth(), 0);
                    drawable = new BitmapDrawable(getResources(), bmp);
                    drawable.setTargetDensity(bmp.getDensity());
                    drawable.setTileModeY(Shader.TileMode.REPEAT);
                    break;
                }
                case fit:
                default: {
                    Bitmap bmp = ImageLoader.getInstance().getBitmap(bgBean.getRes(), 0, 0);
                    drawable = new BitmapDrawable(getResources(), bmp);
                    break;
                }
            }
        }

        return drawable;
    }

    private <T> T parser(@NonNull InputStream inputStream, Class<T> classOfT) {
        T t = null;
        try {
            com.google.gson.JsonParser mJsonParser = new com.google.gson.JsonParser();
            Gson mGson = new Gson();
            JsonReader jreader = new JsonReader(new InputStreamReader(inputStream ,"utf-8"));
            JsonElement element = mJsonParser.parse(jreader);
            t = mGson.fromJson(element, classOfT);
        } catch (Exception ex) {
            Log.e(getClass().getSimpleName(), Log.getStackTraceString(ex));
        } finally {

            return t;
        }
    }

    private boolean isValidFile(String path) {
        if (TextUtils.isEmpty(path)) {
            Log.d(TAG, "isValidFile path == null");
            return false;
        }

        if (path.startsWith(PlatformDef.ASSETS_PRE)) {
            return true;
        }

        File file = new File(path);
        if (file.exists()) {//判断文件目录的存在
            return true;
        }


        return isUrl(path);
    }

    private boolean isUrl(String urls) {
        String regex = "(((gopher|news|nntp|telnet|http|ftp|https|ftps|sftp)?://)?([a-z0-9]+[.])|(www.))" + "\\w+[.|\\/]([a-z0-9]{0,})?[[.]([a-z0-9]{0,})]+((/[\\S&&[^,;\u4E00-\u9FA5]]+)+)?([.][a-z0-9]{0,}+|/?)";//设置正则表达式

        Pattern pat = Pattern.compile(regex.trim());//比对
        Matcher mat = pat.matcher(urls.trim());
        return mat.matches();//判断是否匹配
    }

    /**
     * # mark ------------class or interface
     */
    public static interface OnJsonAnimListener {
        public void onPrepared(LayerAnimatorView view);

        public void onEnd(LayerAnimatorView view);

        public void onError(LayerAnimatorView view, String msg);
    }
}
