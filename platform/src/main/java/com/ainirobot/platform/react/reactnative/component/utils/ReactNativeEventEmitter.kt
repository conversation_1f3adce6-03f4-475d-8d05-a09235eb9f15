package com.ainirobot.platform.react.reactnative.component.utils

import androidx.annotation.Nullable
import android.util.Log
import com.ainirobot.platform.BaseApplication
import com.facebook.react.modules.core.DeviceEventManagerModule

/**
 * @date: 2019-01-15
 * @author: lumeng
 * @desc: rn event
 */

class ReactNativeEventEmitter {

    companion object {

        val TAG = "ReactNativeEventEmitter"

        fun triggerEvent(eventName: String, @Nullable data: Any) {
             Log.i(TAG, "eventName:$eventName,data:$data")
            try {
                BaseApplication.getApplication()
                        .reactContext
                        ?.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                        ?.emit(eventName, data)
            } catch (e:Exception){
                e.printStackTrace()
            }
        }

    }
}
