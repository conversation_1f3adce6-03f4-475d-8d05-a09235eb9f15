
/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.react.server.impl;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.platform.react.server.bean.PlaceInfo;
import com.ainirobot.platform.react.server.bean.PlaceResultInfo;
import com.ainirobot.platform.rn.IMapManager;
import com.google.gson.Gson;
import com.pinyinsearch.util.T9Util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MapManager extends IMapManager.Stub {

    private static final String TAG = MapManager.class.getSimpleName();

    private List<PlaceInfo> mPlaceInfoList = new ArrayList<>();
    private Map<String, List<PlaceResultInfo>> mPlaceResultInfoMap = new HashMap<>();
    private Gson mGson;

    public MapManager() {
        mGson = new Gson();
        updatePlaceInfoList(RobotApi.getInstance().getPlaceList());
    }

    @Override
    public String t9Search(String query) {
        if (TextUtils.isEmpty(query)) {
            return null;
        }
        List<PlaceResultInfo> history = mPlaceResultInfoMap.get(query);
        if (history != null) {
            return mGson.toJson(history);
        }
        List<PlaceResultInfo> results = new ArrayList<>();
        for  (PlaceInfo info: mPlaceInfoList) {
            boolean isMatch = T9Util.match(info.getPinyinSearchUnit(), query);
            Log.d(TAG, "place search isMatch: " + isMatch);
            if (isMatch) {
                int index = info.getName().indexOf(info.getPinyinSearchUnit().getMatchKeyword().toString());
                Log.d(TAG, "place search name: " + info.getName()
                        + ", matchWords: " + info.getPinyinSearchUnit()
                        .getMatchKeyword() + ", index: " + index + ", endIndex: " + (index + info.getPinyinSearchUnit()
                        .getMatchKeyword().length() - 1));
                results.add(new PlaceResultInfo(info.getName(), info.getPinyinSearchUnit().getMatchKeyword().toString(), index,
                        index + info.getPinyinSearchUnit().getMatchKeyword().length() - 1));
            }
        }
        mPlaceResultInfoMap.put(query, results);
        return mGson.toJson(results);
    }

    public void reload() {
        updatePlaceInfoList(RobotApi.getInstance().getPlaceList());
    }

    private void updatePlaceInfoList(List<Pose> poseList) {
        mPlaceInfoList.clear();
        mPlaceResultInfoMap.clear();
        if (poseList != null) {
            for (Pose pose: poseList) {
                mPlaceInfoList.add(new PlaceInfo(pose.getName()));
            }
        }
    }
}
