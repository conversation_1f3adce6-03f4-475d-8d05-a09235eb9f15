package com.ainirobot.platform.react.server.utils;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.logging.Logger;

public class CommandValidator {
    private static final Logger logger = Logger.getLogger(CommandValidator.class.getName());
    private static final Pattern commandPattern = Pattern.compile("^([a-zA-Z0-9_/-]+(?:\\s+[a-zA-Z0-9_/-]+)?)");
    private static final Set<String> disallowedCommands = Collections.synchronizedSet(new HashSet<>());

    static {
        Collections.addAll(disallowedCommands,
                "su", "mount", "chmod", "chown", "chgrp", "setenforce", "restorecon", "setprop",
                "renice", "setpriv", "adduser", "passwd", "mkdir", "rm", "rmdir", "nuke",
                "pm uninstall", "wipe", "shred"
        );
    }

    /**
     * 校验指令是否存在于禁用列表中。
     * @param command 要校验的完整指令
     * @return 如果指令被禁用，则返回true，否则返回false
     */
    public static boolean isCommandDisabled(String command) {
        command = command.trim();
        Matcher matcher = commandPattern.matcher(command);
        if (matcher.find()) {
            String baseCommand = matcher.group(1);
            boolean isDisabled = disallowedCommands.contains(baseCommand);
            logger.info("Command checked: " + baseCommand + " - Disabled: " + isDisabled);
            return isDisabled;
        }
        logger.warning("No valid command found in input: " + command);
        return false;
    }

    /**
     * 添加新的禁用命令到集合中。
     * @param command 要添加的禁用命令
     */
    public static void addDisabledCommand(String command) {
        disallowedCommands.add(command);
        logger.info("Added disabled command: " + command);
    }

    /**
     * @param command 要移除的禁用命令
     */
    public static void removeDisabledCommand(String command) {
        disallowedCommands.remove(command);
        logger.info("Removed disabled command: " + command);
    }
}
