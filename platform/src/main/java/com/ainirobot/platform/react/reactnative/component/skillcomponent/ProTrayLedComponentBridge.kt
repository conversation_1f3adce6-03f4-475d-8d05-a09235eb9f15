package com.ainirobot.platform.react.reactnative.component.skillcomponent

import com.ainirobot.platform.component.LeavePileComponent
import com.ainirobot.platform.component.ProTrayLEDComponent
import com.ainirobot.platform.component.UvcCameraComponent
import com.ainirobot.platform.react.server.utils.ComponentUtils
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactMethod

class ProTrayLedComponentBridge(reactContext: ReactApplicationContext) : BaseComponentBridge<ProTrayLEDComponent>(reactContext) {

    override fun isHDMonopoly(): Bo<PERSON>an {
        return true
    }

    override fun getResouceType(): Array<ResouceType> {
        return arrayOf(ResouceType.LightStrip)
    }

    override fun getName(): String {
        return ComponentUtils.COMPONENT_TRAY_LED
    }

    @ReactMethod
    override fun start(uid: String, param: String?) {
        super._start(uid, param)
    }

    @ReactMethod
    override fun stop(uid: String, timeout: Int?) {
        super._stop(uid, timeout)
    }

    @ReactMethod
    override fun updateParams(uid: String, intent: String, param: String) {
        super._updateParams(uid, intent, param)
    }

    @ReactMethod
    override fun setStatusListener(uid: String, statusListenerId: Int) {
        super._setStatusListener(uid, statusListenerId)
    }

    @ReactMethod
    override fun setFinishListener(uid: String, finishListenerId: Int) {
        super._setFinishListener(uid, finishListenerId)
    }

}