/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.platform.react.reactnative.component.uicomponent.faceparticleview;

import android.app.Activity;
import android.util.AttributeSet;
import android.widget.FrameLayout;

import com.ainirobot.emoji.Emoji;
import com.ainirobot.emoji.common.CommonSmallEmojiLayout;
import com.ainirobot.platform.react.EveActivity;
import com.ainirobot.platform.utils.DimenUtils;
import com.facebook.react.bridge.ReactContext;
/**
 * Created by Orion on 2018/8/7.
 */

public class RNFaceParticleView extends FrameLayout {

    private ReactContext mContext;
    private CommonSmallEmojiLayout commonSmallEmojiLayout;
    private boolean showCenter;
    private boolean showBg;

    public RNFaceParticleView(ReactContext context) {
        super(context);
        init(context);
    }

    public RNFaceParticleView(ReactContext context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public RNFaceParticleView(ReactContext context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public RNFaceParticleView(ReactContext context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context);
    }

    private void init(ReactContext context) {
        this.mContext = context;
        Activity activity = context.getCurrentActivity();
        // 防止进入rn调用init方法时activity还没有onHostResume
        if (activity == null && EveActivity.getActivity().get() != null) {
            activity = EveActivity.getActivity().get();
        }

        commonSmallEmojiLayout = new CommonSmallEmojiLayout(activity);
        addView(commonSmallEmojiLayout);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
    }

    public void startTalk() {
        commonSmallEmojiLayout.speak();
    }

    public void stopTalk() {
        commonSmallEmojiLayout.speakEnd();
    }

    public void destroy() {
        if (commonSmallEmojiLayout != null) {
            commonSmallEmojiLayout.destroy();
        }
    }

    public void hide() {
        commonSmallEmojiLayout.hide();
    }

    public void show() {
        commonSmallEmojiLayout.show();
    }

    public void showCenter(boolean showCenter) {
        this.showCenter = showCenter;
        showBg(showBg);
    }

    public void updateBackgroundUrl(String url) {
        commonSmallEmojiLayout.updateBackgroundUrl(url);
    }

    /**
     * 该方法是变成小表情是否要设置动画的
     */
    public void showBg(boolean showBg) {
        this.showBg = showBg;
        commonSmallEmojiLayout.show(showCenter ? 612 : 329, DimenUtils.getScreenHeightPixels() - 1733, showBg);
    }

    public void bgOperate(String emoji) {
        commonSmallEmojiLayout.playEmojiType(Emoji.EMOJI_TYPE.typeOfName(emoji).getTaskName(),null);
    }
}
