package com.ainirobot.platform.react.reactnative.component.uicomponent.faceparticleview

import android.text.TextUtils
import android.util.Log
import android.view.View
import com.ainirobot.platform.speech.SpeechRegister
import com.facebook.react.bridge.ReadableArray
import com.facebook.react.common.MapBuilder
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.annotations.ReactProp

/**
 * @date: 2019-04-08
 * @author: haohongwei
 * @desc: 封装小眼睛
 */
@ReactModule(name = RNFaceParticleViewManager.REACT_CLASS)
class RNFaceParticleViewManager : SimpleViewManager<RNFaceParticleView>() {

    private var faceParticleView: RNFaceParticleView? = null

    companion object {
        const val COMMAND_UNMOUNT = 1
        const val REACT_CLASS = "RNFaceParticleView"
    }

    override fun getName(): String {
        return REACT_CLASS
    }

    override fun createViewInstance(reactContext: ThemedReactContext): RNFaceParticleView {
        Log.i(REACT_CLASS, "createViewInstance")
        if (faceParticleView != null) {
            faceParticleView?.destroy()
            Log.i(REACT_CLASS, "createViewInstance revomeFaceParticle")
            faceParticleView = null
        }
        var view: RNFaceParticleView = RNFaceParticleView(reactContext)
        SpeechRegister.getInstance().registerTTSStatusListener(mTtsStatusListener)
        faceParticleView = view
        return view
    }

    override fun onDropViewInstance(view: RNFaceParticleView) {
        super.onDropViewInstance(view)
        Log.i(REACT_CLASS, "onDropViewInstance")
        try {
            SpeechRegister.getInstance().unRegisterTTSStatusListener(mTtsStatusListener)
            faceParticleView?.stopTalk()
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    @ReactProp(name = "emojiType")
    fun setSource(view: RNFaceParticleView, emojiTypeString: String) {
        Log.i(REACT_CLASS, "setSource emojiTypeString : " + emojiTypeString)
        if (!TextUtils.isEmpty(emojiTypeString)) {
            view.bgOperate(emojiTypeString)
        } else {
            view.bgOperate("blink_new")
        }
    }

    @ReactProp(name = "showBg")
    fun setShowBg(view: RNFaceParticleView, showBg: Boolean) {
        Log.i(REACT_CLASS, "setShowBg  : " + showBg)
        view.showBg(showBg)
    }

    @ReactProp(name = "bgSource")
    fun setBgSource(view: RNFaceParticleView, url: String) {
        Log.i(REACT_CLASS, "setBgSource  : " + url)
        view.updateBackgroundUrl(url)
    }

    @ReactProp(name = "hide")
    fun setHide(view: RNFaceParticleView, hide: Boolean) {
        Log.i(REACT_CLASS, "setHide  : " + hide)
        if (hide) {
            view.hide()
            faceParticleView?.stopTalk()
            view.visibility = View.GONE
        } else {
            view.show()
            view.visibility = View.VISIBLE
        }
    }

    @ReactProp(name = "showCenter")
    fun setShowCenter(view: RNFaceParticleView, showCenter: Boolean) {
        Log.i(REACT_CLASS, "setShowCenter  : " + showCenter)
        view.showCenter(showCenter)
    }

    @ReactProp(name = "talk")
    fun isStartTalk(view: RNFaceParticleView, talk: Boolean) {
        view.let {
            if (talk) it.startTalk() else it.stopTalk()
        }
    }

    override fun onAfterUpdateTransaction(view: RNFaceParticleView) {
        super.onAfterUpdateTransaction(view)

    }

    override fun getCommandsMap(): Map<String, Int>? {
        return MapBuilder.of(
                "unmount", COMMAND_UNMOUNT)
    }

    override fun receiveCommand(root: RNFaceParticleView, commandId: Int, args: ReadableArray?) {
        Log.i(REACT_CLASS, "receiveCommand  : " + commandId)
        when (commandId) {
            COMMAND_UNMOUNT -> root.destroy()
            else -> Log.e("RNFaceParticleView", "unsupported command $commandId")
        }
    }

    private val mTtsStatusListener = object : SpeechRegister.TTSSpeechStatueListener {
        override fun onStartTTS() {
            Log.i(REACT_CLASS, "onStartTTS")
            faceParticleView?.startTalk()
        }

        override fun onStopTTS(isComplete: Boolean) {
            Log.i(REACT_CLASS, "onSpeechCompleteTTS")
            faceParticleView?.stopTalk()
        }

        override fun onSpeechCompleteTTS(isComplete: Boolean) {
            Log.i(REACT_CLASS, "onSpeechCompleteTTS")
            faceParticleView?.stopTalk()
        }
    }

}
