package com.ainirobot.platform.react

import android.content.Context
import android.util.Log
import com.ainirobot.coreservice.client.SettingsUtil
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.data.provider.AppInfoHelper
import com.ainirobot.platform.react.reactnative.character.Constant
import com.ainirobot.platform.utils.FileHelper
import com.ainirobot.platform.utils.FileUtils
import com.ainirobot.platform.utils.GsonUtil
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import org.json.JSONArray
import java.io.File
import java.util.*
import kotlin.collections.HashMap

//字段只增不减
//v1 包含字段 bundleIndexFilePath 整个bundle文件的路径
//v2 新增字段 bundleBizFilePath Biz Bundle文件的路径 bundlePlatformFilePath
data class AppBeanV2(var bundleIndexFilePath: String? = null,
                     var bundleBizFilePath: String? = null,
                     var bundlePlatformFilePath: String? = null,
                     var rpkBean: OPKBeanV3)

object AppManger {
    private const val TAG = "AppManger"

    private var mAppMap: HashMap<String, AppBeanV2>? = null

    private val mAppData: AppInfoHelper

    init {
        val context = BaseApplication.getContext()
        mAppData = AppInfoHelper(context)

        val rpkInstallList = FileHelper
                .loadJsonFromFile("${context.filesDir.absolutePath}/${Constant.RPK_INSTALL_DIR}/${Constant.RPK_INSTALL_NAME}")
        mAppMap = if (rpkInstallList.isNullOrEmpty()) {
            loadFromDB()
        } else {
            Gson().fromJson(rpkInstallList, object : TypeToken<HashMap<String, AppBeanV2>>() {}.type)
        }
        Log.d(TAG, "AppManager init : " + mAppMap?.size)
    }

    private fun loadFromDB(): HashMap<String, AppBeanV2>? {
        val data = mAppData.allOpkInfo

        val appData = HashMap<String, AppBeanV2>()
        data.forEach {
            it.isSystem = isSystem(it.appid)
            val bean: AppBeanV2 = if (it.opkLoad == "v1") {
                AppBeanV2(it.path + "/index.android.bundle", null, null, it)
            } else {
                AppBeanV2(null, it.path + "/biz.android.bundle", it.path + "/platform.android.bundle", it)
            }
            appData[it.appid] = bean
        }
        Log.d(TAG, "Load app data from db : " + appData.size)
        return appData
    }

    fun addApp(bundleIndexPath: String?, bundleBizPath: String?, bundlePlatformPath: String?, rpkBean: OPKBeanV3) {
        val bean = AppBeanV2(bundleIndexPath, bundleBizPath, bundlePlatformPath, rpkBean)
        addApp(bean)
    }

    fun addApp(bean: AppBeanV2) {
        val appid = bean.rpkBean.appid
        bean.rpkBean.isSystem = isSystem(appid)
        mAppMap?.put(appid, bean)

        val context = BaseApplication.getContext()
        save(context, mAppMap)

        mAppData.replace(bean.rpkBean)
    }

    fun addAppIfNotExists(bean: AppBeanV2) {
        Log.i(TAG, "app info:$bean")
        val appId = bean.rpkBean.appid
        if (mAppMap!!.keys.contains(appId)) {
            Log.i(TAG, "rpk install list exist appId:$appId")
            return
        }
        addApp(bean)
    }

    fun updateApp(rpkBean: OPKBeanV3) {
        rpkBean.isSystem = isSystem(rpkBean.appid)
        mAppMap?.get(rpkBean.appid)?.rpkBean = rpkBean

        val context = BaseApplication.getContext()
        save(context, mAppMap)

        mAppData.update(rpkBean)
    }

    @Synchronized
    private fun save(context: Context, map: HashMap<String, AppBeanV2>?) {
        if (map == null) {
            return
        }
        //opk安装列表
        val jsonArray = JSONArray()
        for (entry in map.entries) {
            jsonArray.put(OPKHelper.OPKInfo2JSON(entry.value.rpkBean))
        }
        SettingsUtil.putString(BaseApplication.getContext(), SettingsUtil.ROBOT_SETTING_OPK_INSTALL_LIST, jsonArray.toString())
        FileHelper.saveToFile(context.filesDir.absolutePath + "/${Constant.RPK_INSTALL_DIR}/",
                GsonUtil.toJson(map), Constant.RPK_INSTALL_NAME)

        Log.d(TAG, "Save succeed")

    }

    fun isAppValid(name: String?): Boolean {
        if (name == null) {
            return false
        }

        if (mAppMap?.containsKey(name)!!) {
            val app = mAppMap?.get(name)
            if (app != null) {
                Log.i(TAG, app.toString())
                return if (FileUtils.checkFileExist(app.bundleIndexFilePath) ||
                        (FileUtils.checkFileExist(app.bundleBizFilePath)
                                && FileUtils.checkFileExist(app.bundlePlatformFilePath))) {
                    //V1 整包版本 或者 V2 拆包版本
                    true
                } else {
                    mAppMap?.remove(name)
                    val context = BaseApplication.getContext()
                    save(context, mAppMap)

                    mAppData.delete(name)
                    false
                }
            }
        }
        return false
    }

    fun removeAppId(name: String?): Boolean {
        if (name == null) {
            return false
        }

        if (mAppMap?.containsKey(name)!!) {
            val app = mAppMap?.remove(name)
            val context = BaseApplication.getContext()
            save(context, mAppMap)
            mAppData.delete(name)
            if (app != null) {
                return when {
                    FileUtils.checkFileExist(app.bundleIndexFilePath) -> {
                        val file = File(app.bundleIndexFilePath)
                        FileHelper.deleteDirectory(file.parentFile)
                        true
                    }
                    FileUtils.checkFileExist(app.bundleBizFilePath) -> {
                        val file = File(app.bundleBizFilePath)
                        FileHelper.deleteDirectory(file.parentFile)
                        true
                    }
                    else -> {
                        false
                    }
                }
            }
        }

        return false

    }

    /**
     * 使用前 请调用isAppValid()
     * */
    fun getAppIndexPathByCharacter(name: String?): String? {
        if (name == null) {
            return null
        }
        if (mAppMap?.containsKey(name)!!) {
            val app = mAppMap?.get(name)
            if (app != null) {
                if (FileUtils.checkFileExist(app.bundleIndexFilePath)) {
                    return app.bundleIndexFilePath
                }
            }
        }
        return null
    }

    /**
     * 使用前 请调用isAppValid()
     * */
    fun getAppBizPathByCharacter(name: String?): String? {
        if (name == null) {
            return null
        }
        if (mAppMap?.containsKey(name)!!) {
            val app = mAppMap?.get(name)
            if (app != null) {
                if (FileUtils.checkFileExist(app.bundleBizFilePath)) {
                    return app.bundleBizFilePath
                }
            }
        }
        return null
    }

    /**
     * 使用前 请调用isAppValid()
     * */
    fun getAppPlatformPathByCharacter(name: String?): String? {
        if (name == null) {
            return null
        }
        if (mAppMap?.containsKey(name)!!) {
            val app = mAppMap?.get(name)
            if (app != null) {
                if (FileUtils.checkFileExist(app.bundlePlatformFilePath)) {
                    return app.bundlePlatformFilePath
                }
            }
        }
        return null
    }

    /**
     * 使用前 请调用isAppValid()
     * */
    fun getRPKByCharacter(name: String?): OPKBeanV3? {
        if (name == null) {
            return null
        }
        if (mAppMap?.containsKey(name)!!) {
            val app = mAppMap?.get(name)
            if (app != null) {
                app.rpkBean.isSystem = isSystem(name)
                return app.rpkBean
            }
        }
        return null
    }

    fun getAllInstallList(): Map<String, AppBeanV2>? {
        return mAppMap
    }

    fun getAllPluginInfo(): MutableList<OPKBeanV3> {
        val pluginList = mutableListOf<OPKBeanV3>()
        mAppMap?.forEach { appId, app ->
            if (app.rpkBean.type == "plugin") {
                pluginList.add(app.rpkBean)
            }
        }
        return pluginList
    }

    fun reload() {
        Log.i(TAG, "Reload")
        val context = BaseApplication.getContext()
        val rpkInstallList = FileHelper
                .loadJsonFromFile("${context.filesDir.absolutePath}/${Constant.RPK_INSTALL_DIR}/${Constant.RPK_INSTALL_NAME}")
        mAppMap = if (rpkInstallList.isNullOrEmpty()) {
            HashMap()
        } else {
            Gson().fromJson(rpkInstallList, object : TypeToken<HashMap<String, AppBeanV2>>() {}.type)
        }
    }

    /**
     * 判断当前OPK是否系统级OPK
     *
     * 目前根据AppId的前缀确定是否为系统级OPK，该方案为临时方案，后续正式方案待定
     */
    private fun isSystem(appId: String): Boolean {
        return appId.startsWith("baoxiaomi_") || appId.startsWith("system_")
    }
}