package com.ainirobot.platform.react.reactnative.component.listener

import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNTextListener
import com.facebook.react.bridge.WritableNativeMap
import com.ainirobot.platform.speech.SpeechRegister
import com.ainirobot.platform.speech.SpeechApiCost

class BridgeTextListener : IRNTextListener.Stub {

    companion object {
        fun obtain(callbackId:Int): BridgeTextListener? {
            return BridgeTextListener(callbackId)
        }
    }

    var id = -1

    private constructor(id: Int) {
        this.id = id
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
    }

    fun triggerEvent(event: String, streamSid: String = "", textSid: String = "") {
        if (id < 0) {
            return
        }
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putString("event", event)
        readableMap.putString("streamSid", streamSid)
        readableMap.putString("textSid", textSid)
        ReactNativeEventEmitter.triggerEvent("onPlayText", readableMap)
    }

    override fun onStart() {
        triggerEvent("onStart")
        SpeechRegister.getInstance().handleListeners(SpeechApiCost.MSG_SPEECH_START, 0, null)
    }

    override fun onStop() {
        triggerEvent("onStop")
        SpeechRegister.getInstance().handleListeners(SpeechApiCost.MSG_SPEECH_STOP, 0, null)
    }

    override fun onError() {
        triggerEvent("onError")
    }

    override fun onComplete() {
        triggerEvent("onComplete")
        SpeechRegister.getInstance().handleListeners(SpeechApiCost.MSG_TTS_SPEECH_COMPLETE, 1, null)
    }

    override fun onStreamComplete(streamSid: String?, textSid: String?) {
        triggerEvent("onStreamComplete", streamSid ?: "", textSid ?: "")
        SpeechRegister.getInstance().handleListeners(SpeechApiCost.MSG_TTS_SPEECH_COMPLETE, 1, null)
    }
}