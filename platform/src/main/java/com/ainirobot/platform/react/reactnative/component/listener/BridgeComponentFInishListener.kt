package com.ainirobot.platform.react.reactnative.component.listener

import android.os.RemoteException
import android.util.Log
import com.ainirobot.platform.component.Component
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNFinishListener
import com.facebook.react.bridge.WritableNativeMap

class BridgeComponentFinishListener : Component.ComponentListener, IRNFinishListener.Stub {

    companion object {
        const val EVENT_NAME = "onFinish"
        const val TAG = "BridgeComponentFinishListener"

        fun obtain(callbackId:Int): BridgeComponentFinishListener? {
            return if(callbackId < 0){
                null
            } else {
                BridgeComponentFinishListener(callbackId)
            }
        }
    }

    var id = 0

    private constructor(id:Int){
        this.id = id
    }

    @Throws(RemoteException::class)
    override fun onFinish(status: Int, data: String?, extraData: String?) {
        Log.i(TAG, "onFinish status:$status,data:$data")
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putInt("status", status)
        data.apply{
            readableMap.putString("data", data)
        }
        extraData?.apply {
            readableMap.putString("extraData", extraData)
        }
        ReactNativeEventEmitter.triggerEvent(EVENT_NAME, readableMap)
    }
}
