package com.ainirobot.platform.react.reactnative.component.common

import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.util.Log
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.react.AppManger
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.component.listener.BridgeRobotSettingListener
import com.ainirobot.platform.rn.listener.IRNRobotSettingListener
import com.facebook.react.bridge.*
import java.util.*

private val TAG = "RobotSettingApi"

class RobotSettingApiBridgeModule(val reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    private val mKeyList = Collections.synchronizedList(ArrayList<String>())
    private val mListeners = Collections.synchronizedList(ArrayList<BridgeRobotSettingListener>())

    override fun getName(): String = "RobotSettingApi"

    @ReactMethod
    fun getRobotString(key: String, result: Promise) {
        try {
            result.resolve(RNClientManager.instance?.robotSettingManager?.getRobotString(key))
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "getRobotString error: $e")
            result.reject(e)
        }
    }

    @ReactMethod
    fun getRobotFloat(key: String, result: Promise) {
        try {
            result.resolve(RNClientManager.instance?.robotSettingManager?.getRobotFloat(key))
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "getRobotFloat error: $e")
            result.reject(e)
        }
    }

    @ReactMethod
    fun getRobotInt(key: String, result: Promise) {
        try {
            result.resolve(RNClientManager.instance?.robotSettingManager?.getRobotInt(key))
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "getRobotInt error: $e")
            result.reject(e)
        }
    }

    @ReactMethod
    fun getRobotGlobalInt(key: String, result: Promise) {
        try {
            result.resolve(RNClientManager.instance?.robotSettingManager?.getRobotGlobalInt(key))
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "getRobotGlobalInt error: $e")
            result.reject(e)
        }
    }

    @ReactMethod
    fun setRobotString(appId: String?, key: String, value: String) {
        Log.d(TAG, "Settings[$appId] : $key   $value")
        //非系统权限禁止调用
        val info = AppManger.getRPKByCharacter(appId)
        if (info == null || !info.isSystem) {
            Log.d(TAG, "Settings[$appId] : $key   $value permission denied")
            return
        }

        try {
            RNClientManager.instance?.robotSettingManager?.setRobotString(key, value)
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "setRobotString error: $e")
        }
    }

    @ReactMethod
    fun setRobotFloat(appId: String?, key: String, value: Float) {
        Log.d(TAG, "Settings[$appId] : $key   $value")
        //非系统权限禁止调用
        val info = AppManger.getRPKByCharacter(appId)
        if (info == null || !info.isSystem) {
            Log.d(TAG, "Settings[$appId] : $key   $value permission denied")
            return
        }

        try {
            RNClientManager.instance?.robotSettingManager?.setRobotFloat(key, value)
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "setRobotFloat error: $e")
        }
    }

    @ReactMethod
    fun setRobotInt(appId: String?, key: String, value: Int) {
        Log.d(TAG, "Settings[$appId] : $key   $value")
        //非系统权限禁止调用
        val info = AppManger.getRPKByCharacter(appId)
        if (info == null || !info.isSystem) {
            Log.d(TAG, "Settings[$appId] : $key   $value permission denied")
            return
        }

        try {
            RNClientManager.instance?.robotSettingManager?.setRobotInt(key, value)
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "setRobotInt error: $e")
        }
    }

    @ReactMethod
    fun registerRobotSettingListener(callbackId: Int, keys: ReadableArray) {
        if (callbackId < 0) {
            Log.d(TAG, "Register setting listener failed : callback invalid")
            return
        }

        val listener = BridgeRobotSettingListener.obtain(callbackId, keys.toArrayList())
        mListeners.add(listener)

        try {
            val values = keys.toArrayList()
            val oldSize = mKeyList.size
            for (key in values) {
                if (!mKeyList.contains(key)) {
                    mKeyList.add(key.toString())
                    Log.d(TAG, "add register:" + key)
                }
            }
            if (oldSize < mKeyList.size) {
                Log.d(TAG, "register to main thread")
                RNClientManager.instance?.robotSettingManager?.unRegisterRobotSettingListener(mRobotSettingListener)
                RNClientManager.instance?.robotSettingManager?.registerRobotSettingListener(
                        mRobotSettingListener, mKeyList)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun unRegisterRobotSettingListener(callbackId: Int) {
        try {
            var iterator = mListeners.iterator()
            while (iterator.hasNext()) {
                val listener = iterator.next()
                if (listener.id == callbackId) {
                    iterator.remove()
                }
            }

            if (mListeners.isEmpty()) {
                clearSettingsListener()
                RNClientManager.instance?.robotSettingManager?.unRegisterRobotSettingListener(mRobotSettingListener)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    private val mRobotSettingListener = object : IRNRobotSettingListener.Stub() {
        override fun onRobotSettingChanged(key: String) {
            Log.i(TAG, "On settings change, key : $key")
            for (listener in mListeners) {
                if (listener.keys.contains(key)) {
                    listener.onRobotSettingChanged(key)
                }
            }
        }
    }

    @ReactMethod
    fun isCharging(promise: Promise) {
        try {
            val batteryStatus: Intent? = IntentFilter(Intent.ACTION_BATTERY_CHANGED).let { ifilter ->
                BaseApplication.getContext().registerReceiver(null, ifilter)
            }
            val status: Int = batteryStatus?.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1) ?: -1
            Log.i("SettingsBridgeModule", "isCharing:$status")
            val isCharging: Boolean = status != 0
            promise.resolve(isCharging)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun clearSettingsListener(appId: String?) {
        clearSettingsListener()
    }

    private fun clearSettingsListener() {
        try {
            mListeners.clear()
            mKeyList.clear()
            RNClientManager.instance?.robotSettingManager?.unRegisterRobotSettingListener(mRobotSettingListener)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}