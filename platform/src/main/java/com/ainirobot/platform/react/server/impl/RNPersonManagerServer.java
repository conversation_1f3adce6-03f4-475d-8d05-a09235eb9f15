
/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.react.server.impl;

import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.person.PersonApi;
import com.ainirobot.platform.rn.IRNPersonManager;
import com.google.gson.Gson;

import java.util.List;
public class RNPersonManagerServer extends IRNPersonManager.Stub {

    @Override
    public int getAllPersonNum(){
        if (PersonApi.getInstance().getAllPersons() == null) {
            return 0;
        }
        return PersonApi.getInstance().getAllPersons().size();
    }

    @Override
    public String getLastPersonId(){
        if (PersonApi.getInstance().getFocusPerson() == null) {
            return "";
        }
        return PersonApi.getInstance().getFocusPerson().getUserId();
    }

    @Override
    public String getLastPersonName() {
        if (PersonApi.getInstance().getFocusPerson() == null) {
            return "";
        }
        return PersonApi.getInstance().getFocusPerson().getName();
    }

    @Override
    public String getAllPerson(boolean isOnlyFace) {
        List<Person> list = isOnlyFace ? PersonApi.getInstance().getAllFaceList()
                : PersonApi.getInstance().getAllPersons();
        if (list == null) {
            return "";
        }
        Gson gson = new Gson();
        return gson.toJson(list);
    }
}
