package com.ainirobot.platform.react.reactnative.component.uicomponent.bigimagefit

import android.util.Log
import com.facebook.react.bridge.ReadableMap
import com.facebook.react.common.MapBuilder
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.annotations.ReactProp
import com.facebook.react.uimanager.events.RCTEventEmitter

@ReactModule(name = BigImageFitManager.REACT_CLASS)
class BigImageFitManager: SimpleViewManager<BigImageFitView>() {
    private lateinit var mContext: ThemedReactContext
    companion object {
        const val REACT_CLASS = "BigImageFitView"
        const val TAG = "BigImageManager"
        const val WIDTH = "width"
        const val HEIGHT = "height"
        const val SRC = "src"
    }

    override fun getName(): String {
        return REACT_CLASS
    }

    override fun createViewInstance(reactContext: ThemedReactContext): BigImageFitView {
        mContext = reactContext
        return BigImageFitView(reactContext)
    }

    @ReactProp(name = "data")
    fun setData(view: BigImageFitView, map: ReadableMap) {
        Log.d(TAG, map.toString())
        val width = map.getInt(WIDTH)
        val height = map.getInt(HEIGHT)
        val src = map.getString(SRC)
        view.setData(src, width, height)
        view.setOnClickListener {
            mContext.getJSModule(RCTEventEmitter::class.java).receiveEvent(view.id, "onClick", null)
        }
    }

    override fun getExportedCustomDirectEventTypeConstants(): MutableMap<String, Any> {
        return MapBuilder.of(
                "onClick", MapBuilder.of("registrationName", "onClick")
        )
    }
}