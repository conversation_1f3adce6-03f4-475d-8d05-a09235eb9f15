package com.ainirobot.platform.react.reactnative.character

import android.content.SharedPreferences
import android.text.TextUtils
import android.util.Log
import com.ainirobot.coreservice.client.ProductInfo
import com.ainirobot.coreservice.client.SettingsUtil
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi
import com.ainirobot.coreservice.client.speech.SkillCallback
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.PlatformDef
import com.ainirobot.platform.appmanager.AppInfo
import com.ainirobot.platform.callbutton.CallButtonManager
import com.ainirobot.platform.character.Character
import com.ainirobot.platform.character.CharacterManager
import com.ainirobot.platform.data.IntentManager
import com.ainirobot.platform.react.AppManger
import com.ainirobot.platform.react.OPKHelper
import com.ainirobot.platform.react.RNReportUtils
import com.ainirobot.platform.react.reactnative.RNDebugReceiver
import com.ainirobot.platform.react.reactnative.crashhandler.bridge.ReactEnvironmentHelper
import com.ainirobot.platform.react.server.control.RNServerManager
import com.ainirobot.platform.speech.SpeechApi
import com.ainirobot.platform.speech.SpeechManager
import com.ainirobot.platform.utils.GsonUtil
import com.google.gson.JsonIOException
import org.json.JSONException
import org.json.JSONObject

data class StartEvent(var isCrash: Boolean, val reqId: Int, val msgId: String, val name: String, var isPreset: Boolean = false)

class ReactCharacter : Character {
    override fun isDefault(): Boolean {
        return false
    }

    override fun isNeedToBeDefault(): Boolean {
        return true
    }

    override fun getCharacterCode(): Int {
        return -1
    }

    override fun getStartIntents(): MutableList<String>? {
        return null
    }

    companion object {
        const val hwException = "_HW_EXCEPTION_"
        const val sharedPreferences = "_react_character_"
        const val defaultRpk = "_default_rpk_"
        //v1 OPK版本信息
        const val defaultRpkPath = "_default_rpk_path_"
        //v2 OPK版本信息
        const val defaultOpkBizPath = "_default_opk_biz_path_"
        const val defaultOpkPlatformPath = "_default_opk_platform_path_"
        var currentCharacter: String? = null
        var isReactRunning: Boolean = false
    }

    private val TAG = ReactCharacter::class.java.simpleName
    private val name: String
    private var startEvent: StartEvent? = null
    private var editor: SharedPreferences.Editor

    constructor(name: String) {
        this.name = name
        val sp = BaseApplication.getContext().getSharedPreferences(sharedPreferences, 0)
        editor = sp.edit()
    }

    fun getName(): String {
        return this.name
    }

    fun start(): Boolean {
        Log.d(TAG, "start: " + currentCharacter + " " + ReactCharacter.isReactRunning.toString())
        Log.d(TAG, "start: isDebug=" + BaseApplication.getApplication().isDebug +
                " isAppValid=" + AppManger.isAppValid(currentCharacter) +
                " startEvent=" + (startEvent?.isPreset?:startEvent));
        if (!BaseApplication.getApplication().isDebug && !AppManger.isAppValid(currentCharacter) && (startEvent?.isPreset != true)) {
            Log.d(TAG, "start failed with bundlepath")
            remindAndReport()
            return false
        }
        startRobot()
        isReactRunning = true
        Log.i("ReactCharacter", "running: " + ReactCharacter.isReactRunning.toString())

        return true
    }

    override fun start(params: String?): Boolean {
        Log.d(TAG, "start params: $params")

        val json = JSONObject(params)
        val opkData = json.optString(Character.OPK_DATA)
        if (opkData.isNullOrEmpty()) {
            currentCharacter = name
            return start()
        }

        try {
            startEvent = GsonUtil.fromJson(opkData, StartEvent::class.java)
        } catch (e: JsonIOException) {
            Log.e(TAG, "start Json exception")
            return false
        }

        Log.e(TAG, "start name : " + name + "   isPreset : " + startEvent!!.isPreset)
        if (!AppManger.isAppValid(name) && !(startEvent!!.isPreset)) {
            Log.e(TAG, "start name invalid")
            return false
        }

        currentCharacter = name
        start()
        return true
    }

    private fun startRobot() {
        SpeechManager.getInstance().openSpeechAsrRecognize()
        val rpkBean = AppManger.getRPKByCharacter(name)?.copy()
        val bundleIndexPath = AppManger.getAppIndexPathByCharacter(name)
        val bundleBizPath = AppManger.getAppBizPathByCharacter(name)
        val bundlePlatformPath = AppManger.getAppPlatformPathByCharacter(name)

//        if (rpkBean != null) {
//            Log.d("nlp_launcher_debug",rpkBean.appid)
//            SpeechApi.getInstance().startApp(rpkBean.appid)
//            SpeechApi.getInstance().moveToForeground(rpkBean.appid)
//        }


        rpkBean?.apply {
            editor.putString(defaultRpk, GsonUtil.toJson(rpkBean))
            SettingsUtil.putString(BaseApplication.getContext(),
                    SettingsUtil.ROBOT_SETTING_OPK_INFO,
                    OPKHelper.OPKInfo2JSON(this).toString())
            OPKHelper.onHandleWithOPK(this, {
                //v1 清空biz platform 两个字段
                editor.putString(defaultOpkBizPath, null)
                editor.putString(defaultOpkPlatformPath, null)
                //v1的opk部分信息
                bundleIndexPath?.apply { editor.putString(defaultRpkPath, bundleIndexPath) }
                RNReportUtils.getInstance().appId = this.appid
                RNReportUtils.getInstance().versionName = this.versionName
                RNReportUtils.getInstance().coreTarget = this.coreTarget
            }, {
                //v2 清空biz 字段
                editor.putString(defaultRpkPath, null)
                //v2的opk部分信息
                bundleBizPath?.apply { editor.putString(defaultOpkBizPath, bundleBizPath) }
                bundlePlatformPath?.apply { editor.putString(defaultOpkPlatformPath, bundlePlatformPath) }
                RNReportUtils.getInstance().appId = this.appid
                RNReportUtils.getInstance().versionName = this.versionName
                RNReportUtils.getInstance().coreTarget = this.coreTarget!!
            }, {
                editor.putString(defaultRpkPath, null)
                //v3的opk部分信息
                bundleBizPath?.apply { editor.putString(defaultOpkBizPath, bundleBizPath) }
                bundlePlatformPath?.apply { editor.putString(defaultOpkPlatformPath, bundlePlatformPath) }
                RNReportUtils.getInstance().appId = this.appid
                RNReportUtils.getInstance().versionName = this.versionName
                RNReportUtils.getInstance().coreTarget = this.coreTarget!!
            })
        }
        editor.commit()

        //重启环境 如果Activity没有onCreate 发消息并不会接收到
        if (startEvent == null) {
            startEvent = StartEvent(false, -1, "", name)
        } else {
            startEvent!!.isCrash = false
        }

        if (RNDebugReceiver.DEBUG) {
            // disable debug && load new opk
            RNDebugReceiver.setDebugEnable(false)
        }

        RNServerManager.getInstance().updateStartEvent(startEvent!!)
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus()) {
            RNServerManager.getInstance().restartRNProcess()
        } else {
            RNServerManager.getInstance().startRNProcess()
        }


    }

    override fun stop(timeout: Long): Boolean {
        Log.d(TAG, "stop isReactRunning $isReactRunning currentCharacter: $currentCharacter")
        val rpkBean = AppManger.getRPKByCharacter(name)
        SpeechApi.getInstance().destroyApp(name)
        isReactRunning = false
        RNServerManager.getInstance().stopRNProcess()
        return false
    }

    override fun getSkillCallback(): SkillCallback {
        return RNServerManager.getInstance().skillCallbackServer
    }

    private fun remindAndReport() {
        if (BaseApplication.getApplication().isDebug) {
            try {
                SpeechManager.getInstance().playText("角色加载失败", null)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun handleRequest(reqId: Int, intent: String?, text: String?, params: String?): Boolean {
        if (intent != null && params != null) {
            val isSwitchCharacter = IntentManager.getInstance().isSwitchCharacter(intent)
            if (isSwitchCharacter) {
                val result = switchCharacter(intent, params)
                if (!result) {
                    remindAndReport()
                }
                return result
            }
        }

        RNServerManager.getInstance().callbackServer.onNewRequest(reqId, intent, text, params)
        return true
    }

    fun switchScene(params: String): Boolean {
        Log.d(TAG, "Switch opk : " + params);
        val data = JSONObject(params);
        val reqId = data.getInt("reqId");
        val appId = data.getString("name");

        val appInfo = AppManger.getRPKByCharacter(appId)
        if (appInfo == null
                || !appInfo.type.equals(AppInfo.OPK_PLUGIN)) {
            return false
        }

        val currentInfo = AppManger.getRPKByCharacter(currentCharacter);
        if (!currentInfo?.type.equals(AppInfo.OPK_HOST)) {
            return false;
        }

        this.handleRequest(reqId, PlatformDef.SWITCH_OPK, null, appId);
        return true;
    }


    /**
     * 切换角色
     *
     * @param params 参数：角色名称 + Type
     * @return
     */
    private fun switchCharacter(intent: String, params: String): Boolean {
        try {
            val info = IntentManager.getInstance().getIntentInfo(intent)
            val json = JSONObject(params)
            var name = json.optString("name")
            if (TextUtils.isEmpty(name)) {
                name = info.character
            }
            Log.i(TAG, " switchCharacter $name")
            ReactEnvironmentHelper.setCurrentCharacterName(name)
            val isAppValid = AppManger.isAppValid(name)
            return if (isAppValid) {
                Log.d(TAG, "$name: reload start")
                currentCharacter = name
                startRobot()
                Log.d(TAG, "$name: reload success")
                true
            } else {
                Log.d(TAG, "$name: reload failed")
                false
            }
        } catch (e: JSONException) {
            e.printStackTrace()
        }

        return false
    }

    override fun handleHWException(function: Int, type: String, data: String): Boolean {
        RNServerManager.getInstance().callbackServer.handleHWException(function, type, data)
        return false
    }

    override fun handleSuspend() {
        SpeechApi.getInstance().stopTTS();
        val foregroundApp = SpeechApi.getInstance().foregroundApp
        if (foregroundApp != null) {
            SpeechApi.getInstance().destroyApp(foregroundApp)
        }

        val callBtnServerIp = RobotSettingApi.getInstance()
            .getRobotString(CallButtonManager.CALL_BUTTON_SERVER_IP_KEY)
        val connectState = RobotSettingApi.getInstance().getRobotString(CallButtonManager.CALL_BUTTON_CONNECT_STATE);
        if (!TextUtils.isEmpty(callBtnServerIp) && TextUtils.equals(connectState, "1")) {
            CallButtonManager.getInstance().setRobotState("busy");
        }

        Log.d(TAG,"handleSuspend isCharging: " + RNServerManager.getInstance().isCharging());
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus()) {
            //充电中，杀掉RN进程
            if(!RNServerManager.getInstance().isInStandByOpkMode() || RNServerManager.getInstance().isCharging()){
                Log.d(TAG,"stopRnProcess when suspend");
                RNServerManager.getInstance().isSuspend = true
                RNServerManager.getInstance().stopRNProcess()
            }else{
                Log.d(TAG,"donot suspend in StandbyOpkMode");
            }
        } else {
            RNServerManager.getInstance().suspend()
        }
    }

    override fun handleRecovery() {
        Log.i(TAG, "handleRecovery")
//        SpeechApi.getInstance().moveToForeground(name)
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus()) {
            RNServerManager.getInstance().isSuspend = false
            startRobot()
        } else {
            startRobot()
            RNServerManager.getInstance().recovery()
        }

    }

    override fun handleApiConnection() {
        Log.i(TAG, "handleApiConnection")
    }

    override fun handleApiDisconnection() {
        Log.i(TAG, "handleApiDisconnection")
    }

    override fun setCharacterListener(listener: Character.CharacterListener?) {

    }

    override fun uninstall(params: String): Boolean {
        Log.d(TAG, "uninstall params: $params")
        CharacterManager.getInstance().unRegisterCharacter(name, com.ainirobot.platform.bean.CharacterInfo.PLATFORM_RN)
        return AppManger.removeAppId(name)
    }

    override fun getCharacterInfo(): String? {
        Log.d(TAG, "getCharacterInfo")

        if (!AppManger.isAppValid(name)) {
            return null
        }

        val opkBeanV2 = AppManger.getRPKByCharacter(name)
        opkBeanV2?.let {
            Log.d(TAG, "getCharacterInfo " + GsonUtil.toJson(CharacterInfo(it.appid, it.installTime)))
            return GsonUtil.toJson(CharacterInfo(it.appid, it.installTime))
        }
        return null
    }
}

data class CharacterInfo(val app_id: String, val install_time: Long)
