package com.ainirobot.platform.react.view;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ainirobot.platform.R;

import java.util.ArrayList;
import java.util.List;

public class GuidedQueryView extends RelativeLayout implements IRobotAnimation.RobotAnimationListener {

    private static final String TAG = GuidedQueryView.class.getSimpleName();

    private Context mContext;
    private TextView mTv_guide_query;
    private ImageView mIv_robot_icon;
    private AnimationDecoratorWrapper mDecorator;
    private IRobotAnimation mCircleRobotAnimation;
    private OnGuidedQueryViewListener mListener;
    private List<String> dataList = new ArrayList<>();

    public void setOnGuidedQueryViewListener(OnGuidedQueryViewListener listener) {
        this.mListener = listener;
    }

    public GuidedQueryView(Context context) {
        this(context, null);
    }

    public GuidedQueryView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        initializeView();
        initializeRobotIcon();
        initQueryAnimation();
    }

    private void initializeView() {
        LayoutInflater.from(mContext).inflate(R.layout.platform_view_guide_query_layout_rn, this);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        setLayoutParams(layoutParams);
        setPadding(60, 0, 60, 0);
        mTv_guide_query = (TextView) findViewById(R.id.tv_guide_query);
        setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(null != mListener){
                    mListener.onClick();
                }
            }
        });
        mIv_robot_icon = (ImageView) findViewById(R.id.iv_robot_icon);
    }

    private void initializeRobotIcon() {
        mCircleRobotAnimation = new RobotDrawable(mContext, mIv_robot_icon);
        mCircleRobotAnimation.setRobotAnimationListener(this);
        mCircleRobotAnimation.setWithAlpha(mTv_guide_query);
        mIv_robot_icon.setImageDrawable((Drawable) mCircleRobotAnimation);
        startDraw();
    }

    private void initQueryAnimation() {
        mDecorator = new AnimationDecoratorWrapper(new IAnimationDecorator() {
            @Override
            public View startAnimation() {
                return mTv_guide_query;
            }

            @Override
            public View stopAnimation() {
                return mTv_guide_query;
            }
        });
    }

    public void setQueryData(List<String> dataList, String headerText) {
        this.dataList = dataList;
        mDecorator.setQueryList(dataList, headerText);
    }

    public void enterAnimation() {
        Log.d(TAG, "enterAnimation");
        StringBuffer sb = new StringBuffer();
        if (dataList != null && dataList.size() > 0) {
            for (String data : dataList) {
                sb.append(data);
            }
        }
        Log.i(TAG, String.format("dataList size :%s", sb.toString()));
        if (dataList != null && dataList.size() > 0) {
            startDraw();
        }
    }

    public boolean enableEnterAnimation() {
        return dataList != null && dataList.size() > 0;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mCircleRobotAnimation.releaseAnimation();
    }

    public void outerAnimation() {
        Log.d(TAG, "outerAnimation");
        stopDraw();
        stopQueryAnimation();
    }

    @Override
    public void setVisibility(int visibility) {
        super.setVisibility(visibility);
        if (visibility == GONE) {
            outerAnimation();
        }
    }

    private void startDraw() {
        mCircleRobotAnimation.startDraw();
    }

    private void stopDraw() {
        mCircleRobotAnimation.stopDraw();
    }

    private void startQueryAnimation() {
        if (mDecorator != null) {
            mDecorator.startAnimation();
        }
    }

    private void stopQueryAnimation() {
        if (mDecorator != null) {
            mDecorator.stopAnimation();
        }
    }

    @Override
    public void animationEnd() {
        if (mListener != null) {
            mListener.animationEnd();
        }
    }

    @Override
    public void animationStart() {
        if (mListener != null) {
            mListener.animationStart();
        }
    }

    @Override
    public void enterAnimationEnd() {
        Log.i(TAG, "enterAnimationEnd");
        startQueryAnimation();
    }

    public interface OnGuidedQueryViewListener {
        void animationEnd();

        void animationStart();

        void onClick();
    }
}
