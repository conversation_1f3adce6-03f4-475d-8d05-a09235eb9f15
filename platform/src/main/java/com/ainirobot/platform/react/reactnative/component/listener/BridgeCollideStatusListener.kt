package com.ainirobot.platform.react.reactnative.component.listener

import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNCollideStatusListener
import com.facebook.react.bridge.WritableNativeMap

class BridgeCollideStatusListener : IRNCollideStatusListener.Stub {

    companion object {
        fun obtain(callbackId:Int): BridgeCollideStatusListener? {
            return BridgeCollideStatusListener(callbackId)
        }
    }

    var id = -1

    private constructor(id: Int) {
        this.id = id
    }

    override fun onCollideStatusChanged(data: String?) {
        triggerEvent("onCollideStatusChanged", data)
    }

    fun triggerEvent(event: String, data: String?) {
        if (id < 0) {
            return
        }
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putString("event", event)
        data?.apply { readableMap.putString("data", data) }
        ReactNativeEventEmitter.triggerEvent("onCollideStatusChangedListener", readableMap)
    }
}