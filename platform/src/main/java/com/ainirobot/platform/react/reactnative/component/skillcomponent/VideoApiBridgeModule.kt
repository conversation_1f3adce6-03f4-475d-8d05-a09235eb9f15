package com.ainirobot.platform.react.reactnative.component.skillcomponent

import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.component.listener.BridgeCommandListener
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.google.gson.Gson

class VideoApiBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    companion object {
        val TAG = "VideoApiBridgeModule"
        val mGson = Gson()
    }

    override fun getName(): String {
        return "VideoApi"
    }

    @ReactMethod
    fun answerAppResult(callbackId: Int,params: String, promise: Promise){
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.answerAppResult(params,BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getUserListWithVideo(callbackId: Int, params: String, promise: Promise){
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getUserListWithVideo(params, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun inviteCallApp(callbackId: Int,params: String, promise: Promise){
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.inviteCallApp(params,BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }
}
