package com.ainirobot.platform.react.reactnative.crashhandler.bridge;

import androidx.collection.ArrayMap;
import android.util.Log;

import java.util.Collections;
import java.util.Map;

/***
 ** <AUTHOR>
 ** @email <EMAIL>
 ** @date 2019/4/23
 *  ReactNative 环境的数据辅助类
 **/
public class ReactEnvironmentHelper {

    public static final String NAME = "Name";

    private static Map<String, Object> sCurrentCharacterState = Collections.synchronizedMap(new ArrayMap<String, Object>());
    private static Map<String, Object> sCurrentScenesState = Collections.synchronizedMap(new ArrayMap<String, Object>());

    /**
     * 初始化环境
     * */
    public static void clearEnvironmentState() {
        Log.i("ReactEnvironmentHelper", "clearEnvironmentState");
        sCurrentCharacterState.clear();
        sCurrentScenesState.clear();
    }

    /**
     * 设置角色名字
     * */
    public static void setCurrentCharacterName(String character) {
        Log.i("ReactEnvironmentHelper", "setCurrentCharacterName : " + character);
        sCurrentCharacterState.put(NAME, character);
    }
    /**
     * 设置场景名字
     * */
    public static void setCurrentScenesName(String scenes) {
        Log.i("ReactEnvironmentHelper", "setCurrentScenesName : " + scenes);
        sCurrentScenesState.put(NAME, scenes);
    }

    /**
     * 获取角色名字
     * */
    public static String getCurrentCharacterName() {
        if (sCurrentCharacterState == null) {
            return "unknow character name";
        }
        return (String) sCurrentCharacterState.get("Name");
    }

    /**
     * 获取场景名字
     * */
    public static String getCurrentScenesName() {
        if (sCurrentScenesState == null) {
            return "unknow scenes name";
        }
        return (String) sCurrentScenesState.get("Name");
    }
}
