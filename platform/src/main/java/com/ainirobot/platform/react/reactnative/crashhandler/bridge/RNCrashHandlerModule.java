
package com.ainirobot.platform.react.reactnative.crashhandler.bridge;

import android.util.Log;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;

public class RNCrashHandlerModule extends ReactContextBaseJavaModule {

    public RNCrashHandlerModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public void initialize() {
        super.initialize();
        ReactEnvironmentHelper.clearEnvironmentState();
    }

    @Override
    public String getName() {
        return "RNCrashHandler";
    }

    @ReactMethod
    public void putCurrentCharacterState(ReadableMap characterState) {
        ReactEnvironmentHelper.setCurrentCharacterName(characterState.getString(ReactEnvironmentHelper.NAME));
    }

    @ReactMethod
    public void putCurrentScenesState(ReadableMap scenesState) {
        ReactEnvironmentHelper.setCurrentScenesName(scenesState.getString(ReactEnvironmentHelper.NAME));
    }

}
