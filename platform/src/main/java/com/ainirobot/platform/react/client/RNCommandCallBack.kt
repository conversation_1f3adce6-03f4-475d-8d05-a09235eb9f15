package com.ainirobot.platform.react.client

/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

import android.os.Handler
import android.os.HandlerThread
import android.os.RemoteException
import com.ainirobot.platform.PlatformDef
import com.ainirobot.platform.react.EveActivity
import com.ainirobot.platform.react.reactnative.character.ReactCharacter
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.IRNCommandCallBack
import com.facebook.react.bridge.WritableNativeMap

/***
 * 所有指令的Callback，例如语音指令的。
 * OPK进程端处理逻辑
 */

class RNCommandCallBack : IRNCommandCallBack.Stub() {
    override fun handleHWException(function: Int, type: String?, data: String?): Boolean {
        CallBackHandler.instance.getHandler().post {
            val readableMap = WritableNativeMap()
            readableMap.putInt("function", function)
            readableMap.putString("type", type)
            readableMap.putString("data", data)
            ReactNativeEventEmitter.triggerEvent(ReactCharacter.hwException, readableMap)
        }

        return false
    }

    @Throws(RemoteException::class)
    override fun onNewRequest(reqId: Int, intent: String?, text: String?, params: String?): Boolean {
        CallBackHandler.instance.getHandler().post {
            val readableMap = WritableNativeMap()
            readableMap.putInt(PlatformDef.MSG_BUNDLE_ID, reqId)
            text?.apply {
                readableMap.putString(PlatformDef.MSG_BUNDLE_TEXT, this)
            }
            params?.apply {
                readableMap.putString(PlatformDef.MSG_BUNDLE_PARAM, this)
            }
            intent?.apply {
                readableMap.putString(PlatformDef.MSG_BUNDLE_INTENT, this)
            }
            ReactNativeEventEmitter.triggerEvent("onSpeechInfo", readableMap)
        }

        return false
    }

    @Throws(RemoteException::class)
    override fun suspend() {
//        CallBackHandler.instance.getHandler().post {
//            ReactNativeEventEmitter.triggerEvent("_CHARACTER_SUSPEND_", "")
//        }

        EveActivity.getActivity().get()!!.onSuspend()
    }

    @Throws(RemoteException::class)
    override fun recovery() {
//        CallBackHandler.instance.getHandler().post {
//            ReactNativeEventEmitter.triggerEvent("_CHARACTER_RECOVERY_", "")
//        }

        EveActivity.getActivity().get()!!.onRecovery()
    }

}

class CallBackHandler private constructor() {
    private var handler: Handler
    companion object {
        val instance = CallBackHandler()
    }

    init {
        val mThread = HandlerThread("loop text")
        mThread.start()
        handler = Handler(mThread.looper)
    }

    fun getHandler(): Handler {
        return handler
    }
}
