/*
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.ainirobot.platform.react.view.animator;

import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import androidx.annotation.NonNull;
import android.util.Log;

import com.ainirobot.platform.react.view.animator.bean.DrawableBean;
import com.ainirobot.platform.react.view.animator.bean.LayerBean;
import com.ainirobot.platform.react.view.animator.control.AnimatorState;
import com.ainirobot.platform.react.view.animator.control.IInvalidateObserver;
import com.ainirobot.platform.react.view.animator.control.InvalidateManager;
import com.ainirobot.platform.react.view.animator.control.Scaling;

import java.util.ArrayList;

/**
 * 在此写用途
 *
 * @FileName: com.ainirobot.platform.ui.animator.AnimatorDrawable.java
 * @author: Orion
 * @date: 2018-11-29 15:29
 */
public class AnimatorDrawable extends LayerDrawable implements BaseDrawable.OnDrawListener, IInvalidateObserver {
    private static final String TAG = AnimatorDrawable.class.getSimpleName();

    private static final int LOOP_FOREVER = -1;

    private static DrawableBean mDrawableBean;

    private AnimatorState mAnimatorState;
    private BaseDrawable[] mDrawables;
    private int mLayerSum = -1;
    private int mCurLoopTimes;

    private AnimatorDrawableListener mListener;

    public static AnimatorDrawable create(@NonNull DrawableBean drawableBean, AnimatorDrawableListener listener) throws IllegalArgumentException {

        if (null == drawableBean) {
            throw new IllegalArgumentException("can't create AnimatorDrawable by null bean!");
        }

        if (null == listener) {
            throw new IllegalArgumentException("can't create AnimatorDrawable by null listener!");
        }

        ArrayList<LayerBean> layers = drawableBean.getLayers();
        if (null == layers || layers.isEmpty()) {
            throw new IllegalArgumentException("can't create AnimatorDrawable by null layer bean!");
        }

        mDrawableBean = drawableBean;

        BaseDrawable[] drawables = new BaseDrawable[layers.size()];

        int index = 0;
        for (LayerBean bean : layers) {
            BaseDrawable drawable = null;
            switch (AnimatorConfig.LayerType.valueOf(bean.getType())) {
                case frame: {
                    /**
                     * ota2 版本中, 会触发两次frame的draw, 导致速度快了两倍.
                     * 解决这个问题后, 用版本号对已发的包做兼容.
                     * */
                    if ("1.0".equals(mDrawableBean.getV())) {
                        bean.setSpeed(bean.getSpeed() / 2);
                    }
                    drawable = new FrameDrawable(bean);
                    break;
                }
                case json: {
                    break;
                }
                case property: {
                    drawable = new PropertyDrawable(bean);
                    break;
                }
                default:
                    throw new IllegalArgumentException("can't create AnimatorDrawable by unknown layer type!");
            }

            drawables[index++] = drawable;
        }

        AnimatorDrawable animatorDrawable = new AnimatorDrawable(drawables);
        animatorDrawable.initialize(listener);
        return animatorDrawable;
    }


    private AnimatorDrawable(@NonNull BaseDrawable[] layers) {
        super(layers);
        mAnimatorState = AnimatorState.idle;
        mDrawables = layers;
    }

    @Override
    public void draw(Canvas canvas) {
        for (BaseDrawable drawable : mDrawables){
            drawable.onScaling(new Scaling().toScaling(mDrawableBean.getW(), mDrawableBean.getH(), canvas));
            drawable.draw(canvas);
        }
    }

    @Override
    public void onPrepared(Drawable self) {
        for (Drawable drawable : mDrawables) {
            if (self == drawable) {
                mLayerSum--;
                break;
            }
        }

        Log.i(TAG, "onPrepared mLayerSum: " + mLayerSum + "  self: " + self.getClass().getName());
        mListener.onPrepared();
    }

    @Override
    public void onError(Drawable self, String message) {
        mListener.onError(String.format("%s in %s", message, self.getClass().getName()));
    }

    ;

    @Override
    public void onEnd(Drawable self) {
        for (int i = 0; i < mDrawables.length; i++) {
            if (mDrawables[i] == self && mDrawableBean.getBase() != i) {
                return;
            }
        }

        if (mCurLoopTimes >= mDrawableBean.getLoops() && mDrawableBean.getLoops() != LOOP_FOREVER) {
            cancel();
            mListener.onEnd();
        }
    }

    @Override
    public void onInvalidate() {
        if (AnimatorState.playing == mAnimatorState) {
            AnimatorDrawable.this.invalidateSelf();
        }
    }

    public void start() {
        Log.d(TAG, "start");
        mAnimatorState = AnimatorState.playing;
        InvalidateManager.getInstance().startObserve(this);
    }

    public void pause() {
        Log.d(TAG, "pause");
        mAnimatorState = AnimatorState.idle;
    }

    public void resume() {
        Log.d(TAG, "resume");
        mAnimatorState = AnimatorState.playing;
    }

    public void cancel() {
        Log.d(TAG, "cancel");
        mAnimatorState = AnimatorState.idle;
        InvalidateManager.getInstance().cancelObserve(this);
        for (BaseDrawable drawable : mDrawables) {
            drawable.reset();
        }
    }

    /**
     * # mark ------------private function
     */
    private void initialize(@NonNull AnimatorDrawableListener l) {
        mCurLoopTimes = 0;
        mListener = l;
        for (BaseDrawable drawable : mDrawables) {
            drawable.initialize(this);
        }
    }

    /**
     * # mark ------------class or interface
     */
    public static interface AnimatorDrawableListener {
        public void onPrepared();

        public void onEnd();

        public void onError(String msg);
    }
}
