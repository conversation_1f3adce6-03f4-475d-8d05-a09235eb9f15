package com.ainirobot.platform.react.reactnative.component.uicomponent.camerafilter.loader;

import android.graphics.ImageFormat;
import android.media.Image;
import android.media.ImageReader;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.util.Log;
import android.view.Surface;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.surfaceshare.SurfaceShareBean;
import com.ainirobot.coreservice.client.surfaceshare.SurfaceShareError;
import com.ainirobot.gpuimage.util.Rotation;
import com.ainirobot.platform.react.client.RNClientManager;
import com.ainirobot.platform.react.reactnative.component.uicomponent.camerafilter.CameraFilterError;
import com.ainirobot.platform.rn.listener.IRNApiListener;
import com.ainirobot.platform.rn.listener.IRNSurfaceShareListener;
import com.ainirobot.platform.utils.ImageUtils;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

public class CameraShareLoader extends CameraLoader {

    private static final String TAG = CameraShareLoader.class.getSimpleName();

    private static final String RESOLUTION_480P = "480P";
    private static final String RESOLUTION_720P = "720P";
    private static final String RESOLUTION_768P = "768P";
    private static final int MAX_IMAGE = 4;

    private ImageReader mImageReader;
    private Surface mSurface;
    private SurfaceShareBean mBean;
    private HandlerThread mBackgroundThread;
    private HandlerThread mDrawThread;
    private ReaderHandler mHandler;
    private Handler mBackgroundHandler;
    private volatile boolean mIsPaused;
    private int mWidth = 1024;
    private int mHeight = 768;
    private Gson mGson;

    public CameraShareLoader() {
        mIsPaused = true;
        mGson = new Gson();
    }

    private static class ReaderHandler extends Handler {

        private CameraShareLoader mLoader;

        ReaderHandler(CameraShareLoader loader, Looper looper) {
            super(looper);
            mLoader = loader;
        }

        @Override
        public void handleMessage(Message msg) {
            if (msg != null) {
                if (mLoader != null && mLoader.mOnPreviewFrameListener != null) {
                    mLoader.mOnPreviewFrameListener
                            .onPreviewFrame((byte[]) msg.obj, msg.arg1, msg.arg2);
                }
            }
        }
    }

    @Override
    public synchronized void onResume() {
        Log.d(TAG, "onResume mIsPaused: " + mIsPaused);
        if (mIsPaused) {
            mIsPaused = false;
            getVisionResolution();
        }
    }

    private void startBackgroundThread() {
        if (mBackgroundThread != null && mBackgroundHandler != null) {
            Log.d(TAG, "startBackgroundThread has already start a mBackgroundThread");
            return;
        }
        mBackgroundThread = new HandlerThread("CameraExternalLoaderBackground");
        mBackgroundThread.start();
        mBackgroundHandler = new Handler(mBackgroundThread.getLooper());
    }

    private void startDrawThread() {
        mDrawThread = new HandlerThread("CameraExternalLoaderDraw");
        mDrawThread.start();
        mHandler = new ReaderHandler(this, mDrawThread.getLooper());
    }

    @Override
    public synchronized void onPause() {
        Log.d(TAG, "onPause mImageReader: " + mImageReader);
        mIsPaused = true;
        if (mImageReader != null) {
            if (RNClientManager.Companion.getInstance() != null
                    && RNClientManager.Companion.getInstance().getDeviceManager() != null) {
                try {
                    RNClientManager.Companion.getInstance().getDeviceManager()
                            .abandonImageFrame(mGson.toJson(mBean));
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
            Log.d(TAG, "onPause before release");
            stopBackgroundThread();
            stopDrawThread();
            mImageReader.close();
            mImageReader = null;
            if (mSurface != null) {
                mSurface.release();
                mSurface = null;
            }
            mBean = null;
        }
    }

    private void getVisionResolution() {
        Log.d(TAG, "getVisionResolution");
        if (ProductInfo.isMiniProduct() || ProductInfo.isCarryProduct()) {
            mWidth = 1280;
            mHeight = 720;
            processCameraShare();
            return;
        }
        Log.d(TAG, "getVisionResolution: mWidth=" + mWidth + " mHeight=" + mHeight);

        if (RNClientManager.Companion.getInstance() != null
                && RNClientManager.Companion.getInstance().getApiManager() != null) {
            try {
                RNClientManager.Companion.getInstance().getApiManager()
                        .getVisionResolution(new IRNApiListener.Stub() {
                            @Override
                            public void onResult(int result, String message, String extraData) {
                                Log.d(TAG, "getVisionResolution result: " + result
                                        + ", message: " + message + ", mIsPaused: " + mIsPaused);
                                try {
                                    JSONObject jsonObject = new JSONObject(message);
                                    String resolution = jsonObject.optString("resolution");
                                    if (!mIsPaused) {
                                        if (RESOLUTION_480P.equalsIgnoreCase(resolution)) {
                                            mWidth = 640;
                                            mHeight = 480;
                                            processCameraShare();
                                        } else if (RESOLUTION_720P.equalsIgnoreCase(resolution)) {
                                            mWidth = 1280;
                                            mHeight = 720;
                                            processCameraShare();
                                        } else if (RESOLUTION_768P.equalsIgnoreCase(resolution)) {
                                            mWidth = 1024;
                                            mHeight = 768;
                                            processCameraShare();
                                        } else {
                                            if (mOnPreviewFrameListener != null) {
                                                mOnPreviewFrameListener.onError(CameraFilterError
                                                        .ERROR_SHARE_GET_RESOLUTION_FAILED, message);
                                            }
                                        }
                                    }
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                    if (mOnPreviewFrameListener != null) {
                                        mOnPreviewFrameListener.onError(CameraFilterError
                                                .ERROR_SHARE_GET_RESOLUTION_FAILED, message);
                                    }
                                }
                            }

                            @Override
                            public void onError(int errorCode, String message, String extraData) {
                            }

                            @Override
                            public void onStatusUpdate(int status, String data, String extraData) {
                            }
                        });
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    private void processCameraShare() {
        Log.d(TAG, "processCameraShare mImageReader: " + mImageReader + ", mWidth: " + mWidth
                + ", mHeight: " + mHeight);
        if (mImageReader != null) {
            mImageReader.close();
            mImageReader = null;
        }
        if(mOnPreviewFrameListener != null){
            mOnPreviewFrameListener.onPreviewChange(mWidth, mHeight);
        }
        startBackgroundThread();
        mImageReader = ImageReader.newInstance(mWidth, mHeight, ImageFormat.YUV_420_888,
                MAX_IMAGE);
        mImageReader.setOnImageAvailableListener(new ImageReader.OnImageAvailableListener() {
            @Override
            public void onImageAvailable(ImageReader reader) {
                Image image = null;
                try {
                    image = reader.acquireLatestImage();
                    if (image != null) {
                        if (!mIsPaused && mHandler != null) {
                            byte[] bytes = ImageUtils.generateNV21Data(image);
                            Message message = new Message();
                            message.arg1 = image.getWidth();
                            message.arg2 = image.getHeight();
                            message.obj = bytes;
                            mHandler.sendMessage(message);
                        }
                    }
                } catch (IllegalStateException e) {
                    e.printStackTrace();
                } finally {
                    if (image != null) {
                        image.close();
                    }
                }
            }
        }, mBackgroundHandler);
        mBean = new SurfaceShareBean();
        mBean.setName("cameraShareUi");
        mBean.setPriority(10);
        int result = -1;
        if (RNClientManager.Companion.getInstance() != null
                && RNClientManager.Companion.getInstance().getDeviceManager() != null) {
            try {
                mSurface = mImageReader.getSurface();
                result = RNClientManager.Companion.getInstance().getDeviceManager().requestImageFrame(mSurface,
                        mGson.toJson(mBean),
                        new IRNSurfaceShareListener.Stub() {
                            @Override
                            public void onStatusUpdate(int status, String message) {
                                Log.d(TAG, "requestImageFrame onStatusUpdate status: " + status
                                        + ", message: " + message);
                                startDrawThread();
                                if (mOnPreviewFrameListener != null) {
                                    mOnPreviewFrameListener.onStatusUpdate(status);
                                }
                            }

                            @Override
                            public void onError(int error, String message) {
                                Log.d(TAG, "requestImageFrame onError: " + error
                                        + ", message: " + message);
                                switch (error) {
                                    case SurfaceShareError.ERROR_CONNECT_SERVER_TIMEOUT:
                                        error = CameraFilterError
                                                .ERROR_SHARE_CONNECT_SERVER_TIMEOUT;
                                        break;
                                    case SurfaceShareError.ERROR_SERVER_EXIT:
                                        error = CameraFilterError
                                                .ERROR_SHARE_SERVER_EXIT;
                                        break;
                                    case SurfaceShareError.ERROR_SET_STREAM_SURFACE_FAILED:
                                        error = CameraFilterError
                                                .ERROR_SHARE_SET_STREAM_SURFACE_FAILED;
                                        break;
                                    case SurfaceShareError.ERROR_SERVER_EXIT_ABNORMALLY:
                                        error = CameraFilterError
                                                .ERROR_SHARE_SERVER_EXIT_ABNORMALLY;
                                        break;
                                    case SurfaceShareError.ERROR_SURFACE_SHARE_PREEMPTED:
                                        error = CameraFilterError
                                                .ERROR_SHARE_SURFACE_SHARE_PREEMPTED;
                                        break;
                                    case SurfaceShareError.ERROR_SURFACE_SHARE_USED:
                                        error = CameraFilterError
                                                .ERROR_SHARE_SURFACE_SHARE_USED;
                                        break;
                                    default:
                                        error = CameraFilterError
                                                .ERROR_SHARE_SET_STREAM_SURFACE_FAILED;
                                        break;
                                }
                                if (mOnPreviewFrameListener != null) {
                                    mOnPreviewFrameListener.onError(error, message);
                                }
                            }
                        });
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        Log.d(TAG, "requestSurfaceShare result: " + result);
        if (result != 0 && mOnPreviewFrameListener != null) {
            mOnPreviewFrameListener.onError(SurfaceShareError
                    .ERROR_SET_STREAM_SURFACE_FAILED, null);
        }
    }

    private void stopBackgroundThread() {
        if (mBackgroundThread == null) {
            Log.i(TAG, "the background thread is null");
            return;
        }

        Log.i(TAG, "stopBackgroundThread thread id:" + mBackgroundThread.getName() + "," + mBackgroundThread.getThreadId());
        mBackgroundThread.quitSafely();
        try {
            mBackgroundThread.join(50);
            mBackgroundThread = null;
            mBackgroundHandler = null;
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
    private void stopDrawThread() {
        if (mDrawThread == null) {
            Log.i(TAG, "mDrawThread is null");
            return;
        }
        mDrawThread.quitSafely();
        try {
            mDrawThread.join(50);
            mDrawThread = null;
            mHandler = null;
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Override
    public float getRadio() {
        return ProductInfo.isMiniProduct() || ProductInfo.isCarryProduct() ? (mWidth * 1.0f / mHeight) : (mHeight * 1.0f / mWidth);
    }
}
