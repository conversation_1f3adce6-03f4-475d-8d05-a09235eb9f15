package com.ainirobot.platform.react.reactnative.component.uicomponent.camera.event

import com.ainirobot.platform.bean.ResponseState
import com.facebook.react.bridge.Arguments
import com.facebook.react.uimanager.events.Event
import com.facebook.react.uimanager.events.RCTEventEmitter

/**
 * @date: 2019-02-27
 * @author: lumeng
 * @desc: 封装camera回调预览事件
 */
class PreviewEvent(viewId:Int,private var state: ResponseState) : Event<PreviewEvent>(viewId) {

    companion object {
        /**
         * 原生组件和js组件的evnent映射
         * 对应 CameraViewManager 中 addEventEmitters 方法里 CameraView 的 PreviewListener 的回调方法
         * 对应 JS 层组件的方法属性 _onPreviewState
         */
        const val EVENT_NAME = "_onPreviewState"
    }

    override fun getEventName(): String {
        return EVENT_NAME
    }

    /**
     * state 为拍摄状态
     * @see ResponseState
     */
    override fun dispatch(rctEventEmitter: RCTEventEmitter) {
        val map = Arguments.createMap()
        map.putString("state",state.toString())
        rctEventEmitter.receiveEvent(viewTag, eventName, map)
    }
}
