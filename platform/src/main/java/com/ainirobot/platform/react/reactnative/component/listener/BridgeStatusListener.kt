package com.ainirobot.platform.react.reactnative.component.listener

import com.ainirobot.coreservice.client.StatusListener
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.facebook.react.bridge.WritableNativeMap

class BridgeStatusListener : StatusListener {

    companion object {
        fun obtain(callbackId:Int): BridgeStatusListener? {
            return if(callbackId < 0){
                null
            } else {
                BridgeStatusListener(callbackId)
            }
        }
    }

    var id = -1
    private constructor(id:Int) {
        this.id = id
    }


    override fun onStatusUpdate(type: String?, data: String?) {
        super.onStatusUpdate(type, data)
        triggerEvent(type,data)
    }

    fun triggerEvent(type: String?,data:String?) {
        if (id < 0) {
            return
        }
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        type?.apply { readableMap.putString("type", type) }
        data?.apply { readableMap.putString("data", data) }
        ReactNativeEventEmitter.triggerEvent("onStatusUpdate", readableMap)
    }

}