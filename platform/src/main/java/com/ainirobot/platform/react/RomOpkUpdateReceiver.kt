package com.ainirobot.platform.react

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.util.Log
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.PlatformDef
import com.ainirobot.platform.control.ControlManager
import com.ainirobot.platform.character.CharacterManager
import com.ainirobot.platform.react.reactnative.character.ReactCharacter
import com.ainirobot.platform.react.server.control.RNServerManager
import com.ainirobot.platform.utils.LocalUtils
import org.json.JSONException
import org.json.JSONObject
import java.util.concurrent.Executors

class RomOpkUpdateReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "RomOpkUpdateReceiver"
        private const val BROADCAST_ACTION = "com.ainirobot.moduleapp.rom_opk_update"
        private const val OPK_PATH = "path"
        private const val OPK_AUTO_START = "auto_start"
        private const val OPK_FORCE_START = "force_start"
        private const val OB_OPK_SWITCH = "ob_opk_switch"
    }

    private val poolExecutor = Executors.newSingleThreadExecutor { r -> Thread(r, "rom_opk_verity_thread") }

    override fun onReceive(context: Context?, intent: Intent?) {
        Log.i(TAG, "intent=$intent")
        if (poolExecutor.isShutdown) {
            Log.e(TAG, "rom opk verity thread pool already shut down," +
                    " don't invoke two time \"RomOpkUpdateReceiver\" in process")
            return
        }
        poolExecutor.execute {
            if (intent == null || intent.action == null || intent.action != BROADCAST_ACTION) {
                Log.i(TAG, "intent exception!")
                return@execute
            }
            updateRomOpk(intent)
            if (!poolExecutor.isShutdown) {
                Log.i(TAG, " update Rom Opk finish,shutdown rom_opk_verity_thread pool")
                poolExecutor.shutdown()
            }
        }

    }

    private fun updateRomOpk(intent: Intent) {
        val opkPath = intent.getStringExtra(OPK_PATH)
        if (TextUtils.isEmpty(opkPath)) {
            Log.e(TAG, "rom opk path is empty!")
            return
        }
        Log.i(TAG, "rom opk path:$opkPath")
        val appBean: AppBeanV2 = OPKHelper.getAppBeanForOPKFile(opkPath, "rom_opk_update") ?: return

        val bundleIndexFilePath = appBean.bundleIndexFilePath
        val bundleBizFilePath = appBean.bundleBizFilePath
        val bundlePlatformFilePath = appBean.bundlePlatformFilePath
        if (bundleIndexFilePath == null && !(bundleBizFilePath != null && bundlePlatformFilePath != null)) {
            Log.i(TAG, "appBean format exception")
            return
        }
        AppManger.addApp(bundleIndexFilePath, bundleBizFilePath, bundlePlatformFilePath, appBean.rpkBean)
        Log.i(TAG, "rom opk info store finish")

        val isRunning = ReactCharacter.isReactRunning
        Log.i(TAG, "isRunning:$isRunning")

        val appId = appBean.rpkBean.appid
        val currentCharacter = ReactCharacter.currentCharacter

        Log.i(TAG, "appId=$appId path=$bundleBizFilePath current= $currentCharacter")
        if (isRunning && currentCharacter == appId) {
            Log.i(TAG, " hot reload rom opk ")
            switchCharacter(appId)
        } else {
            Log.d(TAG, "register rom opk :$appId")
            registerCharacter(appId)
        }
        val isAutoStart = intent.getBooleanExtra(OPK_AUTO_START, false)
        val isForceStart = intent.getBooleanExtra(OPK_FORCE_START, false)

        Log.i(TAG, "rom opk isAutoStart:$isAutoStart ")
        //force start
        if (isAutoStart && isForceStart) {
            Log.i(TAG, "force start")
            forceSwitchCharacter(appId)
            return
        }
        //not force start
        if (isAutoStart) {
            Log.i(TAG, "normal start")
            normalSwitchCharacter(appId)
        }
    }

    private fun registerCharacter(appid: String): Boolean {
        if (appid.isNullOrEmpty()) {
            Log.d(TAG, "registerCharacter appid is empty")
            return false

        }
        Log.i(TAG, "registerCharacter")
        val characterManager = CharacterManager.getInstance()
        val character = ReactCharacter(appid)
        return characterManager.registerCharacterFromRN(character.getName(), character)
    }

    private fun switchCharacter(characterName: String) {
        try {
            RNServerManager.getInstance().isUpdate = true
            val json = JSONObject()
            json.put("name", characterName)
            ControlManager.handleRequest(0, PlatformDef.SWITCH_CHARACTER, "", json.toString())
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    private fun normalSwitchCharacter(characterName: String) {
        val defaultCharacterName = LocalUtils.getGlobalSettings(BaseApplication.getApplication(), OB_OPK_SWITCH, "")
        Log.i(TAG, "normalSwitchCharacter  defaultCharacterName=$defaultCharacterName")
        if (TextUtils.isEmpty(defaultCharacterName)) {
            switchCharacter(characterName)
        } else {
            Log.i(TAG, "find user switch character,don't switch rom opk")
        }
    }

    private fun forceSwitchCharacter(characterName: String) {
        switchCharacter(characterName)
    }

}
