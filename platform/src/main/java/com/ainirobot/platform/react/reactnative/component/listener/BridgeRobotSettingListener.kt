package com.ainirobot.platform.react.reactnative.component.listener

import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNRobotSettingListener
import com.facebook.react.bridge.WritableNativeMap

class BridgeRobotSettingListener : IRNRobotSettingListener.Stub {

    companion object {
        fun obtain(callbackId: Int, keys: ArrayList<Any>): BridgeRobotSettingListener? {
            return BridgeRobotSettingListener(callbackId, keys)
        }
    }

    var id = -1
    var keys = ArrayList<Any>()

    private constructor(id: Int, keys: ArrayList<Any>) {
        this.id = id
        this.keys = keys
    }

    override fun onRobotSettingChanged(key: String?) {
        triggerEvent("onRobotSettingChanged", key)
    }

    fun triggerEvent(event: String, key: String?) {
        if (id < 0 || key == null) {
            return
        }
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putString("key", key)
        readableMap.putString("event", event)
        ReactNativeEventEmitter.triggerEvent("robotSettingChanged", readableMap)
    }
}