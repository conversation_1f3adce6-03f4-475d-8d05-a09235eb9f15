package com.ainirobot.platform.react.reactnative

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.util.Log
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.BuildConfig
import com.ainirobot.platform.control.ControlManager
import com.ainirobot.platform.react.client.RNClientManager
import com.facebook.react.ReactInstrumentation
import org.json.JSONObject
import java.util.*

/***
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-28
 * 用于接收外部开启RN环境的debug状态
 */
class RNDumpReceiver : BroadcastReceiver() {

    companion object {

        const val REQ_TAKE_SNAPSHOT = "take_snapshot";
        const val REQ_STOP_SNAPSHOT = "stop_snapshot";

        const val REQ_DUMP_MEM = "dump_mem";
        const val REQ_STOP_DUMP = "stop_dump";

        const val ACTION_TAKE_SNAPSHOT = "com.ainirobot.moduleapp.take.snapshot";
        const val ACTION_STOP_SNAPSHOT = "com.ainirobot.moduleapp.stop.snapshot";
        const val ACTION_DUMP_MEMINFO = "com.ainirobot.moduleapp.dump.meminfo";
        const val ACTION_STOP_DUMP = "com.ainirobot.moduleapp.stop.dump";

        var mSnapshotTimer: Timer? = null;

        const val TAG = "RNDumpReceiver"

    }

    fun takeSnapshot(dir: String?, interval: Int) {
        Log.d(TAG, "Start heap dump : " + dir + "   " + interval);
        var snapshotDir = dir;
        if (snapshotDir == null) {
            snapshotDir = "/sdcard/snapshot";
        }

        if (snapshotDir.endsWith("/")) {
            snapshotDir = snapshotDir.substring(0, snapshotDir.length - 1);
        }

        dumpMemInfo(snapshotDir)

        if (interval > 0) {
            mSnapshotTimer?.cancel()
            mSnapshotTimer = Timer()
            mSnapshotTimer!!.schedule(object : TimerTask() {
                override fun run() {
                    dumpMemInfo(snapshotDir)
                }
            }, interval * 60 * 1000L, interval * 60 * 1000L);
        }
    }

    fun stopSnapshot() {
        mSnapshotTimer?.cancel()
    }

    private fun dumpMemInfo(snapshotDir: String) {
        if (BuildConfig.HERMES) {
            ReactInstrumentation.dumpMemInfo(snapshotDir)
        } else {
            val data = JSONObject();
            data.put("dir", snapshotDir);
            data.put("interval", -1);
            RNClientManager.instance?.mCommandCallBack?.onNewRequest(
                0,
                REQ_TAKE_SNAPSHOT,
                null,
                data.toString()
            )
        }
    }

    override fun onReceive(context: Context, intent: Intent?) {
        if (intent != null) {
            when (intent.action) {
                ACTION_TAKE_SNAPSHOT -> {
                    var dir = intent.getStringExtra("dir");
                    var interval = intent.getIntExtra("interval", -1);
                    takeSnapshot(dir, interval);
                };
                ACTION_STOP_SNAPSHOT -> {
                    stopSnapshot();
                };
                ACTION_DUMP_MEMINFO -> {
                    var interval = intent.getIntExtra("interval", 1);
                    var scene = intent.getStringExtra("scene");
                    var view = intent.getStringExtra("view");
                    var data = JSONObject();
                    data.put("interval", interval);
                    data.put("scene", scene);
                    data.put("view", view);
                    RNClientManager.instance?.mCommandCallBack?.onNewRequest(0, REQ_DUMP_MEM, null, data.toString());
                };
                ACTION_STOP_DUMP -> {
                    RNClientManager.instance?.mCommandCallBack?.onNewRequest(0, REQ_STOP_DUMP, null, null);
                }
            }

        }
    }
}
