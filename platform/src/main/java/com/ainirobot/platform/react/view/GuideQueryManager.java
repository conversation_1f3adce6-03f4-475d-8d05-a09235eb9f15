package com.ainirobot.platform.react.view;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.util.Log;

import java.util.List;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

public class GuideQueryManager implements GuidedQueryView.OnGuidedQueryViewListener {

    private static final String TAG = "GuideQueryManager";
    private boolean showGuideQuery = false;
    private GuidedQueryView mGuidedQueryView;
    private OnGuideQueryManagerListener mListener;
    private boolean isFirstAnimation = false;
    private boolean isShowQueryByUser = false;

    public void setOnGuideQueryManagerListener(OnGuideQueryManagerListener listener) {
        this.mListener = listener;
    }

    public GuideQueryManager(GuidedQueryView guidedQueryView) {
        this.mGuidedQueryView = guidedQueryView;
        mGuidedQueryView.setOnGuidedQueryViewListener(this);
    }

    public boolean isShowGuideState() {
        return showGuideQuery;
    }

    public void setVisibilityByUser(boolean isShow) {
        isShowQueryByUser = isShow;
    }

    public boolean isVisibilityByUser() {
        return isShowQueryByUser;
    }

    public boolean isShowVisibility() {
        return mGuidedQueryView.getVisibility() == VISIBLE;
    }

    public void setGuideConfig(boolean isShow) {
        Log.i(TAG, String.format("setGuideConfig : %b", showGuideQuery));
        showGuideQuery = isShow;
        if(!isShow){
            showGuideQueryView(false);
        }
    }

    @Override
    public void animationEnd() {
        if (mListener != null) {
            mListener.hideGuideQuery(true);
        }
    }

    @Override
    public void animationStart() {
        if (mListener != null) {
            mListener.hideGuideQuery(false);
        }
    }

    @Override
    public void onClick(){
        if(null != mListener){
            mListener.onClick();
        }
    }

    public void setQueryData(List<String> dataList, String headerText) {
        mGuidedQueryView.setQueryData(dataList, headerText);
    }

    private void startAnimation() {
        boolean enableEnterAnimation = mGuidedQueryView.enableEnterAnimation();
        Log.i(TAG, String.format("startAnimation showGuideQuery:%s, enableEnterAnimation :%b,isShowGuideState:%b", showGuideQuery, enableEnterAnimation, isShowGuideState()));
        if (enableEnterAnimation) {
            isFirstAnimation = false;
            showGuideQueryAnimation();
        }
    }

    private void showGuideQueryAnimation() {
        ObjectAnimator animator = ObjectAnimator.ofFloat(mGuidedQueryView, "alpha", 0.0f, 1.0f);
        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                if (animation.getAnimatedFraction() > 0.5f && !isFirstAnimation) {
                    mGuidedQueryView.enterAnimation();
                    isFirstAnimation = true;
                }
            }
        });
        animator.setDuration(200).start();
    }

    private void stopAnimation() {
        mGuidedQueryView.outerAnimation();
    }

    public void showGuideQueryView(boolean visibility) {
        Log.i(TAG, String.format("showGuideQueryView :%b ,currentConfig :%b", visibility, showGuideQuery));
        if (showGuideQuery) {
            if (isShowVisibility() && visibility) {
                return;
            }
            mGuidedQueryView.setVisibility(visibility ? VISIBLE : GONE);
            if (visibility) {
                startAnimation();
            } else {
                stopAnimation();
            }
        } else {
            mGuidedQueryView.setVisibility(GONE);
        }
    }

    public interface OnGuideQueryManagerListener {
        void hideGuideQuery(boolean isShow);
        void onClick();
    }
}
