package com.ainirobot.platform.react.server.impl;/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.platform.react.server.control.ComponentManager;
import com.ainirobot.platform.rn.IComponentRegistry;
import com.ainirobot.platform.rn.listener.IRNFinishListener;
import com.ainirobot.platform.rn.listener.IRNStatusListener;

public class RNComponentServer extends IComponentRegistry.Stub {

    public static final String TAG = "RNComponentServer";

    @Override
    public void start(String componentName, String uid, String param) throws RemoteException {
        Log.i(TAG, "start componentName:" + componentName + ",uid:" + uid + ",param:" + param);
        ComponentManager.getInstance().start(componentName, uid, param);
    }

    @Override
    public void stop(String componentName, String uid, long timeout) throws RemoteException {
        Log.i(TAG, "stop componentName:" + componentName + ",uid:" + uid + ",timeout:" + timeout);
        ComponentManager.getInstance().stop(componentName, uid, timeout);
    }

    @Override
    public void setStatusListener(String componentName, String uid, IRNStatusListener statusListener)
            throws RemoteException {
        Log.i(TAG, "setStatusListener ,uid:" + uid + ",Thread:" + Thread.currentThread().toString());
        ComponentManager.getInstance().setStatusListener(componentName, uid, statusListener);
    }

    @Override
    public void setFinishListener(String componentName, String uid, IRNFinishListener finishListener)
            throws RemoteException {
        Log.i(TAG, "setFinishListener componentName: " + componentName + ", uid:" + uid);
        ComponentManager.getInstance().setComponentListener(componentName, uid, finishListener);
    }

    @Override
    public void updateParams(String componentName, String uid, String intent, String param)
            throws RemoteException {
        Log.i(TAG, "updateParams componentName: " + componentName + ", uid:" + uid);
        ComponentManager.getInstance().updateParams(componentName, uid, intent, param);
    }

    @Override
    public void onCatalystInstanceDestroy(String componentName) throws RemoteException {

    }
}
