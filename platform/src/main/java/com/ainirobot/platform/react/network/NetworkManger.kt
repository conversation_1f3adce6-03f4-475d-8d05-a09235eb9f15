package com.ainirobot.platform.react.network

import androidx.annotation.WorkerThread
import android.util.Log
import com.ainirobot.platform.react.network.converter.JsonConverterFactory
import com.ainirobot.platform.utils.PropertyUtils
import retrofit2.Retrofit

/***
 ** <AUTHOR>
 ** @email <EMAIL>
 ** @date 2019-07-18
 **/
class NetworkManger private constructor() {

    private var mOrionBaseDomain : String? = null

    companion object {
        val instance = SingletonHolder.holder
    }

    private object SingletonHolder {
        val holder = NetworkManger()
    }

    /**
     * 请在进程启动时 执行
     * */
    fun initDomain() {
        initOrionBaseDomainSafe()
    }

    fun getOrionbaseDomain() : String {
        return mOrionBaseDomain!!
    }

    @WorkerThread
    fun getLunchReportRequest() : LunchReportRequest {
        initOrionBaseDomainSafe()
        val retrofit = Retrofit.Builder()
                .baseUrl(mOrionBaseDomain!!)
                .addConverterFactory(JsonConverterFactory.create())
                .build()
        return retrofit.create(LunchReportRequest::class.java)
    }

    private fun initOrionBaseDomainSafe() {
        if (mOrionBaseDomain == null) {
            mOrionBaseDomain = PropertyUtils.orionBaseDomain
        }
        Log.w("NetworkManger", mOrionBaseDomain)
    }
}