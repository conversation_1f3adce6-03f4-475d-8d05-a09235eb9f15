/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.react.server.impl

import android.util.Log
import com.ainirobot.platform.PlatformDef
import com.ainirobot.platform.control.ControlManager
import com.ainirobot.platform.rn.IIntentManager

class RNIntentManagerServer: IIntentManager.Stub() {

    override fun intentDispatch(reqId: Int, intent: String, text: String, params: String){
        Log.i("RNIntentManagerServer", "intentDispatch,intent:$intent,params:$params")
        if(PlatformDef.SWITCH_CHARACTER == intent) {
            ControlManager.handleRequest(reqId, PlatformDef.SWITCH_CHARACTER, text, params)
        }
    }
}
