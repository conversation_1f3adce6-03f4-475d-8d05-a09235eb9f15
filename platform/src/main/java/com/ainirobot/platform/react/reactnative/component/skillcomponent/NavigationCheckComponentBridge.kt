package com.ainirobot.platform.react.reactnative.component.skillcomponent

import com.ainirobot.platform.component.NavigationCheckComponent
import com.ainirobot.platform.react.server.utils.ComponentUtils
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactMethod

/**
 * @date: 2021-03-05
 * @author: ligeng
 * @desc: Navigation Check Component Bridge for RN
 */
class NavigationCheckComponentBridge(reactContext: ReactApplicationContext) : BaseComponentBridge<NavigationCheckComponent>(reactContext) {

    override fun isHDMonopoly(): Bo<PERSON>an {
        return false
    }

    override fun getResouceType(): Array<ResouceType> {
        return arrayOf(ResouceType.Navigation)
    }

    override fun getName(): String {
        return ComponentUtils.COMPONENT_NAVIGATION_CHECK
    }

    @ReactMethod
    override fun start(uid: String, param: String?) {
        super._start(uid, param)
    }

    @ReactMethod
    override fun stop(uid: String, timeout: Int?) {
        super._stop(uid, timeout)
    }

    @ReactMethod
    override fun updateParams(uid: String, intent: String, param: String) {
        super._updateParams(uid, intent, param)
    }

    @ReactMethod
    override fun setStatusListener(uid: String, statusListenerId: Int) {
        super._setStatusListener(uid, statusListenerId)
    }

    @ReactMethod
    override fun setFinishListener(uid: String, finishListenerId: Int) {
        super._setFinishListener(uid, finishListenerId)
    }

}
