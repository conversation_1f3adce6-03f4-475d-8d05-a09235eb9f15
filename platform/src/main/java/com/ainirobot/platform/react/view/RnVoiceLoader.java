/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.react.view;

import android.content.Context;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.ArrayMap;
import android.util.Log;

import com.ainirobot.platform.BaseApplication;

import java.io.InputStream;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.WeakHashMap;

public class RnVoiceLoader {

    private static final String TAG = RnVoiceLoader.class.getSimpleName();
    private Context mContext;
    private static volatile RnVoiceLoader mInstance;
    private WeakHashMap<String, Bitmap> mBitmapCache;

    private ArrayMap<String, String[]> voiceMap = new ArrayMap<>();

    public static RnVoiceLoader getInstance() {
        if (mInstance == null) {
            synchronized (RnVoiceLoader.class) {
                if (mInstance == null) {
                    mInstance = new RnVoiceLoader();
                }
            }
        }
        return mInstance;
    }
    
    public void clearBitmapCache() {
        Iterator map1it = mBitmapCache.entrySet().iterator();
        while(map1it.hasNext()) {
            Map.Entry<String, Bitmap> entry = (Map.Entry<String, Bitmap>) map1it.next();
            Bitmap bitmap = entry.getValue();
            bitmap.recycle();
            bitmap = null;
        }
        mBitmapCache.clear();
    }

    private RnVoiceLoader() {
        this.mContext = BaseApplication.getContext();
        mBitmapCache = new WeakHashMap<>(100);
        new Thread(new Runnable() {
            @Override
            public void run() {
                loadResource();
            }
        }).start();
    }

    private boolean loadResource() {
        String folderPath = "expression/voice_rn";
        AssetManager manager = mContext.getAssets();
        try {
            String[] folders = manager.list(folderPath);
            Log.i(TAG, "folders = " + Arrays.toString(folders));
            for (String folder : folders) {
                for (Voice.VoiceType voiceType : Voice.VoiceType.values()) {
                    if (voiceType.getName().equals(folder)) {
                        String[] fileNames = manager.list(folderPath + "/" + folder);
                        Arrays.sort(fileNames, mComparator);
                        String[] realFilePaths = new String[fileNames.length];
                        for (int i = 0; i < fileNames.length; i ++) {
                            String realFolderPath = folderPath + "/" + folder + "/" + fileNames[i];
                            realFilePaths[i] = realFolderPath;
                        }
                        Log.i(TAG, "realFilePaths = " + Arrays.toString(realFilePaths));
                        voiceMap.put(voiceType.getName(), realFilePaths);
                    }
                }
            }
            Log.i(TAG, voiceMap.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    private final Comparator<String> mComparator = new Comparator<String>() {
        @Override
        public int compare(String o1, String o2) {
            if (o1.length() > o2.length()) {
                return 1;
            } else if(o1.length() == o2.length()) {
                return o1.compareTo(o2);
            } else {
                return -1;
            }
        }
    };

    public String[] getPathArrays(Voice.VoiceType voiceType) {
        if (voiceMap != null && !voiceMap.isEmpty()) {
            return voiceMap.get(voiceType.getName());
        } else {
            if (loadResource()) {
                return getPathArrays(voiceType);
            }
        }
        return null;
    }

    public List<String> getPathList(Voice.VoiceType voiceType) {
        String[] pathArrays = getPathArrays(voiceType);
        if (pathArrays != null && pathArrays.length > 0) {
            return Arrays.asList(pathArrays);
        }
        return null;
    }

    public Bitmap getDiskBitmap(String pathString) {
        Bitmap bitmap = null;
        if (mBitmapCache.get(pathString) == null) {
            try {
                InputStream in;
                if ((in = mContext.getAssets().open(pathString)) != null) {
                    bitmap = getStreamSampleBitmap(in);
                }
                mBitmapCache.put(pathString, bitmap);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            bitmap = mBitmapCache.get(pathString);
        }
        return bitmap;
    }

    private Bitmap getStreamSampleBitmap(InputStream inputStream) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inSampleSize = 4;
        return BitmapFactory.decodeStream(inputStream, null, options);
    }

}
