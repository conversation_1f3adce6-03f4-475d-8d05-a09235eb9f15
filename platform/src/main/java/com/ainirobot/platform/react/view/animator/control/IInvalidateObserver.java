/*
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.ainirobot.platform.react.view.animator.control;

/**
 * 在此写用途
 *
 * @FileName: com.ainirobot.platform.ui.animator.control.IInvalidateObserver.java
 * @author: <PERSON>
 * @date: 2019-01-14 16:50
 */
public interface IInvalidateObserver {
    public void onInvalidate();
}
