package com.ainirobot.platform.react.reactnative.component.uicomponent.camera


import com.facebook.react.ReactPackage
import com.facebook.react.bridge.NativeModule
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.uimanager.ViewManager
import java.util.*

/**
 * @date: 2019-02-27
 * @author: lumeng
 * @desc: 自定义 camera 组件的 ReactPackage
 */
class CameraViewPackage : ReactPackage {

    private var cameraViewModule: CameraViewModule? = null

    override fun createNativeModules(reactContext: ReactApplicationContext): List<NativeModule> {
        cameraViewModule = CameraViewModule(reactContext)
        return Arrays.asList<NativeModule>(cameraViewModule)
    }

    override fun createViewManagers(reactContext: ReactApplicationContext): List<ViewManager<*, *>> {
        return Arrays.asList<ViewManager<*, *>>(CameraViewManager())
    }

}


