package com.ainirobot.platform.react.reactnative.character

import android.os.Handler
import android.os.Looper
import android.util.Log
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.bean.net.HttpClient
import com.ainirobot.platform.bean.net.Request
import com.ainirobot.platform.bean.net.Response
import com.ainirobot.platform.react.network.NetworkManger
import com.ainirobot.platform.utils.FileHelper
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import com.liulishuo.filedownloader.BaseDownloadTask
import com.liulishuo.filedownloader.FileDownloadSampleListener
import com.liulishuo.filedownloader.FileDownloader
import com.liulishuo.filedownloader.model.FileDownloadStatus
import java.io.IOException
import java.util.*

var LIULISHUO_APK_URL = "http://cos-orionbase.ainirobot.com/publish_file/orionos-core-1/orionos-core-1.0.0.zip"

class HeaderBean(val msg: String, val code: Int, val time: Long)

class CoreDataBean(val url: String, val md5: String)

class CoreBundleBean(val data: CoreDataBean, val header: HeaderBean)

data class CoreInfo(val version: String, val md5: String)

class CoreInfoManager {
    private var coreMap: HashMap<String, String>? = null

    companion object {
        val coreInfoManager = CoreInfoManager()
    }

    private constructor() {
        val context = BaseApplication.getContext()
        val coreInstallList = FileHelper
                .loadJsonFromFile("${context.filesDir.absolutePath}/${Constant.RPK_INSTALL_DIR}/${Constant.CORE_INSTALL_NAME}")
        coreMap = if (coreInstallList.isNullOrEmpty()) {
            HashMap()
        } else {
            val gson = GsonBuilder().create()
            gson.fromJson(coreInstallList, object : TypeToken<HashMap<String, String>>() {}.type)
        }
    }

    fun addAndSaveCoreInfo(version: String, md5: String) {
        coreMap?.put(version, md5)
        save()
    }

    private fun save() {
        if (this.coreMap == null) {
            return
        }

        val context = BaseApplication.getContext()
        val gson = GsonBuilder().create()
        val json = gson.toJson(coreMap)
        Log.d("rpk", "json" + json)
        FileHelper.saveToFile(context.filesDir.absolutePath + "/${Constant.RPK_INSTALL_DIR}/",
                json, Constant.CORE_INSTALL_NAME)
    }

    fun getMd5(version: String): String? {
        if (coreMap?.contains(version)!!) {
            return coreMap?.get(version)
        }

        return null
    }

}

class DownloadCoreBundleHelper {
    private var coreTarget: String
    private var filename: String
    private val path: String
    private val url: String
    private var listener: FileCoreListener
    private val CORE_BUNDLE = "core.android.bundle"

    private var isCache: Boolean = false

    constructor(url: String, path: String, coreTarget: String, listener: FileCoreListener) {
        this.url = url
        this.path = path
        this.coreTarget = coreTarget
        this.filename = String.format("orionos-core-${coreTarget}.+.zip", this.coreTarget)
        this.listener = listener
    }

    private fun createDownloadTask(coreUrl: String, listener: FileCoreListener, version: String, md5: String) {
        val coreFile = path + filename
        if (FileHelper.isExist(coreFile)) {
            FileHelper.deleteTargetFile(coreFile)
        }

        FileDownloader.getImpl().create(coreUrl)
                .setPath(coreFile)
                .setCallbackProgressTimes(300)
                .setMinIntervalUpdateSpeed(400)
                .setListener(object : FileDownloadSampleListener() {
                    override fun completed(task: BaseDownloadTask?) {
                        super.completed(task)

                        if (task?.status == FileDownloadStatus.completed && FileHelper.isExist(coreFile) && FileHelper.unzip(coreFile, path)) {

                            CoreInfoManager.coreInfoManager.addAndSaveCoreInfo(coreTarget, md5)
                            listener.completed(path + CORE_BUNDLE, true)
                            Log.d("rpk", "core js download success: " + task?.path)
                        } else {
                            Log.d("rpk", "core js download error ")
                            listener.error(105, "error")
                        }
                    }

                    override fun error(task: BaseDownloadTask?, e: Throwable?) {
                        super.error(task, e)
                        Log.d("rpk", "core js download " + e?.message)
                        listener.error(101, e?.message)
                    }
                })
                .start()
    }

    fun setCache(isCache: Boolean) {
        this.isCache = isCache
    }

    fun start() {
        Log.d("rpk", "url: $url")

        val file = this.path + this.CORE_BUNDLE
        if (isCache && FileHelper.isExist(file)) {
            this.listener.completed(file, isFromRemote = false)
            return
        }

        HttpClient().execute(Request.Builder().url(url)
                .get()
                .header("Content-Type", "multipart/form-data").build(), object : Response.Listener {
            override fun onResponse(response: Response?) {
                if (response == null) {
                    callback(103,"response is null")
                    return
                }

                Log.d("rpk", "url: $url" + " string: " + response.contentString)
                var coreBundleBean : CoreBundleBean? = null
                try {
                    coreBundleBean = Gson().fromJson(response.contentString, CoreBundleBean::class.java)
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(103,"json parse failed")
                }

                val coreUrl = coreBundleBean?.data?.url
                val md5 = coreBundleBean?.data?.md5
                if (coreUrl != null && md5 != null && isRealDownload(md5)) {
                    createDownloadTask(coreUrl, listener, coreTarget, md5)
                } else {
                    callback(103,"data failed")
                }
            }

            override fun onFailure(e: IOException?) {
                callback(101, e?.message)
            }

        })
    }

    private fun isRealDownload(md5: String?): Boolean {
        if (md5 == null) {
            return false
        }

        val lastMd5 = CoreInfoManager.coreInfoManager.getMd5(this.coreTarget)
        Log.d("rpk", "lastMd5:" + lastMd5)
        if (md5 != lastMd5) {
            return true
        }

        return false
    }

    private fun callback(error: Int, message: String?) {
        val file = this.path + this.CORE_BUNDLE
        Log.d("rpk", file)
        Log.d("rpk", message)
        val runnable = if (FileHelper.isExist(file)) {
            {
                this.listener.completed(file, isFromRemote = false)
            }
        } else {
            {
                this.listener.error(error, message)
            }
        }

        Handler(Looper.getMainLooper()).post(runnable)
    }
}

interface FileCoreListener {
    fun completed(path: String?, isFromRemote: Boolean)
    fun error(error: Int, message: String?)
}

class DownloadCoreBundleBuilder {
    private lateinit var listener: FileCoreListener
    private lateinit var path: String
    private lateinit var coreTarget: String

    constructor(coreTarget: String, path: String, listener: FileCoreListener) {
        this.coreTarget = coreTarget
        this.path = String.format("%s/rpk/core/%s/", path, coreTarget)
        this.listener = listener
    }

    fun build(): DownloadCoreBundleHelper {
        Log.d("rpk", "build " + this.path)
        val url = "${NetworkManger.instance.getOrionbaseDomain()}${Constant.ORION_CORE_API_PATH}?file=orionos-core-${coreTarget}"
//        val url = "http://************:8095/api/v1/open/other/getCoreJS?file=orionos-core-1.0.0.zip"
        return DownloadCoreBundleHelper(url, path, coreTarget, this.listener)
    }
}