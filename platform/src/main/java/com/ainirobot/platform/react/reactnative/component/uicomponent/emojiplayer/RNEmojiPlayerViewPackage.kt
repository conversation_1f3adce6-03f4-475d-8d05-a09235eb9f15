package com.ainirobot.platform.react.reactnative.component.uicomponent.emojiplayer


import com.facebook.react.ReactPackage
import com.facebook.react.bridge.NativeModule
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.uimanager.ViewManager
import java.util.*

/**
 * @date: 2019-04-20
 * @author: haoh<PERSON><PERSON>
 * @desc: 自定义 RNEmojiPlayerView 组件的 ReactPackage
 */
class RNEmojiPlayerViewPackage : ReactPackage {

    override fun createNativeModules(reactContext: ReactApplicationContext): List<NativeModule> {
        return Collections.emptyList()
    }

    override fun createViewManagers(reactContext: ReactApplicationContext): List<ViewManager<*, *>> {
        return Arrays.asList<ViewManager<*, *>>(RNEmojiPlayerViewManager())
    }

}

