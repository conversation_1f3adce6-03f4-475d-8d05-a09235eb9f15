package com.ainirobot.platform.react.reactnative.component.uicomponent.camera

import android.util.Log
import com.ainirobot.platform.react.reactnative.component.uicomponent.camera.event.CaptureEvent
import com.ainirobot.platform.react.reactnative.component.uicomponent.camera.event.PreviewEvent
import com.ainirobot.platform.react.view.CameraView
import com.facebook.react.bridge.ReadableArray
import com.facebook.react.common.MapBuilder
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.UIManagerModule
import java.lang.ref.WeakReference

/**
 * @date: 2019-02-27
 * @author: lumeng
 * @desc: 封装camera
 */
@ReactModule(name = CameraViewManager.REACT_CLASS)
class CameraViewManager : SimpleViewManager<CameraView>() {

    companion object {
        const val COMMAND_START_CAMERA = 1
        const val COMMAND_STOP_CAMERA = 2
        const val COMMAND_TAKE_PICTURE = 3
        const val REACT_CLASS = "NativeCameraView"
        val TAG = "CameraViewManager"
    }

    override fun getName(): String {
        return REACT_CLASS
    }

    override fun getCommandsMap(): Map<String, Int>? {
        Log.i(TAG, "getCommandsMap")
        return MapBuilder.of(
                "startCamera", COMMAND_START_CAMERA,
                "stopCamera", COMMAND_STOP_CAMERA,
                "takePicture", COMMAND_TAKE_PICTURE)
    }

    override fun receiveCommand(root: CameraView, commandId: Int, args: ReadableArray?) {
        Log.i(TAG, "receiveCommand commandId $commandId")
        when (commandId) {
            COMMAND_START_CAMERA -> root?.startCamera()
            COMMAND_STOP_CAMERA -> root?.stopCamera()
            COMMAND_TAKE_PICTURE -> root?.takePicture()
            else -> Log.e(TAG, "unsupported command $commandId")
        }
    }

    override fun getExportedCustomDirectEventTypeConstants(): MutableMap<String, Any> {
        return MapBuilder.of(
                PreviewEvent.EVENT_NAME, MapBuilder.of("registrationName", PreviewEvent.EVENT_NAME),
                CaptureEvent.EVENT_NAME, MapBuilder.of("registrationName", CaptureEvent.EVENT_NAME)
        )
    }

    override fun addEventEmitters(reactContext: ThemedReactContext, view: CameraView) {
        super.addEventEmitters(reactContext, view)
        Log.i(TAG, "addEventEmitters")
        view?.setCaptureForRnListener { state, originalImageFile, compressedFile ->
            Log.i(TAG, "addEventEmitters setCaptureListener")
            reactContext
                    .getNativeModule(UIManagerModule::class.java)
                    ?.eventDispatcher
                    ?.dispatchEvent(CaptureEvent(view.id, state, originalImageFile, compressedFile))
        }
        view?.setPreviewListener { state ->
            Log.i(TAG, "addEventEmitters setPreviewListener")
            reactContext
                    .getNativeModule(UIManagerModule::class.java)
                    ?.eventDispatcher
                    ?.dispatchEvent(PreviewEvent(view.id, state))
        }
    }

    override fun createViewInstance(reactContext: ThemedReactContext): CameraView {
        return createCameraViewInstance(reactContext)
    }

    var cameraViewRef: WeakReference<CameraView>? = null

    private fun createCameraViewInstance(reactContext: ThemedReactContext): CameraView {
        return CameraView(reactContext)

//        return if (cameraViewRef?.get() != null) {
//            cameraViewRef!!.get()!!
//        } else {
//            val cameraView = CameraView(reactContext)
//            cameraViewRef = WeakReference(cameraView)
//            cameraView
//        }
    }

}
