/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.platform.react.reactnative.component.uicomponent.camerafilter;

import android.net.Uri;
import android.util.Log;
import android.view.LayoutInflater;
import android.widget.FrameLayout;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.gpuimage.GPUImageView;
import com.ainirobot.gpuimage.util.Rotation;
import com.ainirobot.platform.R;
import com.ainirobot.platform.react.reactnative.component.uicomponent.camerafilter.loader.CameraLoader;
import com.ainirobot.platform.react.reactnative.component.uicomponent.camerafilter.loader.CameraShareLoader;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.UIManagerModule;

/**
 * Created by Orion on 2018/8/7.
 */

public class CameraFilterView extends FrameLayout {

    private static final String TAG = CameraFilterView.class.getSimpleName();

    private ThemedReactContext mContext;
    private GPUImageView mGPUImageView;
    private CameraLoader mCameraLoader;

    public CameraFilterView(ThemedReactContext context) {
        super(context);
        init(context);
    }

    private void init(ThemedReactContext context) {
        Log.d(TAG, "init context: " + context);
        this.mContext = context;
        LayoutInflater.from(context).inflate(R.layout.platform_fragment_rn_camera_filter, null);
    }

    public void setType(int type) {
        mGPUImageView = new GPUImageView(mContext);
        mGPUImageView.setRenderMode(GPUImageView.RENDERMODE_CONTINUOUSLY);
        if (type == 1) {
            mCameraLoader = new CameraShareLoader();
        } else {
            mContext.getNativeModule(UIManagerModule.class).getEventDispatcher()
                    .dispatchEvent(new CameraFilterOnFailureEvent(CameraFilterView.this.getId(),
                            CameraFilterError.ERROR_COMMON_NOT_SUPPORT_WITHOUT_SHARE));
            destroy();
            return;
        }
//        mGPUImageView.setRatio(mCameraLoader.getRadio()); // 固定使用 4:3 的尺寸
        updateGPUImageRotate();
        addView(mGPUImageView);
        mCameraLoader.setOnPreviewFrameListener(new CameraLoader.OnPreviewFrameListener() {
            @Override
            public void onStatusUpdate(int status) {
                Log.d(TAG, "onStatusUpdate status: " + status);
                if (mGPUImageView != null) {
                    mContext.getNativeModule(UIManagerModule.class).getEventDispatcher()
                            .dispatchEvent(new CameraFilterOnInitSuccessEvent(CameraFilterView
                                    .this.getId()));
                    mGPUImageView.requestRender();
                }
            }

            @Override
            public void onError(int error, String message) {
                Log.d(TAG, "onError error: " + error + ", message: " + message);
                mContext.getNativeModule(UIManagerModule.class).getEventDispatcher()
                        .dispatchEvent(new CameraFilterOnFailureEvent(CameraFilterView.this.getId(),
                                error));
                destroy();
            }

            @Override
            public void onPreviewFrame(byte[] data, int width, int height) {
                Log.d(TAG, "onPreviewFrame width: " + width + ", height: " + height);
                if (mGPUImageView != null) {
                    mGPUImageView.updatePreviewFrame(data, width, height);
                }
            }

            @Override
            public void onPreviewChange(int width, int height) {
                mGPUImageView.setRatio(mCameraLoader.getRadio());
            }
        });
        mCameraLoader.onResume();
    }

    private void updateGPUImageRotate() {
        Rotation rotation = ProductInfo.isMiniProduct() || ProductInfo.isCarryProduct() ? Rotation.NORMAL : Rotation.ROTATION_270;
        mGPUImageView.getGPUImage().setRotation(rotation, true, false);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
    }

    public void destroy() {
        Log.d(TAG, "destroy mCameraLoader: " + mCameraLoader);
        if (mGPUImageView != null) {
            mGPUImageView.post(new Runnable() {
                @Override
                public void run() {
                    if (mGPUImageView != null) {
                        removeView(mGPUImageView);
                        mGPUImageView.onPause();
                        mGPUImageView = null;
                    }
                }
            });
        }
        if (mCameraLoader != null) {
            mCameraLoader.setOnPreviewFrameListener(null);
            mCameraLoader.onPause();
            mCameraLoader = null;
        }
    }

    public void resumePreview() {
        if (mCameraLoader != null) {
            if (mGPUImageView != null) {
                mGPUImageView.setRenderMode(GPUImageView.RENDERMODE_CONTINUOUSLY);
            }
            mCameraLoader.onResume();
        }
    }

    public void pausePreview() {
        if (mCameraLoader != null) {
            if (mGPUImageView != null) {
                mGPUImageView.setRenderMode(GPUImageView.RENDERMODE_WHEN_DIRTY);
            }
            mCameraLoader.onPause();
        }
    }

    public void saveSnapShot(boolean needPause) {
        if (needPause) {
            pausePreview();
        }
        String fileName = System.currentTimeMillis() + ".jpg";
        if (mGPUImageView != null) {
            mGPUImageView.saveToPictures("cameraFilter", fileName, mOnPictureSavedListener);
        }
    }

    private GPUImageView.OnPictureSavedListener mOnPictureSavedListener
            = new GPUImageView.OnPictureSavedListener() {
        @Override
        public void onPictureSaved(Uri uri, String path) {
            Log.d(TAG, "onPictureSaved uri: " + uri.toString() + ", path: " + path);
            mContext.getNativeModule(UIManagerModule.class).getEventDispatcher()
                    .dispatchEvent(new CameraFilterOnSnapShotEvent(CameraFilterView.this.getId(),
                            path));
        }
    };
}
