package com.ainirobot.platform.react.reactnative.component.listener

import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNEmergencyListener
import com.ainirobot.platform.rn.listener.IRNFunctionKeyListener
import com.facebook.react.bridge.WritableNativeMap

class BridgeFunctionKeyListener : IRNFunctionKeyListener.Stub {

    companion object {
        fun obtain(callbackId:Int): BridgeFunctionKeyListener? {
            return BridgeFunctionKeyListener(callbackId)
        }
    }

    var id = -1

    private constructor(id: Int) {
        this.id = id
    }

    override fun onStatusChanged(data: String?) {
        triggerEvent("onFunctionKeyStatusChanged", data)
    }

    fun triggerEvent(event: String, data: String?) {
        if (id < 0) {
            return
        }
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putString("event", event)
        data?.apply { readableMap.putString("data", data) }
        ReactNativeEventEmitter.triggerEvent("onFunctionKeyStatusChangedListener", readableMap)
    }
}