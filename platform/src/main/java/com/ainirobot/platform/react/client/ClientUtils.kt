package com.ainirobot.platform.react.client

import android.util.Log
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.utils.SystemUtils

object ClientUtils {
    private const val TAG = "ClientUtils"

    fun killProcess(pid: Int) {
        Log.d(TAG, "killProcess sandbox")
        val processName = SystemUtils.getProcessName(BaseApplication.getContext())
        if (processName.endsWith("sandbox")) {
            android.os.Process.killProcess(pid)
        }
    }
}