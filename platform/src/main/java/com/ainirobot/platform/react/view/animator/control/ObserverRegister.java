/*
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.ainirobot.platform.react.view.animator.control;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import androidx.annotation.NonNull;
import android.util.Log;

import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 在此写用途
 *
 * @FileName: com.ainirobot.platform.ui.animator.control.InvalidateManager.java
 * @author: <PERSON>
 * @date: 2019-01-14 16:58
 */
public class ObserverRegister {
    private static final String TAG = ObserverRegister.class.getSimpleName();

    private CopyOnWriteArrayList<IInvalidateObserver> mInvalidateList = new CopyOnWriteArrayList<>();

    private static final int MSG_WHAT_INVALIDATE = 1;
    private Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case MSG_WHAT_INVALIDATE: {
                    for (IInvalidateObserver observer : mInvalidateList) {
                        observer.onInvalidate();
                    }
                    break;
                }
                default:
                    break;
            }
        }
    };

    public void registObserver(@NonNull IInvalidateObserver observer) {
        if (null == observer) {
            throw new IllegalArgumentException("can't register null observer!");
        }

        if (mInvalidateList.contains(observer)) {
            Log.w(TAG, "this observer has been registered repeatedly!");
            return;
        }

        mInvalidateList.add(observer);
    }

    public int unRegistObserver(@NonNull IInvalidateObserver observer) {
        if (null == observer) {
            throw new IllegalArgumentException("can't unregister null observer!");
        }

        int ret = -1;
        if (mInvalidateList.contains(observer)) {
            mInvalidateList.remove(observer);
            ret = mInvalidateList.size();
        }

        return ret;
    }

    public void clearObserver() {
        mInvalidateList.clear();
    }

    public void notifyObserver() {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            for (IInvalidateObserver observer : mInvalidateList) {
                observer.onInvalidate();
            }
        } else {
            mHandler.sendEmptyMessage(MSG_WHAT_INVALIDATE);
        }
    }
}
