/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.react.reactnative.component.uicomponent.bubbleview;

import java.util.ArrayDeque;
import java.util.LinkedHashSet;
import java.util.Queue;
import java.util.Random;
import java.util.Set;

/**
 * 生成不重复的随机数
 * 主要用了Set元素不重复的特性
 */
class RandomPositionPolicy implements IPositionPolicy {

    private int mMaxPosition;
    private Queue<Integer> mWorkQueue;
    private int mPosition;

    @Override
    public void setMaxPosition(int position) {
        if (position < 0) {
            return;
        }
        mMaxPosition = position;
        setWorkList();
    }

    @Override
    public int getPosition() {
        if (mMaxPosition == 0) {
            return 0;
        }
        int result;
        if (mWorkQueue.isEmpty()) {
            setWorkList();
        }
        result = mWorkQueue.poll();
        if (mPosition == mMaxPosition) {
            mPosition = 0;
            setWorkList();
        }
        return result;
    }

    /**
     * 生成不重复的随机数list
     * 包括0 不包括size
     */
    private void setWorkList() {
        int size = mMaxPosition + 1;
        mWorkQueue = new ArrayDeque<>();
        Random random = new Random();
        Set<Integer> set = new LinkedHashSet<>();
        //生成不重复的随机数集合
        while (true) {
            set.add(random.nextInt(size));
            if (set.size() == size) {
                break;
            }
        }
        for (Integer randomPosition : set) {
            mWorkQueue.add(randomPosition);
        }
    }

}
