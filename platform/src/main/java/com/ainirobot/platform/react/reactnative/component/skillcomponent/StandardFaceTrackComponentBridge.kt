package com.ainirobot.platform.react.reactnative.component.skillcomponent

import com.ainirobot.platform.component.StandardFaceTrackComponent
import com.ainirobot.platform.react.server.utils.ComponentUtils
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactMethod

class StandardFaceTrackComponentBridge(reactContext: ReactApplicationContext) : BaseComponentBridge<StandardFaceTrackComponent>(reactContext) {

    override fun isHDMonopoly(): Boolean {
        // 根据参数，判断是否独占资源
        // rn 层认为它是独占的
        return true
    }

    override fun getResouceType(): Array<ResouceType> {
        return arrayOf(ResouceType.HeadTurn, ResouceType.Vision, ResouceType.Navigation)
    }

    override fun getName(): String {
        return ComponentUtils.COMPONENT_STANDARD_FACE_TRACK
    }

    @ReactMethod
    override fun start(uid: String, param: String?) {
        super._start(uid, param)
    }

    @ReactMethod
    override fun stop(uid: String, timeout: Int?) {
        super._stop(uid, timeout)
    }

    @ReactMethod
    override fun updateParams(uid: String, intent: String, param: String) {
        super._updateParams(uid, intent, param)
    }

    @ReactMethod
    override fun setStatusListener(uid: String, statusListenerId: Int) {
        super._setStatusListener(uid, statusListenerId)
    }

    @ReactMethod
    override fun setFinishListener(uid: String, finishListenerId: Int) {
        super._setFinishListener(uid, finishListenerId)
    }

}
