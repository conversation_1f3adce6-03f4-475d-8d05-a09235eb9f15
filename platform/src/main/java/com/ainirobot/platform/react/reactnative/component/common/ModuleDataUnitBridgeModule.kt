package com.ainirobot.platform.react.reactnative.component.common

import android.text.TextUtils
import android.util.Log
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.react.server.impl.RNDataServer
import com.ainirobot.platform.rn.listener.IRNDataListener
import com.facebook.react.bridge.*
import kotlinx.coroutines.*
import java.lang.Exception

/**
 * @date: 2019-04-11
 * @author: zhangbaoliang
 * @desc: 获取底层数据中心的数据
 */
class ModuleDataUnitBridgeModule(reactContext: ReactApplicationContext) :
        ReactContextBaseJavaModule(reactContext) {

    private var mDataServer: RNDataServer = RNDataServer();

    companion object {
        var TAG = "ModuleDataUnit"
    }

    override fun getName(): String {
        return "ModuleDataUnit"
    }

    @ReactMethod
    fun getTest(callback: Callback) {
        Log.i(TAG, "getxxxxxx params: ")
        try {
            val strValue = "kflslsksksslsll"
            callback.invoke(strValue)
            Log.i(TAG, "getxxxxxx params: $strValue")
        } catch (e: Exception) {
            e.printStackTrace()
            callback.invoke("")
        }
    }

    @ReactMethod
    fun getConfigs(moduleCode: String, callback: Callback) {
        if (TextUtils.isEmpty(moduleCode)) {
            callback.invoke("ModuleDataUnit getConfigs moduleCode must be available")
            return
        }
        Log.i(TAG, "getCongifs params: $moduleCode")
        try {
            val strValue: String? = mDataServer.getConfigs(moduleCode)
            callback.invoke(strValue)
        } catch (e: Exception) {
            e.printStackTrace()
            callback.invoke("")
        }
    }

    @ReactMethod
    fun getConfigContents(moduleCode: String, callback: Callback) {
        if (TextUtils.isEmpty(moduleCode)) {
            callback.invoke("ModuleDataUnit getConfigContents moduleCode must be available")
            return
        }
        Log.i(TAG, "getConfigContents params: $moduleCode")
        try {
            val strValue: String? = mDataServer.getConfigContents(moduleCode)
            callback.invoke(strValue)
        } catch (e: Exception) {
            e.printStackTrace()
            callback.invoke("")
        }
    }

    @ReactMethod
    fun getResFile(moduleCode: String, filePath: String?, callback: Callback) {
    //    Log.i(TAG, "getResFile")
        if (TextUtils.isEmpty(moduleCode)) {
            callback.invoke("ModuleDataUnit getResFile moduleCode must be available")
            return
        }
        Log.i(TAG, " getResFile:: getCongifs params: $moduleCode + $filePath")
        try {
            val strValue: String? = mDataServer.getResFile(moduleCode, filePath)
            callback.invoke(strValue)
        } catch (e: Exception) {
            e.printStackTrace()
            callback.invoke("")
        }
    }

    @ReactMethod
    fun getCropName(callback: Callback) {
        try {
            val cropName: String? = mDataServer.getCropName()
            callback.invoke(cropName)
        } catch (e: Exception) {
            e.printStackTrace()
            callback.invoke("")
        }
    }

    @ReactMethod
    fun registerUpdateDataListener() {
        Log.i(TAG, "registerUpdateDataListener")
        RNClientManager.instance?.dataManager?.registerUpdateSkillDataListener(
                object : IRNDataListener.Stub() {
                    override fun onDataUpdate(data: String) {
                        Log.i(TAG, "DataUpdateListener:$data")
                        ReactNativeEventEmitter.triggerEvent("NOTIFY_EVENT_LOAD_DATA_SUCCESS", data)
                    }
        })
    }
    @ReactMethod
    fun forceFetchModuledata(moduleCode: String, callback: Callback) {
        if (TextUtils.isEmpty(moduleCode)) {
            callback.invoke("ModuleDataUnit getConfigs moduleCode must be available")
            return
        }
        Log.i(TAG, "getCongifs params: $moduleCode")
        try {
            val strValue: String? = mDataServer.forceFetchModuledata(moduleCode)
            callback.invoke(strValue)
        } catch (e: Exception) {
            e.printStackTrace()
            callback.invoke("")
        }
    }

    @ReactMethod
    fun forceFetchModuledataBatch(moduleCodes: String, callback: Callback) {
        if (moduleCodes != null && moduleCodes.isEmpty()) {
            callback.invoke("ModuleDataUnit getConfigs moduleCodes must be available")
            return
        }
        Log.i(TAG, "forceFetchModuledataBatch params: $moduleCodes")
        try {
            GlobalScope.launch{
                val codes = moduleCodes.split(',')
                codes.forEach(fun(code: String){
                    delay(100)
                    mDataServer.forceFetchModuledata(code)
                })
            }
            callback.invoke("forceFetchModuledataBatch finished")
        } catch (e: Exception) {
            e.printStackTrace()
            callback.invoke("")
        }
    }

    @ReactMethod
    fun getResFileBatch(moduleCode: String, filePaths: String, callback: Callback) {
        Log.i(TAG, "getResFileBatch")
        if (TextUtils.isEmpty(moduleCode) || filePaths!!.isEmpty()) {
            callback.invoke("ModuleDataUnit getResFileBatch moduleCode must be available")
            return
        }
        Log.i(TAG, "getResFileBatch params: $moduleCode + $filePaths")
        try {
            GlobalScope.launch{
                val arrPaths: Array<String>? = mDataServer.getResFileBatch(moduleCode, filePaths)
                val writableArray = Arguments.createArray()
                for (path in arrPaths!!.iterator()){
                    writableArray.pushString(path)
                }
                callback.invoke(writableArray)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            callback.invoke("")
        }
    }
}