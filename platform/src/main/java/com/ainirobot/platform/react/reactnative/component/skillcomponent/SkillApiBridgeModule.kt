package com.ainirobot.platform.react.reactnative.component.skillcomponent

import android.content.Context
import android.util.Log
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.PlatformDef
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.component.listener.BridgeSkillServerCheckListener
import com.ainirobot.platform.react.reactnative.component.listener.BridgeTextListener
import com.ainirobot.platform.react.reactnative.component.listener.BridgeToneListener
import com.ainirobot.platform.react.reactnative.component.listener.EmojiBridgeTextListener
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.google.gson.Gson

class SkillApiBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    companion object {
        val TAG = "SkillApiBridgeModule"
        val mGson = Gson()
    }

    override fun getName(): String {
        return "SkillApi"
    }

    @ReactMethod
    fun playText(callbackId: Int, text: String) {
        Log.i(TAG, "play text:$text")
        try {
            RNClientManager.instance?.speechApiManager?.playText(text, BridgeTextListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun playTextWithParams(callbackId: Int, ttsParams: String, text: String) {
        Log.i(TAG, "play text with params:$text")
        try {
            RNClientManager.instance?.speechApiManager?.playTextWithParams(text, ttsParams, BridgeTextListener.obtain(callbackId))
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun playTextWithSpeak(callbackId: Int, text: String) {
        Log.i(TAG, "play text with speak:$text")
        try {
            RNClientManager.instance?.speechApiManager?.playText(text, EmojiBridgeTextListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun stopTTS() {
        try {
            RNClientManager.instance?.speechApiManager?.stopTTS()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setRecognizeMode(isContinue: Boolean) {
        try {
            RNClientManager.instance?.speechApiManager?.setRecognizeMode(isContinue)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setRecognizeModeForce(isContinue: Boolean) {
        try {
            RNClientManager.instance?.speechApiManager?.setRecognizeModeForce(isContinue)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setRecognizeModeNew(isContinue: Boolean, isCloseStreamData: Boolean) {
        try {
            RNClientManager.instance?.speechApiManager?.setRecognizeModeNew(isContinue, isCloseStreamData)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun sendAgentMessage(type: String, code: Int, message: String) {
        try {
            RNClientManager.instance?.speechApiManager?.sendAgentMessage(type, code, message)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setRecognizable(enable: Boolean) {
        try {
            RNClientManager.instance?.speechApiManager?.setRecognizable(enable)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun queryByText(text: String) {
        try {
            RNClientManager.instance?.speechApiManager?.queryByText(text)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun queryByTextWithThinking(text: String, isShowThinking: Boolean) {
        try {
            RNClientManager.instance?.speechApiManager?.queryByTextWithThinking(text, isShowThinking)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun getSpeechBeamFormingFlag(): Boolean {
        val sp = BaseApplication.getContext().getSharedPreferences(PlatformDef
                .DEVELOPER_CONFIGURATION_ITEM, Context.MODE_PRIVATE)
        val beamFormingSwitch = sp.getBoolean(PlatformDef.BEAMFOMING_ITEM, true)
        Log.d(TAG, "getSpeechBeamFormingFlag beamFormingSwitch : $beamFormingSwitch")
        return beamFormingSwitch
    }

    @ReactMethod
    fun setAngleCenterRange(centerAngle: Float, rangeAngle: Float) {
        //switch is closeFragment return
        try {
            if (!getSpeechBeamFormingFlag()) {
                return
            }
            if (centerAngle >= 0 && rangeAngle >= 0) {
                try {
                    RNClientManager.instance?.speechApiManager?.setAngleCenterRange(centerAngle, rangeAngle)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setCustomizeWakeUpWord(wakeUpWordChinese: String, wakeUpWordPinYin: String, separator: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.speechApiManager?.setCustomizeWakeUpWord(wakeUpWordChinese, wakeUpWordPinYin, separator))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun closeCustomizeWakeUpWord(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.speechApiManager?.closeCustomizeWakeUpWord())
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getPinYinScore(pinyin: String, separator: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.speechApiManager?.getPinYinScore(pinyin, separator))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun queryPinYinFromChinese(chineseWord: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.speechApiManager?.queryPinYinFromChinese(chineseWord))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun queryPinYinMappingTable(pinyin: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.speechApiManager?.queryPinYinMappingTable(pinyin))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun queryUserSetWakeUpWord(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.speechApiManager?.queryUserSetWakeUpWord())
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun startSkillServerCheckListener(callbackId: Int) {
        try {
            RNClientManager.instance?.speechApiManager?.setSkillServerCheckCallBack(BridgeSkillServerCheckListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun stopSkillServerCheckListener() {
        try {
            RNClientManager.instance?.speechApiManager?.unRegisterServerCheck()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun playTone(callbackId: Int, type: String) {
        try {
            RNClientManager.instance?.speechApiManager?.playTone(type, BridgeToneListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun resetAngleCenterRange() {
        try {
            RNClientManager.instance?.speechApiManager?.resetAngleCenterRange()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun playToneByLocalPath(callbackId: Int, path: String) {
        try {
            RNClientManager.instance?.speechApiManager?.playToneByLocalPath(path, BridgeToneListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun stopTone() {
        try {
            RNClientManager.instance?.speechApiManager?.stopTone()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setTTSParams(ttsType: String, value: Int) {
        try {
            RNClientManager.instance?.speechApiManager?.setTTSParams(ttsType, value)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setLangRec(autoLangJson: String) {
        try {
            RNClientManager.instance?.speechApiManager?.setLangRec(autoLangJson)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setASRParams(asrType: String, value: String) {
        try {
            RNClientManager.instance?.speechApiManager?.setASRParams(asrType, value)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setAsrExtendProperty(property: String, promise: Promise) {
        try {
            val result = RNClientManager.instance?.speechApiManager?.setAsrExtendProperty(property)
            Log.d(TAG, "setAsrExtendProperty result is $result")
            promise.resolve(result)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun downloadTtsAudio(ttsEntitiesJson: String) {
        try {
            RNClientManager.instance?.speechApiManager?.downloadTtsAudio(ttsEntitiesJson)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setSyncCustomNlpData(map: Map<String, Any>) {
        try {
            RNClientManager.instance?.speechApiManager?.setSyncCustomNlpData(map)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setAsyncCustomNlpData(opt: String, data: String): String? {
        try {
            return RNClientManager.instance?.speechApiManager?.setAsyncCustomNlpData(opt, data)
        } catch (e: Exception) {
            e.printStackTrace()
            return "SkillApiBridgeModule setAsyncCustomNlpData error"
        }
    }

    @ReactMethod
    fun resetNlpState() {
        try {
            RNClientManager.instance?.speechApiManager?.resetNlpState()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setNLPDebug(value: Boolean) {
        try {
            RNClientManager.instance?.speechApiManager?.setNLPDebug(value)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun getSpokemanListByLanguage(lang: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.speechApiManager?.getSpokemanListByLanguage(lang))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun closeStreamDataReceived(paramJson: String) {
        try {
            RNClientManager.instance?.speechApiManager?.closeStreamDataReceived(paramJson)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun isRecognizeContinue(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.speechApiManager?.isRecognizeContinue())
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isRecognizable(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.speechApiManager?.isRecognizable())
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun playStreamText(callbackId: Int, text: String, textSid: String, streamSid: String) {
        Log.i(TAG, "play text:$text")
        try {
            RNClientManager.instance?.speechApiManager?.playStreamText(streamSid, textSid, text, BridgeTextListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
