package com.ainirobot.platform.react.reactnative.component.common

import com.ainirobot.platform.utils.CountdownAlarmUtils
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod

class CountdownAlarmManagerBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String {
        return "CountdownAlarmManager"
    }

    @ReactMethod
    fun setStartCruiseTimer(time: String) {
        CountdownAlarmUtils.getInstance().setCountdownAlarm(time)
    }

}
