package com.ainirobot.platform.react

import android.text.TextUtils
import android.util.Log
import com.ainirobot.coreservice.client.Definition
import com.ainirobot.coreservice.client.RobotApi
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.bi.annotation.OpkOpStatus
import com.ainirobot.platform.bi.annotation.OpkOpType
import com.ainirobot.platform.bi.wrapper.ReportControl
import com.ainirobot.platform.bi.wrapper.manager.bi.impl.OpkOperationPoint
import com.ainirobot.platform.utils.FileHelper
import com.ainirobot.platform.utils.GsonUtil
import com.ainirobot.platform.utils.OpkFile
import com.ainirobot.platform.utils.imageloader.Md5
import org.json.JSONObject
import java.io.File
import java.io.IOException
import javax.annotation.Nullable

//字段只增不减
//v1 包含字段 appid versionCode versionName coreVersion bizMd5用于校验biz_bundle
//v2 新增字段 opkLoad 修改字段 coreVersion => coreTarget platformMd5用于校验platform_bundle
//v3 新增字段 type 用于区分是否插件opk path opk内容存储路径 plugin 用于记录插件信息 isSystem 标识OPK是否系统OPK
data class OPKBeanV3(var appid: String, var versionCode: Int,
                     var versionName: String, var coreVersion: String?,
                     var coreTarget: String?, var opkLoad: String?, var installTime: Long = 0, val bizMd5: String?, val platformMd5: String?,
                     val type: String?, var path: String?, var configId: String?, var configPath: String?, var appName: String?,
                     var plugin: MutableList<OPKBeanV3>?, var isSystem: Boolean = false, var supportModel: String?, var osType: String?)

object OPKHelper {
    private const val ORION_OS_FILE = "orionOs.json"
    private const val PLATFORM_BUNDLE = "platform.android.bundle"
    private const val BIZ_BUNDLE = "biz.android.bundle"
    private const val INDEX_BUNDLE = "index.android.bundle"
    const val DEFAULT_AVAILABLE_VALUE = 10

    private const val TAG = "OPKHelper"

    //opk 备份文件
    private const val BACKUP_FILES = "/backup_files/"

    //opk后缀名
    private const val OPK_SUFFIX = ".opk"

    fun getAppBeanForOPKFile(opkPath: String, id: String): AppBeanV2? {

        val file = File(opkPath)
        if (!file.exists()) {
            Log.e(TAG, "opk file not exist")
            return null
        }

        val memorySize = FileHelper.getAvailableExternalMemorySize()
        if (file.length() * DEFAULT_AVAILABLE_VALUE > memorySize) {
            Log.e(TAG, "sdcard no available free ${file.length() * 5} available: $memorySize")
            return null
        }

        val parentFile = file.parentFile.absolutePath + "/" + file.nameWithoutExtension

        if (OpkFile.verify(file) != 0) {
            Log.e(TAG, "verify failed, please check opk file")
            reportOpkOpCmd(id, "", OpkOpType.OPK_SIGNED, OpkOpStatus.RECEIVE_CMD)
            return null
        }

        reportOpkOpCmd(id, "", OpkOpType.OPK_SIGNED, OpkOpStatus.OPK_OP_SUCCESS)

        Log.d(TAG, "path: $parentFile")
        if (FileHelper.unzip(opkPath, parentFile)) {
            Log.d(TAG, "opkPath: $opkPath")
            val json = FileHelper.loadJsonFromFile("$parentFile/$ORION_OS_FILE")
            Log.d(TAG, "json: $json")
            if (TextUtils.isEmpty(json)) {
                return null
            }
            val jsonOrionOS = JSONObject(json)
            val opkLoad = jsonOrionOS.optString("opkLoad", null)
            val appId = jsonOrionOS.optString("appid", null)
            val opkBeanV3 = GsonUtil.fromJson(json, OPKBeanV3::class.java)
            //备份opk文件
            try {
                Log.d(TAG, "appid:$appId")
                Log.d(TAG, "opkBeanV2.appid:" + opkBeanV3.appid)
                val backupPath = FileHelper.getCacheDir(BaseApplication.getContext()).toString() + BACKUP_FILES + appId + '.' + file.extension
                Log.d(TAG, "backupParentPath:$backupPath")
                val backupFile = File(backupPath)
                if (!backupFile.parentFile.exists()) {
                    backupFile.parentFile.mkdirs()
                }
                //保留当前最新的安装包
                backupFile.parentFile.listFiles().iterator().forEach {
                    it.delete()
                }
                backupFile.createNewFile()
                FileHelper.copyFile(File(opkPath), backupFile)
            } catch (e: IOException) {
                e.printStackTrace()
                Log.d(TAG, "backup file failed")
            }
            FileHelper.deleteTargetFile(opkPath)
            Log.d(TAG, "bizMd5" + opkBeanV3.bizMd5)
            //校验bundle有效性,需要兼容之前没有md5信息bundle
            if (opkLoad == "v1") {
                if (opkBeanV3.bizMd5 != null && opkBeanV3.bizMd5 != Md5.generateFileMD5("$parentFile/$INDEX_BUNDLE")) {
                    Log.d(TAG, "opk v1 bundle check failed")
                    //上报加载错误
                    reportOpkOpCmd(id, opkBeanV3.appid, OpkOpType.OPK_UNZIP, OpkOpStatus.LAUNCHER_FAIL)
                    RobotApi.getInstance().updateRnInstallStatus(0, id, Definition.STATUS_RN_INSTALL_FAILED, "{\"code\":1002, \"message\":\"error rpk\"}", null)
                    return null
                }
            } else if (opkLoad == "v2") {
                if (opkBeanV3.bizMd5 != null && opkBeanV3.platformMd5 != Md5.generateFileMD5("$parentFile/$PLATFORM_BUNDLE") || opkBeanV3.bizMd5 != Md5.generateFileMD5("$parentFile/$BIZ_BUNDLE")) {
                    Log.d(TAG, "opk v2 bundle check failed")
                    //上报加载错误
                    reportOpkOpCmd(id, opkBeanV3.appid, OpkOpType.OPK_UNZIP, OpkOpStatus.LAUNCHER_FAIL)
                    RobotApi.getInstance().updateRnInstallStatus(0, id, Definition.STATUS_RN_INSTALL_FAILED, "{\"code\":1002, \"message\":\"error rpk\"}", null)
                    return null
                }
            } else if (opkLoad == "v3") {
                if (opkBeanV3.bizMd5 != null && opkBeanV3.platformMd5 != Md5.generateFileMD5("$parentFile/$PLATFORM_BUNDLE") || opkBeanV3.bizMd5 != Md5.generateFileMD5("$parentFile/$BIZ_BUNDLE")) {
                    Log.d(TAG, "opk v2 bundle check failed")
                    //上报加载错误
                    reportOpkOpCmd(id, opkBeanV3.appid, OpkOpType.OPK_UNZIP, OpkOpStatus.LAUNCHER_FAIL)
                    RobotApi.getInstance().updateRnInstallStatus(0, id, Definition.STATUS_RN_INSTALL_FAILED, "{\"code\":1002, \"message\":\"error rpk\"}", null)
                    return null
                }

                val oldOPKInfo = AppManger.getRPKByCharacter(opkBeanV3.appid)
                opkBeanV3.configId = oldOPKInfo?.configId
                opkBeanV3.configPath = oldOPKInfo?.configPath
                opkBeanV3.plugin = oldOPKInfo?.plugin
            }

            if (TextUtils.isEmpty(opkBeanV3.appid)) {
                reportOpkOpCmd(id, "", OpkOpType.OPK_UNZIP, OpkOpStatus.LAUNCHER_SUCCESS)
                return null
            }

            reportOpkOpCmd(id, opkBeanV3.appid, OpkOpType.OPK_UNZIP, OpkOpStatus.OPK_OP_SUCCESS)

            val appidFile = file.parentFile.absolutePath + "/" + opkBeanV3.appid
            opkBeanV3.installTime = System.currentTimeMillis()
            opkBeanV3.path = appidFile

            try {
                if (appidFile != parentFile) {
                    FileHelper.deleteDirectory(File(appidFile))
                    FileHelper.copyDirectory(File(parentFile), File(appidFile))
                    FileHelper.deleteDirectory(File(parentFile))
                }
            } catch (e: IOException) {
                reportOpkOpCmd(id, "", OpkOpType.OPK_UNZIP, OpkOpStatus.LAUNCHER_FAIL)
                e.printStackTrace()
                return null
            }
            return when (opkLoad) {
                "v2", "v3" -> {
                    val bundleBizFilePath = "$appidFile/$BIZ_BUNDLE"
                    val bundlePlatformFilePath = "$appidFile/$PLATFORM_BUNDLE"
                    if (FileHelper.isExist(bundleBizFilePath) && FileHelper.isExist(bundlePlatformFilePath)) {
                        AppBeanV2(null, bundleBizFilePath, bundlePlatformFilePath, opkBeanV3)
                    } else {
                        null
                    }
                }
                "v1", null -> {
                    val bundleIndexFilePath = "$appidFile/$INDEX_BUNDLE"
                    if (FileHelper.isExist(bundleIndexFilePath)) {
                        AppBeanV2(bundleIndexFilePath, null, null, opkBeanV3)
                    } else {
                        null
                    }
                }
                else -> {
                    null
                }
            }
        }
        reportOpkOpCmd(id, "", OpkOpType.OPK_UNZIP, OpkOpStatus.RECEIVE_CMD)
        return null
    }

    fun onHandleWithOPK(opkBeanV3: OPKBeanV3, v1Callback: ((opkBeanV3: OPKBeanV3) -> Unit), v2Callback: ((opkBeanV3: OPKBeanV3) -> Unit), v3Callback: ((opkBeanV3: OPKBeanV3) -> Unit)) {
        when (opkBeanV3.opkLoad) {
            "v3" -> {
                v3Callback.invoke(opkBeanV3)
            }
            "v2" -> {
                v2Callback.invoke(opkBeanV3)
            }
            "v1", null -> {
                v1Callback.invoke(opkBeanV3)
            }
            else -> {
            }
        }
    }

    /**
     * 将opk信息转换成Json
     * */
    const val APPID = "appId"
    const val VERSION_NAME = "versionName"
    const val CORE_VERSION = "coreVersion"
    const val OPK_LOAD = "opkLoad"

    fun OPKInfo2JSON(opkBeanV3: OPKBeanV3): JSONObject {
        val jsonObject = JSONObject()
        jsonObject.put(APPID, opkBeanV3.appid)
        jsonObject.put(VERSION_NAME, opkBeanV3.versionName)
        jsonObject.put(CORE_VERSION, opkBeanV3.coreVersion ?: opkBeanV3.coreTarget)
        jsonObject.put(OPK_LOAD, opkBeanV3.opkLoad)
        return jsonObject
    }

    fun reportOpkOpCmd(msgId: String, appid: String, @OpkOpType type: Int, @OpkOpStatus status: Int) {
        var errMsg = ""
        var tmpMsgId = msgId
        var tmpAppid = appid

        if (TextUtils.isEmpty(msgId) || TextUtils.isEmpty(appid)) {
            Log.w(EveActivity.TAG, "reportOpkOpCmd: param is error msgId: $msgId appid: $appid")
            errMsg = "param error"
            tmpMsgId = ""
            tmpAppid = ""
        }

        ReportControl.getInstance().reportMsg(OpkOperationPoint(tmpMsgId, tmpAppid, type, status, errMsg))
    }

    /**
     *根据appid返回对应的备份路径
     */
    fun getBackupPath(appId: String?): String {
        return FileHelper.getCacheDir(BaseApplication.getContext()).toString() + BACKUP_FILES + appId + OPK_SUFFIX

    }

    /**
     * 版本比较
     *
     * @param oldVersion
     * @param newVersion
     * @return
     */
    fun compareVersion(oldVersion: String, newVersion: String): Int {
        Log.d(TAG, "Compare version : $oldVersion  new : $newVersion")
        if (TextUtils.isEmpty(newVersion)) {
            return -1
        }
        val oldV = oldVersion.split(".").toTypedArray()
        val newV = newVersion.split(".").toTypedArray()
        val min = Math.min(oldV.size, newV.size)
        for (index in 0 until min) {
            return try {
                val oldIndex = oldV[index].toInt()
                val newIndex = newV[index].toInt()

                if (oldIndex == newIndex) {
                    continue
                }

                if (newIndex > oldIndex) 1 else -1
            } catch (e: NumberFormatException) {
                e.printStackTrace()
                -1
            }
        }

        if (oldV.size == newV.size) {
            return 0
        }
        return if (newV.size > oldV.size) 1 else -1
    }

}
