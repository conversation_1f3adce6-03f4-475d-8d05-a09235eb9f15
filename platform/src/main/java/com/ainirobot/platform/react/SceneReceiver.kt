package com.ainirobot.platform.react

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.ainirobot.platform.PlatformDef
import com.ainirobot.platform.control.ControlManager
import org.json.JSONException
import org.json.JSONObject

class SceneReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent != null) {
            try {
                val json = JSONObject()
                json.put("name", intent.getStringExtra("name"))

                ControlManager.handleRequest(0, PlatformDef.LAUNCH_RN, "", json.toString());
            } catch (e: JSONException) {
                e.printStackTrace()
            }
        }
    }


}
