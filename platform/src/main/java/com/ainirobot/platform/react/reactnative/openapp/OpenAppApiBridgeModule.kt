package com.ainirobot.platform.react.reactnative.openapp

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.text.TextUtils
import android.util.Log
import com.ainirobot.coreservice.client.Definition
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.R
import com.ainirobot.platform.react.view.FloatDialogManager
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.google.gson.Gson

class OpenAppApiBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    companion object {
        val TAG = "OpenAppApiBridgeModule"
        val mGson = Gson()
    }

    override fun getName(): String {
        return "OpenAppApi"
    }

    private fun checkPackageInfo(context: Context, packageName: String): Boolean {
        var packageInfo: PackageInfo? = null
        try {
            packageInfo = context.packageManager.getPackageInfo(packageName, 0)
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return packageInfo != null
    }

    @ReactMethod
    fun openThirdPartyApp(packageName: String,activityName: String): Boolean {
        return openThirdPartyAppLocal(packageName, activityName, "", true)
    }

    @ReactMethod
    fun openThirdPartyAppIfKillRn(packageName: String, activityName: String, isKillRN: Boolean): Boolean {
        return openThirdPartyAppLocal(packageName, activityName, "", isKillRN)
    }

    @ReactMethod
    fun openThirdPartyAppNeedParams(packageName: String, activityName: String, params: String): Boolean {
        return openThirdPartyAppLocal(packageName, activityName, params,true)
    }

    private fun openThirdPartyAppLocal(packageName: String, activityName: String, params: String, isKillRN: Boolean): Boolean {
        Log.d(TAG, "openThirdPartyAppLocal activityName = $activityName");
        val context = BaseApplication.getContext()
        val packageManager = context.getPackageManager()
        if (checkPackageInfo(context, packageName)) {
            var intent: Intent?

            if (!TextUtils.isEmpty(activityName)) {
                intent = Intent()
                intent!!.setComponent(ComponentName(packageName, activityName))
                if (intent!!.resolveActivityInfo(packageManager, PackageManager.MATCH_DEFAULT_ONLY) == null) {
                    intent = packageManager.getLaunchIntentForPackage(packageName)
                    // FloatDialogManager.getInstance().showFloatDialog(FloatDialogManager
                    //         .FloatDialogType.TYPE_RESPONSE_LITE, BaseApplication.getContext()
                    //         .getString(R.string.response_no_Activity))
                }
            } else {
                intent = packageManager.getLaunchIntentForPackage(packageName)
            }

            if (intent == null) {
                Log.d(TAG, "can not start package = $packageName")
                return false
            }
            if (!TextUtils.isEmpty(params)) {
                intent.putExtra("params", params)
            }
            if (isKillRN) {
                intent!!.addCategory(Definition.CATEGORY_OPEN_APP)
            }
            intent!!.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
            return true
        } else {
            FloatDialogManager.getInstance().showFloatDialog(FloatDialogManager
                    .FloatDialogType.TYPE_RESPONSE_LITE, BaseApplication.getContext()
                    .getString(R.string.response_no_app))
            return false
        }
    }

    @ReactMethod
    fun openThirdPartyAppForResult(packageName: String, activityName: String, promise: Promise) {
        openThirdPartyAppForResultLocal(packageName, activityName, "", true, promise)
    }

    @ReactMethod
    fun openThirdPartyAppIfKillRnForResult(packageName: String, activityName: String, isKillRN: Boolean, promise: Promise) {
        openThirdPartyAppForResultLocal(packageName, activityName, "", isKillRN, promise)
    }

    @ReactMethod
    fun openThirdPartyAppNeedParamsForResult(packageName: String, activityName: String, params: String, promise: Promise) {
        openThirdPartyAppForResultLocal(packageName, activityName, params, true, promise)
    }

    private fun openThirdPartyAppForResultLocal(packageName: String, activityName: String, params: String, isKillRN: Boolean, promise: Promise) {
        Log.d(TAG, "openThirdPartyAppForResult activityName = $activityName");
        val context = BaseApplication.getContext()
        val packageManager = context.getPackageManager()
        if (checkPackageInfo(context, packageName)) {
            var intent: Intent?

            if (!TextUtils.isEmpty(activityName)) {
                intent = Intent()
                intent!!.setComponent(ComponentName(packageName, activityName))
                if (intent!!.resolveActivityInfo(packageManager, PackageManager.MATCH_DEFAULT_ONLY) == null) {
                    intent = packageManager.getLaunchIntentForPackage(packageName)
                    //  FloatDialogManager.getInstance().showFloatDialog(FloatDialogManager
                    //          .FloatDialogType.TYPE_RESPONSE_LITE, BaseApplication.getContext()
                    //          .getString(R.string.response_no_Activity))
                }
            } else {
                intent = packageManager.getLaunchIntentForPackage(packageName)
            }

            if (intent == null) {
                Log.d(TAG, "can not start package = $packageName")
                try {
                    promise.resolve(false);
                } catch (e: Exception) {
                    promise.reject(e)
                }
                return
            }
            if (!TextUtils.isEmpty(params)) {
                intent.putExtra("params", params)
            }
            if (isKillRN) {
                intent!!.addCategory(Definition.CATEGORY_OPEN_APP)
            }
            intent!!.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
            try {
                promise.resolve(true)
            } catch (e: Exception) {
                promise.reject(e)
            }
        } else {
            FloatDialogManager.getInstance().showFloatDialog(FloatDialogManager
                    .FloatDialogType.TYPE_RESPONSE_LITE, BaseApplication.getContext()
                    .getString(R.string.response_no_app))
            try {
                promise.resolve(false);
            } catch (e: Exception) {
                promise.reject(e)
            }
        }
    }
}
