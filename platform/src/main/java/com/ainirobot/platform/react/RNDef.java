package com.ainirobot.platform.react;/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

public class RNDef {

    public static final String RN_SERVER_SERVICE_NAME = "com.ainirobot.platform.react.server.control.RNServerService";
    public static final String RN_SERVER_ACTION_NAME = "com.ainirobot.platform.rn.server.SERVICE";
    public static final String RN_ACTION_NAME = "com.ainirobot.platform.rn.SERVICE";

    public static final int BIND_API = 0x1;
    public static final int BIND_COMPONENT = 0x2;
    public static final int BIND_SPEECH_API = 0x3;
    public static final int BIND_DATA = 0x4;
    public static final int BIND_LIGHT = 0x5;
    public static final int BIND_PERSON = 0x6;
    public static final int BIND_DEVICE = 0x07;
    public static final int BIND_INTENT = 0x08;
    public static final int BIND_SETTING = 0X09;
    public static final int BIND_PACKAGE = 0X10;
    public static final int BIND_NLP_APK_CONTROL = 0x11;
    public static final int BIND_MSG = 0X12;
    public static final int BIND_MAP = 0x13;
    public static final int BIND_CALL_BUTTON = 0x14;
    public static final int BIND_ROBOT_INFO = 0x15;
    public static final int BIND_OKHTTP = 0x16;

}
