package com.ainirobot.platform.react.reactnative.component.uicomponent.statusbar;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.PixelFormat;
import android.net.ConnectivityManager;
import android.net.wifi.SupplicantState;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Bundle;
import android.telephony.PhoneStateListener;
import android.telephony.SignalStrength;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.ContextThemeWrapper;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.R;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.ref.WeakReference;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class StatusBar implements IStatusBar {

    private static final String TAG = StatusBar.class.getSimpleName();
    private final static String ACTION_SIM_STATE_CHANGED
            = "android.intent.action.SIM_STATE_CHANGED";
    static final int[] SIM_STATE_NO_SERVICE = new int[]{R.attr.state_mobile_type_no_service};
    static final int[] SIM_STATE_2G = new int[]{R.attr.state_mobile_type_2g};
    static final int[] SIM_STATE_3G = new int[]{R.attr.state_mobile_type_3g};
    static final int[] SIM_STATE_4G = new int[]{R.attr.state_mobile_type_4g};
    static final int[] SIM_STATE_NO_DATA = new int[]{R.attr.state_mobile_type_no_data};
    static final int[] BATTERY_PLUGGED = new int[]{R.attr.state_battery_plugged};
    private volatile static StatusBar sInstance;
    private SignalStrength mMobileSignalStrength = null;
    private final WindowManager mWindowManager;
    private volatile View mStatusBarView = null;
    private ImageView mStatusBatteryItemView = null;
    private TextView mStatusBatteryTextView = null;
    private TextView mStatusBatteryChargingTextView = null;
    private ImageView mStatusWifiItemView = null;
    private ImageView mStatusMobileItemView = null;
    private TextView mStatusTimeTextView = null;
    private ImageView mStatusMultiRobotIconView = null;
    private TextView mStatusMultiRobotNumTextView = null;
    private static final WindowManager.LayoutParams sLayoutParams = new WindowManager
            .LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE, PixelFormat.TRANSLUCENT);
    private final TelephonyManager mTelephonyManager;
    private WifiManager mWifiManager;
    private volatile boolean mIsShow = false;
    private WeakReference<Activity> mActivity;
    private List<MultiRobotStatus> mRobotStatusList;
    private final Gson mGson;

    public static StatusBar getInstance() {
        if (sInstance == null) {
            synchronized (StatusBar.class) {
                if (sInstance == null) {
                    sInstance = new StatusBar();
                }
            }
        }
        return sInstance;
    }

    public StatusBar() {
        mWindowManager = (WindowManager) BaseApplication.getApplication().getSystemService(Context.WINDOW_SERVICE);
        mTelephonyManager = (TelephonyManager) BaseApplication.getContext().getSystemService(Context.TELEPHONY_SERVICE);
        mWifiManager = (WifiManager) BaseApplication.getApplication().getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        mGson = new Gson();

        sLayoutParams.gravity = Gravity.END | Gravity.TOP;
        sLayoutParams.packageName = BaseApplication.getContext().getPackageName();
        sLayoutParams.setTitle("RobotStatusBar");
        sLayoutParams.alpha = 1.0f;
        sLayoutParams.dimAmount = 0f;
    }

    private void initView() {
        if(mStatusBarView == null){
            mStatusBarView = LayoutInflater.from(new ContextThemeWrapper(BaseApplication.getApplication(), R.style.AppTheme)).inflate(R.layout.statusbar, null, false);
            mStatusBatteryItemView = (ImageView) mStatusBarView.findViewById(R.id.quick_battery);
            mStatusBatteryTextView = (TextView) mStatusBarView.findViewById(R.id.quick_battery_value);
            mStatusBatteryChargingTextView = (TextView) mStatusBarView.findViewById(R.id.quick_battery_charge);
            mStatusWifiItemView = (ImageView) mStatusBarView.findViewById(R.id.quick_wifi);
            mStatusMobileItemView = (ImageView) mStatusBarView.findViewById(R.id.quick_mobile);
            mStatusTimeTextView = (TextView) mStatusBarView.findViewById(R.id.quick_time);
            mStatusMultiRobotIconView = (ImageView) mStatusBarView.findViewById(R.id.robot_icon);
            mStatusMultiRobotIconView.setVisibility(View.INVISIBLE);
            mStatusMultiRobotNumTextView = (TextView) mStatusBarView.findViewById(R.id.multi_robot_number);
            mStatusMultiRobotNumTextView.setVisibility(View.INVISIBLE);
            addStatusBarView();
        }
    }

    private synchronized void addStatusBarView() {
        Log.d(TAG,"addStatusBarView: mIsShow = " + mIsShow);
        if (mIsShow) {
            return;
        }
        if(mStatusBarView != null && !mStatusBarView.isAttachedToWindow()){
            Log.d(TAG,"addStatusBarView:　Real do addView.");
            mWindowManager.addView(mStatusBarView, sLayoutParams);
            mIsShow = true;
        }
    }

    private synchronized void removeStatusBarView() {
        Log.d(TAG,"removeStatusBarView: mIsShow = " + mIsShow);
        if (!mIsShow) {
            return;
        }
        if(mStatusBarView != null){
            Log.d(TAG,"removeStatusBarView:　Real do removeView.");
            mWindowManager.removeView(mStatusBarView);
            mIsShow = false;
        }
    }

    private void deInitView() {
        mStatusBarView = null;
        mStatusBatteryItemView = null;
        mStatusBatteryTextView = null;
        mStatusBatteryChargingTextView = null;
        mStatusWifiItemView = null;
        mStatusMobileItemView = null;
        mStatusTimeTextView = null;
        mStatusMultiRobotIconView = null;
        mStatusMultiRobotNumTextView = null;
    }

    @Override
    public void onCreate() {
        initView();

        final IntentFilter filter = new IntentFilter();
        filter.addAction(WifiManager.WIFI_STATE_CHANGED_ACTION);
        filter.addAction(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION);
        filter.addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION);
        filter.addAction(WifiManager.RSSI_CHANGED_ACTION);
        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        filter.addAction(ACTION_SIM_STATE_CHANGED);
        mBroadcastReceiver.onReceive(BaseApplication.getContext(), BaseApplication.getApplication().registerReceiver(mBroadcastReceiver, filter));
        mBroadcastReceiver.onReceive(BaseApplication.getContext(), BaseApplication.getApplication().registerReceiver(mBroadcastReceiver, new IntentFilter(Intent.ACTION_BATTERY_CHANGED))); // update
        mTelephonyManager.listen(mPhoneStateListener, PhoneStateListener.LISTEN_CELL_INFO | PhoneStateListener.LISTEN_SERVICE_STATE
                | PhoneStateListener.LISTEN_SIGNAL_STRENGTHS);
    }

    @Override
    public void onStart(Activity activity) {
        mActivity = new WeakReference<>(activity);
        updateMobileConnectionInfo();
        updateWifiConnectionInfo(safeGetWifiManager().getConnectionInfo());
        updateTime();

        //注册多机状态广播监听
        Log.d(TAG,"onStart register mulit status broadcast ");
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Definition.INTENT_MULTI_ROBOT_STATUS_UPDATE);
        BaseApplication.getContext().registerReceiver(mMultiRobotReceiver,intentFilter);

    }

    @Override
    public void onStop() {
        removeStatusBarView();
        //取消注册多机状态广播监听
        Log.d(TAG,"onStop unRegister mulit status broadcast ");
        BaseApplication.getContext().unregisterReceiver(mMultiRobotReceiver);
    }

    @Override
    public void onDestroy() {
        removeStatusBarView();
        deInitView();
    }

    private void updateMobileConnectionInfo() {
        if (mTelephonyManager == null) {
            return;
        }
        int simState = mTelephonyManager.getSimState();
//        Log.d(TAG, "updateMobileConnectionInfo simState=" + simState + ", mStatusMobileItemView: " + mStatusMobileItemView);
        updateMobileNetworkTypeImage(simState);
        if (simState == TelephonyManager.SIM_STATE_READY) {
            mStatusMobileItemView.setVisibility(View.VISIBLE);
            mStatusMobileItemView.setImageLevel((mMobileSignalStrength != null) ? mMobileSignalStrength.getLevel() : 0);
        } else {
            mStatusMobileItemView.setVisibility(View.GONE);
        }

        checkIsShowBatteryIcon();
    }


    final BroadcastReceiver mMultiRobotReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null) {
                switch (intent.getAction()) {
                    case Definition.INTENT_MULTI_ROBOT_STATUS_UPDATE:
                        Bundle bundle = intent.getExtras();
                        String eventData = bundle.getString("data");
                        //Log.d(TAG,"get multi robot status: " + eventData);
                        handleMultipleRobotStatus(eventData);
                        break;
                    default:
                        break;
                }
            }
        }
    };

    final BroadcastReceiver mBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null) {
                switch (intent.getAction()) {
                    case Intent.ACTION_BATTERY_CHANGED:
                        updateBatteryInfo(intent);
                        updateWifiConnectionInfo(safeGetWifiManager().getConnectionInfo());
                        updateMobileConnectionInfo();
                        break;
                    case ACTION_SIM_STATE_CHANGED:
                    case ConnectivityManager.CONNECTIVITY_ACTION:
                    case WifiManager.WIFI_STATE_CHANGED_ACTION:
                    case WifiManager.NETWORK_STATE_CHANGED_ACTION:
                    case WifiManager.SCAN_RESULTS_AVAILABLE_ACTION:
                    case WifiManager.RSSI_CHANGED_ACTION:
                        updateWifiConnectionInfo(safeGetWifiManager().getConnectionInfo());
                        updateMobileConnectionInfo();
                        break;
                    default:
                        break;

                }
            }
        }
    };

    WifiManager safeGetWifiManager() {
        return mWifiManager != null ? mWifiManager : (mWifiManager = (WifiManager) BaseApplication
                .getApplication().getApplicationContext().getSystemService(Context.WIFI_SERVICE));
    }

    void updateMobileNetworkTypeImage(int simState) {
        if (mTelephonyManager == null) {
            return;
        }
        if (simState != TelephonyManager.SIM_STATE_READY) {
            mStatusMobileItemView.setVisibility(View.GONE);
            mStatusMobileItemView.setImageState(null, true);
            return;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (!mTelephonyManager.isDataEnabled()) {
                mStatusMobileItemView.setVisibility(View.VISIBLE);
                mStatusMobileItemView.setImageState(SIM_STATE_NO_DATA, true);
                checkIsShowBatteryIcon();
                return;
            }
        } else {
            if (mTelephonyManager.getDataState() == TelephonyManager.DATA_DISCONNECTED) {
                mStatusMobileItemView.setVisibility(View.VISIBLE);
                mStatusMobileItemView.setImageState(SIM_STATE_NO_DATA, true);
                checkIsShowBatteryIcon();
                return;
            }
        }
        final int networkType = mTelephonyManager.getNetworkType();
        switch (networkType) {
            case TelephonyManager.NETWORK_TYPE_GPRS:
            case TelephonyManager.NETWORK_TYPE_EDGE:
            case TelephonyManager.NETWORK_TYPE_CDMA:
            case TelephonyManager.NETWORK_TYPE_1xRTT:
            case TelephonyManager.NETWORK_TYPE_IDEN:
                mStatusMobileItemView.setVisibility(View.VISIBLE);
                mStatusMobileItemView.setImageState(SIM_STATE_2G, true);
                break;
            case TelephonyManager.NETWORK_TYPE_UMTS:
            case TelephonyManager.NETWORK_TYPE_EVDO_0:
            case TelephonyManager.NETWORK_TYPE_EVDO_A:
            case TelephonyManager.NETWORK_TYPE_HSDPA:
            case TelephonyManager.NETWORK_TYPE_HSUPA:
            case TelephonyManager.NETWORK_TYPE_HSPA:
            case TelephonyManager.NETWORK_TYPE_EVDO_B:
            case TelephonyManager.NETWORK_TYPE_EHRPD:
            case TelephonyManager.NETWORK_TYPE_HSPAP:
                mStatusMobileItemView.setVisibility(View.VISIBLE);
                mStatusMobileItemView.setImageState(SIM_STATE_3G, true);
                break;
            case TelephonyManager.NETWORK_TYPE_LTE:
                mStatusMobileItemView.setVisibility(View.VISIBLE);
                mStatusMobileItemView.setImageState(SIM_STATE_4G, true);
                break;
            default:
                mStatusMobileItemView.setVisibility(View.VISIBLE);
                mStatusMobileItemView.setImageState(SIM_STATE_NO_SERVICE, true);
                break;
        }
        checkIsShowBatteryIcon();
    }

    void updateBatteryInfo(Intent intent) {
        int level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0);
        int plugged = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0);
//        Log.d(TAG, "updateBatteryInfo level: " + level + ", plugged: " + plugged);
        mStatusBatteryItemView.setImageLevel(level * 100);
        mStatusBatteryItemView.setImageState(plugged != 0 ? BATTERY_PLUGGED : null, true);
        mStatusBatteryChargingTextView.setVisibility(plugged != 0 ? View.VISIBLE : View.GONE);
        mStatusBatteryTextView.setText(level + "%");
    }

    void updateWifiConnectionInfo(WifiInfo wifiInfo) {
//        Log.d(TAG, "updateWifiConnectionInfo level: " + WifiManager.calculateSignalLevel(wifiInfo.getRssi(), 4));
        if (isWifiConnected(wifiInfo)) {
            mStatusWifiItemView.setVisibility(View.VISIBLE);
            mStatusWifiItemView.setImageLevel(WifiManager.calculateSignalLevel(wifiInfo.getRssi(), 4));
        } else {
            mStatusWifiItemView.setVisibility(View.GONE);
            mStatusWifiItemView.setImageLevel(4);
        }

        checkIsShowBatteryIcon();
    }

    private void checkIsShowBatteryIcon() {
        if (mStatusWifiItemView.getVisibility() == View.VISIBLE && mStatusMobileItemView.getVisibility() == View.VISIBLE) {
            mStatusBatteryItemView.setVisibility(View.GONE);
        } else {
            mStatusBatteryItemView.setVisibility(View.VISIBLE);
        }
    }

    public static boolean isWifiConnected(WifiInfo wifiInfo) {
        return (wifiInfo != null) && ((wifiInfo.getSupplicantState() == SupplicantState.COMPLETED)) && (wifiInfo.getNetworkId() != -1);
    }

    final PhoneStateListener mPhoneStateListener = new PhoneStateListener() {

        public void onDataConnectionStateChanged(int state, int networkType) {
//            Log.d(TAG, "onDataConnectionStateChanged");
        }

        public void onServiceStateChanged(android.telephony.ServiceState serviceState) {
            // Log.d(TAG, "onServiceStateChanged:: serviceState=" +
            // serviceState);
            updateMobileConnectionInfo();
        }

        public void onCellInfoChanged(java.util.List<android.telephony.CellInfo> cellInfo) {
            // Log.d(TAG, "onCellInfoChanged:: cellInfo=" + cellInfo);
            updateMobileConnectionInfo();
        }

        public void onSignalStrengthsChanged(android.telephony.SignalStrength signalStrength) {
            // Log.d(TAG, "onSignalStrengthsChanged:: signalStrength=" +
            // signalStrength);
            mMobileSignalStrength = signalStrength;
            updateMobileConnectionInfo();
        }
    };

    private void updateTime() {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm", Locale.CHINA);
        Date date = new Date(System.currentTimeMillis());
        String curDate = formatter.format(date);
        mStatusTimeTextView.setText(curDate);
        DelayTask.submit(new Runnable() {
            @Override
            public void run() {
                if (mActivity.get() != null) {
                    mActivity.get().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            updateTime();
                        }
                    });
                }
            }
        }, 2000);
    }

    @Override
    public synchronized void showStatusBar(boolean isShow) {
        if (isShow) {
            addStatusBarView();
        } else {
            removeStatusBarView();
        }
    }

    @Override
    public synchronized void showStatusBarByType(boolean isShow, int type) {
    }

    //处理多机状态数据
    private void handleMultipleRobotStatus(String eventData){
        if(mStatusBarView == null){
            return;
        }

        String language =  BaseApplication.getContext().getResources().getConfiguration().locale.getLanguage();

        //阿拉伯语情况，不显示
        if(language.equals("ar")){
            return;
        }
        
        int otherRobotNum = 0;

        //其他机器的状态数据
        try {
            List<MultiRobotStatus> mOtherRobotStatusList = null;
            Type dataType = new TypeToken<List<MultiRobotStatus>>(){}.getType();
            if(!TextUtils.isEmpty(eventData)){
                mOtherRobotStatusList = mGson.fromJson(eventData, dataType);
            }

            if(mOtherRobotStatusList != null && mOtherRobotStatusList.size() > 0){
                otherRobotNum = mOtherRobotStatusList.size();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if(otherRobotNum == 0){

            //多机数量为0，不显示图标和数量
            mStatusBarView.post(new Runnable() {
                @Override
                public void run() {
                    mStatusMultiRobotIconView.setVisibility(View.INVISIBLE);
                    mStatusMultiRobotNumTextView.setVisibility(View.INVISIBLE);
                }
            });
        }else{

            //多机数量不为0，显示多机图标和数量
            final int finalOtherRobotNum = otherRobotNum;
            mStatusBarView.post(new Runnable() {
                @Override
                public void run() {
                    mStatusMultiRobotIconView.setVisibility(View.VISIBLE);
                    mStatusMultiRobotNumTextView.setVisibility(View.VISIBLE);
                    mStatusMultiRobotNumTextView.setText(String.valueOf(finalOtherRobotNum));
                }
            });

        }

    }

}
