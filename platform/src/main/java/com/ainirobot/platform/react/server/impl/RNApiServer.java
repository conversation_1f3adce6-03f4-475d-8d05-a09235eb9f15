/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.react.server.impl;

import android.content.Context;
import android.os.IBinder;
import android.os.PowerManager;
import android.os.RemoteException;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.bean.BackgroundTaskEvent;
import com.ainirobot.coreservice.bean.Task;
import com.ainirobot.coreservice.bean.TaskEvent;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.TaskEventProxy;
import com.ainirobot.coreservice.client.TaskProxy;
import com.ainirobot.coreservice.client.actionbean.GongFuBean;
import com.ainirobot.coreservice.client.actionbean.HeadTurnBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.subtype.DeviceSubType;
import com.ainirobot.coreservice.utils.FileUtils;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.data.FeatureConfig;
import com.ainirobot.platform.react.reactnative.component.uicomponent.external.view.ExternalDisplay;
import com.ainirobot.platform.react.server.control.RNServerManager;
import com.ainirobot.platform.react.server.utils.RNServerUtils;
import com.ainirobot.platform.rn.IApiRegistry;
import com.ainirobot.platform.rn.listener.IRNApiListener;
import com.ainirobot.platform.rn.listener.IRNAtomizerStatusListener;
import com.ainirobot.platform.rn.listener.IRNCollideStatusListener;
import com.ainirobot.platform.rn.listener.IRNEmergencyListener;
import com.ainirobot.platform.rn.listener.IRNFunctionKeyListener;
import com.ainirobot.platform.rn.listener.IRNRemoteStandByListener;
import com.ainirobot.platform.rn.listener.IRNRobotStatusListener;
import com.ainirobot.platform.utils.VisionUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class RNApiServer extends IApiRegistry.Stub {

    private static final String TAG = RNApiServer.class.getSimpleName();
    private static final String MODEL_MEISSA = "CM-GB01N";
    private static final String MODEL_MEISSA_COST_DOWN = "CM-GB01C";
    private static final String MODEL_MEISSA_1P5 = "OS-R-SG02S";
    private static final String MODEL_LARA = "CM-GB01L";
    private static final String MODEL_LARA_DUAL = "CM-GB01D";
    private static final String MODEL_LARA_DUAL_PIE = "CM-GB02D";
    private static final String MODEL_LARA_DUAL_PIE_COST_DOWN = "CM-GB03D";
    private static final String MODEL_SAIPH_DP = "OS-R-AD01S";

    private int mReqId = 0;
    private RobotApi mApi = RobotApi.getInstance();
    private final Object CALLBACK_LOCK = new Object();
    private final List<Callback> mCallbacks;
    private final Object STATUS_CALLBACK_LOCK = new Object();
    private final List<StatusCallback> mStatusCallbacks;
    private final Object EMERGENCY_CALLBACK_LOCK = new Object();
    private final Map<Integer, IRNEmergencyListener> mEmergencyCallbacks;
    private final Object FUNCTION_KEY_CALLBACK_LOCK = new Object();
    private final Map<Integer, IRNFunctionKeyListener> mFunctionKeyCallbacks;
    private final Object ATOMIZER_CALLBACK_LOCK = new Object();
    private final Map<Integer, IRNAtomizerStatusListener> mDryStatusCallbacks;
    private final Object REMOTE_STANDBY_CALLBACK_LOCK = new Object();
    private final Map<Integer, IRNRemoteStandByListener> mRemoteStandbyCallbacks;
    private final Object COLLIDE_STATUS_CALLBACK_LOCK = new Object();
    private final Map<Integer, IRNCollideStatusListener> mCollideStatusCallbacks;
    private final Object ELECTRIC_DOOR_STATUS_CALLBACK_LOCK = new Object();
    private final Map<Integer, IRNRobotStatusListener> mElectricDoorStatusCallbacks;

    public RNApiServer() {
        mCallbacks = new ArrayList<>();
        mEmergencyCallbacks = new HashMap<>();
        mStatusCallbacks = new ArrayList<>();
        mFunctionKeyCallbacks = new HashMap<>();
        mDryStatusCallbacks = new ConcurrentHashMap<>();
        mRemoteStandbyCallbacks = new ConcurrentHashMap<>();
        mCollideStatusCallbacks = new ConcurrentHashMap<>();
        mElectricDoorStatusCallbacks = new ConcurrentHashMap<>();
    }

    @Override
    public void disableEmergency() {
        mApi.disableEmergency();
    }

    @Override
    public void disableBattery() {
        mApi.disableBattery();
    }

    @Override
    public void setLanguage(String language) {
        Log.d(TAG, "RNApiServer setLanguage");
        mApi.setLanguage(language);
    }

    @Override
    public int getLanguageList(IRNApiListener listener) {
        return mApi.getLanguageList(mReqId, createCallback(listener));
    }

    @Override
    public int getPlaceList(IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.getPlaceList(mReqId, createCallback(listener));
    }

    @Override
    public int getPlaceListWithName(IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.getPlaceListWithName(mReqId, createCallback(listener));
    }

    @Override
    public int getPlace(String param, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.getPlace(mReqId, param, createCallback(listener));
    }

    @Override
    public int getPlacesByType(int typeId, IRNApiListener listener) {
        return mApi.getPlacesByType(mReqId, typeId, createCallback(listener));
    }

    @Override
    public int isRobotInlocations(String param, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.isRobotInlocations(mReqId, param, createCallback(listener));
    }

    @Override
    public int isRobotEstimate(IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.isRobotEstimate(mReqId, createCallback(listener));
    }

    @Override
    public int resetHead(IRNApiListener listener) {
        return mApi.resetHead(mReqId, createCallback(listener));
    }

    @Override
    public int switchCamera(String mode, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.switchCamera(mReqId, mode, createCallback(listener));
    }

    @Override
    public int turnHead(String headTurnBean, IRNApiListener listener) {
        Gson gson = new Gson();
        HeadTurnBean bean = gson.fromJson(headTurnBean, HeadTurnBean.class);
        return mApi.turnHead(mReqId, bean, createCallback(listener));
    }

    @Override
    public int stopTurnHead(IRNApiListener listener) {
        return mApi.stopTurnHead(mReqId, createCallback(listener));
    }

    @Override
    public int getHeadStatus(IRNApiListener listener) {
        return mApi.getHeadStatus(mReqId, createCallback(listener));
    }

    @Override
    public int textToMp3(String text, String fullFile, String fileName, IRNApiListener listener) {
        return mApi.textToMp3(mReqId, text, fullFile, fileName, createCallback(listener));
    }

    @Override
    public int stopChargingByApp() {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.stopChargingByApp();
    }

    @Override
    public int canRobotReboot(IRNApiListener listener) {
        return mApi.canRobotReboot(mReqId, createCallback(listener));
    }

    @Override
    public int getMapName(IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.getMapName(mReqId, createCallback(listener));
    }

    @Override
    public int getPosition(IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.getPosition(mReqId, createCallback(listener));
    }

    @Override
    public int remoteWakeUpTimes(IRNApiListener listener) {
        return mApi.remoteWakeUpTimes(mReqId, createCallback(listener));
    }

    @Override
    public int getBatteryTimeRemaining(IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.getBatteryTimeRemaining(mReqId, createCallback(listener));
    }

    @Override
    public int getChargeTimeRemaining(IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.getChargeTimeRemaining(mReqId, createCallback(listener));
    }

    @Override
    public int getAskWayList(IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.getAskWayList(mReqId, createCallback(listener));
    }

    @Override
    public int startVision(IRNApiListener listener) {
        return mApi.startVision(mReqId, createCallback(listener));
    }

    @Override
    public int stopVision(IRNApiListener listener) {
        return mApi.stopVision(mReqId, createCallback(listener));
    }

    @Override
    public int startBackupVision(IRNApiListener listener) {
        return mApi.startBackupVision(mReqId, createCallback(listener));
    }

    @Override
    public int stopBackupVision(IRNApiListener listener) {
        return mApi.stopBackupVision(mReqId, createCallback(listener));
    }

    @Override
    public int skillDataReport(String param, IRNApiListener listener) {
        return mApi.skillDataReport(mReqId, param, createCallback(listener));
    }

    @Override
    public int setLambColor(int target, int color) {
        return mApi.setLambColor(mReqId, target, color);
    }

    @Override
    public int setLambAnimation(int target, int start, int end, int startTime,
                                int endTime, int repeat, int onTime, int freeze) {
        return mApi.setLambAnimation(mReqId, target, start, end, startTime,
                endTime, repeat, onTime, freeze);
    }

    @Override
    public int robotReboot(String reason) {
        PowerManager powerManager = (PowerManager) BaseApplication.getContext()
                .getSystemService(Context.POWER_SERVICE);
        if (powerManager != null) {
            try {
                powerManager.reboot(reason);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return 0;
    }

    @Override
    public int restartRNProcess() {
        RNServerManager.getInstance().restartRNProcess();
        return 0;
    }

    @Override
    public boolean isLara() {
        String productModel = RobotSettings.getProductModel();
        return MODEL_LARA.equals(productModel)
                || MODEL_LARA_DUAL.equals(productModel)
                || MODEL_LARA_DUAL_PIE.equals(productModel)
                || MODEL_LARA_DUAL_PIE_COST_DOWN.equals(productModel);
    }

    @Override
    public boolean isMeissa() {
        String productModel = RobotSettings.getProductModel();
        return MODEL_MEISSA.equals(productModel)
                || MODEL_MEISSA_COST_DOWN.equals(productModel)
                || MODEL_MEISSA_1P5.equals(productModel);
    }

    @Override
    public boolean isSaiphDp(){
        String productModel = RobotSettings.getProductModel();
        Log.d(TAG, " productModel = " + productModel);
        return MODEL_SAIPH_DP.equals(productModel);
    }

    @Override
    public boolean isMini() throws RemoteException {
        return false;
    }

    @Override
    public int getRobotSn(IRNApiListener listener) {
        return mApi.getRobotSn(createCallback(listener));
    }

    @Override
    public int getNavigationConfig(IRNApiListener listener){
        return mApi.getNavigationConfig(mReqId, createCallback(listener));
    }

    @Override
    public int playTrick(String param, IRNApiListener listener) {
        Gson gson = new Gson();
        GongFuBean bean = gson.fromJson(param, GongFuBean.class);
        return mApi.startPlayAction(mReqId, bean, createCallback(listener));
    }

    @Override
    public int stopPlayTrick() {
        return mApi.stopPlayAction(mReqId);
    }

    @Override
    public int getCanRotateSupport(IRNApiListener listener) {
        return mApi.getCanRotateSupport(mReqId, createCallback(listener));
    }

    @Override
    public int turnLeft(float speed, float angle, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.turnLeft(mReqId, speed, angle, createCallback(listener));
    }

    @Override
    public int turnRight(float speed, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.turnRight(mReqId, speed, createCallback(listener));
    }

    @Override
    public int turnRight2(float speed, float angle, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.turnRight(mReqId, speed, angle, createCallback(listener));
    }

    @Override
    public int stopMove(IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.stopMove(mReqId, createCallback(listener));
    }

    @Override
    public void sendStatusReport(String type, String data) {
        if (!mApi.isApiConnectedService()) {
            Log.w(TAG, "sendStatusReport: not connect service");
            return;
        }
        mApi.sendStatusReport(type, data);
    }

    @Override
    public int getLocation(String param, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.getLocation(mReqId, param, createCallback(listener));
    }

    @Override
    public boolean isActive() {
        return mApi.isActive();
    }

    @Override
    public int goForward(float speed, float distance, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.goForward(mReqId, speed, distance, createCallback(listener));
    }

    @Override
    public int goBackward(float speed, float distance, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.goBackward(mReqId, speed, distance, createCallback(listener));
    }

    @Override
    public int rotate(float speed, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.rotate(mReqId, speed, createCallback(listener));
    }

    @Override
    public int startVelocityReport(IRNApiListener listener) {
        return Definition.CMD_SEND_ERROR_UNSUPPORTED;
    }

    @Override
    public int stopVelocityReport(IRNApiListener listener) {
        return Definition.CMD_SEND_ERROR_UNSUPPORTED;
    }

    @Override
    public int setMaxAcceleration(float xAcc, float yAcc, float zAcc, IRNApiListener listener) {
        return Definition.CMD_SEND_ERROR_UNSUPPORTED;
    }

    @Override
    public int motionLine(String direction, float speed, float distance, IRNApiListener listener) {
        return Definition.CMD_SEND_ERROR_UNSUPPORTED;
    }

    @Override
    public int motionArc(float distance, float angle, float angularSpeed, float latency, IRNApiListener listener) {
        return Definition.CMD_SEND_ERROR_UNSUPPORTED;
    }

    @Override
    public int motionArcWithObstacles(float lineSpeed, float angularSpeed, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.motionArcWithObstacles(mReqId, lineSpeed, angularSpeed, createCallback(listener));
    }

    @Override
     public int getPlaceListByMapName(String mapName, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.getPlaceListByMapName(mReqId, mapName, createCallback(listener));
    }

    @Override
    public int getPlaceListWithNameList(String param, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.getPlaceListWithNameList(mReqId, param, createCallback(listener));
    }

    @Override
    public int goPosition(String position, String velocity, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.goPosition(mReqId, position, velocity, createCallback(listener));
    }

    @Override
    public int goPosition2(String position, float linearSpeed, float angularSpeed, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.goPosition(mReqId, position, linearSpeed, angularSpeed, createCallback(listener));
    }

    @Override
    public int stopGoPosition() {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.stopGoPosition(mReqId);
    }

    @Override
    public int getNavigationLineSpeed(IRNApiListener listener) {
        return Definition.CMD_SEND_ERROR_UNSUPPORTED;
    }

    @Override
    public int getNavigationAngleSpeed(IRNApiListener listener) {
        return Definition.CMD_SEND_ERROR_UNSUPPORTED;
    }

    @Override
    public int moveHead(String hmode, String vmode, int hangle, int vangle, int hMaxSpeed, int vMaxSpeed, IRNApiListener listener) {
        return mApi.moveHead(mReqId, hmode, vmode, hangle, vangle, hMaxSpeed, vMaxSpeed, createCallback(listener));
    }

    @Override
    public int isHeaderConnected(IRNApiListener listener) {
        return mApi.isHeaderConnected(mReqId, createCallback(listener));
    }

    @Override
    public int getChargeStatus(IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.getChargeStatus(mReqId, createCallback(listener));
    }

    @Override
    public int setStartChargePoseAction(long timeout, IRNApiListener listener) {
//        return mApi.setStartChargePoseAction(mReqId, timeout, createCallback(listener));
        return Definition.CMD_SEND_ERROR_UNSUPPORTED;
    }

    @Override
    public int startNaviToAutoChargeAction(long timeout, double distance, long avoidTime, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.startNaviToAutoChargeAction(mReqId, timeout, distance, avoidTime, createCallback(listener));
    }

    @Override
    public int stopAutoChargeAction(boolean isResetHW) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.stopAutoChargeAction(mReqId, isResetHW);
    }

    @Override
    public int getEmergencyStatus(IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.getEmergencyStatus(mReqId, createCallback(listener));
    }

    @Override
    public int startInspection(long time, boolean isReInspection, IRNApiListener listener) {
        return mApi.startInspection(mReqId, time, isReInspection, createCallback(listener));
    }

    @Override
    public int stopInspection(boolean isResetHW) {
        return mApi.stopInspection(mReqId, isResetHW);
    }

    @Override
    public int resumeSpecialPlaceTheta(String placeName, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.resumeSpecialPlaceTheta(mReqId, placeName, createCallback(listener));
    }

    @Override
    public int stopResumeSpecialPlaceThetaAction() {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.stopResumeSpecialPlaceThetaAction(mReqId);
    }

    @Override
    public int remoteRequestQrcode(IRNApiListener listener) {
        return mApi.remoteRequestQrcode(mReqId, createCallback(listener));
    }

    @Override
    public int checkIfHasObstacle(double startAngle, double endAngle, double distance, IRNApiListener listener) {
//        if (!isMeissa()) {
//            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
//        }
        return mApi.checkIfHasObstacle(mReqId, startAngle, endAngle, distance, createCallback(listener));
    }

    @Override
    public int getBatteryLevel() {
        return mApi.getBatteryLevel();
    }

    @Override
    public boolean rnHeartBeat() {
        Log.d(TAG, "rnHeartBeat fun");
        return RNServerManager.getInstance().rnHeartBeat();
    }

    @Override
    public int getPersonInfoFromNet(String personId, List<String> pictures, IRNApiListener listener) {
        Log.d(TAG, "getPersonInfoFromNet fun");
        return mApi.getPersonInfoFromNet(mReqId, personId, pictures, createCallback(listener));
    }

    @Override
    public int getHeadCount(IRNApiListener listener) {
        Log.d(TAG, "getHeadCount fun");
        return mApi.getHeadCount(mReqId, createCallback(listener));
    }

    @Override
    public int updatePictureReportConfig(String config, IRNApiListener listener) {
        Log.d(TAG, "updatePictureReportConfig fun");
        return mApi.updatePictureReportConfig(mReqId, config, createCallback(listener));
    }

    @Override
    public int capScreen(int type, IRNApiListener listener) {
        return mApi.capScreen(mReqId, type, createCallback(listener));
    }

    @Override
    public int getCpuTemperature(IRNApiListener listener) {
        return mApi.getCpuTemperature(mReqId, createCallback(listener));
    }

    @Override
    public boolean updateRobotStatus(int status) {
        return mApi.updateRobotStatus(status);
    }

    @Override
    public void getDoorStatus(int type, IRNApiListener listener) {
        mApi.getDoorStatus(mReqId, type, createCallback(listener));
    }

    @Override
    public void scoreRecordingTask(String params, IRNApiListener listener) {
        mApi.scoreRecordingTask(mReqId, params, createCallback(listener));
    }

    @Override
    public void setLockEnable(int type, int bord, boolean enable) {
        mApi.setLockEnable(mReqId, type, bord, enable);
    }

    @Override
    public void monitorCollideStatus(int callbackId, IRNCollideStatusListener listener) {
        if (listener == null) {
            return;
        }
        synchronized (COLLIDE_STATUS_CALLBACK_LOCK) {
            if (mCollideStatusCallbacks.size() <= 0) {
                mApi.registerStatusListener(Definition.STATUS_ANTI_COLLISION_STRIP, mCollideStatusListener);
            }
            mCollideStatusCallbacks.put(callbackId, listener);
        }
    }

    @Override
    public void unMonitorCollideStatus(int callbackId) {
        synchronized (COLLIDE_STATUS_CALLBACK_LOCK) {
            if (mCollideStatusCallbacks.containsKey(callbackId)) {
                mCollideStatusCallbacks.remove(callbackId);
                if (mCollideStatusCallbacks.size() <= 0) {
                    mApi.unregisterStatusListener(mCollideStatusListener);
                }
            }
        }
    }


    @Override
    public void monitorDryStatus(int callbackId, IRNAtomizerStatusListener listener) {
        if (listener == null) {
            return;
        }
        synchronized (ATOMIZER_CALLBACK_LOCK) {
            if (mDryStatusCallbacks.size() <= 0) {
                mApi.registerStatusListener(Definition.STATUS_XD_DRY, mAtomizerStatusListener);
            }
            mDryStatusCallbacks.put(callbackId, listener);
        }
    }

    @Override
    public void unMonitorDryStatus(int callbackId) {
        synchronized (ATOMIZER_CALLBACK_LOCK) {
            if (mDryStatusCallbacks.containsKey(callbackId)) {
                mDryStatusCallbacks.remove(callbackId);
                if (mDryStatusCallbacks.size() <= 0) {
                    mApi.unregisterStatusListener(mAtomizerStatusListener);
                }
            }
        }
    }

    @Override
    public void monitorRemoteStandBy(int callbackId, IRNRemoteStandByListener listener) {
        if (listener == null) {
            return;
        }
        synchronized (REMOTE_STANDBY_CALLBACK_LOCK) {
            if (mRemoteStandbyCallbacks.size() <= 0) {
                mApi.registerStatusListener("remote_standby_request", mRemoteStandbyStatusListener);
            }
            mRemoteStandbyCallbacks.put(callbackId, listener);
        }
    }

    @Override
    public void unMonitorRemoteStandBy(int callbackId) {
        synchronized (REMOTE_STANDBY_CALLBACK_LOCK) {
            if (mRemoteStandbyCallbacks.containsKey(callbackId)) {
                mRemoteStandbyCallbacks.remove(callbackId);
                if (mRemoteStandbyCallbacks.size() <= 0) {
                    mApi.unregisterStatusListener(mRemoteStandbyStatusListener);
                }
            }
        }
    }

    @Override
    public int goCharging(){
        return mApi.goCharging(mReqId);
    }

    @Override
    public void startTakeOverEmergency(int callbackId, IRNEmergencyListener listener) {
        if (listener == null) {
            return;
        }
        synchronized (EMERGENCY_CALLBACK_LOCK) {
            if (mEmergencyCallbacks.size() <= 0) {
                mApi.disableEmergency();
                mApi.registerStatusListener(Definition.STATUS_EMERGENCY, mEmergencyStatusListener);
            }
            mEmergencyCallbacks.put(callbackId, listener);
        }
        mApi.getRobotStatus(Definition.STATUS_EMERGENCY, mEmergencyStatusListener);
    }

    @Override
    public void stopTakeOverEmergency(int callbackId) {
        synchronized (EMERGENCY_CALLBACK_LOCK) {
            if (mEmergencyCallbacks.containsKey(callbackId)) {
                mEmergencyCallbacks.remove(callbackId);
                if (mEmergencyCallbacks.size() <= 0) {
                    mApi.enableEmergency();
                    mApi.unregisterStatusListener(mEmergencyStatusListener);
                }
            }
        }
    }


    @Override
    public void startTakeOverFunctionKey(int callbackId, IRNFunctionKeyListener listener) {
        Log.d(TAG, "startTakeOverFunctionKey");
        if (listener == null) {
            return;
        }
        synchronized (FUNCTION_KEY_CALLBACK_LOCK) {
            if (mFunctionKeyCallbacks.size() <= 0) {
                mApi.disableFunctionKey();
                mApi.registerStatusListener(Definition.STATUS_MULTI_FUNCTION_SWITCH, mFunctionKeyStatusListener);
            }
            mFunctionKeyCallbacks.put(callbackId, listener);
            mApi.getRobotStatus(Definition.STATUS_MULTI_FUNCTION_SWITCH, mFunctionKeyStatusListener);
        }
    }

    @Override
    public void stopTakeOverFunctionKey(int callbackId) {
        Log.d(TAG, "stopTakeOverFunctionKey");
        synchronized (FUNCTION_KEY_CALLBACK_LOCK) {
            if (mFunctionKeyCallbacks.containsKey(callbackId)) {
                mFunctionKeyCallbacks.remove(callbackId);
                if (mFunctionKeyCallbacks.size() <= 0) {
                    mApi.enableFunctionKey();
                    mApi.unregisterStatusListener(mFunctionKeyStatusListener);
                }
            }
        }
    }

    private void clearEmergencyCallback() {
        synchronized (EMERGENCY_CALLBACK_LOCK) {
            mEmergencyCallbacks.clear();
            mApi.enableEmergency();
            mApi.unregisterStatusListener(mEmergencyStatusListener);
        }
    }

    private void clearFunctionKeyCallback() {
        synchronized (FUNCTION_KEY_CALLBACK_LOCK) {
            mFunctionKeyCallbacks.clear();
            mApi.enableFunctionKey();
            mApi.unregisterStatusListener(mFunctionKeyStatusListener);
        }
    }

    @Override
    public int getVisionResolution(IRNApiListener listener) {
        return mApi.getVisionResolution(mReqId, createCallback(listener));
    }

    private StatusListener mEmergencyStatusListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
            if (TextUtils.isEmpty(data)) {
                return;
            }
            synchronized (EMERGENCY_CALLBACK_LOCK) {
                for (int callbackId : mEmergencyCallbacks.keySet()) {
                    IRNEmergencyListener listener = mEmergencyCallbacks.get(callbackId);
                    if (listener == null) {
                        stopTakeOverEmergency(callbackId);
                        return;
                    }
                    try {
                        listener.onEmergencyStatusChanged(data);
                    } catch (RemoteException e) {
                        RNServerUtils.handleRemoteException("ApiCallback onEmergencyStatusChanged", e);
                        stopTakeOverEmergency(callbackId);
                    }
                }
            }
        }
    };


    private StatusListener mCollideStatusListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
            if (TextUtils.isEmpty(data)) {
                return;
            }
            synchronized (COLLIDE_STATUS_CALLBACK_LOCK) {
                for (int callbackId : mCollideStatusCallbacks.keySet()) {
                    IRNCollideStatusListener listener = mCollideStatusCallbacks.get(callbackId);
                    if (listener == null) {
                        unMonitorCollideStatus(callbackId);
                        return;
                    }
                    try {
                        listener.onCollideStatusChanged(data);
                    } catch (RemoteException e) {
                        RNServerUtils.handleRemoteException("ApiCallback onCollideStatusChanged", e);
                        unMonitorCollideStatus(callbackId);
                    }
                }
            }
        }
    };


    private StatusListener mAtomizerStatusListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
            if (TextUtils.isEmpty(data)) {
                return;
            }
            synchronized (ATOMIZER_CALLBACK_LOCK) {
                for (int callbackId : mDryStatusCallbacks.keySet()) {
                    IRNAtomizerStatusListener listener = mDryStatusCallbacks.get(callbackId);
                    if (listener == null) {
                        unMonitorDryStatus(callbackId);
                        return;
                    }
                    try {
                        listener.onDryStatusChanged(data);
                    } catch (RemoteException e) {
                        RNServerUtils.handleRemoteException("ApiCallback onDryStatusChanged", e);
                        unMonitorDryStatus(callbackId);
                    }
                }
            }
        }
    };

    private StatusListener mRemoteStandbyStatusListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
            if (TextUtils.isEmpty(data)) {
                return;
            }
            synchronized (REMOTE_STANDBY_CALLBACK_LOCK) {
                for (int callbackId : mRemoteStandbyCallbacks.keySet()) {
                    IRNRemoteStandByListener listener = mRemoteStandbyCallbacks.get(callbackId);
                    if (listener == null) {
                        unMonitorRemoteStandBy(callbackId);
                        return;
                    }
                    try {
                        listener.onRemoteStandByChanged(data);
                    } catch (RemoteException e) {
                        RNServerUtils.handleRemoteException("ApiCallback onRemoteStandbyChanged", e);
                        unMonitorRemoteStandBy(callbackId);
                    }
                }
            }
        }
    };


    private StatusListener mFunctionKeyStatusListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
            Log.d(TAG, "functionKey onStatusUpdate type: " + type + ", data: " + data);
            if (TextUtils.isEmpty(data)) {
                return;
            }
            try {
                JSONObject jsonObject = new JSONObject(data);
                if (!jsonObject.has("state")) {
                    return;
                }
                synchronized (FUNCTION_KEY_CALLBACK_LOCK) {
                    for (int callbackId : mFunctionKeyCallbacks.keySet()) {
                        IRNFunctionKeyListener listener = mFunctionKeyCallbacks.get(callbackId);
                        if (listener == null) {
                            stopTakeOverFunctionKey(callbackId);
                            return;
                        }
                        try {
                            listener.onStatusChanged(data);
                        } catch (RemoteException e) {
                            RNServerUtils.handleRemoteException("ApiCallback onFunctionKeyStatusChanged", e);
                            stopTakeOverFunctionKey(callbackId);
                        }
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    };


    public void answerAppResult(String param, IRNApiListener listener) {
        mApi.answerAppResult(mReqId, param, createCallback(listener));
    }

    @Override
    public void getUserListWithVideo(String params, IRNApiListener listener) {
        mApi.getUserListWithVideo(mReqId, params, createCallback(listener));
    }

    @Override
    public void inviteCallApp(String params, IRNApiListener listener) {
        mApi.inviteCallApp(mReqId, params, createCallback(listener));
    }

    @Override
    public boolean updateStandbyStatus(boolean standbyStart, String jsonData) throws RemoteException {
        return mApi.updateStandbyStatus(standbyStart, jsonData);
    }

    @Override
    public int taskModeReport(String taskId, String taskMode, int taskResult, IRNApiListener listener) throws RemoteException {
        return mApi.taskModeReport(mReqId, taskId, taskMode, taskResult, createCallback(listener));
    }

    @Override
    public int execTaskReport(String taskId, String taskType, String execResult, String execData, IRNApiListener listener) throws RemoteException {
        return mApi.taskExecReport(mReqId, taskId, taskType, execResult, execData, createCallback(listener));
    }

    @Override
    public int taskCommandReport(String cmdId, String cmdType, String execResult, String execData, IRNApiListener listener) throws RemoteException {
        return mApi.taskCommandReport(mReqId, cmdId, cmdType, execResult, execData, createCallback(listener));
    }


    @Override
    public int taskGroupQueue(String allowTaskType, String taskGroupTypeList, IRNApiListener listener) throws RemoteException {
        return mApi.taskGroupQueue(mReqId, allowTaskType, taskGroupTypeList, createCallback(listener));
    }

    @Override
    public int taskCreate(String parentTaskId, String taskType, String taskList, IRNApiListener listener) throws RemoteException {
        return mApi.taskCreate(mReqId, parentTaskId, taskType, taskList, createCallback(listener));
    }

    @Override
    public int taskTake(String taskId, IRNApiListener listener) throws RemoteException {
        return mApi.taskTake(mReqId, taskId, createCallback(listener));
    }

    @Override
    public int cabinetPutGoodsStatus(String taskId, String stationType, String stationId, String stationOrderId, IRNApiListener listener) throws RemoteException {
        return mApi.cabinetPutGoodsStatus(mReqId, taskId, stationType, stationId, stationOrderId, createCallback(listener));
    }

    @Override
    public int taskManual(String taskId, IRNApiListener listener) throws RemoteException {
        return mApi.taskManual(mReqId, taskId, createCallback(listener));
    }

    @Override
    public int taskCancel(String taskId, String cancelResult, IRNApiListener listener) throws RemoteException {
        return mApi.taskCancel(mReqId, taskId, cancelResult, createCallback(listener));
    }

    @Override
    public int taskPosQrcode(String qrcodeType, String posName, IRNApiListener listener) throws RemoteException {
        return mApi.taskPosQrcode(mReqId, qrcodeType, posName, createCallback(listener));
    }

    @Override
    public int getSpecialDishLabels(IRNApiListener listener) throws RemoteException {
        return mApi.getSpecialDishLabels(mReqId, createCallback(listener));
    }

    @Override
    public int shutdown(String reason) throws RemoteException {

        try {
            Class<?> serviceManager = Class.forName("android.os.ServiceManager");
            Method getService = serviceManager.getMethod("getService", String.class);
            Object remoteService = getService.invoke(null, Context.POWER_SERVICE);
            Class<?> stub = Class.forName("android.os.IPowerManager$Stub");
            Method asInterface = stub.getMethod("asInterface", IBinder.class);
            Object powerManager = asInterface.invoke(null, remoteService);
            Method shutdown = powerManager.getClass().getDeclaredMethod("shutdown",
                    boolean.class, String.class, boolean.class);
            shutdown.invoke(powerManager, false, reason, true);
        } catch (Exception e) {
            //nothing to do
        }

        return 0;
    }

    @Override
    public int getAppToken(String app_type, String app_name, String app_version, IRNApiListener listener) throws RemoteException {
        return mApi.getAppToken(mReqId, app_type, app_name, app_version, createCallback(listener));
    }

    @Override
    public int taskSucc(String taskId, IRNApiListener listener) throws RemoteException {
        return mApi.taskSucc(mReqId, taskId, createCallback(listener));
    }

    @Override
    public int getNaviPathInfo(String startPoseJson, String endPoseJson, IRNApiListener listener) throws RemoteException {

        Pose startPose = new Pose();
        Pose endPose = new Pose();
        try {
            JSONObject startOb = new JSONObject(startPoseJson);
            JSONObject endOb = new JSONObject(endPoseJson);

            startPose.setX((float) startOb.optDouble("x"));
            startPose.setY((float) startOb.optDouble("y"));
            startPose.setTheta((float) startOb.optDouble("theta"));

            endPose.setX((float) endOb.optDouble("x"));
            endPose.setY((float) endOb.optDouble("y"));
            endPose.setTheta((float) endOb.optDouble("theta"));
        } catch (JSONException e) {
            e.printStackTrace();
            return -1;
        }

        return mApi.getNaviPathInfo(mReqId, startPose, endPose, createCallback(listener));
    }

    @Override
    public int getNaviPathInfoToGoal(List<String> golaNames, IRNApiListener listener) throws RemoteException {
        return mApi.getNaviPathInfoToGoals(mReqId, golaNames, createCallback(listener));
    }

    public int floorPositionList(String floorId, String posName, String status, IRNApiListener listener) throws RemoteException {
        return mApi.floorPositionList(mReqId, floorId, posName, status, createCallback(listener));
    }

    @Override
    public int setXDPowderEnable(boolean enable){
        return mApi.setXDPowerEnable(mReqId,enable,null);
    }

    @Override
    public int setXDFanEnable(boolean enable){
        return mApi.setXDFanEnable(mReqId,enable,null);
    }

    @Override
    public int setXDRank(int rank){
        return mApi.setXDRank(mReqId,rank,null);
    }

    @Override
    public int moduleCodeConfigUpload(String moduleCode, String description, String configName, String configJson, IRNApiListener listener) {
        return mApi.moduleCodeConfigUpload(mReqId, moduleCode, description, configName, configJson, createCallback(listener));
    }

    @Override
    public void enableBattery() {
        if (ProductInfo.isSaiphXdOrBigScreen() && FeatureConfig.isAllowChargingChat()){
            //消毒机型,在充电可用时,不允许上层opk enableBattery.
            return;
        }
        mApi.enableBattery();
    }

    @Override
    public void registerStatusListener(String type, IRNRobotStatusListener listener) {
        mApi.registerStatusListener(type, createStatusCallback(listener));
    }

    @Override
    public void unregisterStatusListener(IRNRobotStatusListener listener) {
        synchronized (STATUS_CALLBACK_LOCK) {
            for (StatusCallback callback : mStatusCallbacks) {
                if (callback.listener == listener) {
                    mStatusCallbacks.remove(callback);
                    mApi.unregisterStatusListener(callback);
                }
            }
        }
    }

    public void getDefWelcomeTTS(final IRNApiListener listener) {
        final Callback callback = new Callback(listener) {
            @Override
            public void onResult(int result, String message) {
                try {
                    if (VisionUtils.isRemoteDetectSuccess(message)) {
                        Log.i(TAG, "get default welcome info:" + message);
                        Person person = new Person();
                        VisionUtils.updatePersonData(person, message, true);
                        listener.onResult(result, person.toGson(), "");
                    } else {
                        listener.onResult(result, message, "");
                    }
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
                onCommandFinish(this);
            }
        };
        synchronized (CALLBACK_LOCK) {
            mCallbacks.add(callback);
        }
        mApi.getDefWelcomeTTS(mReqId, callback);
    }

    @Override
    public void takePicture(int type, final IRNApiListener listener) {
        mApi.takePicture(mReqId, String.valueOf(type), createCallback(listener));
    }

    @Override
    public void uploadCruisePicture(String params, final IRNApiListener listener) {
        mApi.uploadCruisePicture(mReqId, params, createCallback(listener));
    }

    public String reportTask(int taskMode, String taskType, String name, String subTaskListStr) {
        return reportRobotCurrentTask(taskMode, taskType, name, "", subTaskListStr);
    }

    @Override
    public String reportRobotCurrentTask(int taskMode, String taskType, String name, String taskId, String subTaskListStr) {
        Log.d(TAG, "reportTask subTaskList: " + subTaskListStr);
        TaskProxy task = new TaskProxy();
        task.setType(taskType);
        task.setName(name);
        task.setMode(taskMode);
        task.setTaskId(taskId);
        task.setStatus(Task.TASK_STATUS_START);
        if (!TextUtils.isEmpty(subTaskListStr)) {
            Gson gson = new Gson();
            final Type type = new TypeToken<TaskEvent[]>() {
            }.getType();
            TaskEvent[] subTaskList = gson.fromJson(subTaskListStr, type);
            task.setSubTaskList(subTaskList);
        }
        return mApi.reportTask(task);
    }

    @Override
    public void reportTaskEvent(String taskEvent) {
        Gson gson = new Gson();
        mApi.reportTaskEvent(new TaskEventProxy(gson.fromJson(taskEvent, TaskEvent.class)));
    }

    @Override
    public void reportBackgroundTaskEvent(String taskEvent) {
        Gson gson = new Gson();
        mApi.reportBackgroundTaskEvent(gson.fromJson(taskEvent, BackgroundTaskEvent.class));
    }

    @Override
    public String getCurrentTaskId() {
        List<Task> taskList = mApi.getCurrentTasks();
        if (taskList != null && taskList.size() > 0) {
            return taskList.get(0).getTaskId();
        }
        return "";
    }

    @Override
    public int getMultiFloorConfigAndPose(IRNApiListener listener){
        return mApi.getMultiFloorConfigAndPose(mReqId, createCallback(listener));
    }

    @Override
    public int getMultiFloorConfigAndCommonPose(IRNApiListener listener){
        return mApi.getMultiFloorConfigAndCommonPose(mReqId, createCallback(listener));
    }

    private Callback createCallback(IRNApiListener listener) {
        if (listener == null) {
            return null;
        }
        Callback callback = new Callback(listener);
        synchronized (CALLBACK_LOCK) {
            mCallbacks.add(callback);
        }
        return callback;
    }

    private StatusCallback createStatusCallback(IRNRobotStatusListener listener) {
        if (listener == null) {
            return null;
        }
        StatusCallback callback = new StatusCallback(listener);
        synchronized (STATUS_CALLBACK_LOCK) {
            mStatusCallbacks.add(callback);
        }
        return callback;
    }

    public void clearCallbacks() {
        Log.d(TAG, "clearCallbacks");
        clearEmergencyCallback();
        clearFunctionKeyCallback();
        synchronized (CALLBACK_LOCK) {
            for (Callback callback : mCallbacks) {
                callback.stop();
            }
            mCallbacks.clear();
        }
        synchronized (STATUS_CALLBACK_LOCK) {
            for (StatusCallback callback : mStatusCallbacks) {
                callback.stop();
            }
            mStatusCallbacks.clear();
        }
        mApi.stopPlayAction(mReqId);
    }

    private void onCommandFinish(Callback callback) {
        synchronized (CALLBACK_LOCK) {
            callback.stop();
            mCallbacks.remove(callback);
        }
    }

    private class Callback extends CommandListener {
        private IRNApiListener listener;

        Callback(IRNApiListener listener) {
            this.listener = listener;
        }

        public void stop() {
            listener = null;
        }

        @Override
        public void onResult(int result, String message, String extraData) {
            try {
                if (listener != null) {
                    listener.onResult(result, message, extraData);
                }
                onCommandFinish(this);
            } catch (RemoteException e) {
                RNServerUtils.handleRemoteException("ApiCallback onResult", e);
            }
        }

        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            try {
                if (listener != null) {
                    listener.onStatusUpdate(status, data, extraData);
                }
            } catch (RemoteException e) {
                RNServerUtils.handleRemoteException("ApiCallback onStatusUpdate", e);
            }
        }

        @Override
        public void onError(int errorCode, String errorString, String extraData) {
            try {
                if (listener != null) {
                    listener.onError(errorCode, errorString, extraData);
                }
                onCommandFinish(this);
            } catch (RemoteException e) {
                RNServerUtils.handleRemoteException("ApiCallback onError", e);
            }
        }
    }

    private class StatusCallback extends StatusListener {
        private IRNRobotStatusListener listener;

        StatusCallback(IRNRobotStatusListener listener) {
            this.listener = listener;
        }

        public void stop() {
            mApi.unregisterStatusListener(this);
            listener = null;
        }

        @Override
        public void onStatusUpdate(String status, String data) {
            try {
                if (listener != null) {
                    listener.onStatusUpdate(status, data);
                }
            } catch (RemoteException e) {
                RNServerUtils.handleRemoteException("ApiStatusCallback onStatusUpdate", e);
            }
        }
    }

    @Override
    public String getLocalAndServerSupportLanguageList() {
        return mApi.getLocalAndServerSupportLanguageList();
    }

    @Override
    public void robotStandby() {
        mApi.robotStandby(mReqId,null);
    }

    @Override
    public void robotStandByEnd() {
        mApi.robotStandbyEnd(mReqId);
    }

    @Override
    public int queryRadarStatus(IRNApiListener listener) throws RemoteException {
        return mApi.queryRadarStatus(mReqId, createCallback(listener));
    }

    @Override
    public int getScreenBrightness() {
        int brightness = 0;
        try {
            brightness = Settings.System.getInt(BaseApplication.getContext()
                    .getContentResolver(), Settings.System.SCREEN_BRIGHTNESS);
        } catch (Settings.SettingNotFoundException e) {
            e.printStackTrace();
        }
        return brightness;
    }

    @Override
    public boolean setScreenBrightness(int brightness){
        boolean result = Settings.System.putInt(BaseApplication.getContext().getContentResolver(),
                Settings.System.SCREEN_BRIGHTNESS, brightness);
        return result;
    }

    @Override
    public int updateRadarStatus(boolean openRadar,IRNApiListener listener){
        return mApi.updateRadarStatus(mReqId,openRadar,createCallback(listener));
    }

    @Override
    public int queryUvcCameraConnectedStatus(IRNApiListener listener) throws RemoteException {
        return mApi.queryUvcCameraConnectedStatus(mReqId , createCallback(listener));
    }

    @Override
    public int getNaviAngSpeed(String speed, IRNApiListener listener) throws RemoteException {
        return mApi.getNaviAngSpeed(mReqId, speed, createCallback(listener));
    }

    @Override
    public void proactiveProblemReport(double occurrenceTime, String issueType, String packageName, String pageName, String description){
        Log.d(TAG,"proactiveProblemReport");
        mApi.proactiveProblemReport((long)occurrenceTime,issueType,packageName,pageName,description);
    }

    @Override
    public void setElectricDoorCtrl(int ctrlCmd, IRNApiListener listener) throws RemoteException {
        Log.d(TAG,"setElectricDoorCtrl");
        mApi.setElectricDoorCtrl(mReqId , ctrlCmd , createCallback(listener));
    }

    @Override
    public void getElectricDoorStatus(IRNApiListener listener) throws RemoteException {
        Log.d(TAG,"getElectricDoorStatus");
        mApi.getElectricDoorStatus(mReqId , createCallback(listener));
    }

    @Override
    public boolean isSupportElectricDoor() throws RemoteException {
        Log.d(TAG,"isSupportElectricDoor");
        boolean b = FileUtils.hasElectricDoor();
        Log.d(TAG,"isSupportElectricDoor :"+b);
        return FileUtils.hasElectricDoor();
    }

    @Override
    public boolean isWaiterProCarry() throws RemoteException {
        Log.d(TAG,"isWaiterProCarry");
        boolean b = DeviceSubType.getInstance().isWaiterProCarry();
        Log.d(TAG,"isWaiterProCarry :"+b);
        return b;
    }

    @Override
    public boolean isSupportElevator() throws RemoteException {
        Log.d(TAG,"isSupportElevator");
        boolean b = FileUtils.hasElevator();
        Log.d(TAG,"isSupportElevator :"+b);
        return FileUtils.hasElevator();
    }

    @Override
    public void getMotionDistance(IRNApiListener listener) throws RemoteException {
        Log.d(TAG,"getMotionDistance");
        mApi.getMotionDistance(mReqId , createCallback(listener));
    }

    @Override
    public void setQueryFeedback(String chatMaxSid, String feedbackResult) {
        mApi.setQueryFeedback(mReqId, chatMaxSid, feedbackResult, null);
    }

    @Override
    public void registerElectricDoorStateListener(int callbackId, IRNRobotStatusListener listener) throws RemoteException {
        if (listener == null) {
            return;
        }
        synchronized (ELECTRIC_DOOR_STATUS_CALLBACK_LOCK) {
            if (mElectricDoorStatusCallbacks.size() <= 0) {
                mApi.registerStatusListener(Definition.STATUS_CAN_ELECTRIC_DOOR_CTRL, mElectricDoorStatusListener);
            }
            mElectricDoorStatusCallbacks.put(callbackId, listener);
        }
    }

    @Override
    public void unRegisterElectricDoorStateListener(int callbackId) {
        synchronized (ELECTRIC_DOOR_STATUS_CALLBACK_LOCK) {
            if (mElectricDoorStatusCallbacks.containsKey(callbackId)) {
                mElectricDoorStatusCallbacks.remove(callbackId);
                if (mElectricDoorStatusCallbacks.size() <= 0) {
                    mApi.unregisterStatusListener(mElectricDoorStatusListener);
                }
            }
        }
    }

    private StatusListener mElectricDoorStatusListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
            if (TextUtils.isEmpty(data)) {
                return;
            }
            synchronized (ELECTRIC_DOOR_STATUS_CALLBACK_LOCK) {
                for (int callbackId : mElectricDoorStatusCallbacks.keySet()) {
                    IRNRobotStatusListener listener = mElectricDoorStatusCallbacks.get(callbackId);
                    if (listener == null) {
                        unRegisterElectricDoorStateListener(callbackId);
                        return;
                    }
                    try {
                        listener.onStatusUpdate(type, data);
                    } catch (RemoteException e) {
                        RNServerUtils.handleRemoteException("ApiCallback onElectricDoorStateUpdate", e);
                        unRegisterElectricDoorStateListener(callbackId);
                    }
                }
            }
        }
    };

    @Override
    public boolean isSupportKKCamera() throws RemoteException {
        Log.d(TAG,"isSupportKKCamera");
        boolean b = FileUtils.hasKKCamera();
        Log.d(TAG,"isSupportKKCamera :"+b);
        return FileUtils.hasKKCamera();
    }

    @Override
    public void switchBigScreenMode(String mode) throws RemoteException {
        ExternalDisplay.INSTANCE.switchMode(mode, null);
    }

    @Override
    public String getLineSpeed() {
        return mApi.getLineSpeed();
    }

    @Override
    public void setLineSpeed(String speed, IRNApiListener listener){
        mApi.setLineSpeed(mReqId, speed, createCallback(listener));
    }

    @Override
    public void deliveryRobotTask(String paramJson,IRNApiListener listener) {
        mApi.deliveryRobotTask(mReqId,paramJson, createCallback(listener));
    }

    @Override
    public void deliveryRobotStatusNotify(String paramJson,IRNApiListener listener) {
        mApi.deliveryRobotStatusNotify(mReqId,paramJson, createCallback(listener));
    }

    @Override
    public void deliveryMonitorDetail(String paramJson,IRNApiListener listener) {
        mApi.deliveryMonitorDetail(mReqId,paramJson, createCallback(listener));
    }
    @Override
    public void deliveryRobotComment(String paramJson,IRNApiListener listener) {
        mApi.deliveryRobotComment(mReqId,paramJson,createCallback(listener));
    }

    @Override
    public void deliveryRemoteCall(String orderInfo,IRNApiListener listener){
        mApi.deliveryRemoteCall(mReqId, orderInfo, createCallback(listener));
    }

    @Override
    public void deliveryReport(String orderInfo,IRNApiListener listener){
        mApi.deliveryReport(mReqId, orderInfo, createCallback(listener));
    }

    @Override
    public void pauseNavigation(boolean isPause,IRNApiListener listener){
        mApi.pauseNavigation(mReqId, isPause, createCallback(listener));
    }

    @Override
    public void switchMap(String mapName,IRNApiListener listener){
        mApi.switchMap(mReqId, mapName, createCallback(listener));
    }

    @Override
    public void setFixedEstimate(String param,IRNApiListener listener){
        mApi.setFixedEstimate(mReqId, param, createCallback(listener));
    }

    @Override
    public void installApk(String fullPathName, String taskID) {
        mApi.installApk(mReqId, fullPathName, taskID);
    }
    @Override
    public void startRadarAlign(String poseName, IRNApiListener listener){
        mApi.startRadarAlign(mReqId, poseName, createCallback(listener));
    }

    @Override
    public void stopRadarAlign(){
        mApi.stopRadarAlign(mReqId);
    }

    @Override
    public void startRadarAlign2(String poseName, long startAlignTimeout, long retryDelayTime,
                                 long navigationTimeout, IRNApiListener listener){
        mApi.startRadarAlign(mReqId, poseName, startAlignTimeout, retryDelayTime, navigationTimeout, createCallback(listener));
    }

    @Override
    public boolean hasHeightLimitCamera() throws RemoteException {
        boolean hasHeightLimitCamera = mApi.hasHeightLimitCamera();
        Log.d(TAG,"hasHeightLimitCamera :" + hasHeightLimitCamera);
        return hasHeightLimitCamera;
    }

    @Override
    public int goForwardWithAvoid(float speed, float distance, boolean avoid, IRNApiListener listener) {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return mApi.goForward(mReqId, speed, distance, avoid, createCallback(listener));
    }

    @Override
    public void startNavigationFollow(String followId, int lostFindTimeout, IRNApiListener listener) {
        mApi.startNavigationFollowAction(mReqId, followId, lostFindTimeout, createCallback(listener));
    }

    @Override
    public void stopNavigationFollow() {
        mApi.stopNavigationFollowAction(mReqId);
    }

    @Override
    public void detectQrCode(String file, IRNApiListener listener) {
        mApi.detectQrCode(mReqId, file, createCallback(listener));
    }

    @Override
    public void disableOutsideMapAlarm() {
        mApi.disableOutsideMapAlarm();
    }

    @Override
    public void enableOutsideMapAlarm() {
        mApi.enableOutsideMapAlarm();
    }

    @Override
    public void getElevatorStatus(IRNApiListener listener) {
        mApi.getElevatorStatus(mReqId, createCallback(listener));
    }

    public int rotateInPlace(int direction, float speed, float angle, IRNApiListener listener) throws RemoteException {
        if (isLara()) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
//        return mApi.rotateInPlace(mReqId, direction, speed, angle, createCallback(listener));
        return 0;
    }
}
