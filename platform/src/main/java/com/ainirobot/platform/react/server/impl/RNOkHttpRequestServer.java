package com.ainirobot.platform.react.server.impl;

import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.platform.react.server.utils.CommandValidator;
import com.ainirobot.platform.rn.CallbackRegistry;
import com.ainirobot.platform.rn.OkHttpRequestRegistry;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import okhttp3.*;

public class RNOkHttpRequestServer extends OkHttpRequestRegistry.Stub {

    private static final String TAG = RNOkHttpRequestServer.class.getSimpleName();
    private static final OkHttpClient client = new OkHttpClient();
    private static final String DEFAULT_CONTENT_TYPE = "application/json; charset=utf-8";

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(8);
    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    private enum HttpMethod {
        GET, POST, PUT, DELETE
    }

    public void startExecution(String command, CallbackRegistry callback) {
        try {
            if (CommandValidator.isCommandDisabled(command)) {
                CallbackFailure(callback, "This 【"+command+"】 has been disabled!");
                return;
            }
            String baseCommand = command.split(" ")[0];
            Future<Boolean> futureResult = isCommandAvailableOnDevice(baseCommand);
            if (!futureResult.get()) {
                CallbackFailure(callback, "adb shell not supported: " + baseCommand);
                return;
            }
            scheduler.execute(() -> {
                StringBuilder output = new StringBuilder();
                Process process = null;
                BufferedReader reader = null;
                try {
                    ProcessBuilder builder = new ProcessBuilder(command.split(" "));
                    process = builder.start();

                    reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                    String line;
                    while ((line = reader.readLine()) != null) {
                        output.append(line).append("\n");
                    }

                    int exitCode = process.waitFor();
                    if (exitCode == 0) {
                        CallbackSuccess(callback, output.toString());
                    } else {
                        throw new IOException("Command exited with code " + exitCode);
                    }
                } catch (IOException | InterruptedException e) {
                    Log.e(TAG, "Failed to execute command: " + e.getMessage(), e);
                    CallbackFailure(callback, "Failed to execute command: " + e.getMessage());
                } finally {
                    closeQuietly(reader);
                    if (process != null) {
                        process.destroy();
                    }
                }
            });
        } catch (ExecutionException | InterruptedException e) {
            Log.e(TAG, "Error checking command availability: " + e.getMessage(), e);
            CallbackFailure(callback, "Error checking command availability: " + e.getMessage());
        }
    }


    @Override
    public void getIpconfig(CallbackRegistry callback) {
        scheduler.execute(() -> {
            StringBuilder result = new StringBuilder();
            Process process = null;
            BufferedReader reader = null;
            try {
                ProcessBuilder builder = new ProcessBuilder("ifconfig");
                process = builder.start();
                reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                while ((line = reader.readLine()) != null) {
                    result.append(line).append("\n");
                }
                int exitCode = process.waitFor();
                if (exitCode != 0) {
                    throw new IOException("Command exited with code " + exitCode);
                }
                CallbackSuccess(callback, result.toString());
            } catch (IOException | InterruptedException e) {
                Log.e(TAG, "Failed to execute ip addr command: " + e.getMessage(), e);
                CallbackFailure(callback, "Failed to execute ip addr command: " + e.getMessage());
            } finally {
                closeQuietly(reader);
                if (process != null) {
                    process.destroy();
                }
            }
        });
    }


    @Override
    public void getPing(String param, CallbackRegistry callback) {
        if (!isValidPingParam(param)) {
            CallbackFailure(callback, "Invalid parameter for ping command");
            return;
        }
        scheduler.execute(() -> {
            StringBuilder output = new StringBuilder();
            Process process = null;
            BufferedReader reader = null;
            try {
                ProcessBuilder builder = new ProcessBuilder("/system/bin/ping", "-c", "4", param);
                process = builder.start();
                reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
                int exitCode = process.waitFor();
                if (exitCode != 0) {
                    throw new IOException("Ping command exited with code " + exitCode);
                }

                String result = parsePingOutput(output.toString());
                CallbackSuccess(callback, result);
            } catch (IOException | InterruptedException e) {
                Log.e(TAG, "Failed to execute ping command: " + e.getMessage(), e);
                CallbackFailure(callback, "Failed to execute ping command: " + e.getMessage());
            } finally {
                closeQuietly(reader);
                if (process != null) {
                    process.destroy();
                }
            }
        });
    }


    private boolean isValidPingParam(String param) {
        // 允许的字符集可以根据需求调整
        return param != null && param.matches("^[a-zA-Z0-9.-]+$");
    }

    @Override
    public void sendRequest(String requestParams, CallbackRegistry callback) {
        Log.d(TAG, "sendRequest:" + requestParams);
        if (requestParams == null || requestParams.isEmpty()) {
            CallbackFailure(callback, "Request parameters cannot be null or empty");
            return;
        }

        try {
            JSONObject jsonParams = new JSONObject(requestParams);
            HttpMethod method = HttpMethod.valueOf(jsonParams.optString("method", "GET").toUpperCase());
            String body = jsonParams.optString("body", "{}");
            String requestUrl = jsonParams.optString("url", null);
            int timeout = jsonParams.optInt("timeout", -1);
            JSONArray headsArray = jsonParams.optJSONArray("heads");

            if (requestUrl == null || requestUrl.isEmpty()) {
                CallbackFailure(callback, "Request URL cannot be null or empty");
                return;
            }
            Headers headers = buildHeaders(headsArray);
            OkHttpClient clientToUse = timeout > 0 ? client.newBuilder()
                    .readTimeout(timeout, TimeUnit.SECONDS)
                    .writeTimeout(timeout, TimeUnit.SECONDS)
                    .connectTimeout(timeout, TimeUnit.SECONDS)
                    .build() : client;

            switch (method) {
                case POST:
                case PUT:
                    sendRequestWithBody(clientToUse, requestUrl, body, headers, method, callback);
                    break;
                case DELETE:
                    sendDeleteRequest(clientToUse, requestUrl, headers, callback);
                    break;
                case GET:
                default:
                    sendGetRequest(clientToUse, requestUrl, headers, callback);
                    break;
            }
        } catch (JSONException e) {
            Log.e(TAG, "Failed to parse request parameters: " + e.getMessage(), e);
            CallbackFailure(callback, "Failed to parse request parameters: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            Log.e(TAG, "Invalid HTTP method: " + e.getMessage(), e);
            CallbackFailure(callback, "Invalid HTTP method: " + e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "Request failed: " + e.getMessage(), e);
            CallbackFailure(callback, "Request failed: " + e.getMessage());
        }
    }


    private Headers buildHeaders(JSONArray headsArray) {
        Headers.Builder headersBuilder = new Headers.Builder();
        if (headsArray != null) {
            for (int i = 0; i < headsArray.length(); i++) {
                JSONObject item = headsArray.optJSONObject(i);
                if (item != null) {
                    String key = item.optString("key", null);
                    String param = item.optString("param", null);
                    if (key != null && !key.isEmpty() && param != null && !param.isEmpty()) {
                        headersBuilder.add(key, param);
                        Log.d(TAG, "Key: " + key + ", Param: " + param);
                    } else {
                        Log.w(TAG, "Invalid header key or param: Key=" + key + ", Param=" + param);
                    }
                }
            }
        } else {
            headersBuilder.add("Content-Type", DEFAULT_CONTENT_TYPE);
            headersBuilder.add("x-timestamp", String.valueOf(System.currentTimeMillis()));
        }
        return headersBuilder.build();
    }

    private void sendGetRequest(OkHttpClient client, String url, Headers headers, CallbackRegistry callback) {
        Request request = new Request.Builder().url(url).headers(headers).get().build();
        executeRequest(client, request, callback);
    }

    private void sendRequestWithBody(OkHttpClient client, String url, String body, Headers headers, HttpMethod method, CallbackRegistry callback) {
        RequestBody requestBody = RequestBody.create(body, MediaType.parse(DEFAULT_CONTENT_TYPE));
        Request.Builder requestBuilder = new Request.Builder().url(url).headers(headers);
        if (method == HttpMethod.POST) {
            requestBuilder.post(requestBody);
        } else if (method == HttpMethod.PUT) {
            requestBuilder.put(requestBody);
        }

        executeRequest(client, requestBuilder.build(), callback);
    }

    private void sendDeleteRequest(OkHttpClient client, String url, Headers headers, CallbackRegistry callback) {
        Request request = new Request.Builder().url(url).headers(headers).delete().build();
        executeRequest(client, request, callback);
    }

    private void executeRequest(OkHttpClient client, Request request, CallbackRegistry callback) {
        Call call = client.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "Request failed: " + e.getMessage(), e);
                CallbackFailure(callback, e.getMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try (Response res = response) {
                    if (!res.isSuccessful()) {
                        CallbackFailure(callback, "Unexpected code " + res);
                        return;
                    }
                    Headers headers = res.headers();
                    ResponseBody responseBody = res.body();
                    if (responseBody == null) {
                        CallbackFailure(callback, "Response body is null");
                        return;
                    }
                    JSONObject result = new JSONObject();
                    result.put("response", responseBody.string());
                    result.put("headers", headers.toString());
                    CallbackSuccess(callback, result.toString());
                } catch (IOException e) {
                    Log.e(TAG, "Response processing failure: " + e.getMessage(), e);
                    CallbackFailure(callback, "Response processing failure: " + e.getMessage());
                } catch (JSONException e) {
                    throw new RuntimeException(e);
                }
            }
        });
    }

    private void CallbackFailure(CallbackRegistry callback, String message) {
        Log.d(TAG, "CallbackFailure:" + message);
        mainHandler.post(() -> {
            try {
                callback.onFailure(message);
            } catch (RemoteException ex) {
                Log.e(TAG, "Callback failure: " + ex.getMessage(), ex);
            }
        });
    }

    private void CallbackSuccess(CallbackRegistry callback, String data) {
        Log.d(TAG, "CallbackSuccess:" + data);
        mainHandler.post(() -> {
            try {
                callback.onSuccess(data);
            } catch (RemoteException ex) {
                Log.e(TAG, "Callback failure: " + ex.getMessage(), ex);
            }
        });
    }

    private void closeQuietly(AutoCloseable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (Exception e) {
                Log.e(TAG, "Failed to close resource: " + e.getMessage(), e);
            }
        }
    }

    private String parsePingOutput(String output) {
        StringBuilder result = new StringBuilder();
        String[] lines = output.split("\n");
        for (String line : lines) {
            if (line.contains("time=")) {
                String formattedLine = formatPingLine(line);
                result.append(formattedLine).append("\n");
            }
        }
        return result.toString();
    }

    private String formatPingLine(String line) {
        int timeIndex = line.indexOf("time=");
        if (timeIndex != -1) {
            int msIndex = line.indexOf(" ms", timeIndex);
            if (msIndex != -1) {
                String timeValue = line.substring(timeIndex + 5, msIndex);
                return "RTT: " + timeValue + " ms";
            }
        }
        return line;
    }


    private Future<Boolean> isCommandAvailableOnDevice(String command) {
        return scheduler.submit(() -> {
            Process process = null;
            try {
                process = new ProcessBuilder("which", command).start();
                int exitCode = process.waitFor();
                return exitCode == 0;
            } catch (IOException | InterruptedException e) {
                Log.e(TAG, "Failed to check command on device: " + e.getMessage(), e);
                return false;
            } finally {
                if (process != null) {
                    process.destroy();
                }
            }
        });
    }
}
