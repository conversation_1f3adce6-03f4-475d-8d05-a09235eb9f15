package com.ainirobot.platform.react.reactnative.component.uicomponent.external.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.Drawable
import android.hardware.display.DisplayManager
import android.hardware.display.DisplayManager.DisplayListener
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Display
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.ImageView
import com.ainirobot.coreservice.client.ProductInfo
import com.ainirobot.coreservice.client.RobotSettings
import com.ainirobot.platform.R
import com.ainirobot.platform.bi.wrapper.ReportControl
import com.ainirobot.platform.bi.wrapper.manager.bi.impl.BigScreenStatusPoint

/**
 * 大屏播放控制
 */
@SuppressLint("StaticFieldLeak")
object ExternalDisplay {

    private const val TAG = "ExternalDisplay"

    private const val MODE_MIRROR = "mirror" //镜像
    private const val MODE_MULTI = "multi" //分屏

    private lateinit var context: Context

    private var screen: ExternalDisplayScreen? = null
    private var type: Int? = null
    private var display: Display? = null
    private lateinit var defImage: ImageView

    var listener: DisplayListener? = null

    private val handler = Handler(Looper.getMainLooper())

    fun init(context: Context, type: Int? = null) {
        if (this::context.isInitialized) {
            return
        }
        this.context = context.applicationContext
        this.type = type
        initDisplayAndScreen(context)
        registerDisplayListener(context)
        initDefaultImage()
    }

    fun show(view: View) {
        handler.post {
            screen?.show(view)
        }
    }

    /**
     * 设置默认图片
     */
    fun setDefaultImage(drawable: Drawable) {
        handler.post {
            this.defImage.setImageDrawable(drawable)
        }
    }

    /**
     * 重置到默认图片
     */
    fun show() {
        show(defImage)
    }

    /**
     * 隐藏大屏显示
     * 大屏显示为多进程层级，该接口只隐藏当前进程控制的层级
     */
    fun hide() {
        handler.post {
            screen?.dismiss()
        }
    }

    fun switchMode(mode: String, view: View? = null) {
        when (mode) {
            MODE_MIRROR -> hide()
            MODE_MULTI -> show(view ?: defImage)
        }
    }

    private fun initDefaultImage() {
        Log.d(TAG, "Init default image : ${ProductInfo.isMeissaPlus()}")
        defImage = ImageView(this.context)
        defImage.layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        if (ProductInfo.isSaiphBigScreen() || ProductInfo.isMeissaPlus()) {
            defImage.setImageResource(R.drawable.big_screen_default_image_saiph)
        } else {
            defImage.setImageResource(R.drawable.big_screen_default_image)
        }
        show(defImage)
    }

    /**
     * 初始化Display
     */
    private fun initDisplayAndScreen(context: Context) {
        display = getDisplay(context)
        Log.d(TAG, "Init display and screen : ${display?.displayId}")
        if (display != null) {
            screen = ExternalDisplayScreen(this.context, display)
            type?.let { screen?.setType(it) }
            report(1)
        } else {
            report(2)
        }
    }

    /**
     * 注册Display事件监听
     */
    private fun registerDisplayListener(context: Context) {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        displayManager.registerDisplayListener(object : DisplayListener {
            override fun onDisplayAdded(displayId: Int) {
                <EMAIL>(displayId)
            }

            override fun onDisplayRemoved(displayId: Int) {
                <EMAIL>(displayId)
            }

            override fun onDisplayChanged(displayId: Int) {
                <EMAIL>(displayId)
            }
        }, null)
    }

    /**
     * 获取大屏
     */
    private fun getDisplay(context: Context): Display? {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val displays = displayManager.getDisplays(DisplayManager.DISPLAY_CATEGORY_PRESENTATION)
        if (displays.isEmpty()) {
            Log.d(TAG, "External display not exits")
            return null
        }
        return displays[0]
    }

    private fun onDisplayAdded(displayId: Int) {
        Log.d(TAG, "On display added : $displayId")
        if (display == null) {
            initDisplayAndScreen(context)
        }
        listener?.onDisplayAdded(displayId)
    }

    private fun onDisplayRemoved(displayId: Int) {
        Log.d(TAG, "On display removed : $displayId")
        if (displayId == display?.displayId) {
            switchDisplay()
        }
        listener?.onDisplayRemoved(displayId)
    }

    private fun onDisplayChanged(displayId: Int) {
        Log.d(TAG, "On display changed : $displayId")
        listener?.onDisplayChanged(displayId)
    }

    private fun switchDisplay() {
        val content = screen?.content
        screen?.dismiss()

        initDisplayAndScreen(context)
        if (content != null) {
            screen?.show(content)
        }
    }

    /**
     * 大屏状态数据上报
     */
    private fun report(status: Int) {
        ReportControl.getInstance().reportMsg(BigScreenStatusPoint(status, 0))
    }

    /**
     * 临时策略
     */
    private fun isMessiaPlus(): Boolean {
        val model = RobotSettings.getProductModel()
        return model == "OS-R-DP01S"
    }

}