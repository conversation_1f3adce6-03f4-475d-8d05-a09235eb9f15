package com.ainirobot.platform.react.reactnative.component.common

import android.content.Context
import android.content.pm.PackageInfo
import android.graphics.Point
import android.hardware.display.DisplayManager
import android.os.Build
import com.ainirobot.coreservice.client.ProductInfo
import com.ainirobot.coreservice.client.RobotSettings

import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.utils.DeviceUtils
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import org.json.JSONObject
import java.util.*

class InitialBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    private val mContext = BaseApplication.getContext()

    override fun getName(): String {
        return "Initial"
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun getInitialProps(): String {
        return loadOrionPrams()
    }

    private fun loadOrionPrams(): String {

        var applicationInfo: PackageInfo? = null

        try {
            applicationInfo = mContext.packageManager.getPackageInfo(mContext.packageName, 0)
        } catch (e: Exception) {
        }

        val json = JSONObject()

        try {
            when {
                RNClientManager.instance?.apiManager?.isLara()
                        ?: false -> json.put("platform", "Lara")
                RNClientManager.instance?.apiManager?.isMeissa()
                        ?: false -> json.put("platform", "Meissa")
                RNClientManager.instance?.apiManager?.isMini()
                        ?: false -> json.put("platform", "Mini")
                else -> json.put("platform", "")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            json.put("platform", "")
        }

        json.put("robot_version", RobotSettings.getVersion())
        json.put("installPath", mContext.packageResourcePath)

        json.put("bigScreenSize", getBigScreenSize())


        if (applicationInfo != null) {
            json.put("appPkgName", applicationInfo.toString())
            json.put("versionCode", applicationInfo.versionCode)
            json.put("versionName", applicationInfo.versionName)
        } else {
            json.put("versionCode", -1)
            json.put("versionName", null)
        }

        var mAndroidId: String? = null

        if (mAndroidId == null) {
            mAndroidId = DeviceUtils.getAndroidId(mContext)
        }
        json.put("ro.product.brand", Build.BRAND)
        json.put("ro.build.display.id", Build.DISPLAY)
        json.put("ro.build.id", Build.ID)
        json.put("ro.product.manufacturer", Build.MANUFACTURER)
        json.put("ro.product.model", RobotSettings.getProductModel())
        json.put("ro.build.type", Build.TYPE)
        json.put("ro.build.user", Build.USER)
        json.put("ro.build.version.codename", Build.VERSION.CODENAME)
        json.put("ro.build.version.incremental", Build.VERSION.INCREMENTAL)
        json.put("ro.build.version.release", Build.VERSION.RELEASE)
        json.put("ro.build.version.sdk", Build.VERSION.SDK_INT)
        json.put("language", Locale.getDefault().language)
        json.put("Sn", RobotSettings.getSystemSn())
        json.put("character", DeviceUtils.getCharacterName())
        json.put("scenes", DeviceUtils.getScenesName())

        json.put("isSupportBaiduOutdoor", true)
        json.put("isSupportBodyFollow", true)
        json.put("isSupportFMOffsetHeight", true)
        json.put("isSupportNewApiInCore1_10", true)
        json.put("isSupportMeissaMovedViews", true)
        json.put("isSupportMonitorNetConnectivityChanges", true)
        json.put("isSupportCropApi", true)
        json.put("moduleDataCenterV2", true)
        json.put("isSupportControlManager", true)
        json.put("isSupportSwitchLauncherGlobal", true)
        json.put("isSupportSpeech_setTTSParams_LangRec_AsrExtendProperty", true)
        json.put("isSupportBubbleView", true)

        json.put("isSupportMultiComponent", true)
        json.put("isSupportSkillServerCheck", true)
        json.put("isSupportRobotReboot", true)
        json.put("isSupportForceFetchModuleData", true)
        json.put("isSupportEmojiVersion2", true)
        json.put("isSupportPlayToneByLocalPath", true)
        json.put("isSupportNewExDisplay", true)
        json.put("isSupportheckIfHasObstacle", true)
        json.put("isSupportStopTone", true)
        json.put("isSupportUpdateRobotStatusAPI", true)
        json.put("isSupportMultiLanguage", true)
        json.put("isSupportDoorStatus", true)
        json.put("isSupportGuideTrack", true)
        json.put("isSupportPlugin", true)
        json.put("isSupportTextToMp3", true)
        json.put("isSupportGetRobotName", true)
        json.put("isSupportTakeOverEmergency", true)
        json.put("cameraFilterVersion", 1)
        json.put("isSupportAndroidSettings", true)
        json.put("isSupportAndroidSystemSettings", true)
        json.put("isSupportAnswerApp", true)
        json.put("isSupportGetUserList", true)
        json.put("isSupportInviteCall", true)
        json.put("isSupportNlpParser", true)
        json.put("deviceBridgeVer", 1)
        json.put("isSupportControlVision", true)
        json.put("isSupportPlayTextWithSpeak", true)
        json.put("isSupportSetASRParamsAndPlayTtsWithParams", true)
        json.put("isSupportEnterStandby", true)
        json.put("isSupportGetSpokemanListByLanguage", true)
        json.put("isSupportOpenApp", true)
        json.put("isSupportGetDefWelcomeTTS", true)
        json.put("uiControlVer", 1)
        json.put("isSupportTaskModeReport", true)
        json.put("isSupportEnableBattery", true)
        json.put("isSupportDownloadTtsAudio", true)
        json.put("isSupportTaskExecReport", true)
        json.put("isSupportConfigUpdateNotify", true)
        json.put("isSupportDisableUpdate", true)
        json.put("isSupportTakeOverFunctionKey", true)
        json.put("isSupportModuleCodeConfigUpload", true)
        json.put("lightControlVer", 1)
        json.put("isSupportBasicMotion", true)
        json.put("isSupportSystemId", true)
        json.put("isSupportOpkToken", true)
        json.put("isSupportTakePicture", true)
        json.put("isSupportRobotTask", true)
        json.put("robotTaskVer", 2)
        json.put("isSupportRobotSettingApi", true)
        json.put("isSupportRobotSettingPermission", true)
        json.put("isSupportNLPApkControl",true)
        json.put("isSupportTaskCommandReport", true)
        json.put("isSupportGetSpecialDishLabels", true)
        json.put("msgControlVer", 2)
        json.put("isSupportElapsedRealtime", true)
        json.put("isSupportSurfaceShareUsed", true)
        json.put("isSupportTaskInterface", true)
        json.put("multiRobotNavigationVer", 1)
        json.put("isSupportMotionArcWithObstacles", true)
        json.put("mapControlVer", 1)
        json.put("isSupportShutdown", true)
        json.put("isSupportChargingPileAction", true)
        json.put("isSupportNaviPathInfo", true)
        json.put("isSupportDumpMem",true)
        json.put("isSupportGetNaviConfig",true)
        json.put("isSupportGetTemperature",true)
        json.put("isSupportSetXDPowderEnable",true)
        json.put("isSupportSetXDFanEnable",true)
        json.put("isSupportSetXDRank",true)
        json.put("isSupportMonitorDryStatus",true)
        json.put("isSupportUnMonitorDryStatus",true)
        json.put("isSupportGoCharging",true)
        json.put("isSupportLanguageList",true)
        json.put("isSupportGetMultiFloorConfigAndPose", true)
        json.put("isSupportGetMultiFloorConfigAndCommonPose", true)
        json.put("isSupportGetNaviAngSpeed", true)

        json.put("isSupportRobotStandBy",true)
        json.put("isSupportRobotStandByEnd",true)
        json.put("isSupportRadar",true)
        json.put("isSupportMonitorRemoteStandby",true)
        json.put("isSupportUnMonitorRemoteStandby",true)
        json.put("isSupportGetScreenBrightness",true)
        json.put("isSupportSetScreenBrightness",true)
        json.put("isSupportMonitorCollideStatus",true)
        json.put("isSupportUnMonitorCollideStatus",true)
        json.put("isSupportProactiveProblemReport",true)
        json.put("isSupportSetElectricDoorCtrl",true)
        json.put("isSupportGetElectricDoorStatus",true)
        json.put("isSupportElectricDoor",true)
        json.put("isSupportQueryFeedback",true)
        json.put("isSupportElevator",true)
        json.put("isSupportKKCamera",true)
        json.put("isSupportHuaZhu",true)
        json.put("isSupportCallButton",true)
        json.put("isSupportAutoCharge", isSupportAutoCharge())
        json.put("hasHeightLimitCamera",true)
        json.put("isSupportStatusListenerAndTryNaviBack",true)

        BaseApplication.getApplication().customReactInitParam?.apply {
            this.entries.forEach {
                json.put(it.key, it.value)
            }
        }
        return json.toString()
    }

    private fun isSupportAutoCharge(): Boolean {
        return ProductInfo.isChargeIrProduct() || ProductInfo.isMeissa2() || ProductInfo.isMeissaPlus() || ProductInfo.isMiniProduct()
    }

    private fun getBigScreenSize(): JSONObject? {
        val dm = mContext.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val displays = dm.getDisplays(DisplayManager.DISPLAY_CATEGORY_PRESENTATION)
        if (displays.isNotEmpty()) {
            val p = Point()
            displays[0].getSize(p)
            val json = JSONObject()
            json.put("width", p.x)
            json.put("height", p.y)
            return json
        }
        return null
    }
}
