package com.ainirobot.platform.react.reactnative.component.common

import com.ainirobot.platform.react.client.RNClientManager
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod

class MapBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    companion object {
        const val TAG = "MapBridgeModule"
    }
    override fun getName(): String {
        return "MapManager"
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun t9Search(query: String): String? {
        return RNClientManager.instance?.mapManager?.t9Search(query)
    }
}
