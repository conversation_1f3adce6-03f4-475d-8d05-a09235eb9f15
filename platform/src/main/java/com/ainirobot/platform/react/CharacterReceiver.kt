package com.ainirobot.platform.react

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.ainirobot.coreservice.client.RobotApi
import com.ainirobot.platform.PlatformDef
import com.ainirobot.platform.control.ControlManager
import com.ainirobot.platform.character.CharacterManager
import com.ainirobot.platform.react.reactnative.character.ReactCharacter
import org.json.JSONException
import org.json.JSONObject

class CharacterReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent != null) {

            if (!RobotApi.getInstance().isActive) {
                Log.e("CharacterReceiver", "ModuleApp not active")
                return
            }

            val character = intent.getStringExtra("name")
            Log.d("CharacterReceiver", "$character")
            if (character != null) {
                switchCharacter(character)
                return
            }

            val opkPath = intent.getStringExtra("path")
            if (opkPath != null) {
                Log.d("CharacterReceiver", opkPath)
                val appBean: AppBeanV2? = OPKHelper.getAppBeanForOPKFile(opkPath, "") ?: return
                if (!(appBean?.bundleIndexFilePath != null || (appBean?.bundleBizFilePath != null && appBean?.bundlePlatformFilePath != null))) {
                    return
                }
                if (appBean.rpkBean.appid == null) {
                    return
                }
                Log.d("CharacterReceiver", ReactCharacter.isReactRunning.toString())
                Log.d("CharacterReceiver", appBean?.rpkBean?.appid + " " + appBean.bundleBizFilePath + " current: " + ReactCharacter.currentCharacter)
                AppManger.addApp(appBean?.bundleIndexFilePath, appBean.bundleBizFilePath, appBean.bundlePlatformFilePath, appBean.rpkBean)
                if (ReactCharacter.isReactRunning && ReactCharacter.currentCharacter == appBean?.rpkBean?.appid) {
                    switchCharacter(appBean?.rpkBean?.appid!!)
                } else {
                    Log.d("CharacterReceiver", "currentCharacter " + ReactCharacter.currentCharacter)
                    registerCharacter(appBean?.rpkBean?.appid!!)
                }
            } else {
                Log.d("CharacterReceiver", "not have path")
            }
        }
    }

    private fun registerCharacter(appid: String): Boolean {
        if (appid.isNullOrEmpty()) {
            Log.d("CharacterReceiver", "registerCharacter appid is empty")
            return false

        }
        Log.i("CharacterReceiver", "registerCharacter")
        val characterManager = CharacterManager.getInstance()
        val character = ReactCharacter(appid)
        return characterManager.registerCharacterFromRN(character.getName(), character)
    }

    fun switchCharacter(characterName: String) {
        try {
            val json = JSONObject()
            json.put("name", characterName)

            ControlManager.handleRequest(0, PlatformDef.SWITCH_CHARACTER, "", json.toString());
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }
}
