package com.ainirobot.platform.react.reactnative.component.skillcomponent

import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.component.listener.BridgeMsgListener
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod

class MsgBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    companion object {
        val TAG = "MsgBridgeModule"
    }

    override fun getName(): String {
        return "MsgPushManager"
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun getMessages(type: Int, appId: String?, appPath: String?): String? {
        return try {
            RNClientManager.instance?.msgManager?.getMessage(type, appId, appPath)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun getMessageContent(msgId: String): String? {
        return try {
            RNClientManager.instance?.msgManager?.getMessageContent(msgId)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun getDetail(detailId: String): String? {
        return try {
            RNClientManager.instance?.msgManager?.getDetail(detailId)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    @ReactMethod
    fun increaseDetailPlayTimes(detailId: String) {
        try {
            RNClientManager.instance?.msgManager?.increaseDetailPlayTimes(detailId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun deleteDetail(detailId: String) {
        try {
            RNClientManager.instance?.msgManager?.deleteDetail(detailId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun registerMsgChanged(callbackId: Int, type: Int, appId: String?, appPath: String?) {
        try {
            if (callbackId >= 0) {
                RNClientManager.instance?.msgManager?.registerMsgChanged(callbackId, type,
                        appId, appPath, BridgeMsgListener.obtain(callbackId))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun unregisterMsgChanged(callbackId: Int) {
        try {
            RNClientManager.instance?.msgManager?.unregisterMsgChanged(callbackId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
