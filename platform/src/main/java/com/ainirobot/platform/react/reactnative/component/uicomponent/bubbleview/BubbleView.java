package com.ainirobot.platform.react.reactnative.component.uicomponent.bubbleview;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Color;
import android.os.Handler;
import android.os.Message;
import android.os.MessageQueue;
import androidx.annotation.Nullable;
import android.text.Layout;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateInterpolator;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;


import com.ainirobot.platform.data.RobotInfo;
import com.ainirobot.platform.utils.DimenUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * 表情的气泡:
 */
public class BubbleView extends LinearLayout {

    private static final String TAG = "BubbleView";
    private static final int COLORS_NUM = 6;
    private static final int DEFAULT_SWITCH_DURATION = 5 * 1000;
    private static final int SWITCH_MESSAGE = 0x11;
    private static final int DEFAULT_ANIMATION_DURATION = 300;
    private static final int DEFAULT_HEIGHT = 527;
    private static int contentIndex = 0; //气泡内容编号，奇数显示leftBubble,偶数显示rightBubble

    private TextView mLeftBubble;
    private TextView mRightBubble;
    private List<String> mBubbles;
    private RelativeLayout mLeftContent;
    private RelativeLayout mRightContent;
    private IPositionPolicy mBubblePolicy;
    private IPositionPolicy mColorPolicy;
    private Map<Integer, List<Integer>> mColorsMap;
    //左右一组颜色和data
    private List<Integer> mCurrentColorList;
    private List<String> mCurrentBubbleList;
    private Handler mHandler;
    private int mDuration;
    //左右两个容器的高度
    private int mContentHeight;
    private int mScreenWidth;
    private int mSwitchTime;
    private int mOrientation;

    public BubbleView(Context context) {
        super(context);
        init();
    }

    public BubbleView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public BubbleView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public void setBubbles(List<String> bubbles) {
        this.mBubbles = bubbles;
        if (this.mBubbles != null) {
            Log.d(TAG, mBubbles.toString());
        }
        update();
    }

    public void setDuration(int duration) {
        this.mDuration = duration;
    }

    public void setSwitchTime(int time) {
        this.mSwitchTime = time;
    }

    public void setContentHeight(int height) {
        this.mContentHeight = height;
        for (int i = 0; i < getChildCount(); i++) {
            View view = getChildAt(i);
            if (view != null) {
                ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
                layoutParams.height = height;
                view.requestLayout();
            }
        }
    }

    private void update() {
        removeAllViews();
        if (length() == 0) {
            return;
        }
        if (length() == 1) {
            addView(mLeftContent);
            reSetContent(mLeftContent, 0);
        } else {
            addView(mLeftContent);
            addView(mRightContent);
            reSetContent(mLeftContent, 0);
            reSetContent(mRightContent, 1);
        }
        dataChange();
    }

    private void reSetContent(View view, int type) {
        int width = mScreenWidth - getPaddingLeft() - getPaddingRight();
        int height = mContentHeight;
        Log.d(TAG, "reSetContent height :"+height);
        int left = getPaddingLeft();
        int right = left + width;
        if (length() == 1) {
            //只有一个气泡，居中
            view.setLeft(left);
            view.setTop(height / 2);
            view.setRight(right);
            view.setBottom(height / 2 + height);
            return;
        }
        //左边气泡
        if (type == 0) {
            view.setLeft(left);
            view.setRight(right);
            if (mOrientation == Configuration.ORIENTATION_LANDSCAPE) {
                view.setTop(height / 4 * 3);
                view.setBottom((height / 4 * 3) + height);
            } else {
                view.setTop(0);
                view.setBottom(height);
            }

            return;
        }
        //右边气泡
        if (type == 1) {
            view.setLeft(left);
            view.setRight(right);
            if (mOrientation == Configuration.ORIENTATION_LANDSCAPE) {
                Log.d(TAG, "reSet R Bubble top: "+(height / 5));
                view.setTop(height / 5);
                view.setBottom(height / 5 + height);
            } else {
                view.setTop(height);
                view.setBottom(height * 2);
            }
        }
    }

    private void dataChange() {
        mBubblePolicy.setMaxPosition(length() - 1);
        if (mHandler.hasMessages(SWITCH_MESSAGE)) {
            mHandler.removeMessages(SWITCH_MESSAGE);
        }
        switchBubble();
    }

    private int length() {
        if (this.mBubbles == null) {
            return 0;
        }
        return this.mBubbles.size();
    }

    private void setColorsMapData() {
        Resources resources = getContext().getResources();
        for (int i = 1; i <= COLORS_NUM; i++) {
            int leftRef = resources.getIdentifier("bubble" + i + "_left",
                "drawable", getContext().getPackageName());
            int rightRef = resources.getIdentifier("bubble" + i + "_right",
                "drawable", getContext().getPackageName());
            List<Integer> list = new ArrayList<>();
            if (leftRef != 0) {
                list.add(leftRef);
            }
            if (rightRef != 0) {
                list.add(rightRef);
            }
            mColorsMap.put(i - 1, list);
        }
    }

    private void init() {
        mColorsMap = new ConcurrentHashMap<>();
        mCurrentBubbleList = new ArrayList<>();

        mDuration = DEFAULT_ANIMATION_DURATION;
        mContentHeight = DEFAULT_HEIGHT;
        mSwitchTime = DEFAULT_SWITCH_DURATION;
        mScreenWidth = getResources().getDisplayMetrics().widthPixels;
        // 横屏缩小适配
        mOrientation = getContext().getResources().getConfiguration().orientation;
        Log.d(TAG, "SwitchEmojiManager init "+ mOrientation);


        mBubblePolicy = new RandomPositionPolicy();
        mColorPolicy = new RandomPositionPolicy();
        setColorsMapData();
        mColorPolicy.setMaxPosition(mColorsMap.size() - 1);
        mHandler = new MyHandler(this);

        setPadding(63, 0, 63, 0);
        setOrientation(VERTICAL);
        setGravity(Gravity.CENTER);

        mLeftBubble = getBubbleView(0);
        mLeftContent = getBubbleContentView(0);
        mLeftContent.addView(mLeftBubble);

        mRightBubble = getBubbleView(1);
        mRightContent = getBubbleContentView(1);
        mRightContent.addView(mRightBubble);
    }

    private void setBubbleRandomColorBackground() {
        int index = mColorPolicy.getPosition();
        List<Integer> list = mColorsMap.get(index);
        if (list == null) {
            return;
        }
        String refName = "";
        for (int i = 0; i < list.size(); i++) {
            int ref = list.get(i);
            refName += getContext().getResources().getResourceEntryName(ref) + ",";
        }
        Log.d(TAG, "setBubbleRandomColorBackground " + refName);
        mCurrentColorList = list;
    }

    private void setBubbleRandomBubbles() {
        if (length() == 0) {
            return;
        }
        if (length() == 1) {
            mCurrentBubbleList.clear();
            mCurrentBubbleList.add(mBubbles.get(0));
            return;
        }
        int leftIndex = mBubblePolicy.getPosition();
        int rightIndex = mBubblePolicy.getPosition();
        while (leftIndex == rightIndex) {
            leftIndex = mBubblePolicy.getPosition();
        }
        Log.d(TAG, "setBubbleRandomBubbles "
            + "leftIndex=" + leftIndex + ",rightIndex=" + rightIndex);
        if (leftIndex > length() || rightIndex > length()) {
            Log.d(TAG, "leftIndex or rightIndex error");
            return;
        }
        mCurrentBubbleList.clear();
        mCurrentBubbleList.add(mBubbles.get(leftIndex));
        mCurrentBubbleList.add(mBubbles.get(rightIndex));
    }

    private void switchBubble() {
        getContext().getMainLooper().getQueue().addIdleHandler(new MessageQueue.IdleHandler() {
            @Override
            public boolean queueIdle() {
                startAnimation();
                return false;
            }
        });
        mHandler.sendEmptyMessageDelayed(SWITCH_MESSAGE, mSwitchTime);
    }

    private void hideLeftBubble(){
        mLeftBubble.setVisibility(View.INVISIBLE);
    }

    private void hideRightBubble(){
        mRightBubble.setVisibility(View.INVISIBLE);
    }

    private void showLeftBubble(){
        mLeftBubble.setVisibility(View.VISIBLE);
    }

    private void showRightBubble(){
        mRightBubble.setVisibility(View.VISIBLE);
    }

    private void startAnimation() {
        if (length() == 0) {
            return;
        }
        setBubbleRandomBubbles();
        if (length() == 1) {
            scaleAndAlphaAnimation(mLeftBubble, null);
            setBubbleAndBackground(0);
            return;
        }

        contentIndex++;
        Log.d(TAG,"contentIndex: " + contentIndex);
        if(contentIndex%2 == 0){
            hideLeftBubble();
            showRightBubble();
            //偶数显示右边气泡
            scaleAndAlphaAnimation(mRightBubble, new AnimatorListenerAdapter() {
                @Override
                public void onAnimationStart(Animator animation) {
                    //右边开始展示
                    setBubbleAndBackground(1);
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                }
            });

        }else{

            hideRightBubble();
            showLeftBubble();
            //奇数显示左边气泡
            scaleAndAlphaAnimation(mLeftBubble, new AnimatorListenerAdapter() {
                @Override
                public void onAnimationStart(Animator animation) {
                    //左边bubble开始展示,左右颜色不一样
                    setBubbleAndBackground(0);
                }

                @Override
                public void onAnimationEnd(Animator animation) { ;
                }
            });
        }
    }

    private void setBubbleAndBackground(int type) {
        setBubbleRandomColorBackground();
        if (mCurrentColorList == null) {
            Log.d(TAG, "setBubbleAndBackground mCurrentColorList is null");
            return;
        }
        if (mCurrentBubbleList == null) {
            Log.d(TAG, "setBubbleAndBackground mCurrentBubbleList is null");
            return;
        }

        Log.d(TAG, "setReMeasure");
        if (type == 0) {
            setMeasure(mLeftBubble, type);
        } else {
            setMeasure(mRightBubble, type);
        }
    }

    /**
     * 手动定位
     *
     * @param view
     * @param type
     */
    private void setMeasure(TextView view, int type) {
        view.setBackgroundResource(mCurrentColorList.get(type));
        String showText = mCurrentBubbleList.get(type);
        view.setText(showText);
        if (mOrientation == Configuration.ORIENTATION_PORTRAIT) {
            if (Locale.getDefault().getLanguage().startsWith("zh") && showText.length() > 14) {
                view.setTextSize(TypedValue.COMPLEX_UNIT_SP, 26);
            } else {
                view.setTextSize(TypedValue.COMPLEX_UNIT_SP, 36);
            }
        }
        int maxWidth = mScreenWidth - getPaddingLeft() - getPaddingRight();
        int maxHeight = mContentHeight;
        Log.d(TAG, "maxWidth=" + maxWidth + ",maxHeight=" + (maxHeight));
        int w = View.MeasureSpec.makeMeasureSpec(maxWidth, MeasureSpec.AT_MOST);
        int h = View.MeasureSpec.makeMeasureSpec(maxHeight, MeasureSpec.AT_MOST);
        view.measure(w, h);
        int measuredHeight = view.getMeasuredHeight();
        int measureWidth = view.getMeasuredWidth();

        //按字符测量得到宽，然后加上.9的左右边距
        int margin = RobotInfo.isRTL() ? 50 : 150;
        int hopWidth = allCharWidth(view) + margin;
        Log.i(TAG, "type=" + type + ",measureWidth=" + measureWidth + ",measureHeight=" + measuredHeight);

        //左右容器上下坐标一样
        int topPoint = (maxHeight - measuredHeight) / 2;
        view.setTop(topPoint);
        view.setBottom(topPoint + measuredHeight);
        //左边
        if (type == 0) {
            int left = 0;
            int right = hopWidth;
            //如果只有一个，那么居中显示
            if (length() == 1) {
                left = (maxWidth - hopWidth) / 2;
                right += left;
            }
            //否则左对齐
            view.setLeft(left);
            view.setRight(right);
            return;
        }
        //右边
        view.setLeft(maxWidth - hopWidth);
        view.setRight(maxWidth);
    }

    private int allCharWidth(TextView textView) {
        String text = textView.getText().toString();
        if (TextUtils.isEmpty(text)) {
            return 0;
        }

        Layout layout = textView.getLayout();
        String oneLineStr = text.substring(layout.getLineStart(0), layout.getLineEnd(0));
        float lineWidth = 0;
        for (int i = 0; i != oneLineStr.length(); ++i) {
            char ch = oneLineStr.charAt(i);
            lineWidth += textView.getPaint().measureText(String.valueOf(ch));
        }
        Log.d(TAG, "oneLineString=" + oneLineStr);
        Log.d(TAG, "lineWidth=" + lineWidth);
        return (int) lineWidth;
    }

    private void scaleAndAlphaAnimation(View view, final Animator.AnimatorListener listener) {
        view.setScaleX(0);
        view.setScaleY(0);
        view.setAlpha(0);
        view.setPivotX(view == mLeftBubble ? 200 : view.getWidth() - 200);
        view.setPivotY(view.getHeight());
        view.animate().scaleX(1).scaleY(1).alpha(1)
            .setInterpolator(new AccelerateInterpolator())
            .setListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationStart(Animator animation) {
                    if (listener != null) {
                        listener.onAnimationStart(animation);
                    }
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    if (listener != null) {
                        listener.onAnimationEnd(animation);
                    }
                }
            })
            .setDuration(mDuration).start();
    }

    private RelativeLayout getBubbleContentView(int type) {
        RelativeLayout content = new RelativeLayout(getContext());
        LinearLayout.MarginLayoutParams layoutParams = new LayoutParams(
            LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);

        Log.d(TAG, "getBubbleContentView type :"+type+", mOrientation:"+mOrientation);
        if (mOrientation == Configuration.ORIENTATION_LANDSCAPE &&  type == 1){
            Log.d(TAG, "getBubbleContentView setMargin ");
            layoutParams.setMargins(0, - DimenUtils.dp2Px(120),0,0);
        }

        layoutParams.height = mContentHeight;
        content.setLayoutParams(layoutParams);
        content.setGravity(Gravity.CENTER_VERTICAL);
        return content;
    }

    private TextView getBubbleView(int type) {
        TextView view = new TextView(getContext());
        view.setTextColor(Color.WHITE);
        view.setTextSize(TypedValue.COMPLEX_UNIT_SP, (mOrientation == Configuration.ORIENTATION_LANDSCAPE ? 18 : 36));
        view.setGravity(Gravity.CENTER_VERTICAL);
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        if (type == 0) {
            params.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
        } else {
            params.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
        }
        view.setLayoutParams(params);
        view.setAlpha(0);
        return view;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        Log.d(TAG, "onDetachedFromWindow");
        mHandler.removeMessages(SWITCH_MESSAGE);
    }

    private static class MyHandler extends Handler {
        private WeakReference<BubbleView> mReference;

        public MyHandler(BubbleView view) {
            this.mReference = new WeakReference<>(view);
        }

        @Override
        public void handleMessage(Message msg) {
            BubbleView view = mReference.get();
            if (view == null) {
                return;
            }
            switch (msg.what) {
                case SWITCH_MESSAGE:
                    view.switchBubble();
                    break;
                default:
                    break;
            }
        }
    }

}
