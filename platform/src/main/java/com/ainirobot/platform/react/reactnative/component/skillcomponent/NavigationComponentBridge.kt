package com.ainirobot.platform.react.reactnative.component.skillcomponent

import com.ainirobot.platform.component.NavigationComponent
import com.ainirobot.platform.react.server.utils.ComponentUtils
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactMethod

/**
 * @date: 2019-03-27
 * @author: lumeng
 * @desc: Navigation Component Bridge for RN
 */
class NavigationComponentBridge(reactContext: ReactApplicationContext) : BaseComponentBridge<NavigationComponent>(reactContext) {

    override fun isHDMonopoly(): Boolean {
        return true
    }

    override fun getResouceType(): Array<ResouceType> {
        return arrayOf(ResouceType.Navigation)
    }

    override fun getName(): String {
        return ComponentUtils.COMPONENT_NAVIGATION
    }

    @ReactMethod
    override fun start(uid: String, param: String?) {
        super._start(uid, param)
    }

    @ReactMethod
    override fun stop(uid: String, timeout: Int?) {
        super._stop(uid, timeout)
    }

    @ReactMethod
    override fun updateParams(uid: String, intent: String, param: String) {
        super._updateParams(uid, intent, param)
    }

    @ReactMethod
    override fun setStatusListener(uid: String, statusListenerId: Int) {
        super._setStatusListener(uid, statusListenerId)
    }

    @ReactMethod
    override fun setFinishListener(uid: String, finishListenerId: Int) {
        super._setFinishListener(uid, finishListenerId)
    }

}
