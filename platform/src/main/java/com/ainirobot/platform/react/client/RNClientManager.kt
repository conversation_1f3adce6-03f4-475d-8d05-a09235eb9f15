package com.ainirobot.platform.react.client

/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.os.Process
import android.os.RemoteException
import android.util.Log
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.light.LightManager
import com.ainirobot.platform.msg.MsgController
import com.ainirobot.platform.react.RNDef
import com.ainirobot.platform.react.server.impl.*
import com.ainirobot.platform.rn.*

class RNClientManager private constructor() {
    private var conn: ServiceConnection? = null
    var mRNManagerService: IRNManagerService? = null
    private val mSpeechCallBack: RNSpeechCallBack = RNSpeechCallBack()
    val mCommandCallBack: RNCommandCallBack = RNCommandCallBack()
    //死亡代理
    private val mBinderDeathRecipient = object : IBinder.DeathRecipient {

        override fun binderDied() {
            Log.e(TAG, "binderDied")
            ClientUtils.killProcess(Process.myPid())
            if (null == mRNManagerService) {
                return
            }
            mRNManagerService!!.asBinder().unlinkToDeath(this, 1)
            mRNManagerService = null
            conn = null
            apiRegistry = null
            componentRegistry = null
            speechApiRegistry = null
            dataRegistry = null
            lightRegistry = null
            personManagerRegistry = null
            deviceRegistry = null
            rnMsgManager = null
        }
    }

    internal var apiRegistry: IApiRegistry? = null
    // if null kill
    val apiManager: IApiRegistry?
        get() {
            if (apiRegistry == null) {
                apiRegistry = RNApiServer.asInterface(queryBinder(RNDef.BIND_API))
            }
            if (apiRegistry == null) {
                Log.e(TAG, "apiManager apiRegistry null")
            }
            return apiRegistry
        }

    internal var componentRegistry: IComponentRegistry? = null
    val componentManager: IComponentRegistry?
        get() {
            if (componentRegistry == null) {
                componentRegistry = RNComponentServer.asInterface(queryBinder(RNDef.BIND_COMPONENT))
            }
            if (componentRegistry == null) {
                Log.e(TAG, "componentManager componentRegistry null")
            }
            return componentRegistry
        }

    internal var speechApiRegistry: ISpeechApiRegistry? = null
    val speechApiManager: ISpeechApiRegistry?
        get() {
            if (speechApiRegistry == null) {
                speechApiRegistry = RNSpeechApiServer.asInterface(queryBinder(RNDef.BIND_SPEECH_API))
            }
            if (speechApiRegistry == null) {
                Log.e(TAG, "speechApiManager speechApiRegistry null")
            }
            return speechApiRegistry
        }

    internal var dataRegistry: IDataRegistry? = null
    val dataManager: IDataRegistry?
        get() {
            if (dataRegistry == null) {
                dataRegistry = RNDataServer.asInterface(queryBinder(RNDef.BIND_DATA))
            }
            if (dataRegistry == null) {
                Log.e(TAG, "dataManager dataRegistry null")
            }

            return dataRegistry
        }

    internal var lightRegistry: ILightRegistry? = null
    val lightManager: ILightRegistry?
        get() {
            if (lightRegistry == null) {
                lightRegistry = LightManager.asInterface(queryBinder(RNDef.BIND_LIGHT))
            }
            if (lightRegistry == null) {
                Log.e(TAG, "lightManager lightRegistry null")
            }

            return lightRegistry
        }

    internal var deviceRegistry: IDeviceRegistry? = null
    val deviceManager: IDeviceRegistry?
        get() {
            if (deviceRegistry == null) {
                deviceRegistry = RNDeviceServer.asInterface(queryBinder(RNDef.BIND_DEVICE))
            }
            if (deviceRegistry == null) {
                Log.e(TAG, "deviceManager deviceRegistry null")
            }

            return deviceRegistry
        }

    internal var personManagerRegistry: IRNPersonManager? = null
    val personManager: IRNPersonManager?
        get() {
            if (personManagerRegistry == null) {
                personManagerRegistry = RNPersonManagerServer.asInterface(queryBinder(RNDef.BIND_PERSON))
            }
            if (personManagerRegistry == null) {
                Log.e(TAG, "personManager personManagerRegistry null")
            }

            return personManagerRegistry
        }

    internal var intentRegister: IIntentManager? = null
    val intentManager: IIntentManager?
        get() {
            if (intentRegister == null) {
                intentRegister = IIntentManager.Stub.asInterface(queryBinder(RNDef.BIND_INTENT))
            }
            if (intentRegister == null) {
                Log.e(TAG, "intentRegister intentRegister null")
            }

            return intentRegister
        }

    internal var robotSettingRegistry: IRNRobotSettingRegistry? = null
    val robotSettingManager: IRNRobotSettingRegistry?
        get() {
            if (robotSettingRegistry == null) {
                robotSettingRegistry = RNRobotSettingServer.asInterface(queryBinder(RNDef.BIND_SETTING))
            }
            if (robotSettingRegistry == null) {
                Log.e(TAG, "robotSettingManager robotSettingRegistry null")
            }
            return robotSettingRegistry;
        }

    internal var rnUpdateManager: IRNUpdateManager? = null
    val updateManager: IRNUpdateManager?
        get() {
            if (rnUpdateManager == null) {
                rnUpdateManager = RNUpdateManagerServer.asInterface(queryBinder(RNDef.BIND_PACKAGE))
            }
            if (rnUpdateManager == null) {
                Log.e(TAG, "robotSettingManager robotSettingRegistry null")
            }
            return rnUpdateManager
        }

    private var nlpControlRegistry: IRNApkControlRegistry? = null

    val nlpApkControl: IRNApkControlRegistry?
        get() {
            if (nlpControlRegistry == null) {
                nlpControlRegistry = IRNApkControlRegistry.Stub.asInterface(queryBinder(RNDef.BIND_NLP_APK_CONTROL))
            }
            if (nlpControlRegistry == null) {
                Log.e(TAG, "nlpControlRegistry intentRegister null")
            }
            return nlpControlRegistry
        }

    internal var rnMsgManager: IMsgRegistry? = null
    val msgManager: IMsgRegistry?
        get() {
            if (rnMsgManager == null) {
                rnMsgManager = MsgController.asInterface(queryBinder(RNDef.BIND_MSG))
            }
            if (rnMsgManager == null) {
                Log.e(TAG, "msgManager msgRegistry null")
            }
            return rnMsgManager
        }

    internal var rnMapManager: IMapManager? = null
    val mapManager: IMapManager?
        get() {
            if (rnMapManager == null) {
                rnMapManager = MapManager.asInterface(queryBinder(RNDef.BIND_MAP))
            }
            if (rnMapManager == null) {
                Log.e(TAG, "mapManager rnMapManager null")
            }
            return rnMapManager
        }

    internal var rnCallButtonManager: IRNCallButtonManager? = null
    val callButtonManager: IRNCallButtonManager?
        get() {
            if (rnCallButtonManager == null) {
                rnCallButtonManager = RNCallButtonManagerServer.asInterface(queryBinder(RNDef.BIND_CALL_BUTTON))
            }
            if (rnCallButtonManager == null) {
                Log.e(TAG, "callButtonManager rnCallButtonManager null")
            }
            return rnCallButtonManager
        }

    internal var rnProductInfoRegistry: IProductInfoRegistry? = null
    val productInfoManager: IProductInfoRegistry?
        get() {
            if (rnProductInfoRegistry == null) {
                rnProductInfoRegistry = RNProductInfoServer.asInterface(queryBinder(RNDef.BIND_ROBOT_INFO))
            }
            if (rnProductInfoRegistry == null) {
                Log.e(TAG, "productInfoManager rnProductInfoRegistry null")
            }
            return rnProductInfoRegistry
        }

    internal var rnOkHttpRequestRegistry: OkHttpRequestRegistry? = null
    val okhttpRequestManager: OkHttpRequestRegistry?
        get() {
            if (rnOkHttpRequestRegistry == null) {
                rnOkHttpRequestRegistry = RNOkHttpRequestServer.asInterface(queryBinder(RNDef.BIND_OKHTTP))
            }
            if (rnOkHttpRequestRegistry == null) {
                Log.e(TAG, "okhttpRequestManager rnOkHttpRequestRegistry null")
            }
            return rnOkHttpRequestRegistry
        }


    @Synchronized
    fun bindModuleService(context: Context) {
        Log.e(TAG, "bindModuleService $mRNManagerService")
        if (mRNManagerService != null) {
            return
        }

        if (null == conn) {
            val component = ComponentName(BaseApplication.getContext().packageName,
                    RNDef.RN_SERVER_SERVICE_NAME)
            val intent = Intent(RNDef.RN_SERVER_ACTION_NAME)
            intent.component = component
            conn = object : ServiceConnection {
                override fun onServiceConnected(name: ComponentName, service: IBinder) {
                    Log.e(TAG, "onServiceConnected name: $name")
                    mRNManagerService = IRNManagerService.Stub.asInterface(service)
                    try {
                        mRNManagerService!!.setCommandCallBack(mCommandCallBack)
                        mRNManagerService!!.setSpeechCallBack(mSpeechCallBack)
                        mRNManagerService!!.asBinder().linkToDeath(mBinderDeathRecipient, 1)
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }

                override fun onServiceDisconnected(name: ComponentName) {
                    Log.e(TAG, "onServiceDisconnected name: $name")
                    mRNManagerService = null
                }
            }
            context.bindService(intent, conn!!, Context.BIND_AUTO_CREATE)
            Log.e(TAG, "bind Service！")
        } else {
            Log.e(TAG, "have bind Service！")
        }
    }


    //获取Binder
    private fun queryBinder(binderCode: Int): IBinder? {
        var binder: IBinder? = null
        try {
            if (null != mRNManagerService) {
                binder = mRNManagerService!!.queryBinder(binderCode)
            }
        } catch (e: RemoteException) {
            e.printStackTrace()
        }

        return binder
    }

    companion object {
        private val TAG = RNClientManager::class.java.simpleName

        @Volatile
        private var sInstance: RNClientManager? = null

        val instance: RNClientManager?
            get() {
                if (null == sInstance) {
                    synchronized(RNClientManager::class.java) {
                        if (null == sInstance) {
                            sInstance = RNClientManager()
                        }
                    }
                }
                return sInstance
            }
    }
}
