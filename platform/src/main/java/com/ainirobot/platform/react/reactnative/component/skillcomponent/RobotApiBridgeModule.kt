package com.ainirobot.platform.react.reactnative.component.skillcomponent

import android.graphics.Color
import android.util.Log
import com.ainirobot.coreservice.bean.BackgroundTaskEvent
import com.ainirobot.coreservice.bean.Task
import com.ainirobot.coreservice.bean.TaskEvent
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.component.listener.*
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeJson
import com.facebook.react.bridge.*
import com.google.gson.Gson


class RobotApiBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    companion object {
        val mGson = Gson()
    }

    override fun getName(): String {
        return "RobotApi"
    }

    @ReactMethod
    fun isActive(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.isActive)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun disableEmergency() {
        try {
            RNClientManager.instance?.apiManager?.disableEmergency()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    @ReactMethod
    fun startTakeOverEmergency(callbackId: Int) {
        try {
            if (callbackId >= 0) {
                RNClientManager.instance?.apiManager?.startTakeOverEmergency(callbackId, BridgeEmergencyListener.obtain(callbackId))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun stopTakeOverEmergency(callbackId: Int) {
        try {
            RNClientManager.instance?.apiManager?.stopTakeOverEmergency(callbackId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun disableBattery() {
        try {
            RNClientManager.instance?.apiManager?.disableBattery()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun startPlayAction(callbackId: Int, params: ReadableMap, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.playTrick(ReactNativeJson.convertMapToJson(params).toString(), BridgeActionListener.obtain(callbackId)))
        } catch (e: Exception) {
            promise.reject(e)
        }
    }

    @ReactMethod
    fun stopPlayAction(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.stopPlayTrick())
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getPlaceList(promise: Promise) {
        try {
            val ret = RNClientManager.instance?.apiManager?.getPlaceList(PromiseCommandListener(promise))
            if (ret != 0) {
                promise.reject(ret.toString(), "getPlaceList failed")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getPlacesByType(typeId: Int, promise: Promise) {
        try {
            val ret = RNClientManager.instance?.apiManager?.getPlacesByType(typeId, PromiseCommandListener(promise))
            if (ret != 0) {
                promise.reject(ret.toString(), "getPlacesByType failed")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getLanguageList(callbackId: Int, promise: Promise) {
        try {
            val ret = RNClientManager.instance?.apiManager?.getLanguageList(BridgeCommandListener.obtain(callbackId))
            if (ret != 0) {
                promise.reject(ret.toString(), "getLanguageList failed")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getDoorStatus(callbackId: Int, type: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getDoorStatus(type, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun scoreRecordingTask(callbackId: Int, params: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.scoreRecordingTask(params, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun setLockEnable(type: Int, bord: Int, enable: Boolean, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.setLockEnable(type, bord, enable))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun textToMp3(callbackId: Int, text: String, fullFile: String, fileName: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.textToMp3(text, fullFile, fileName, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getPlaceListWithName(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getPlaceListWithName(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getPlaceListByMapName(callbackId: Int, mapName: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getPlaceListByMapName(mapName, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getPlaceListWithNameList(callbackId: Int, param: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getPlaceListWithNameList(param, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getPlace(callbackId: Int, param: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getPlace(param, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isRobotInlocations(callbackId: Int, param: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.isRobotInlocations(param, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isRobotEstimate(promise: Promise) {
        try {
            val ret = RNClientManager.instance?.apiManager?.isRobotEstimate(PromiseCommandListener(promise))
            if (ret != 0) {
                promise.reject(ret.toString(), "isRobotEstimate failed")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun resetHead(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.resetHead(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun switchCamera(callbackId: Int, mode: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.switchCamera(mode, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun setLambColor(target: Int, colorString: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.setLambColor(target, Color.parseColor(colorString)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun setLambAnimation(target: Int, startColorString: String, endColorString: String, startTime: Int, endTime: Int, repeat: Int, onTime: Int, freezeColorString: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.setLambAnimation(target, Color.parseColor(startColorString), Color.parseColor(endColorString), startTime, endTime, repeat, onTime, Color.parseColor(freezeColorString)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getPosition(promise: Promise) {
        try {
            val ret = RNClientManager.instance?.apiManager?.getPosition(PromiseCommandListener(promise))
            if (ret != 0) {
                promise.reject(ret.toString(), "getPosition failed")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getMapName(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getMapName(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getLocation(param: String, promise: Promise) {
        try {
            val ret = RNClientManager.instance?.apiManager?.getLocation(param, PromiseCommandListener(promise))
            if (ret != 0) {
                promise.reject(ret.toString(), "getLocation failed")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun remoteWakeUpTimes(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.remoteWakeUpTimes(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getBatteryTimeRemaining(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getBatteryTimeRemaining(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getChargeTimeRemaining(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getChargeTimeRemaining(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun startVision(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.startVision(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun stopVision(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.stopVision(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun startBackupVision(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.startBackupVision(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun stopBackupVision(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.stopBackupVision(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun canRobotReboot(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.canRobotReboot(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getAskWayList(promise: Promise) {
        try {
            val ret = RNClientManager.instance?.apiManager?.getAskWayList(PromiseCommandListener(promise))
            if (ret != 0) {
                promise.reject(ret.toString(), "getAskWayList failed")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun skillDataReport(param: String) {
        Log.d("skillDataReport", "param = $param")
        RNClientManager.instance?.apiManager?.skillDataReport(param, null)
    }

    @ReactMethod
    fun robotReboot(reason: String) {
        try {
            RNClientManager.instance?.apiManager?.robotReboot(reason)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun getCanRotateSupport(callbackId: Int) {
        try {
            RNClientManager.instance?.apiManager?.getCanRotateSupport(BridgeCommandListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun turnLeft(callbackId: Int, speed: Double, angle: Double, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.turnLeft(speed.toFloat(),
                    angle.toFloat(), BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun turnRight(callbackId: Int, speed: Double, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.turnRight(speed.toFloat(),
                    BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun turnRight2(callbackId: Int, speed: Double, angle: Double, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.turnRight2(speed.toFloat(),
                    angle.toFloat(), BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun goForward(callbackId: Int, speed: Double, distance: Double, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.goForward(speed.toFloat(),
                    distance.toFloat(), BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun goBackward(callbackId: Int, speed: Double, distance: Double, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.goBackward(speed.toFloat(),
                    distance.toFloat(), BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun stopMove(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.stopMove(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun rotate(callbackId: Int, speed: Double, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.rotate(speed.toFloat(), BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun startVelocityReport(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.startVelocityReport(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun stopVelocityReport(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.stopVelocityReport(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun setMaxAcceleration(callbackId: Int, xAcc: Double, yAcc: Double, zAcc: Double, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.setMaxAcceleration(xAcc.toFloat(), yAcc.toFloat(), zAcc.toFloat(), BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun motionLine(callbackId: Int, direction: String, speed: Double, distance: Double, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.motionLine(direction, speed.toFloat(), distance.toFloat(), BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun motionArc(callbackId: Int, distance: Double, angle: Double, angularSpeed: Double, latency: Double, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.motionArc(distance.toFloat(), angle.toFloat(), angularSpeed.toFloat(), latency.toFloat(), BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun motionArcWithObstacles(callbackId: Int, lineSpeed: Double, angularSpeed: Double, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.motionArcWithObstacles(lineSpeed.toFloat(), angularSpeed.toFloat(), BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun goPosition(callbackId: Int, position: String, velocity: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.goPosition(position, velocity, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun goPosition2(callbackId: Int, position: String, linearSpeed: Double, angularSpeed: Double, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.goPosition2(position, linearSpeed.toFloat(), angularSpeed.toFloat(), BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun stopGoPosition(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.stopGoPosition())
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getNavigationLineSpeed(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getNavigationLineSpeed(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getNavigationAngleSpeed(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getNavigationAngleSpeed(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun moveHead(callbackId: Int, hmode: String, vmode: String, hangle: Int, vangle: Int, hMaxSpeed: Int, vMaxSpeed: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.moveHead(hmode, vmode, hangle, vangle, hMaxSpeed, vMaxSpeed, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getHeadStatus(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getHeadStatus(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isHeaderConnected(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.isHeaderConnected(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getChargeStatus(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getChargeStatus(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun setStartChargePoseAction(callbackId: Int, timeout: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.setStartChargePoseAction(timeout.toLong(), BridgeActionListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun startNaviToAutoChargeAction(callbackId: Int, timeout: Int, distance: Double, avoidTime: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.startNaviToAutoChargeAction(timeout.toLong(), distance, avoidTime.toLong(), BridgeActionListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun stopAutoChargeAction(isResetHW: Boolean, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.stopAutoChargeAction(isResetHW))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getEmergencyStatus(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getEmergencyStatus(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun startInspection(callbackId: Int, time: Int, isReInspection: Boolean, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.startInspection(time.toLong(), isReInspection, BridgeActionListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun stopInspection(isResetHW: Boolean, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.stopInspection(isResetHW))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun resumeSpecialPlaceTheta(callbackId: Int, placeName: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.resumeSpecialPlaceTheta(placeName, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun stopResumeSpecialPlaceThetaAction(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.stopResumeSpecialPlaceThetaAction())
        } catch (e: Exception) {
            promise.reject(e)
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun remoteRequestQrcode(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.remoteRequestQrcode(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun checkIfHasObstacle(callbackId: Int, startAngle: Double, endAngle: Double, distance: Double, promise: Promise) {

        try {
            promise.resolve(RNClientManager.instance?.apiManager?.checkIfHasObstacle(startAngle,
                    endAngle, distance, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getBatteryLevel(promise: Promise) {

        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getBatteryLevel())
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun rnHeartBeat(promise: Promise) {
        try {
            promise.resolve(false)
//            promise.resolve(RNClientManager.instance?.apiManager?.rnHeartBeat())
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getPersonInfoFromNet(callbackId: Int, personId: Int, picArr: ReadableArray, promise: Promise) {
        try {
            var pictures: List<String>? = null
            if (picArr != null) {
                pictures = picArr.toArrayList() as List<String>
            }

            promise.resolve(RNClientManager.instance?.apiManager?.getPersonInfoFromNet(
                    personId.toString(), pictures, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getHeadCount(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getHeadCount(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun setLanguage(language: String) {
        RNClientManager.instance?.apiManager?.setLanguage(language);
    }

    @ReactMethod
    fun sendStatusReport(type: String, data: String) {
        try {
            RNClientManager.instance?.apiManager?.sendStatusReport(type, data)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun updatePictureReportConfig(config: String, callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.updatePictureReportConfig(config, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun capScreen(type: Int, callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.capScreen(type, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun updateRobotStatus(status: Int) {
        try {
            RNClientManager.instance?.apiManager?.updateRobotStatus(status)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun updateStandbyStatus(standbyStart: Boolean, jsonData: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.updateStandbyStatus(standbyStart, jsonData));
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getDefWelcomeTTS(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getDefWelcomeTTS(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun taskModeReport(callbackId: Int, taskId: String?, taskMode: String, taskResult: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.taskModeReport(taskId, taskMode,
                    taskResult, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun taskExecReport(callbackId: Int, taskId: String?, taskType: String, execResult: String?, execData: String?, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.execTaskReport(taskId, taskType, execResult,
                    execData, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun taskCommandReport(callbackId: Int, cmdId: String?, cmdType: String, execResult: String?, execData: String?, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.taskCommandReport(cmdId, cmdType, execResult,
                    execData, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun enableBattery() {
        try {
            RNClientManager.instance?.apiManager?.enableBattery()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun takePicture(callbackId: Int, type: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.takePicture(type, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun uploadCruisePicture(callbackId: Int, param: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.uploadCruisePicture(param, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun reportRobotTask(taskType: String, name: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.reportTask(Task.TASK_MODE_FOREGROUND, taskType, name, ""))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun reportRobotCurrentTask(taskType: String, name: String, taskId: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.reportRobotCurrentTask(Task.TASK_MODE_FOREGROUND, taskType, name, taskId, ""))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun reportRobotTaskData(taskType: String, name: String, subTaskList: String?, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.reportTask(Task.TASK_MODE_FOREGROUND, taskType, name, subTaskList))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun reportRobotCurrentTaskWithSubTask(taskType: String, name: String, taskId: String, subTaskList: String?, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.reportRobotCurrentTask(Task.TASK_MODE_FOREGROUND, taskType, name, taskId, subTaskList))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun reportRobotTaskEvent(taskId: String, taskData: String, eventType: String, eventData: String, promise: Promise) {
        try {
            val event = TaskEvent()
            event.taskId = taskId
            event.taskData = taskData
            event.eventType = eventType
            event.eventData = eventData
            event.isCreateOnStart = 1
            promise.resolve(RNClientManager.instance?.apiManager?.reportTaskEvent(mGson.toJson(event)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun reportRobotTaskEventData(taskEvent: String) {
        try {
            RNClientManager.instance?.apiManager?.reportTaskEvent(taskEvent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun reportBackgroundTaskEvent(reportEventMap: ReadableMap) {
        val taskEvent = BackgroundTaskEvent().apply {
            if (reportEventMap.hasKey("currentTaskId")) {
                currentTaskId = reportEventMap.getString("currentTaskId")
            }
            if (reportEventMap.hasKey("currentTaskType")) {
                currentTaskType = reportEventMap.getString("currentTaskType")
            }
            if (reportEventMap.hasKey("backgroundTaskList")) {
                val backgroundTaskListArray = reportEventMap.getArray("backgroundTaskList")
                val backgroundTaskList: MutableList<TaskEvent> = ArrayList()
                backgroundTaskListArray?.let { array ->
                    for (i in 0 until array.size()) {
                        array.getMap(i)?.let { taskEventMap ->
                            val taskEventItem = TaskEvent().apply {
                                if (taskEventMap.hasKey("taskId")) {
                                    taskId = taskEventMap.getString("taskId")
                                }
                                if (taskEventMap.hasKey("taskType")) {
                                    taskType = taskEventMap.getString("taskType")
                                }
                            }
                            backgroundTaskList.add(taskEventItem)
                        }
                    }
                }
                this.backgroundTaskList = backgroundTaskList
            }
        }
        try {
            RNClientManager.instance?.apiManager?.reportBackgroundTaskEvent(mGson.toJson(taskEvent))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun getCurrentTaskId(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.currentTaskId)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun startTakeOverFunctionKey(callbackId: Int) {
        try {
            if (callbackId >= 0) {
                RNClientManager.instance?.apiManager?.startTakeOverFunctionKey(callbackId, BridgeFunctionKeyListener.obtain(callbackId))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun stopTakeOverFunctionKey(callbackId: Int) {
        try {
            RNClientManager.instance?.apiManager?.stopTakeOverFunctionKey(callbackId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun moduleCodeConfigUpload(callbackId: Int, moduleCode: String, description: String?, configName: String, configJson: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.moduleCodeConfigUpload(moduleCode, description, configName,
                    configJson, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun floorPositionList(
            callbackId: Int,
            floorId: String,
            posName: String,
            status: String,
            promise: Promise
    ) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.floorPositionList(floorId, posName, status,
                   BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun taskGroupQueue(callbackId: Int, allowTaskType: String, taskGroupTypeList: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.taskGroupQueue(allowTaskType, taskGroupTypeList,
                    BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun taskCreate(callbackId: Int, parentTaskOutid: String, taskType: String, taskList: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.taskCreate(parentTaskOutid, taskType, taskList,
                    BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun taskTake(callbackId: Int, taskId: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.taskTake(taskId,
                    BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun cabinetPutGoodsStatus(callbackId: Int, taskId: String, stationType: String, stationId: String, stationOrderId: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.cabinetPutGoodsStatus(taskId, stationType, stationId, stationOrderId,
                BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun taskManual(callbackId: Int, taskId: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.taskManual(taskId,
                    BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun taskCancel(callbackId: Int, taskId: String, cancelResult: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.taskCancel(taskId, cancelResult,
                    BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun taskSucc(callbackId: Int, taskId: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.taskSucc(taskId,
                    BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun taskPosQrcode(callbackId: Int, qrcodeType: String, posName: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.taskPosQrcode(qrcodeType, posName,
                    BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getSpecialDishLabels(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getSpecialDishLabels(
                    BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun shutdown(reason: String) {
        try {
            RNClientManager.instance?.apiManager?.shutdown(reason)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun getNaviPathInfo(callbackId: Int, startPoseJson: String, endPoseJson: String, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getNaviPathInfo(startPoseJson,
                    endPoseJson, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getNaviPathInfoToGoals(callbackId: Int, goalNameArr: ReadableArray, promise: Promise) {
        try {
            var goalNameList: List<String>? = null
            if (goalNameArr != null){
                goalNameList = goalNameArr.toArrayList() as List<String>
            }
            promise.resolve(RNClientManager.instance?.apiManager?.getNaviPathInfoToGoal(goalNameList,
                    BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getNavigationConfig(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getNavigationConfig(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getMaxTemperature(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(null)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun setXDPowderEnable(enable: Boolean,promise: Promise){
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.setXDPowderEnable(enable))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun setXDFanEnable(enable: Boolean,promise: Promise){
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.setXDFanEnable(enable))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun setXDRank(rank: Int,promise: Promise){
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.setXDRank(rank))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun monitorAtomizerDryStatus(callbackId: Int) {
        try {
            if (callbackId >= 0) {
                RNClientManager.instance?.apiManager?.monitorDryStatus(callbackId, BridgeDryStatusListener.obtain(callbackId))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun unMonitorAtomizerDryStatus(callbackId: Int) {
        try {
            RNClientManager.instance?.apiManager?.unMonitorDryStatus(callbackId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun goCharging(){
        try {
            RNClientManager.instance?.apiManager?.goCharging()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun getLocalAndServerSupportLanguageList(promise: Promise){
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getLocalAndServerSupportLanguageList())
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getMultiFloorConfigAndPose(callbackId: Int, promise: Promise){
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getMultiFloorConfigAndPose(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun robotStandby(){
        try {
            RNClientManager.instance?.apiManager?.robotStandby()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun getMultiFloorConfigAndCommonPose(callbackId: Int, promise: Promise){
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getMultiFloorConfigAndCommonPose(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getNaviAngSpeed(callbackId: Int, speed: String, promise: Promise) {
        try {
            promise.resolve(
                RNClientManager.instance?.apiManager?.getNaviAngSpeed(
                    speed,
                    BridgeCommandListener.obtain(callbackId)
                )
            )
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun robotStandByEnd(){
        try {
            RNClientManager.instance?.apiManager?.robotStandByEnd()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun queryRadarStatus(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.queryRadarStatus(
                    BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun monitorRemoteStandBy(callbackId: Int) {
        try {
            if (callbackId >= 0) {
                RNClientManager.instance?.apiManager?.monitorRemoteStandBy(callbackId, BridgeRemoteStandbyListener.obtain(callbackId))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun unMonitorRemoteStandBy(callbackId: Int) {
        try {
            RNClientManager.instance?.apiManager?.unMonitorRemoteStandBy(callbackId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun getScreenBrightness(promise: Promise) {

        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getScreenBrightness())
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun setScreenBrightness(brightness: Int,promise: Promise){
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.setScreenBrightness(brightness))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun updateRadarStatus(callbackId: Int,openRadar: Boolean, promise: Promise){
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.updateRadarStatus(openRadar,BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun monitorCollideStatus(callbackId: Int) {
        try {
            if (callbackId >= 0) {
                RNClientManager.instance?.apiManager?.monitorCollideStatus(callbackId, BridgeCollideStatusListener.obtain(callbackId))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun unMonitorCollideStatus(callbackId: Int) {
        try {
            RNClientManager.instance?.apiManager?.unMonitorCollideStatus(callbackId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun proactiveProblemReport(occurrenceTime: Double, issueType: String, packageName: String, pageName: String, description: String) {
        try {
            RNClientManager.instance?.apiManager?.proactiveProblemReport(occurrenceTime,issueType,packageName,pageName,description)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setElectricDoorCtrl(callbackId: Int, ctrlCmd: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.setElectricDoorCtrl(ctrlCmd, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getElectricDoorStatus(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getElectricDoorStatus(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isSupportElectricDoor(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.isSupportElectricDoor)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isSupportElevator(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.isSupportElevator)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isSupportKKCamera(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.isSupportKKCamera)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isWaiterProCarry(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.isWaiterProCarry)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun setQueryFeedback(chatMaxSid: String, feedbackResult: String, promise: Promise) {
        try {
            promise.resolve(
                RNClientManager.instance?.apiManager?.setQueryFeedback(
                    chatMaxSid,
                    feedbackResult
                )
            )
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun registerElectricDoorStateListener(callbackId: Int) {
        try {
            if (callbackId >= 0) {
                RNClientManager.instance?.apiManager?.registerElectricDoorStateListener(
                    callbackId,
                    BridgeElectricDoorStateListener.obtain(callbackId))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun unRegisterElectricDoorStateListener(callbackId: Int) {
        RNClientManager.instance?.apiManager?.unRegisterElectricDoorStateListener(callbackId)
    }

    @ReactMethod
    fun getMotionDistance(callbackId: Int) {
        try {
            RNClientManager.instance?.apiManager?.getMotionDistance(
                BridgeCommandListener.obtain(
                    callbackId
                )
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun deliveryRobotTask(callbackId: Int, paramJson: String) {
        try {
            RNClientManager.instance?.apiManager?.deliveryRobotTask(paramJson,BridgeCommandListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    @ReactMethod
    fun deliveryRobotStatusNotify(callbackId: Int, paramJson: String) {
        try {
            RNClientManager.instance?.apiManager?.deliveryRobotStatusNotify(paramJson,BridgeCommandListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    @ReactMethod
    fun deliveryMonitorDetail(callbackId: Int, paramJson: String) {
        try {
            RNClientManager.instance?.apiManager?.deliveryMonitorDetail(paramJson,BridgeCommandListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    @ReactMethod
    fun deliveryRobotComment(callbackId: Int, paramJson: String) {
        try {
            RNClientManager.instance?.apiManager?.deliveryRobotComment(paramJson,BridgeCommandListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun startRadarAlign(callbackId: Int, poseName: String) {
        try {
            RNClientManager.instance?.apiManager?.startRadarAlign(poseName, BridgeCommandListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun startRadarAlign2(callbackId: Int, poseName: String, startAlignTimeout: Int, retryDelayTime: Int, navigationTimeout: Int) {
        try {
            RNClientManager.instance?.apiManager?.startRadarAlign2(poseName, startAlignTimeout.toLong(),
                retryDelayTime.toLong(), navigationTimeout.toLong(), BridgeCommandListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun stopRadarAlign() {
        try {
            RNClientManager.instance?.apiManager?.stopRadarAlign()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun deliveryRemoteCall(callbackId: Int, orderInfo: String) {
        try {
            RNClientManager.instance?.apiManager?.deliveryRemoteCall(orderInfo,BridgeCommandListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun deliveryReport(callbackId: Int, orderInfo: String) {
        try {
            RNClientManager.instance?.apiManager?.deliveryReport(orderInfo,BridgeCommandListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun pauseNavigation(callbackId: Int, isPause: Boolean) {
        try {
            RNClientManager.instance?.apiManager?.pauseNavigation(isPause,BridgeCommandListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun switchMap(callbackId: Int, mapName: String) {
        try {
            RNClientManager.instance?.apiManager?.switchMap(mapName,BridgeCommandListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setFixedEstimate(callbackId: Int, param: String) {
        try {
            RNClientManager.instance?.apiManager?.setFixedEstimate(param,BridgeCommandListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun installApk(fullPathName: String, taskID: String) {
        try {
            RNClientManager.instance?.apiManager?.installApk(fullPathName, taskID)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun hasHeightLimitCamera(promise: Promise) {
        try {
            val result = RNClientManager.instance?.apiManager?.hasHeightLimitCamera()
            promise.resolve(result)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    fun getLineSpeed(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getLineSpeed())
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun setLineSpeed(callbackId: Int, speed: String) {
        try {
            RNClientManager.instance?.apiManager?.setLineSpeed(speed, BridgeCommandListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun goForwardWithAvoid(callbackId: Int, speed: Double, distance: Double, avoid: Boolean, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.goForwardWithAvoid(speed.toFloat(),
                distance.toFloat(), avoid, BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun registerStatusListener(callbackId: Int, type: String) {
        try {
            if (callbackId >= 0) {
                RNClientManager.instance?.apiManager?.registerStatusListener(type, BridgeRobotStateListener.obtain(callbackId))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun unregisterStatusListener(callbackId: Int) {
        try {
            RNClientManager.instance?.apiManager?.unregisterStatusListener(BridgeRobotStateListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun startNavigationFollow(callbackId: Int, followId: String, lostFindTimeout: Int) {
        try {
            RNClientManager.instance?.apiManager?.startNavigationFollow(followId, lostFindTimeout, BridgeActionListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun stopNavigationFollow() {
        try {
            RNClientManager.instance?.apiManager?.stopNavigationFollow()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun detectQrCode(callbackId: Int, file: String) {
        try {
            RNClientManager.instance?.apiManager?.detectQrCode(file, BridgeCommandListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun disableOutsideMapAlarm() {
        try {
            RNClientManager.instance?.apiManager?.disableOutsideMapAlarm()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun enableOutsideMapAlarm() {
        try {
            RNClientManager.instance?.apiManager?.enableOutsideMapAlarm()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun getElevatorStatus(callbackId: Int) {
        try {
            RNClientManager.instance?.apiManager?.getElevatorStatus(BridgeCommandListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun rotateInPlace(callbackId: Int, direction: Int, speed: Double, angle: Double, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.rotateInPlace(direction,
                speed.toFloat(), angle.toFloat(), BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }
}
