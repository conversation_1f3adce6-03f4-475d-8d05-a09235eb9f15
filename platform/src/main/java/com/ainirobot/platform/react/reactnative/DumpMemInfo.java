package com.ainirobot.platform.react.reactnative;

import android.content.Context;
import android.os.Debug;
import android.os.Debug.MemoryInfo;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.base.cpumemory.config.SharePluginInfo;
import com.ainirobot.base.util.DeviceUtil;
import com.ainirobot.base.util.ShellUtils;
import com.ainirobot.base.util.ShellUtils.CommandResult;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DumpMemInfo {

    private static final String TAG = "DumpMemInfo";

    static SimpleDateFormat format = new SimpleDateFormat("HH:mm:ss");
    static final String SPLIT="|";
    private String mView;
    private String mScene;

    public DumpMemInfo(){
        deleteFileIfExist();
    }

    public void setCurrentApp(String scene,String view){
        this.mView=view;
        this.mScene=scene;
    }

    public  void writeMemInfo(String pname) {
        try {
            CommandResult result = ShellUtils.execCommand("dumpsys meminfo " + pname, false);
            if (result != null && result.successMsg != null) {
//                Pattern pattern = Pattern.compile("(?<=Java Heap:).*?(?=\n)");
//                Matcher matcher = pattern.matcher(result.successMsg);
//                Log.d(TAG, matcher.group(0));

                //writeDetails(result.successMsg, pid);

                String[] data = result.successMsg.split("\n");
                if (data == null) {
                    return;
                }

                String pid = "";
                String summary = "Time: " + format.format(new Date()) + "   ";
                for (String info : data) {

                    if (info.contains("pid")) {
                        info = info.replace("** MEMINFO in pid ", "");
                        pid = info.split(" ")[0];
                        continue;
                    }

                    if (info.contains("Java Heap:")
                            || info.contains("Native Heap:")
                            || info.contains("Graphics:")
                            || info.contains("Private Other:")) {
                        summary += SPLIT+info;
                        continue;
                    }

                    if (info.contains("TOTAL SWAP PSS")) {
                        summary +=  SPLIT+info.split("TOTAL SWAP PSS")[0];
                        if(!TextUtils.isEmpty(mScene)) {
                            summary += SPLIT+mScene;
                        }
                        if(!TextUtils.isEmpty(mView)) {
                            summary += "-"+mView;
                        }
                        break;
                    }
                }

                writeSummary(summary, pid);
            }
        } catch (Exception var18) {
            var18.printStackTrace();
        }
    }

    private  void writeDetails(String details, String pid) {
        try {
            File file = new File(getDir(), pid + ".log");
            FileWriter writer = new FileWriter(file, true);
            writer.write("Time: " + format.format(new Date()) + "\n");
            writer.write(details);
            writer.write("\n");
            writer.flush();
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private  void writeSummary(String details, String pid) {
        try {
            Log.d(TAG, details);
            File file = new File(getDir(), "mem_summary.log");
            FileWriter writer = new FileWriter(file, true);
            writer.write(details);
            writer.write("\n");
            writer.flush();
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private  File getDir() {
        File dir = new File(Environment.getExternalStorageDirectory(), "meminfo");
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir;
    }

    public void deleteFileIfExist(){
        File file = new File(getDir(), "mem_summary.log");
        if(file.exists())
            file.delete();
    }
}