package com.ainirobot.platform.react.reactnative.component.uicomponent.blur

import androidx.appcompat.widget.ContentFrameLayout
import android.util.Log
import android.view.View
import com.ainirobot.platform.react.reactnative.component.uicomponent.blur.BlurTask.createNewBlurTask
import com.facebook.react.ReactRootView
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.views.view.ReactViewGroup

class BlurViewModule(val reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String = "BlurViewModule"

    @ReactMethod
    fun blurViewWaitToUse(hasFaceParticle: Boolean) {
        Log.i("blur", "BlurViewModule blurViewWaitToUse")
        try {
            val activity = reactContext.currentActivity ?: return

            val root = activity.window.decorView.findViewById(android.R.id.content) as ContentFrameLayout
            val rnRoot = root.getChildAt(0) as ReactRootView
            val views = arrayOfNulls<View>(2)
            views[0] = rnRoot.getChildAt(0)
            for (i in 1..rnRoot.childCount) {
                if (rnRoot.getChildAt(i) is ReactViewGroup) {
                    views[1] = rnRoot.getChildAt(i) as ReactViewGroup
                    break
                }
            }
            createNewBlurTask(null, views, reactContext, activity, hasFaceParticle, 20)?.execute()

//            val uiManager = reactApplicationContext.getNativeModule(UIManagerModule::class.java)
//            uiManager.addUIBlock { nativeViewHierarchyManager ->
//                try {
////                    val view = nativeViewHierarchyManager.resolveView(viewTag)
//                    val root = activity.window.decorView.findViewById(android.R.id.content) as ContentFrameLayout
//                    val rnRoot = root.getChildAt(0) as ReactRootView
//                    val views = arrayOfNulls<View>(2)
//                    views[0] = rnRoot.getChildAt(0)
//                    views[1] = rnRoot.getChildAt(1)
////                    var view: ReactViewGroup? = null
////                    for (i in 0..rnRoot.childCount) {
////                        if (rnRoot.getChildAt(i) is ReactViewGroup) {
////                            view = rnRoot.getChildAt(i) as ReactViewGroup
////                            break
////                        }
////                    }
//                    createNewBlurTask(null, views, reactContext, activity, hasFaceParticle, 20)?.execute()
//                } catch (e: Throwable) {
//                    e.printStackTrace()
//                    Log.e("blur", "" + e)
//                }
//            }
        } catch (e: Throwable) {
            e.printStackTrace()
            Log.e("blur", "blurModule " + e)
        }
    }

}