package com.ainirobot.platform.react.reactnative.component.common;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Callback;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import java.util.HashMap;

public class TimeoutModule extends ReactContextBaseJavaModule {

    private final String TAG = "TimeoutModule";
    private Handler handler = new Handler(Looper.getMainLooper());
    private HashMap<Integer, Runnable> timeoutMap = new HashMap<>();

    public TimeoutModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return TAG;
    }

    @ReactMethod
    public void setTimeout(final int timeoutId, int duration, final Callback callback) {
        Log.d(TAG, "setTimeout: timeoutId=" + timeoutId + ", duration=" + duration);
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                callback.invoke(timeoutId);
                timeoutMap.remove(timeoutId);
            }
        };
        timeoutMap.put(timeoutId, runnable);
        handler.postDelayed(runnable, duration);
    }

    @ReactMethod
    public void clearTimeout(int timeoutId) {
        Log.d(TAG, "clearTimeout: timeoutId=" + timeoutId);
        Runnable runnable = timeoutMap.get(timeoutId);
        if (runnable != null) {
            handler.removeCallbacks(runnable);
            timeoutMap.remove(timeoutId);
        }
    }
}
