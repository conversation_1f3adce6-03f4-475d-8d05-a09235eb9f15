package com.ainirobot.platform.react.reactnative.component.listener

import android.text.TextUtils
import android.util.Log
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNApiListener
import com.facebook.react.bridge.WritableNativeMap
import org.json.JSONObject

class BridgeCommandListener : IRNApiListener.Stub {

    companion object{
        fun obtain(callbackId:Int): BridgeCommandListener? {
            return if(callbackId < 0){
                null
            } else {
                BridgeCommandListener(callbackId)
            }
        }
    }

    var id = 0
    private constructor(id:Int) {
        this.id = id
    }

    override fun onResult(result: Int, message: String?, extraData: String?) {
        Log.e("BridgeCommandListener", "onResult message:$message")
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putInt("result", result)
        readableMap.putString("event", "onResult")
        readableMap.putString("message", message)
        readableMap.putString("extraData", extraData)
        if (!TextUtils.isEmpty(message)) {
            try {
                val json = JSONObject(message)
                if (json.has("is_zip") && json.getBoolean("is_zip") && json.has("zip_data")) {
                    readableMap.putBoolean("isZip", true)
                    readableMap.putString("message", json.getString("zip_data"))
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        ReactNativeEventEmitter.triggerEvent("onCommand", readableMap)
    }

    override fun onStatusUpdate(status: Int, data: String?, extraData: String?) {
        Log.e("BridgeCommandListener", "onStatusUpdate data:$data")
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putInt("status", status)
        readableMap.putString("event", "onStatusUpdate")
        readableMap.putString("message", data)
        readableMap.putString("extraData", extraData)
        ReactNativeEventEmitter.triggerEvent("onCommand", readableMap)
    }

    override fun onError(errorCode: Int, errorString: String?, extraData: String?) {
        Log.e("BridgeCommandListener", "onError errorString:$errorString")
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putInt("code", errorCode)
        readableMap.putString("event", "onError")
        readableMap.putString("message", errorString)
        readableMap.putString("extraData", extraData)
        ReactNativeEventEmitter.triggerEvent("onCommand", readableMap)
    }
}
