package com.ainirobot.platform.react.reactnative.component.uicomponent.camerafilter

import android.util.Log
import com.facebook.react.bridge.ReadableArray
import com.facebook.react.common.MapBuilder
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.annotations.ReactProp

@ReactModule(name = CameraFilterViewManager.REACT_CLASS)
class CameraFilterViewManager : SimpleViewManager<CameraFilterView>() {

    private var cameraFilterView: CameraFilterView? = null

    companion object {
        const val REACT_CLASS = "RNCameraFilterView"
        const val TAG = REACT_CLASS
        const val COMMAND_SAVE_SNAP_SHOT = 1
        const val COMMAND_DESTROY = 2
        const val COMMAND_PAUSE_PREVIEW = 3
        const val COMMAND_RESUME_PREVIEW = 4

        enum class Events constructor(private val mName: String) {
            EVENT_ONINITSUCCESS("_onCameraFilterInitSuccess"),
            EVENT_ONFAILURE("_onCameraFilterFailure"),
            EVENT_ONSNAPSHOT("_onCameraFilterSnapShot");

            override fun toString(): String {
                return mName
            }
        }
    }

    override fun getName(): String {
        return REACT_CLASS
    }

    override fun createViewInstance(reactContext: ThemedReactContext): CameraFilterView {
        Log.i(REACT_CLASS, "createViewInstance")
        if (cameraFilterView != null) {
            cameraFilterView?.destroy()
            Log.i(REACT_CLASS, "createViewInstance removeCameraShareView")
            cameraFilterView = null
        }
        val view: CameraFilterView = CameraFilterView(reactContext)
        cameraFilterView = view
        return view
    }

    @ReactProp(name = "type")
    fun setType(view: CameraFilterView, type: Int) {
        Log.i(REACT_CLASS, "setType type:$type")
        cameraFilterView?.setType(type)
    }

    override fun onDropViewInstance(view: CameraFilterView) {
        super.onDropViewInstance(view)
        Log.i(REACT_CLASS, "onDropViewInstance")
        try {
            cameraFilterView?.destroy()
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    override fun getExportedCustomDirectEventTypeConstants(): Map<String, Any>? {
        val builder = MapBuilder.builder<String, Any>()
        for (event in Events.values()) {
            builder.put(event.toString(), MapBuilder.of("registrationName", event.toString()))
        }
        return builder.build()
    }

    override fun getCommandsMap(): MutableMap<String, Int> {
        val builder = MapBuilder.builder<String, Int>()
        builder.put("saveSnapShot", COMMAND_SAVE_SNAP_SHOT)
        builder.put("destroy", COMMAND_DESTROY)
        builder.put("pausePreview", COMMAND_PAUSE_PREVIEW)
        builder.put("resumePreview", COMMAND_RESUME_PREVIEW)
        return builder.build()
    }

    override fun receiveCommand(root: CameraFilterView, commandId: Int, args: ReadableArray?) {
        Log.i(REACT_CLASS, "receiveCommand: " + commandId)
        when (commandId) {
            COMMAND_SAVE_SNAP_SHOT -> args?.apply { root.saveSnapShot(args.getBoolean(0)) }
            COMMAND_DESTROY -> root.destroy()
            COMMAND_PAUSE_PREVIEW -> root.pausePreview()
            COMMAND_RESUME_PREVIEW -> root.resumePreview()
            else -> Log.e("CameraFilterView", "unsupported command $commandId")
        }
    }

}