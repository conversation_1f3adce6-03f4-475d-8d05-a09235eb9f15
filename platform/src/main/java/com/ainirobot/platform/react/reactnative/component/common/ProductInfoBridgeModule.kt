package com.ainirobot.platform.react.reactnative.component.common

import com.ainirobot.platform.react.client.RNClientManager
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod

class ProductInfoBridgeModule(reactContext: ReactApplicationContext) :
        ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String {
        return "ProductInfo"
    }

    @ReactMethod
    fun isMiniProduct(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isMiniProduct)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isDeliveryProduct(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isDeliveryProduct)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isOverSea(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isOverSea)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isDeliveryOverSea(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isDeliveryOverSea)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isBaseDeliveryOverSea(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isBaseDeliveryOverSea)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isLaraForSale(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isLaraForSale)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isMeissa(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isMeissa)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isMeissa1P5(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isMeissa1P5)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isMeissaPlus(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isMeissaPlus)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isMeissa2(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isMeissa2)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isSaiphXD(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isSaiphXD)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isSaiphBigScreen(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isSaiphBigScreen)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isSaiphXdOrBigScreen(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isSaiphXdOrBigScreen)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isMiniOverSea(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isMiniOverSea)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isSaiphChargeIr(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isSaiphChargeIr)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isSaiphRgbdFm1(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isSaiphRgbdFm1)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isSaiph(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isSaiph)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isSaiphMall(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isSaiphMall)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isSaiphPro(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isSaiphPro)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isAlnilamPro(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isAlnilamPro)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isChargeIrProduct(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isChargeIrProduct)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isElevatorCtrlProduct(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isElevatorCtrlProduct)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isMiniProductSupportMultiRobot(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isMiniProductSupportMultiRobot)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun isCarryProduct(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.isCarryProduct)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun hasElectricDoor(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.productInfoManager?.hasElectricDoor())
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

}
