package com.ainirobot.platform.react.reactnative.component.skillcomponent

import com.ainirobot.platform.react.server.utils.ComponentUtils

import com.ainirobot.platform.component.BodyFollowComponent
import com.facebook.react.bridge.*

class BasicMotionComponentBridge(reactContext: ReactApplicationContext) : BaseComponentBridge<BodyFollowComponent>(reactContext) {

    override fun isHDMonopoly(): Boolean {
        return true
    }

    override fun getResouceType(): Array<ResouceType> {
        return arrayOf(ResouceType.Navigation,ResouceType.HeadTurn)
    }

    override fun getName(): String {
        return ComponentUtils.COMPONENT_BASIC_MOTION
    }

    @ReactMethod
    override fun start(uid: String, param: String?) {
        super._start(uid, param)
    }

    @ReactMethod
    override fun stop(uid: String, timeout: Int?) {
        super._stop(uid, timeout)
    }

    @ReactMethod
    override fun updateParams(uid: String, intent: String, param: String) {
        super._updateParams(uid, intent, param)
    }

    @ReactMethod
    override fun setStatusListener(uid: String, statusListenerId: Int) {
        super._setStatusListener(uid, statusListenerId)
    }

    @ReactMethod
    override fun setFinishListener(uid: String, finishListenerId: Int) {
        super._setFinishListener(uid, finishListenerId)
    }

}
