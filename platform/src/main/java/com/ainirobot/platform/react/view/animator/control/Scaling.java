/*
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.ainirobot.platform.react.view.animator.control;

import android.graphics.Canvas;

/**
 * 根据资源文件中指定的画布尺寸和实际的画布尺寸计算 横向/纵向 缩放比例
 *
 * @params: horizontal 横向缩放比例 (float)
 * @params: vertical 纵向缩放比例 (float)
 * @FileName: com.ainirobot.platform.ui.animator.Scaling.java
 * @author: Orion
 * @date: 2019-01-11 11:25
 */
public class Scaling {

    private float horizontal;
    private float vertical;

    public Scaling toScaling(int width, int height, Canvas canvas) {
        if (null == canvas) {
            horizontal = 1.0f;
            vertical = 1.0f;
            return this;
        }

        if (0 >= height) {
            vertical = 1.0f;
        } else {
            vertical = (float) canvas.getHeight() / height;
        }

        if (0 >= width) {
            horizontal = 1.0f;
        } else {
            horizontal = (float) canvas.getWidth() / width;
        }

        return this;
    }

    public float getHorizontal() {
        return horizontal;
    }

    public float getVertical() {
        return vertical;
    }
}
