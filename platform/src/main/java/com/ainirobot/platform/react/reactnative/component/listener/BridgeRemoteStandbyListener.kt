package com.ainirobot.platform.react.reactnative.component.listener

import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNRemoteStandByListener
import com.facebook.react.bridge.WritableNativeMap

class BridgeRemoteStandbyListener : IRNRemoteStandByListener.Stub {

    companion object {
        fun obtain(callbackId:Int): BridgeRemoteStandbyListener? {
            return BridgeRemoteStandbyListener(callbackId)
        }
    }

    var id = -1

    private constructor(id: Int) {
        this.id = id
    }

    override fun onRemoteStandByChanged(data: String?) {
        triggerEvent("onRemoteStandbyChanged", data)
    }

    fun triggerEvent(event: String, data: String?) {
        if (id < 0) {
            return
        }
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putString("event", event)
        data?.apply { readableMap.putString("data", data) }
        ReactNativeEventEmitter.triggerEvent("onRemoteStandbyChangedListener", readableMap)
    }
}