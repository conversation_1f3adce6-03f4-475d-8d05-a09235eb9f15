package com.ainirobot.platform.react.reactnative.component.listener

import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNVolumeListener
import com.facebook.react.bridge.WritableNativeMap

class BridgeVolumeListener : IRNVolumeListener.Stub {

    companion object {
        fun obtain(callbackId:Int): BridgeVolumeListener? {
            return BridgeVolumeListener(callbackId)
        }
    }

    var id = -1

    private constructor(id: Int) {
        this.id = id
    }

    override fun onVolumeChanged(streamType: Int, newVolume: Int, oldVolume: Int) {
        triggerEvent("onVolumeChanged", streamType, newVolume, oldVolume)
    }

    fun triggerEvent(event: String, streamType: Int, newVolume: Int, oldVolume: Int) {
        if (id < 0) {
            return
        }
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putString("event", event)
        streamType?.apply { readableMap.putInt("streamType", streamType) }
        newVolume?.apply { readableMap.putInt("newVolume", newVolume) }
        oldVolume?.apply { readableMap.putInt("oldVolume", oldVolume) }
        ReactNativeEventEmitter.triggerEvent("onVolumeChangedListener", readableMap)
    }
}
