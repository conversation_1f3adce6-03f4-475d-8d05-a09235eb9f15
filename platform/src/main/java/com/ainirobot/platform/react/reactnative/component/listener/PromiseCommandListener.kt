package com.ainirobot.platform.react.reactnative.component.listener

import com.ainirobot.platform.rn.listener.IRNApiListener
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.WritableNativeMap

class PromiseCommandListener : IRNApiListener.Stub {

    var promise:Promise
    constructor(promise: Promise) {
        this.promise = promise
    }

    override fun onResult(result: Int, message: String?, extraData: String?) {
        var readableMap = WritableNativeMap()
        readableMap.putInt("result", result)
        message?.apply {
            readableMap.putString("message", message)
        }
        extraData?.apply {
            readableMap.putString("extraData", extraData)
        }
        this.promise.resolve(readableMap)
    }

    override fun onStatusUpdate(status: Int, data: String?, extraData: String?) {

    }

    override fun onError(errorCode: Int, message: String?, extraData: String?) {
        this.promise.reject(errorCode.toString(), message)
    }
}