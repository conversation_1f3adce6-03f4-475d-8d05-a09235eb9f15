package com.ainirobot.platform.react.reactnative.component.skillcomponent

import android.util.Log
import com.ainirobot.coreservice.client.ProductInfo
import com.ainirobot.platform.react.client.RNClientManager
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.google.gson.Gson

class LightApiBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    companion object {
        val TAG = "LightApiBridgeModule"
        val mGson = Gson()
    }

    override fun getName(): String {
        return "LightApi"
    }

    @ReactMethod
    fun playEffect(effect: String) {
        try {
            if (ProductInfo.isSaiph()) {
                Log.i(TAG, "playEffect:$effect")
                RNClientManager.instance?.lightManager?.playEffect(effect)
            } else {
                Log.i(TAG, "setSceneLedEffect:$effect")
                RNClientManager.instance?.lightManager?.setSceneLedEffect(effect)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun playColor(color: String) {
        try {
            Log.i(TAG, "playColor:$color")
            RNClientManager.instance?.lightManager?.playColor(color)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun playMultipleColor(color: String) {
        try {
            Log.i(TAG, "playMultipleColor:$color")
            RNClientManager.instance?.lightManager?.playMultipleColor(color)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun playMultipleColorGroup(color: String) {
        try {
            Log.i(TAG, "playMultipleColorGroup:$color")
            RNClientManager.instance?.lightManager?.playMultipleColor(color)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun playAnimation(animation: String) {
        try {
            Log.i(TAG, "playAnimation:$animation")
            RNClientManager.instance?.lightManager?.playAnimation(animation)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun playNavigationAnimation(animation: String) {
        try {
            Log.i(TAG, "playNavigationAnimation:$animation")
            RNClientManager.instance?.lightManager?.playNavigationAnimation(animation)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun playDoorAnimation(animation: String) {
        try {
            Log.i(TAG, "playDoorAnimation:$animation")
            RNClientManager.instance?.lightManager?.playDoorAnimation(animation)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setIobLedEffect(effect: String) {
        try {
            Log.i(TAG, "setIobLedEffect:$effect")
            RNClientManager.instance?.lightManager?.setSceneLedEffect(effect)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}
