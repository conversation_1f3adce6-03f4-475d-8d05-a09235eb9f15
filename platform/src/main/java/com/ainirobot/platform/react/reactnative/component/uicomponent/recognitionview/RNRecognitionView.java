/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.platform.react.reactnative.component.uicomponent.recognitionview;

import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.widget.FrameLayout;

import com.ainirobot.platform.R;
import com.ainirobot.platform.bean.RecognitionAnimationData;
import com.ainirobot.platform.react.view.RecognitionView;
import com.facebook.react.bridge.ReactContext;

import java.util.List;

/**
 * Created by Orion on 2018/8/7.
 */

public class RNRecognitionView extends FrameLayout {

    private ReactContext mContext;
    private RecognitionView mRecognizeView;
    private RNRecognitionViewEventListener mEventListener;


    public RNRecognitionView(ReactContext context) {
        super(context);
        init(context);
    }

    public RNRecognitionView(ReactContext context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public RNRecognitionView(ReactContext context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public RNRecognitionView(ReactContext context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context);
    }

    public void setRNRecognitionViewListener(RNRecognitionViewEventListener EventListener){
        this.mEventListener = EventListener;
    }

    private void init(ReactContext context) {
        this.mContext = context;
        LayoutInflater.from(getContext()).inflate(R.layout.platform_fragment_rn_videoplayer, null);
        mRecognizeView = new RecognitionView(context);
        mRecognizeView.setEventListener(new RecognitionView.RecognitionViewEventListener(){
            @Override
            public void onClick(){
                Log.i("RNRecognitionView", "onClick");
                if(null != mEventListener){
                    mEventListener.onClick();
                }
            }
        });
        addView(mRecognizeView);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        hideBottomWaveAnimation();
    }

    public void showBottomWaveAnimation() {
        mRecognizeView.showBottomWaveAnimation();
    }

    public void hideBottomWaveAnimation() {
        mRecognizeView.hideBottomWaveAnimation();
    }

    public void setQueryData(List<String> dataList, String headerText) {
        mRecognizeView.setQueryData(dataList, headerText);
    }

    public void setQueryDataForServer(RecognitionAnimationData data) {
        mRecognizeView.setQueryDataForServer(data);
    }

    public void showGuideAnimation(boolean isShow) {
        if (mRecognizeView != null) {
            mRecognizeView.showGuideAnimation(isShow);
        }
    }

    public void setGuideAnimationConfig(boolean enable) {
        mRecognizeView.setGuideAnimationConfig(enable);
    }

    public interface RNRecognitionViewEventListener{
        void onClick();
    }

    public void clearRecognitionText(){
        mRecognizeView.setRecognizeText("");
    }

}
