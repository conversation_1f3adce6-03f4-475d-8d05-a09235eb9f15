package com.ainirobot.platform.react.reactnative.component.common

import com.ainirobot.platform.react.reactnative.DumpMemManager
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod

class DumpMemInfoBridge (reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    override fun getName(): String {
        return "DumpMemInfo"
    }
    @ReactMethod
    fun sendDumpMemCommand(scene: String,view: String){
        DumpMemManager.getInstance().dumpMem(scene,view)
    }

    @ReactMethod
    fun init(){
        DumpMemManager.getInstance().init()
    }

    @ReactMethod
    fun startDumpMem(interval: Int,scene: String, view: String){
        DumpMemManager.getInstance().startDumpMem(interval,scene,view)
    }

    @ReactMethod
    fun cancelDumpTimer(){
        DumpMemManager.getInstance().cancelDumpTimer()
    }
}