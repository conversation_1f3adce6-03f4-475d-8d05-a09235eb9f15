package com.ainirobot.platform.react.reactnative.component.listener

import android.util.Log
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNApkControlListener
import com.facebook.react.bridge.WritableNativeMap
import org.json.JSONObject

class BridgeRNControlListener : IRNApkControlListener.Stub {
    companion object{
        fun obtain(callbackId:Int): BridgeRNControlListener? {
            return if(callbackId < 0){
                null
            } else {
                BridgeRNControlListener(callbackId)
            }
        }
    }

    var id = -1
    private constructor(id:Int) {
        this.id = id
    }

    fun triggerEvent(event: String, apkPackage: String,data: String?) {
        var readableMap = WritableNativeMap()
        Log.v("caixj","---brige-----"+event)
        readableMap.putInt("id", id)
        readableMap.putString("event", event)
        readableMap.putString("apkPackage", apkPackage)
        data?.apply {
            readableMap.putString("eventdata", data)
        }
        ReactNativeEventEmitter.triggerEvent("onNLPApkControl", readableMap)
    }

    override fun getCallbackId(): Int {
        return id
    }

    override fun onTriggerCommand(apkPackage: String, command: String?) {
        triggerEvent("onTriggerCommand", apkPackage, command)
    }

    override fun onProcessDied(apkPackage: String) {
        triggerEvent("onProcessDied",apkPackage,null)
    }

    override fun onPageStateChanged(apkPackage: String, state: String?) {
        triggerEvent("onPageStateChanged",apkPackage,state)
    }

    override fun onAppNotResponding(apkPackage: String) {
        triggerEvent("onAppNotResponding", apkPackage, null)
    }

    override fun onTopActivityChanged(apkPackage: String, activityName: String) {
        val json = JSONObject()
        json.put("apkPackage",apkPackage)
        json.put("activityName",activityName)
        triggerEvent("onTopActivityChanged",apkPackage,json.toString())
    }

    override fun onProcessVisible(apkPackage: String) {
        triggerEvent("onProcessVisible", apkPackage, null)
    }

    override fun onProcessInVisible(apkPackage: String) {
        triggerEvent("onProcessInVisible", apkPackage, null)
    }

    override fun onServiceConnected(apkPackage: String) {
        triggerEvent("onServiceConnected", apkPackage, null)
    }

    override fun onServiceDisconnected(apkPackage: String) {
        triggerEvent("onServiceDisconnected", apkPackage, null)
    }

    override fun onRobotMessengerReady(apkPackage: String) {
        triggerEvent("onRobotMessengerReady", apkPackage, null)
    }

    override fun onApkInstallResult(apkPackage: String, success: Boolean, errorMsg: String, taskId: String) {
        val json = JSONObject()
        json.put("apkPackage", apkPackage)
        json.put("success", success)
        json.put("errorMsg", errorMsg)
        json.put("taskId", taskId)
        triggerEvent("onApkInstallResult", apkPackage, json.toString())
    }
}