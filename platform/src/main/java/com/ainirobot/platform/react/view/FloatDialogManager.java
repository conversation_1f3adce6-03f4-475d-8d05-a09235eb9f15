package com.ainirobot.platform.react.view;

import android.graphics.PixelFormat;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.R;

import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static android.content.Context.WINDOW_SERVICE;
import static android.view.ViewGroup.LayoutParams.WRAP_CONTENT;

import org.json.JSONObject;

public class FloatDialogManager {

    private static final String TAG = "FloatDialogManager";

    private View mFloatView;
    private ScheduledThreadPoolExecutor mScheduled;
    private ScheduledFuture mFuture;
    private static final long SHOW_TIME_LENGTH = 3000l;

    private ScheduledThreadPoolExecutor mRefreshNeedShowSchedule;
    private ScheduledFuture mRefreshNeedShowFuture;
    private boolean needShow = true;
    private volatile boolean isShowing = false;
    private static final long REFRESH_PERIOD = 4000l;
    public static final int DIALOG_TYPE_TOP = 1;
    public static final int DIALOG_TYPE_BOTTOM = 2;
    public static final int DIALOG_TYPE_CENTER = 3;
    //显示的toast类型
    public FloatDialogType mDialogType = FloatDialogType.TYPE_RESPONSE_LITE;

    public enum FloatDialogType {
        TYPE_RESPONSE_LITE,
        TYPE_RESPONSE_SENSOR_ERROR,   //移除所有正在显示dialog后，显示自定义时长传感器异常dialog
    }

    private static final int MSG_SHOW_DIALOG = 0;
    private static final int MSG_REMOVE_ALL_DIALOG = 1;

    private Handler mHandler = new Handler(Looper.getMainLooper()){
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what){
                case MSG_SHOW_DIALOG:
                    String content = (String) msg.obj;
                    doShowLightResponseDialog(content);
                    break;
                case MSG_REMOVE_ALL_DIALOG:
                    doRemoveAll();
                    break;
            }
        }
    };

    private static FloatDialogManager mInstance = new FloatDialogManager();
    public static FloatDialogManager getInstance(){
        if(mInstance == null){
            mInstance = new FloatDialogManager();
        }
        return mInstance;
    }

    public boolean showFloatDialog(FloatDialogType type, String content) {
        return showFloatDialog(type, content, SHOW_TIME_LENGTH, DIALOG_TYPE_CENTER);
    }

    public boolean showFloatDialog(FloatDialogType type, String content, long showTime, int dialogType) {
        try {
            JSONObject object = new JSONObject();
            object.put("showTime", showTime);
            object.put("content", content);
            object.put("dialogType", dialogType);
            return initFloatDialog(type, object.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private boolean initFloatDialog(FloatDialogType type, String content) {

        Log.i(TAG, "show float dialog, float dialog type: " + type);

        if (TextUtils.isEmpty(content)) {
            return false;
        }

        if (floatDialogIsShow()) {
            return false;
        }
        mDialogType = type;

        switch (type) {

            case TYPE_RESPONSE_SENSOR_ERROR:
                removeAll();
                shutDownRefresh();
            case TYPE_RESPONSE_LITE:
                Log.d(TAG, "needShow = " + needShow);
                if (needShow) {
                    Message msg = Message.obtain();
                    msg.what = MSG_SHOW_DIALOG;
                    msg.obj = content;
                    mHandler.sendMessage(msg);
                    needShow = false;
                    startRefreshNeedShowTimer(REFRESH_PERIOD);
                    return true;
                }
                return false;

            default:
                return false;
        }
    }

    private void doShowLightResponseDialog(String content){
        Log.i(TAG, "do Show float dialog, float dialog, content: " + content);
        String info = "";
        long showTime = REFRESH_PERIOD;
        int dialogType = DIALOG_TYPE_CENTER;
        try {
            JSONObject object = new JSONObject(content);
            info = object.optString("content");
            showTime = object.optLong("showTime", SHOW_TIME_LENGTH);
            dialogType = object.optInt("dialogType", DIALOG_TYPE_CENTER);
        } catch (Exception e) {
            e.printStackTrace();
        }
        doRemoveAll();
        initLightResponseView(info);
        WindowManager.LayoutParams params = getLightResponseParams(dialogType);
        WindowManager manager = (WindowManager) BaseApplication.getContext().getSystemService(WINDOW_SERVICE);
        if (manager == null) {
            Log.i(TAG, "window manager get error, return!!!");
            return;
        }
        manager.addView(mFloatView, params);
        isShowing = true;
        startScheduledExec(showTime);
    }

    public boolean isShowingSensorErrorDialog() {
        return isShowing && mDialogType == FloatDialogType.TYPE_RESPONSE_SENSOR_ERROR;
    }

    public void removeAll(){
        mHandler.sendEmptyMessage(MSG_REMOVE_ALL_DIALOG);
    }

    private void doRemoveAll(){
        Log.i(TAG, "remove all float dialog, current view" + mFloatView);
        if(mFloatView == null){
            isShowing = false;
            return;
        }
        WindowManager manager = (WindowManager) BaseApplication.getContext().getSystemService(WINDOW_SERVICE);
        if (manager == null) {
            isShowing = false;
            Log.i(TAG, "window manager get error, return!!!");
            return;
        }
        try {
            manager.removeView(mFloatView);
            isShowing = false;
            shutDownFuture();
        } catch (Exception e) {
            e.printStackTrace();
        }
        mFloatView = null;
    }

    private void initLightResponseView(String content){
        mFloatView = LayoutInflater.from(BaseApplication.getContext()).inflate(R.layout.platform_float_dialog_window, null);
        TextView tv_text = (TextView) mFloatView.findViewById(R.id.tv_content);
        tv_text.setText(TextUtils.isEmpty(content) ? "" : content);
    }

    private WindowManager.LayoutParams getLightResponseParams(int type){
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        layoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        layoutParams.format = PixelFormat.RGBA_8888;
        layoutParams.width = WRAP_CONTENT;
        layoutParams.height = WRAP_CONTENT;
        //layoutParams.windowAnimations = android.R.style.Animation_Translucent;
        layoutParams.gravity = getDialogType(type);
//        layoutParams.y = 200;
        return layoutParams;

    }

    private int getDialogType(int type) {
        int dialogType = Gravity.CENTER_HORIZONTAL;
        switch (type) {
            case DIALOG_TYPE_TOP:
                dialogType = dialogType | Gravity.TOP;
                break;
            case DIALOG_TYPE_BOTTOM:
                dialogType = dialogType | Gravity.BOTTOM;
                break;
            default:
                dialogType = dialogType | Gravity.CENTER_VERTICAL;
                break;
        }
        return dialogType;
    }

    private void startScheduledExec(Long timeDelay){
        Log.i(TAG, "start scheduled executor, time delay: " + timeDelay);
        shutDownFuture();
        if(mScheduled == null){
            mScheduled = new ScheduledThreadPoolExecutor(1);
        }
        mFuture = mScheduled.schedule(new Runnable() {
            @Override
            public void run() {
                Log.i(TAG, "future is comming");
                removeAll();
            }
        }, timeDelay, TimeUnit.MILLISECONDS);
    }

    private void shutDownFuture(){
        Log.i(TAG, "shut down future");
        if (!(mFuture == null || mFuture.isCancelled() || mFuture.isDone())){
            mFuture.cancel(true);
        }
        mFuture = null;
    }

    private void startRefreshNeedShowTimer(long period) {
        Log.d(TAG, "start needShow refresh timer, period = " + period);
        shutDownRefresh();
        if (mRefreshNeedShowSchedule == null) {
            mRefreshNeedShowSchedule = new ScheduledThreadPoolExecutor(1);
        }
        mRefreshNeedShowFuture = mRefreshNeedShowSchedule.schedule(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "refresh needShow in");
                needShow = true;
            }
        }, period, TimeUnit.MILLISECONDS);
    }

    private void shutDownRefresh() {
        Log.i(TAG, "shut down refresh needShow future");
        if (!(mRefreshNeedShowFuture == null || mRefreshNeedShowFuture.isCancelled() || mRefreshNeedShowFuture.isDone())) {
            mRefreshNeedShowFuture.cancel(true);
        }
        mRefreshNeedShowFuture = null;
    }

    public boolean floatDialogIsShow() {
        return mFloatView != null;
    }

}
