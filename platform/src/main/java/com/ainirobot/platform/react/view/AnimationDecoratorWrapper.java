/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.react.view;

import android.view.View;

import java.util.List;


public class AnimationDecoratorWrapper implements IAnimationDecorator {

    private IAnimationDecorator mDecorator;
    private AnimationDecoratorImpl mAnimation;
    private boolean isStop = false;

    public AnimationDecoratorWrapper(IAnimationDecorator decorator) {
        this.mDecorator = decorator;
        mAnimation = new AnimationDecoratorImpl();
    }

    public void setQueryList(List<String> dataList, String headerText) {
        mAnimation.setQueryList(dataList, headerText);
    }

    @Override
    public View startAnimation() {
        View animationView = mDecorator.startAnimation();
        if (!mAnimation.isRunning()) {
            mAnimation.startAnimation(animationView);
        }
        isStop = false;
        return animationView;
    }

    @Override
    public View stopAnimation() {
        View animationView = mDecorator.stopAnimation();
        if (!isStop) {
            isStop = true;
            mAnimation.stopAnimation();
        }
        return animationView;
    }
}
