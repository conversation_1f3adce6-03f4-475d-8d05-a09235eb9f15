package com.ainirobot.platform.react

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter

class CountdownAlarmReceiver : BroadcastReceiver() {

    val TAG = "CountdownAlarmReceiver"

    val ACTION_COUNT_DOWN_TIMER = "trigger_count_down_timer"

    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent != null) {
            Log.d(TAG, "intent.action : " + intent.action)
            when (intent.action) {
                ACTION_COUNT_DOWN_TIMER -> {
                    ReactNativeEventEmitter.triggerEvent("trigger_count_down_timer", intent.action)
                }
            }
        }
    }

}
