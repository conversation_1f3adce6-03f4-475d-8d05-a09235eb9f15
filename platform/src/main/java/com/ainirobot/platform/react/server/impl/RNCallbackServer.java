package com.ainirobot.platform.react.server.impl;

import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.platform.react.server.utils.RNServerUtils;
import com.ainirobot.platform.rn.IRNCommandCallBack;

public class RNCallbackServer {

    public static final String TAG = RNCallbackServer.class.getSimpleName();

    private IRNCommandCallBack mCommandCallback;

    public void onNewRequest(int reqId, String intent, String text, String params) {
        try {
            Log.d(TAG, "onNewRequest reqId: " + reqId + ", intent: " + intent
                    + ", text: " + text + ", params: " + params);
            if (mCommandCallback == null) {
                Log.e(TAG, "onNewRequest callback null");
                return;
            }
            mCommandCallback.onNewRequest(reqId, intent, text, params);
        } catch (RemoteException e) {
            RNServerUtils.handleRemoteException("onNewRequest", e);
        }
    }

    public void handleHWException(int function, String type, String data) {
        try {
            Log.d(TAG, "handleHWException function: " + function
                    + ", type: " + type + ", data: " + data);
            if (mCommandCallback == null) {
                Log.e(TAG, "handleHWException callback null");
                return;
            }
            mCommandCallback.handleHWException(function, type, data);
        } catch (RemoteException e) {
            RNServerUtils.handleRemoteException("handleHWException", e);
        }
    }

    public void suspend() {
        try {
            Log.d(TAG, "Handle suspend function");
            if (mCommandCallback == null) {
                Log.e(TAG, "Handle suspend callback null");
                return;
            }
            mCommandCallback.suspend();
        } catch (RemoteException e) {
            RNServerUtils.handleRemoteException("handleHWException", e);
        }
    }

    public void recovery() {
        try {
            Log.d(TAG, "Handle suspend function");
            if (mCommandCallback == null) {
                Log.e(TAG, "Handle suspend callback null");
                return;
            }
            mCommandCallback.recovery();
        } catch (RemoteException e) {
            RNServerUtils.handleRemoteException("handleHWException", e);
        }
    }

    public void updateCallback(IRNCommandCallBack callBack) {
        Log.d(TAG, "updateCallback callBack: " + callBack);
        mCommandCallback = callBack;
    }
}
