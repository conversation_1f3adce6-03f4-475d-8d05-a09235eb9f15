package com.ainirobot.platform.react

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.util.Log
import com.ainirobot.coreservice.client.RobotApi
import com.ainirobot.platform.PlatformDef
import com.ainirobot.platform.control.ControlManager
import com.ainirobot.platform.character.CharacterManager
import com.ainirobot.platform.react.reactnative.character.ReactCharacter
import com.ainirobot.platform.utils.FileHelper
import com.ainirobot.platform.utils.LocalUtils
import org.json.JSONException
import org.json.JSONObject
import java.io.File

class InstallPluginReceiver : BroadcastReceiver() {

    val TAG = "InstallPluginReceiver"

    val ACTION_UNINSTALL_PLUGIN = "com.ainirobot.uninstall.plugin"
    val ACTION_PORTAL = "com.ainirobot.set.portal"

    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent != null) {
            when (intent.action) {
                ACTION_UNINSTALL_PLUGIN -> {
                    val appid = intent.getStringExtra("appid")
                    if (appid == null) {
                        Log.d(TAG, "Uninstall plugin failed : appid must not be null")
                        return
                    }
                    uninstallPlugin(appid)
                }

                ACTION_PORTAL -> {
                    val appId = intent.getStringExtra("appid")
                    if (appId == null) {
                        Log.d(TAG, "Set portal opk failed: appid must not be null")
                        return
                    }

                    val info = AppManger.getRPKByCharacter(appId)
                    if (info == null || info.type != "plugin") {
                        Log.d(TAG, "Set portal opk failed : app not exits or not plugin")
                        return
                    }
                    LocalUtils.set(context, "portal", appId)
                }

                else -> {
                    val opkPath = intent.getStringExtra("path")

                    //获取宿主的appId
                    var host = intent.getStringExtra("host")
                    if (opkPath != null) {
                        Log.d(TAG, opkPath)
                        val appBean: AppBeanV2? = OPKHelper.getAppBeanForOPKFile(opkPath, "")
                                ?: return
                        Log.d(TAG, "Opk info, biz path : ${appBean?.bundleBizFilePath} , platform path : ${appBean?.bundlePlatformFilePath} ")
                        if (!(appBean?.bundleIndexFilePath != null
                                        || (appBean?.bundleBizFilePath != null
                                        && appBean?.bundlePlatformFilePath != null))) {
                            return
                        }

                        val opkInfo = appBean.rpkBean
                        //处理Plugin
                        if ("plugin" == opkInfo.type) {
                            installPlugin(appBean, host)
                        } else {
                            installOpk(appBean)
                        }
                    } else {
                        Log.d(TAG, "not have path")
                    }
                }
            }
        }
    }

    private fun uninstallPlugin(appid: String) {
        Log.d(TAG, "Start uninstall plugin : $appid")
        AppManger.removeAppId(appid)
    }

    private fun installPlugin(appBean: AppBeanV2, hostId: String?) {
        var host = hostId
        if (TextUtils.isEmpty(host)) {
//                        Log.d(TAG, "Install plugin failed : not host id")
            //不指定宿主的话，默认豹小秘
            host = "baoxiaomi_91d5a88c4eafda508216fb516dad4a80"
        }

        val hostInfo = AppManger.getRPKByCharacter(host)
        if (hostInfo == null) {
            Log.d(TAG, "Install plugin failed : host not found")
            return
        }

        val oldPluginInfo = AppManger.getRPKByCharacter(appBean.rpkBean.appid)
        val iterator = hostInfo.plugin?.iterator()
        //删除已卸载的无效插件
        while (iterator != null && iterator.hasNext()) {
            val it = iterator.next()
            if (it.appid == appBean.rpkBean.appid) {
                iterator.remove()
            }
        }

        if (hostInfo.plugin != null) {
            hostInfo.plugin!!.add(appBean.rpkBean)
        } else {
            hostInfo.plugin = mutableListOf(appBean.rpkBean)
        }

//        if (oldPluginInfo?.path != null
//                && oldPluginInfo.path != appBean.rpkBean.path
//                && File(oldPluginInfo.path).exists()) {
//            Log.d(TAG, "Delete old opk ${oldPluginInfo.path}")
//            FileHelper.deleteDirectory(File(oldPluginInfo.path))
//        }

//        val pluginDir = File(hostInfo.path, "plugin")
//        if (!pluginDir.exists()) {
//            pluginDir.mkdirs()
//        }
//
//        val pluginBundlePath = appBean.bundleBizFilePath;
//        val pluginPath = pluginBundlePath?.substring(0, pluginBundlePath.lastIndexOf("/"))
//
//        Log.d(TAG, "Install plugin, hostPath : $hostPath , pluginPath : $pluginPath")
//
//        //将Plugin复制到宿主的plugin目录下
//        Log.d(TAG, "Copy plugin  : $pluginBundlePath , to : ${pluginDir.absolutePath}")
//        FileHelper.copyDirectory(File(pluginPath), File(pluginDir, appBean.rpkBean.appid))
//
//        FileHelper.deleteDirectory(File(pluginPath))

        if (ReactCharacter.isReactRunning && ReactCharacter.currentCharacter == host) {
            switchCharacter(host)
        }

        AppManger.addApp(appBean.bundleIndexFilePath, appBean.bundleBizFilePath, appBean.bundlePlatformFilePath, appBean.rpkBean)
    }

    private fun installOpk(appBean: AppBeanV2) {
        val hostPath = appBean.rpkBean.path

        //宿主OPK资源存储方式不同
        if ("host" == appBean.rpkBean.type) {
            Log.d(TAG, "Start copy host resource, path : $hostPath")

            val appInfo = AppManger.getRPKByCharacter(appBean.rpkBean.appid)
            //拷贝插件信息
            appBean.rpkBean.plugin = appInfo?.plugin ?: mutableListOf()

            //拷贝资源
//            appInfo?.plugin?.forEach {
//                FileHelper.copyDirectory(File("${it.path}/extra"), File("${appBean.rpkBean.path}/extra"))
//            }
        }

        val oldVersion = AppManger.getRPKByCharacter(appBean.rpkBean.appid)
//        if (oldVersion?.path != null
//                && oldVersion.path != appBean.rpkBean.path
//                && File(oldVersion.path).exists()) {
//            Log.d(TAG, "Delete old opk ${oldVersion.path}")
//            FileHelper.deleteDirectory(File(oldVersion.path))
//        }

        Log.d(TAG, ReactCharacter.isReactRunning.toString())
        Log.d(TAG, appBean.rpkBean.appid + " " + appBean.bundleBizFilePath + " current: " + ReactCharacter.currentCharacter)
        AppManger.addApp(appBean.bundleIndexFilePath, appBean.bundleBizFilePath, appBean.bundlePlatformFilePath, appBean.rpkBean)
        if (ReactCharacter.isReactRunning && ReactCharacter.currentCharacter == appBean.rpkBean.appid) {
            if (!RobotApi.getInstance().isActive) {
                Log.e(TAG, "ModuleApp not active")
                return
            }
            switchCharacter(appBean.rpkBean.appid)
        } else {
            Log.d(TAG, "currentCharacter " + ReactCharacter.currentCharacter)
            registerCharacter(appBean.rpkBean.appid)
        }
    }

    private fun registerCharacter(appid: String): Boolean {
        if (appid.isNullOrEmpty()) {
            Log.d(TAG, "registerCharacter appid is empty")
            return false

        }
        Log.i(TAG, "registerCharacter : $appid")
        val characterManager = CharacterManager.getInstance()
        val character = ReactCharacter(appid)
        return characterManager.registerCharacterFromRN(character.getName(), character)
    }

    fun switchCharacter(characterName: String?) {
        try {
            val json = JSONObject()
            json.put("name", characterName)

            ControlManager.handleRequest(0, PlatformDef.SWITCH_CHARACTER, "", json.toString());
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

}
