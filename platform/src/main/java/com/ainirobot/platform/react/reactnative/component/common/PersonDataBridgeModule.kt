package com.ainirobot.platform.react.reactnative.component.common

import android.util.Log
import com.ainirobot.platform.react.client.RNClientManager
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod

class PersonDataBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    companion object {
        const val TAG = "PersonDataBridgeModule"
    }
    override fun getName(): String {
            return "PersonManager"
    }

    @ReactMethod
    fun getAllPersonNum(promise: Promise) {
        try {
            val num = RNClientManager.instance?.personManager?.allPersonNum
            Log.d(TAG, "num is $num")
            promise.resolve(num)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getLastPersonId(promise: Promise) {
        try {
            val id = RNClientManager.instance?.personManager?.lastPersonId
            Log.d(TAG, "id is $id")
            promise.resolve(id)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getLastPersonName(promise: Promise) {
        try {
            val name = RNClientManager.instance?.personManager?.lastPersonName
            promise.resolve(name)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getAllPerson(isOnlyFace: Boolean, promise: Promise) {
        try {
            val name = RNClientManager.instance?.personManager?.getAllPerson(isOnlyFace)
            promise.resolve(name)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }
}
