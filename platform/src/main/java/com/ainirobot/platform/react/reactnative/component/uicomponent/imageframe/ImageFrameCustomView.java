package com.ainirobot.platform.react.reactnative.component.uicomponent.imageframe;

import android.annotation.TargetApi;
import android.content.Context;
import android.os.Build;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import android.util.AttributeSet;
import android.view.View;
import android.util.Log;

/**
 * 序列帧解析分离,需要的时候只需要传入一个解析器即可
 */
public class ImageFrameCustomView extends View {
  private ImageFrameHandler imageFrameHandler;

  public ImageFrameCustomView(Context context) {
    super(context);
  }

  public ImageFrameCustomView(Context context, @Nullable AttributeSet attrs) {
    super(context, attrs);
  }

  public ImageFrameCustomView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
    super(context, attrs, defStyleAttr);
  }

  @TargetApi(Build.VERSION_CODES.LOLLIPOP)
  public ImageFrameCustomView(Context context, @Nullable AttributeSet attrs, int defStyleAttr,
                              int defStyleRes) {
    super(context, attrs, defStyleAttr, defStyleRes);
  }

  @Override
  protected void onDetachedFromWindow() {
    if (imageFrameHandler != null) {
      imageFrameHandler.stop();
    }
    Log.e("ImageFrame", "onDetachedFromWindow: stop");

    super.onDetachedFromWindow();
  }

  public void startImageFrame(final ImageFrameHandler imageFrameHandler) {
    if (this.imageFrameHandler == null) {
      this.imageFrameHandler = imageFrameHandler;
    }else{
      this.imageFrameHandler.stop();
      this.imageFrameHandler = imageFrameHandler;
    }
    post(new Runnable() {
      @Override
      public void run() {
        imageFrameHandler.start();
      }
    });

  }

  public void stop() {
      if (imageFrameHandler != null) {
          imageFrameHandler.stop();
      }
  }

  @Nullable
  public ImageFrameHandler getImageFrameHandler() {
    return imageFrameHandler;
  }
}
