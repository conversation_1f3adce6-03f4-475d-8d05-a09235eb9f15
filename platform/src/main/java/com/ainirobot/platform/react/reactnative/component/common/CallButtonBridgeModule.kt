package com.ainirobot.platform.react.reactnative.component.common

import android.util.Log
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNCallButtonListener
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod

class CallButtonBridgeModule(reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String {
        return "CallButton"
    }

    @ReactMethod
    fun connect(serverIp: String) {
        RNClientManager.instance?.callButtonManager?.connect(serverIp)
    }

    @ReactMethod
    fun disconnect() {
        RNClientManager.instance?.callButtonManager?.disconnect()
    }

    @ReactMethod
    fun syncButtonMapping(buttonMapJson: String) {
        RNClientManager.instance?.callButtonManager?.syncButtonMapping(buttonMapJson)
    }

    @ReactMethod
    fun setEventListener() {
        Log.d(
            "CallButtonBridgeModule",
            "setEventListener : " + RNClientManager.instance?.callButtonManager
        )
        RNClientManager.instance?.callButtonManager?.setEventListener(object :
            IRNCallButtonListener.Stub() {
            override fun callPrepare(message: String) {
                Log.d("CallButtonBridgeModule", "callPrepare")
                ReactNativeEventEmitter.triggerEvent("callPrepare", message)
            }

            override fun call(message: String) {
                Log.d("CallButtonBridgeModule", "call")
                ReactNativeEventEmitter.triggerEvent("call", message)
            }

            override fun cancel(message: String) {
                Log.d("CallButtonBridgeModule", "cancel")
                ReactNativeEventEmitter.triggerEvent("cancel", message)
            }

            override fun buttonMappingChange(message: String) {
                Log.d("CallButtonBridgeModule", "buttonMappingChange")
                ReactNativeEventEmitter.triggerEvent("buttonMappingChange", message)
            }

            override fun buttonPress(message: String) {
                Log.d("buttonPress", message)
                ReactNativeEventEmitter.triggerEvent("buttonPress", message)
            }


        })
    }

    @ReactMethod
    fun replyCallPrepare(result: String, msg: String, data: String) {
        RNClientManager.instance?.callButtonManager?.replyCallPrepare(result, msg, data)
    }

    @ReactMethod
    fun replyCall(result: String, msg: String) {
        RNClientManager.instance?.callButtonManager?.replyCall(result, msg)
    }

    @ReactMethod
    fun replyCancel(result: String, msg: String) {
        RNClientManager.instance?.callButtonManager?.replyCancel(result, msg)
    }

    @ReactMethod
    fun replyButtonMappingChange(result: String, msg: String) {
        RNClientManager.instance?.callButtonManager?.replyButtonMappingChange(result, msg)
    }

    @ReactMethod
    fun setRobotState(state: String) {
        RNClientManager.instance?.callButtonManager?.setRobotState(state)
    }

    @ReactMethod
    fun getCurrentDataMapping(result: Promise) {
        try {
            result.resolve(RNClientManager.instance?.callButtonManager?.currentDataMapping)
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun getMapName(result: Promise) {
        try {
            result.resolve(RNClientManager.instance?.callButtonManager?.mapName)
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun unbindButton(buttonId: String, locationName: String) {
        RNClientManager.instance?.callButtonManager?.unbindButton(buttonId, locationName)
    }
}
