package com.ainirobot.platform.react.reactnative.component.listener

import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNRobotStatusListener
import com.facebook.react.bridge.WritableNativeMap

class BridgeElectricDoorStateListener : IRNRobotStatusListener.Stub {

    companion object {
        fun obtain(callbackId: Int): BridgeElectricDoorStateListener? {
            return if (callbackId < 0) {
                null
            } else {
                BridgeElectricDoorStateListener(callbackId)
            }
        }
    }

    var id = -1

    private constructor(id: Int) {
        this.id = id
    }

    override fun onStatusUpdate(type: String?, data: String?) {
        triggerEvent(type, data)
    }

    fun triggerEvent(type: String?, data: String?) {
        if (id < 0) {
            return
        }
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        type?.apply { readableMap.putString("type", type) }
        data?.apply { readableMap.putString("data", data) }
        readableMap.putString("event", "onElectricDoorStateUpdate")
        ReactNativeEventEmitter.triggerEvent("onElectricDoorStateUpdateListener", readableMap)
    }

}