package com.ainirobot.platform.react.server.impl;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioManager;
import android.os.RemoteException;
import android.text.TextUtils;
import android.view.Surface;

import com.ainirobot.base.cpumemory.utils.CpuUsage;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.surfaceshare.SurfaceShareApi;
import com.ainirobot.coreservice.client.surfaceshare.SurfaceShareBean;
import com.ainirobot.coreservice.client.surfaceshare.SurfaceShareListener;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.bi.WakeUpIdWrapper;
import com.ainirobot.platform.data.DeviceManager;
import com.ainirobot.platform.data.listener.ConnectivityListener;
import com.ainirobot.platform.react.server.utils.RNServerUtils;
import com.ainirobot.platform.rn.IDeviceRegistry;
import com.ainirobot.platform.rn.listener.IRNConnectivityListener;
import com.ainirobot.platform.rn.listener.IRNSurfaceShareListener;
import com.google.gson.Gson;
import com.ainirobot.platform.rn.listener.IRNVolumeListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RNDeviceServer extends IDeviceRegistry.Stub {

    private static final String ACTION_VOLUME_CHANGED_ACTION = "android.media.VOLUME_CHANGED_ACTION";
    public static final String EXTRA_VOLUME_STREAM_TYPE = "android.media.EXTRA_VOLUME_STREAM_TYPE";
    public static final String EXTRA_VOLUME_STREAM_VALUE =
            "android.media.EXTRA_VOLUME_STREAM_VALUE";
    public static final String EXTRA_PREV_VOLUME_STREAM_VALUE =
            "android.media.EXTRA_PREV_VOLUME_STREAM_VALUE";
    private final Object CALLBACK_LOCK = new Object();
    private final List<ConnectivityCallback> mCallbacks;
    private final Object SURFACE_CALLBACK_LOCK = new Object();
    private final List<SurfaceCallback> mSurfaceCallbacks;
    private final Object VOLUME_CALLBACK_LOCK = new Object();
    private final Map<Integer, IRNVolumeListener> mVolumeCallbacks;
    AudioManager mAudioManager;
    private Gson mGson;

    public RNDeviceServer() {
        mCallbacks = new ArrayList<>();
        mSurfaceCallbacks = new ArrayList<>();
        mGson = new Gson();
        mVolumeCallbacks = new HashMap<>();
        mAudioManager = (AudioManager) BaseApplication.getContext()
                .getSystemService(Context.AUDIO_SERVICE);
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_VOLUME_CHANGED_ACTION);
        BroadcastReceiver mVolumeReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (ACTION_VOLUME_CHANGED_ACTION.equals(intent.getAction())) {
                    int streamType = intent.getIntExtra(EXTRA_VOLUME_STREAM_TYPE, -1);
                    int newVolume = intent.getIntExtra(EXTRA_VOLUME_STREAM_VALUE, -1);
                    int oldVolume = intent.getIntExtra(EXTRA_PREV_VOLUME_STREAM_VALUE, -1);
                    synchronized (VOLUME_CALLBACK_LOCK) {
                        for (int callbackId : mVolumeCallbacks.keySet()) {
                            IRNVolumeListener listener = mVolumeCallbacks.get(callbackId);
                            if (listener != null) {
                                try {
                                    listener.onVolumeChanged(streamType, newVolume, oldVolume);
                                } catch (RemoteException e) {
                                    mVolumeCallbacks.remove(callbackId);
                                }
                            }
                        }
                    }
                }
            }
        };
        BaseApplication.getContext().registerReceiver(mVolumeReceiver, filter);
    }

    @Override
    public void startBluetoothDetect(String type) {
        DeviceManager.getInstance().startBluetoothDetect(type);
    }

    @Override
    public String getWakeupId() {
        return WakeUpIdWrapper.getInstance().getWakeUpId();
    }

    @Override
    public void generateWakeupId() {
        WakeUpIdWrapper.getInstance().generateWakeUpId();
    }

    @Override
    public String getCorpUuid() {
        return RobotSettings.getCorpUUID(BaseApplication.getContext());
    }

    @Override
    public String getVoiceCorpId() {
        return RobotSettings.getVoiceCorpId(BaseApplication.getContext());
    }

    @Override
    public String getRobotName() {
        return RobotSettings.getRobotName(BaseApplication.getContext());
    }

    @Override
    public void registerConnectivityChanged(IRNConnectivityListener listener) {
        DeviceManager.getInstance().registerConnectivityChanged(createConnectivityCallback(listener));
    }

    @Override
    public void unregisterConnectivityChanged() {
        DeviceManager.getInstance().unregisterConnectivityChanged();
        synchronized (CALLBACK_LOCK) {
            for (ConnectivityCallback callback : mCallbacks) {
                callback.stop();
            }
            mCallbacks.clear();
        }
    }

    @Override
    public void toggleAirplaneMode() {
        DeviceManager.getInstance().toggleAirplaneMode();
    }

    @Override
    public String getCpuUsage() {
        return CpuUsage.getCpuUsage();
    }

    @Override
    public int requestImageFrame(Surface surface, String param, IRNSurfaceShareListener listener) {
        if (TextUtils.isEmpty(param)) {
            return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
        }
        SurfaceShareBean bean = mGson.fromJson(param, SurfaceShareBean.class);
        if (bean == null) {
            return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
        }
        return SurfaceShareApi.getInstance().requestImageFrame(surface, bean,
                createSurfaceCallback(bean, listener));
    }

    @Override
    public int abandonImageFrame(String param) {
        if (TextUtils.isEmpty(param)) {
            return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
        }
        SurfaceShareBean bean = mGson.fromJson(param, SurfaceShareBean.class);
        if (bean == null) {
            return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
        }
        return SurfaceShareApi.getInstance().abandonImageFrame(bean);
    }

    public int getStreamVolume(int streamType) {
        return mAudioManager.getStreamVolume(streamType);
    }

    @Override
    public void setStreamVolume(int streamType, int index, int flags) {
        mAudioManager.setStreamVolume(streamType, index, flags);
    }

    @Override
    public void registerVolumeChanged(int callbackId, IRNVolumeListener listener) {
        createVolumeCallback(callbackId, listener);
    }

    @Override
    public void unregisterVolumeChanged(int callbackId) {
        synchronized (VOLUME_CALLBACK_LOCK) {
            IRNVolumeListener callback = mVolumeCallbacks.get(callbackId);
            if (callback != null) {
                mVolumeCallbacks.remove(callbackId);
            }
        }
    }

    @Override
    public boolean isSurfaceShareUsed() {
        return SurfaceShareApi.getInstance().isUsed();
    }

    private ConnectivityCallback createConnectivityCallback(IRNConnectivityListener listener) {
        if (listener == null) {
            return null;
        }
        ConnectivityCallback callback = new ConnectivityCallback(listener);
        synchronized (CALLBACK_LOCK) {
            mCallbacks.add(callback);
        }
        return callback;
    }

    private static class ConnectivityCallback extends ConnectivityListener {
        private IRNConnectivityListener listener;

        ConnectivityCallback(IRNConnectivityListener listener) {
            this.listener = listener;
        }

        public void stop() {
            listener = null;
        }

        @Override
        public void onConnectStateChanged(String networkType, String simState,
                                          String mobileType, boolean mobileConnect) {
            if (listener != null) {
                try {
                    listener.onConnectStateChanged(networkType, simState,
                            mobileType, mobileConnect);
                } catch (RemoteException e) {
                    RNServerUtils
                            .handleRemoteException("DeviceCallback onConnectStateChanged", e);
                }
            }
        }
    }

    private SurfaceCallback createSurfaceCallback(SurfaceShareBean bean,
                                                  IRNSurfaceShareListener listener) {
        if (listener == null) {
            return null;
        }
        SurfaceCallback callback = new SurfaceCallback(bean, listener);
        synchronized (SURFACE_CALLBACK_LOCK) {
            mSurfaceCallbacks.add(callback);
        }
        return callback;
    }

    private void onCommandFinish(SurfaceCallback callback) {
        synchronized (SURFACE_CALLBACK_LOCK) {
            callback.stop();
            mSurfaceCallbacks.remove(callback);
        }
    }

    private class SurfaceCallback extends SurfaceShareListener {
        private IRNSurfaceShareListener listener;
        private SurfaceShareBean bean;

        SurfaceCallback(SurfaceShareBean bean, IRNSurfaceShareListener listener) {
            this.bean = bean;
            this.listener = listener;
        }

        public void stop() {
            abandonImageFrame(mGson.toJson(bean));
            listener = null;
        }

        @Override
        public void onError(int error, String message) {
            if (listener != null) {
                try {
                    listener.onError(error, message);
                    onCommandFinish(this);
                } catch (RemoteException e) {
                    RNServerUtils
                            .handleRemoteException("SurfaceCallback onError", e);
                }
            }
        }

        @Override
        public void onStatusUpdate(int status, String message) {
            if (listener != null) {
                try {
                    listener.onStatusUpdate(status, message);
                } catch (RemoteException e) {
                    RNServerUtils
                            .handleRemoteException("SurfaceCallback onStatusUpdate", e);
                }
            }
        }
    }

    private void createVolumeCallback(int callbackId,
                                      IRNVolumeListener listener) {
        if (listener == null) {
            return;
        }
        synchronized (VOLUME_CALLBACK_LOCK) {
            mVolumeCallbacks.put(callbackId, listener);
        }
    }

    public void clearCallbacks() {
        synchronized (CALLBACK_LOCK) {
            for (ConnectivityCallback callback : mCallbacks) {
                callback.stop();
            }
            mCallbacks.clear();
        }

        synchronized (SURFACE_CALLBACK_LOCK) {
            for (SurfaceCallback callback : mSurfaceCallbacks) {
                callback.stop();
            }
            mSurfaceCallbacks.clear();
        }

        synchronized (VOLUME_CALLBACK_LOCK) {
            mVolumeCallbacks.clear();
        }
    }
}
