package com.ainirobot.platform.react.reactnative.component.common

import android.content.Intent
import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import com.ainirobot.coreservice.client.Definition
import com.ainirobot.coreservice.client.SettingsUtil
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.facebook.react.bridge.*

/**
 * @date: 2019-09-16
 * @author: zyc
 * @desc: 获取当前系统设置信息　
 */
class AndroidSettingsBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    private val TAG = AndroidSettingsBridgeModule::class.java.simpleName
    private val ACTION_SETTING = "com.ainirobot.moduleapp.global.settings"
    private val mContext = BaseApplication.getContext()
    private val mContentObservers = HashMap<String, ContentObserver>()
    private val mHandler = Handler(Looper.getMainLooper())

    override fun getName(): String {
        return "AndroidSettings"
    }

    @ReactMethod
    fun getInt(key: String, result: Promise) {
        try {
            result.resolve(Settings.Global.getInt(mContext.contentResolver, key, 0))
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun getIntByDefault(key: String, def: Int, result: Promise) {
        try {
            result.resolve(Settings.Global.getInt(mContext.contentResolver, key, def))
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun putInt(key: String, value: Int, result: Promise) {
        try {
            result.resolve(Settings.Global.putInt(mContext.contentResolver, key, value))
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun getString(key: String, result: Promise) {
        try {
            result.resolve(Settings.Global.getString(mContext.contentResolver, key))
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun putString(key: String, value: String?, result: Promise) {
        try {
            var finalValue = ""
            if (value != null) {
                finalValue = value
            }
            //TODO:次数代码不是很合理，后续可以使用其它策略删除
            if (!TextUtils.equals(key, Definition.ROBOT_SETTING_SPEAKER_ROLE)) {
                sendSettingBroadcast(key, finalValue)
            } else {
                RNClientManager.instance?.robotSettingManager?.setRobotString(key, value)
            }
            result.resolve(Settings.Global.putString(mContext.contentResolver, key, finalValue))
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun getSystemInt(key: String, result: Promise) {
        try {
            result.resolve(Settings.System.getInt(mContext.contentResolver, key, 0))
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun getSystemIntByDefault(key: String, def: Int, result: Promise) {
        try {
            result.resolve(Settings.System.getInt(mContext.contentResolver, key, def))
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun putSystemInt(key: String, value: Int, result: Promise) {
        try {
            result.resolve(Settings.System.putInt(mContext.contentResolver, key, value))
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun getSystemString(key: String, result: Promise) {
        try {
            result.resolve(Settings.System.getString(mContext.contentResolver, key))
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun putSystemString(key: String, value: String, result: Promise) {
        try {
            result.resolve(Settings.System.putString(mContext.contentResolver, key, value))
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    fun sendSettingBroadcast(key: String, value: String) {
        var intent = Intent(ACTION_SETTING)
        intent.putExtra("key", key)
        intent.putExtra("value", value)
        mContext.sendBroadcast(intent)
    }

    @ReactMethod
    fun addSettingsListener(keys: ReadableArray) {
        try {
            val values = keys.toArrayList()
            for (key in values) {
                addSettingsListener(key.toString())
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun addSettingsListener(key: String) {
        if (mContentObservers.contains(key)) {
            return
        }

        val contentObserver = object : ContentObserver(mHandler) {
            override fun onChange(selfChange: Boolean, uri: Uri?) {
                Log.i(TAG, "On settings change, key : $key")
                ReactNativeEventEmitter.triggerEvent("_android_settings_", key)
            }
        }
        mContext.contentResolver.registerContentObserver(SettingsUtil.getUriFor(key),
                true, contentObserver)
        mContentObservers[key] = contentObserver
    }

    @ReactMethod
    fun removeSettingsListener(keys: ReadableArray) {
        try {
            for (key in keys.toArrayList()) {
                removeSettingsListener(key.toString())
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun removeSettingsListener(key: String) {
        if (!mContentObservers.contains(key)) {
            return
        }

        val contentObserver = mContentObservers[key]
        mContext.contentResolver.unregisterContentObserver(contentObserver)
    }

    @ReactMethod
    fun clearSettingsListener() {
        try {
            for ((key, value) in mContentObservers) {
                Log.d(TAG, "Clear settings listener : $key")
                mContext.contentResolver.unregisterContentObserver(value)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onCatalystInstanceDestroy() {
        super.onCatalystInstanceDestroy()
        clearSettingsListener()
    }
}
