package com.ainirobot.platform.react.reactnative.component.common

import com.ainirobot.platform.utils.CommonUtils
import com.ainirobot.platform.utils.FileUtils
import com.ainirobot.platform.utils.RNAudioRecorderUtils
import com.facebook.react.bridge.*

/**
 * @date: 2019-02-01
 * @author: lumeng
 * @desc: 通用工具的 BridgeModule
 */
class CommonUtilBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    private val offlineFeedbackDir = "/sdcard/offline/"

    override fun getName(): String {
        return "CommonUtil"
    }

    @ReactMethod
    fun getSkills(promise: Promise) {
        promise.reject("getSkill", "not handle")
    }

    @ReactMethod
    fun getSkillsJson(promise: Promise) {
        promise.reject("getSkillsJson", "not handle")
    }


    @ReactMethod
    fun getStandardQueries(promise: Promise) {
        promise.reject("getStandardQueries", "not handle")
    }

    @ReactMethod
    fun deleteFile(filePath: String) {
        FileUtils.deleteFile(filePath)
    }

    @ReactMethod
    fun startRecord(sampleRate:Int,bufferSize:Int){
        RNAudioRecorderUtils.getInstance().startRecord(sampleRate,bufferSize)
    }

    @ReactMethod
    fun stopRecord(promise: Promise){
        try {
            RNAudioRecorderUtils.getInstance().stopRecord {
                promise.resolve(it)
            }
        }catch (e: Exception) {
            promise.reject("stopRecord", e.message)
        }

    }

    fun filename(path: String): String {
        val i = path.lastIndexOf("/")
        return if (i >= 0) path.substring(i + 1) else path
    }

    @ReactMethod
    fun backupFile(sourcePath: String){
        try {
            val destFilePath = offlineFeedbackDir + filename(sourcePath)
            RNAudioRecorderUtils.getInstance().copyFile(sourcePath, destFilePath)
        } catch (e: Exception) {
            e.printStackTrace();
        }
    }

    @ReactMethod
    fun getBackupFiles(promise: Promise) {
        try {
            val files = RNAudioRecorderUtils.getInstance().getRecordFiles(offlineFeedbackDir)
            if(files == null){
                promise.resolve(null)
            }else{
                val fileArray = WritableNativeArray()
                files.forEach {
                    fileArray.pushString(it)
                }
                promise.resolve(fileArray)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getAudioDuration(path: String, promise: Promise){
        val duration = CommonUtils.getDuration(path)
        promise.resolve(duration.toInt())
    }
}
