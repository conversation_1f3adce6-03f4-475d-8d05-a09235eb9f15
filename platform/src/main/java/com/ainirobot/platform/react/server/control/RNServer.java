package com.ainirobot.platform.react.server.control;/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

import android.os.IBinder;
import android.util.Log;

import com.ainirobot.platform.react.RNDef;
import com.ainirobot.platform.rn.IRNCommandCallBack;
import com.ainirobot.platform.rn.IRNManagerService;
import com.ainirobot.platform.rn.IRNSpeechCallBack;

public class RNServer extends IRNManagerService.Stub {

    private static final String TAG = RNServer.class.getSimpleName();

    RNServer() {
        Log.d(TAG, "RNServer new");
    }

    @Override
    public void setSpeechCallBack(IRNSpeechCallBack scb) {
        RNServerManager.getInstance().setSpeechCallBack(scb);
    }

    @Override
    public void setCommandCallBack(IRNCommandCallBack scb) {
        RNServerManager.getInstance().setCommandCallBack(scb);
    }

    @Override
    public IBinder queryBinder(int binderCode) {
        switch (binderCode) {
            case RNDef.BIND_API:
                return RNServerManager.getInstance().getApiServer();
            case RNDef.BIND_COMPONENT:
                return RNServerManager.getInstance().getComponentServer();
            case RNDef.BIND_SPEECH_API:
                return RNServerManager.getInstance().getSpeechApiServer();
            case RNDef.BIND_DATA:
                return RNServerManager.getInstance().getDataServer();
            case RNDef.BIND_LIGHT:
                return RNServerManager.getInstance().getLightServer();
            case RNDef.BIND_PERSON:
                return RNServerManager.getInstance().getPersonServer();
            case RNDef.BIND_DEVICE:
                return RNServerManager.getInstance().getDeviceServer();
            case RNDef.BIND_INTENT:
                return RNServerManager.getInstance().getRNIntentManagerServer();
            case RNDef.BIND_SETTING:
                return RNServerManager.getInstance().getRobotSettingServer();
            case RNDef.BIND_PACKAGE:
                return RNServerManager.getInstance().getRNPackageManagerServer();
            case RNDef.BIND_NLP_APK_CONTROL:
                return RNServerManager.getInstance().getNLPApkControlServer();
            case RNDef.BIND_MSG:
                return RNServerManager.getInstance().getMsgServer();
            case RNDef.BIND_MAP:
                return RNServerManager.getInstance().getMapServer();
            case RNDef.BIND_CALL_BUTTON:
                return RNServerManager.getInstance().getRNCallButtonManagerServer();
            case RNDef.BIND_OKHTTP:
                return RNServerManager.getInstance().getRNOkHttpRequestServer();
            default:
                return null;
        }
    }

}
