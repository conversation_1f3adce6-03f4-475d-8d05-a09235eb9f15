package com.ainirobot.platform.react.reactnative.component.common

import android.util.Log
import androidx.fragment.app.FragmentActivity
import com.ainirobot.platform.R
import com.ainirobot.platform.common.fragment.WebViewFragment
import com.ainirobot.platform.speech.SpeechApi
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.modules.core.DeviceEventManagerModule

class WebViewBridgeModule(private val reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(reactContext) {
    private val TAG = WebViewBridgeModule::class.java.simpleName
    private var webViewFragment: WebViewFragment? = null

    override fun getName(): String {
        return "WebViewApi"
    }

    @ReactMethod
    fun showWebView(url: String,scale: Float, promise: Promise) {
        Log.d(TAG, "showWebView: $url")
        try {
            val activity = reactContext.currentActivity as? FragmentActivity
            activity?.runOnUiThread {
                // 如果已经存在Fragment，直接加载新URL
                if (webViewFragment != null && webViewFragment?.isAdded == true) {
                    Log.d(TAG, "WebView Fragment exists, loading new URL")
                    webViewFragment?.loadUrl(url,scale)
                    promise.resolve("URL loading started")
                    return@runOnUiThread
                }

                // 如果不存在Fragment，创建新的
                Log.d(TAG, "Creating new WebView Fragment")
                webViewFragment = WebViewFragment.newInstance(url,scale).apply {
                    setCallback(createCallback(promise))
                }

                activity.supportFragmentManager.beginTransaction()
                    .setCustomAnimations(
                        R.anim.slide_in,
                        R.anim.fade_out,
                        R.anim.fade_in,
                        R.anim.slide_out
                    )
                    .add(android.R.id.content, webViewFragment!!)
                    .addToBackStack(null)
                    .commitAllowingStateLoss() // 使用commitAllowingStateLoss替代commit

                // 确保事务被执行
                activity.supportFragmentManager.executePendingTransactions()
            }
        } catch (e: Exception) {
            Log.e(TAG, "showWebView error: ${e.message}", e)
            promise.reject("WEBVIEW_ERROR", e.message)
        }
    }

    @ReactMethod
    fun goBack(promise: Promise) {
        Log.d(TAG, "goBack")
        webViewFragment?.goBack()
        promise.resolve("Go back")
    }

    @ReactMethod
    fun goForward(promise: Promise) {
        Log.d(TAG, "goForward")
        webViewFragment?.goForward()
        promise.resolve("Go forward")
    }

    @ReactMethod
    fun reload(promise: Promise) {
        Log.d(TAG, "reload")
        webViewFragment?.reload()
        promise.resolve("Reload")
    }

    @ReactMethod
    fun hideWebView(updatePage: Boolean, promise: Promise) {
        Log.d(TAG, "hideWebView: updatePage=$updatePage")
        try {
            val activity = reactContext.currentActivity as? FragmentActivity
            activity?.runOnUiThread {
                webViewFragment?.let { fragment ->
                    Log.d(TAG, "Removing WebView Fragment")
                    // 检查Fragment是否已添加
                    if (fragment.isAdded) {
                        activity.supportFragmentManager.beginTransaction()
                            .setCustomAnimations(
                                R.anim.slide_in,
                                R.anim.fade_out,
                                R.anim.fade_in,
                                R.anim.slide_out
                            )
                            .remove(fragment)
                            .commitAllowingStateLoss() // 使用commitAllowingStateLoss替代commit

                        // 确保事务被执行
                        activity.supportFragmentManager.executePendingTransactions()
                        Log.d(TAG, "WebView Fragment has been removed")
                    } else {
                        Log.d(TAG, "WebView Fragment is not added, cannot remove")
                    }
                    webViewFragment = null
                    //dom清空上报y由opk层统一上报，避免重复上报不好排查
                    promise?.resolve("WebView closed")
                } ?: run {
                    Log.d(TAG, "WebView Fragment is null, nothing to hide")
                    promise?.resolve("No WebView to close")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "hideWebView error: ${e.message}", e)
            promise?.reject("WEBVIEW_ERROR", e.message)
        }
    }

    @ReactMethod
    fun loadUrl(url: String, scale: Float, promise: Promise) {
        Log.d(TAG, "loadUrl: $url")
        try {
            val activity = reactContext.currentActivity as? FragmentActivity
            activity?.runOnUiThread {
                if (webViewFragment?.isAdded == true) {
                    // 如果Fragment存在且已添加，直接加载新URL
                    Log.d(TAG, "Loading URL in existing WebView")
                    webViewFragment?.loadUrl(url,scale)
                    promise.resolve("URL loading started")
                } else {
                    // 如果Fragment不存在，创建新的
                    Log.d(TAG, "No WebView Fragment exists, creating new one")
                    showWebView(url, scale, promise)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "loadUrl error: ${e.message}", e)
            promise.reject("WEBVIEW_ERROR", e.message)
        }
    }

    @ReactMethod
    fun simulateClick(index: Int, promise: Promise) {
        Log.d(TAG, "simulateClick: index=$index")
        try {
            val activity = reactContext.currentActivity as? FragmentActivity
            activity?.runOnUiThread {
                if (webViewFragment?.isAdded == true) {
                    Log.d(TAG, "Simulating click in WebView")
                    webViewFragment?.simulateClick(index)
                    promise.resolve("Click simulated")
                } else {
                    Log.d(TAG, "WebView is not available for simulating click")
                    promise.reject("WEBVIEW_ERROR", "WebView is not available")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "simulateClick error: ${e.message}", e)
            promise.reject("WEBVIEW_ERROR", e.message)
        }
    }

    private fun createCallback(promise: Promise): WebViewFragment.WebViewCallback {
        return object : WebViewFragment.WebViewCallback {
            override fun onPageStarted(url: String) {
                sendEvent("page_started", url)
            }

            override fun onPageFinished(url: String) {
                sendEvent("page_finished", url)
            }

            override fun onClose(updatePage: Boolean) {
                Log.d(TAG, "onClose callback called with updatePage=$updatePage")
                hideWebView(updatePage, promise)
                sendEvent("close", true.toString())
            }

            override fun onMessage(tag: String, message: String) {
                Log.d(TAG, "tag=$tag=====message$message")
                SpeechApi.getInstance().sendAgentMessage(tag, 100, message)
            }

            override fun onError(error: String) {
                sendEvent("error", error)
            }

            override fun onExecuteFinish() {
                sendEvent("execute_finish", "")
            }
        }
    }

    private fun sendEvent(eventName: String, data: String) {
        try {
            val params = Arguments.createMap().apply {
                putString("data", data)
            }
            reactContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                .emit(eventName, params)
        } catch (e: Exception) {
            Log.e(TAG, "Error sending event: $eventName", e)
        }
    }

}