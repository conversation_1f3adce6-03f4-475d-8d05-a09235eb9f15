package com.ainirobot.platform.react.reactnative.component.uicomponent.external

import android.hardware.display.DisplayManager
import android.util.Log
import android.view.View
import android.view.Window
import android.view.WindowManager
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.component.uicomponent.external.view.ExternalDisplay
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReactContext
import com.facebook.react.common.MapBuilder
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.ViewGroupManager
import com.facebook.react.uimanager.annotations.ReactProp
import com.facebook.react.uimanager.events.RCTEventEmitter
import com.facebook.react.views.view.ReactViewGroup

class ExternalDisplayViewGroupManager : ViewGroupManager<ReactViewGroup>() {

    companion object {
        private const val TAG = "ExternalDisplayViewGroupManager"
    }

    private lateinit var view: ReactViewGroup
    private var content: View? = null

    init {
        Log.w(TAG, "ExternalDisplayViewGroupManager init()")
    }

    override fun getName(): String = "ExDisplay"

    override fun createViewInstance(reactContext: ThemedReactContext): ReactViewGroup {
        view = ExternalDisplayViewGroup(reactContext)
        initExternalDisplay(reactContext)
        return view
    }

    private fun initExternalDisplay(context: ThemedReactContext) {
        ExternalDisplay.init(context, WindowManager.LayoutParams.TYPE_SYSTEM_DIALOG)
        ExternalDisplay.listener = object : DisplayManager.DisplayListener {
            override fun onDisplayAdded(displayId: Int) {
                sendEvent(context, "onDisplayAdded")
            }

            override fun onDisplayRemoved(displayId: Int) {
                sendEvent(context, "onDisplayRemoved")
            }

            override fun onDisplayChanged(displayId: Int) {
                sendEvent(context, "onDisplayChanged")
            }
        }
    }

    private fun sendEvent(context: ThemedReactContext, eventName: String) {
        val event = Arguments.createMap()
        event.putString("type", eventName)
        context.getJSModule<RCTEventEmitter>(RCTEventEmitter::class.java)
            .receiveEvent(
                view.getId(),
                "topChange",
                event
            )
    }

    override fun getExportedCustomBubblingEventTypeConstants(): MutableMap<String, Any>? {
        return MapBuilder.builder<String, Any>()
            .put(
                "topChange",
                MapBuilder.of(
                    "phasedRegistrationNames",
                    MapBuilder.of("bubbled", "onChange")
                )
            )
            .build()
    }

    class ExternalDisplayViewGroup(context: ThemedReactContext) : ReactViewGroup(context) {

        fun onReceiveNativeEvent() {
            val event = Arguments.createMap()
            event.putString("message", "MyMessage")
            val reactContext = context as ReactContext
            reactContext.getJSModule(RCTEventEmitter::class.java).receiveEvent(
                id,
                "topChange",
                event
            )
        }
    }

    override fun addView(parent: ReactViewGroup, child: View, index: Int) {
        Log.e(TAG, "addView : $child")
        this.content = child
        ExternalDisplay.show(child)
    }

    override fun removeView(parent: ReactViewGroup, view: View?) {
        Log.e(TAG, "removeView")
        this.content = null
        ExternalDisplay.show()
    }

    override fun removeAllViews(parent: ReactViewGroup) {
        Log.e(TAG, "removeAllViews")
        this.content = null
        ExternalDisplay.show()
    }

    override fun onDropViewInstance(view: ReactViewGroup) {
        Log.e(TAG, "onDropViewInstance")
        super.onDropViewInstance(view)
        ExternalDisplay.hide()
    }

    @ReactProp(name = "mode")
    fun switchMode(view: ReactViewGroup, mode: String) {
        Log.d(TAG, "External display switch mode : $mode")
        RNClientManager.instance?.apiManager?.switchBigScreenMode(mode)
        ExternalDisplay.switchMode(mode, this.content)
    }
}