/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.react.view;


public class Voice {

    public enum VoiceType {
        START("voice_start", true, 40),
        LISTEN("voice_listen", false, 40),
        END("voice_end", true, 40),
        SPEAK("voice_speak", false, 40),
        LOADING("voice_loading", false, 40);

        private String name;

        private boolean oneShot;

        private int duration;

        VoiceType(String name, boolean oneShot, int duration) {
            this.name = name;
            this.oneShot = oneShot;
            this.duration = duration;
        }

        public String getName() {
            return name;
        }

        public boolean isOneShot() {
            return oneShot;
        }

        public int getDuration() {
            return duration;
        }


        @Override
        public String toString() {
            return "VoiceType name : " + name + " oneShot: " + oneShot + " duration: " + duration;
        }
    }
}
