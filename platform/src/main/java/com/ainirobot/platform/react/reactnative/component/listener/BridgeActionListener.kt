package com.ainirobot.platform.react.reactnative.component.listener

import android.os.RemoteException
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNApiListener
import com.facebook.react.bridge.WritableNativeMap

class BridgeActionListener : IRNApiListener.Stub {

    companion object{
        fun obtain(callbackId:Int): BridgeActionListener? {
            return if(callbackId < 0){
                null
            } else {
                BridgeActionListener(callbackId)
            }
        }
    }

    var id = -1
    private constructor(id:Int) {
        this.id = id
    }

    fun triggerEvent(event: String, param: Int, param1: String?, extraData: String?) {
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putString("event", event)
        readableMap.putInt("status", param)
        if (param1 != null) {
            readableMap.putString("message", param1)
        }
        if (extraData != null) {
            readableMap.putString("extraData", extraData)
        }
        ReactNativeEventEmitter.triggerEvent("onAction", readableMap)
    }

    @Throws(RemoteException::class)
    override fun onResult(status: Int, responseString: String?, extraData: String?) {
        android.util.Log.e("BridgeActionListener ", "onResult")
        triggerEvent("onResult", status, responseString, extraData)
    }

    @Throws(RemoteException::class)
    override fun onError(errorCode: Int, errorString: String?, extraData: String?) {
        if(errorString != null) {
            android.util.Log.e("BridgeActionListener ", "onError:"+errorString)
        } else {
            android.util.Log.e("BridgeActionListener ", "onError: null")
        }

        triggerEvent("onError", errorCode, errorString, extraData)
    }

    @Throws(RemoteException::class)
    override fun onStatusUpdate(status: Int, data: String?, extraData: String?) {
        android.util.Log.e("BridgeActionListener ", "onStatusUpdate")
        triggerEvent("onStatusUpdate", status, data, extraData)
    }
}