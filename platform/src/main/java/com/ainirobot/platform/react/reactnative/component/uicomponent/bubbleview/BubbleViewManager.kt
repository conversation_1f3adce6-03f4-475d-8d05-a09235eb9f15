package com.ainirobot.platform.react.reactnative.component.uicomponent.bubbleview

import android.util.Log
import com.facebook.react.bridge.ReadableMap
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.annotations.ReactProp

@ReactModule(name = BubbleViewManager.REACT_CLASS)
class BubbleViewManager: SimpleViewManager<BubbleView>() {

    companion object {
        const val REACT_CLASS = "RCTBubbleView"
        const val TAG = "BubbleViewManager"
    }

    override fun getName(): String {
        return REACT_CLASS
    }

    override fun createViewInstance(reactContext: ThemedReactContext): BubbleView {
        return BubbleView(reactContext)
    }

    @ReactProp(name = "params")
    fun setParams(view: BubbleView, map: ReadableMap) {
        Log.d(TAG, map.toString())
        if (map.hasKey("duration")) {
            val duration = map.getInt("duration")
            view.setDuration(duration)
        }
        if (map.hasKey("height")) {
            val height = map.getInt("height")
            view.setContentHeight(height)
        }
        if (map.hasKey("switchTime")) {
            val time = map.getInt("switchTime")
            view.setSwitchTime(time)
        }
        if (map.hasKey("bubbles")) {
            val array = map.getArray("bubbles")
            if (array == null) {
                Log.d(TAG, "array is null")
                return
            }
            val strList = ArrayList<String?>()
            var index = 0
            while(index < array.size()) {
                strList.add(array.getString(index))
                index ++
            }
            view.setBubbles(strList)
        }
    }
}