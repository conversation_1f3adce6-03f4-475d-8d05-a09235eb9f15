package com.ainirobot.platform.react.reactnative.component.uicomponent.external.view;

import android.app.Presentation;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Point;
import android.os.Bundle;
import android.util.Log;
import android.view.Display;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import androidx.annotation.NonNull;

class ExternalDisplayScreen extends Presentation {

    private static final String TAG = "ExternalDisplayScreen";

    private final BSRootView root;
    private View content;

    ExternalDisplayScreen(Context context, Display display) {
        super(context, display);

        this.root = initRootView(context);
        Point p = new Point();
        display.getSize(p);
        this.root.setMeasureParams(p.x, p.y);

        getWindow().addFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
    }

    private BSRootView initRootView(Context context) {
        BSRootView root = new BSRootView(context);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
        );
        root.setBackgroundColor(Color.GRAY);
        root.setLayoutParams(params);
        return root;
    }

    public void setType(int type) {
        getWindow().setType(type);
    }

    public void show(@NonNull View content) {
        Log.d(TAG, "Show display content : " + content);
        //清除旧View
        if (this.content != null) {
            root.removeView(this.content);
            root.refreshDrawableState();
        }
        this.content = content;
        root.addView(content);

        this.show();
    }

    @Override
    public void dismiss() {
        Log.d(TAG, "External display dismiss and remove view : " + this.content);
        if (this.content != null) {
            root.removeView(this.content);
            root.refreshDrawableState();
        }

        this.content = null;
        super.dismiss();
    }

    public View getContent() {
        return this.content;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(root);
    }
}