package com.ainirobot.platform.react.reactnative.component.uicomponent.imageframe

import com.facebook.react.bridge.Arguments
import com.facebook.react.uimanager.events.Event
import com.facebook.react.uimanager.events.RCTEventEmitter

class FrameEndEvent(viewId: Int, private val animType: Int) : Event<FrameEndEvent>(viewId) {

    override fun getEventName(): String {
        return  ImageFrameManager.EVENT_FRAME_END
    }

    override fun dispatch(rctEventEmitter: RCTEventEmitter) {
        val map = Arguments.createMap()
        animType?.apply {
            map.putInt("animType", animType)
        }
        rctEventEmitter.receiveEvent(viewTag, eventName, map)
    }
}