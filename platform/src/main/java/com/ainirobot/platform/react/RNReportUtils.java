package com.ainirobot.platform.react;

public class RNReportUtils {

    private static volatile RNReportUtils sRNReportUtils;

    private String appId;
    private String versionName;
    private String coreTarget;

    private RNReportUtils() {
    }

    public static RNReportUtils getInstance() {
        if (sRNReportUtils == null) {
            synchronized (RNReportUtils.class) {
                if (sRNReportUtils == null) {
                    sRNReportUtils = new RNReportUtils();
                }
            }
        }
        return sRNReportUtils;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppId() {
        return appId;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setCoreTarget(String coreTarget) {
        this.coreTarget = coreTarget;
    }

    public String getCoreTarget() {
        return coreTarget;
    }
}
