package com.ainirobot.platform.react.reactnative.component.uicomponent.slideverifyimageview;

import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import com.ainirobot.platform.R;
import com.ainirobot.platform.react.view.captcha.Captcha;
import com.ainirobot.platform.utils.FileUtils;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.UIManagerModule;

import java.io.FileInputStream;
import java.io.FileNotFoundException;

public class SlideVerifyImageView extends LinearLayout {
    private static final String TAG = "SlideVerifyImageView";
    private View mSlideVerifyImageView = null;
    private Captcha mCaptcha;
    private ThemedReactContext mContext;

    public SlideVerifyImageView(ThemedReactContext context) {
        this(context, null);
    }

    public SlideVerifyImageView(ThemedReactContext context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SlideVerifyImageView(ThemedReactContext context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public void init(ThemedReactContext context) {
        Log.d(TAG, "init");
        this.mContext = context;
        setLayoutParams(new ViewGroup.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, 900));
        mSlideVerifyImageView = LayoutInflater.from(context).inflate(R.layout.slide_verify_image_view, this);
        mSlideVerifyImageView.setLayoutParams(new ViewGroup.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, 900));

        mCaptcha = mSlideVerifyImageView.findViewById(R.id.captCha);
        mCaptcha.setCaptchaListener(new Captcha.CaptchaListener() {
            @Override
            public String onAccess(long time) {
                Log.d(TAG, "验证通过" + time);
                mContext.getNativeModule(UIManagerModule.class).getEventDispatcher()
                        .dispatchEvent(new SlideVerifySuccessEvent(SlideVerifyImageView.this.getId()));
                return getResources().getString(R.string.vertify_access);
            }

            @Override
            public String onFailed(int count) {
                Log.d(TAG, "验证失败" + count);
                mContext.getNativeModule(UIManagerModule.class).getEventDispatcher()
                        .dispatchEvent(new SlideVerifyFailureEvent(SlideVerifyImageView.this.getId()));
                return getResources().getString(R.string.vertify_failed);
            }

            @Override
            public String onMaxFailed() {
                Log.d(TAG, "验证超过最大次数");
                mContext.getNativeModule(UIManagerModule.class).getEventDispatcher()
                        .dispatchEvent(new SlideVerifyMaxFailureEvent(SlideVerifyImageView.this.getId()));
                return getResources().getString(R.string.vertify_access_exceed_max_times);
            }

            @Override
            public String onStartSlide() {
                Log.d(TAG, "onStartSlide");
                return null;
            }
        });

    }

    public void setSrcImg(String srcImg) {
        Log.e(TAG, "setSrcImg= " + srcImg);
        if (FileUtils.checkFileExist(srcImg)) {
            try {
                this.mCaptcha.setBitmap(new FileInputStream(srcImg));
                invalidate();   // 更新画板
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }
        }
    }

}

