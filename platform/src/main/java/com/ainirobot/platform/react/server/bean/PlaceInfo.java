package com.ainirobot.platform.react.server.bean;

import com.pinyinsearch.model.PinyinSearchUnit;
import com.pinyinsearch.util.PinyinUtil;

public class PlaceInfo {

    private String name;
    private PinyinSearchUnit pinyinSearchUnit;

    public PlaceInfo(String name) {
        this.name = name;
        this.pinyinSearchUnit = new PinyinSearchUnit(name);
        this.pinyinSearchUnit.setBaseData(name);
        PinyinUtil.parse(this.pinyinSearchUnit);
    }

    public String getName() {
        return name;
    }

    public PinyinSearchUnit getPinyinSearchUnit() {
        pinyinSearchUnit.getPinyinUnits();
        return pinyinSearchUnit;
    }

    @Override
    public String toString() {
        return "PlaceInfo{" +
                "name='" + name + '\'' +
                ", matchKeyword=" + pinyinSearchUnit.getMatchKeyword().toString() +
                '}';
    }
}
