package com.ainirobot.platform.react.server.impl

import android.app.IActivityController
import android.app.IProcessObserver
import android.content.ComponentName
import android.content.Intent
import android.content.pm.PackageManager
import android.os.IBinder
import android.os.UserHandle
import androidx.annotation.CallSuper
import android.util.Log
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.control.IAppMessengerProxy
import com.ainirobot.platform.rn.IRNApkControlRegistry
import com.ainirobot.platform.rn.listener.IRNApkControlListener
import com.ainirobot.platform.utils.SystemUtils
import com.ainirobot.platform.utils.observe.ApkObserve
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.lang.reflect.Method
import java.util.concurrent.ConcurrentHashMap

interface OpkStateChangeListener {
    fun onOpkProcessDied()
}

interface RNApkStateChangeListener {
    fun onPageStateChanged(apkPackage: String, state: String)
    fun onTriggerCommand(apkPackage: String, command: String)
    fun onServiceConnected(apkPackage: String)
    fun onServiceDisconnected(apkPackage: String)
    fun onRobotMessengerReady(apkPackage: String)

    fun onProcessDied(apkPackage: String)
    fun onProcessInVisible(apkPackage: String)
    fun onProcessVisible(apkPackage: String)
    fun onAppNotResponding(apkPackage: String)
    fun onTopActivityChanged(apkPackage: String, activityName: String)
    fun onApkInstallResult(apkPackage: String, success: Boolean, errorMsg: String, taskId: String)
}

interface OpkApkStateChangeListener : OpkStateChangeListener, RNApkStateChangeListener

class RNNLPApkControlSever() : IRNApkControlRegistry.Stub(), OpkApkStateChangeListener {
    companion object {
        const val TAG = "RNApkControlSever"
    }

    private val mOpkListenerMap: MutableMap<String, AbsRNApkChangeListenerBinderProxy> = ConcurrentHashMap()
    private val mAppMessengerMap: MutableMap<String, IAppMessengerProxy> = ConcurrentHashMap()

    override fun onRobotMessage(apkPackage: String?, messsage: String?): Int {
        Log.i(TAG, "onRobotMessage:$apkPackage message:$messsage")
        val appMessengerProxy = mAppMessengerMap[apkPackage]
        return appMessengerProxy?.onRobotMessage(messsage) ?: -1
    }

    override fun setApplicationState(apkPackage: String?, isEnable: Boolean) {
        Log.i(TAG, "setApplicationState apkPackage:$apkPackage isEnable:$isEnable")
        SystemUtils.setApplicationState(apkPackage, isEnable)
    }

    override fun addListener(apkPackage: String, listener: IRNApkControlListener?) {
        ActivityProcessObserver.addNLPApkStateChangeListener(apkPackage, this)
        val apkControlListenerBinder = getListenerClazzByPackage(apkPackage)
        apkControlListenerBinder.setApkControlListenerBinder(listener)
        val clazz = mOpkListenerMap[apkPackage]
        if (clazz == null) {
            mOpkListenerMap[apkPackage] = apkControlListenerBinder
        }
    }

    fun getListenerClazzByPackage(apkPackage: String): AbsRNApkChangeListenerBinderProxy {
        val listener = mOpkListenerMap[apkPackage]
        if (listener != null) {
            return listener
        } else {
            return DefaultRNApkChangeListenerBinderProxy::class.java
                    .getConstructor().newInstance()
        }
    }

    override fun removeListener(apkPackage: String) {
        Log.i(TAG, "removeListener apkPackage:$apkPackage")
        val packageListenerMap = mOpkListenerMap
        packageListenerMap.apply {
            packageListenerMap.remove(apkPackage)
            if (packageListenerMap.isEmpty()) {
                ActivityProcessObserver.removeNLPApkStateChangeListener(apkPackage)
            }
        }
        SystemUtils.forceStopPackage(apkPackage)
    }

    fun getAppMessengerProxy(packageName: String): IAppMessengerProxy? {
        return mAppMessengerMap[packageName]
    }

    fun putAppMessengerProxy(messengerProxy: IAppMessengerProxy) {
        mAppMessengerMap[messengerProxy.packageName] = messengerProxy
    }

    fun removeAppMessengerProxy(packageName: String) {
        mAppMessengerMap.remove(packageName)
    }

    override fun forceStopPackage(apkPackage: String?) {
        Log.i(TAG, "forceStopPackage apkPackage:$apkPackage")
        SystemUtils.forceStopPackage(apkPackage)
    }

    override fun grantRuntimePermission(apkPackage: String, permission: String) {
        Log.i(TAG, "grantRuntimePermission apkPackage:$apkPackage,permission:$permission")
        val packageManager = BaseApplication.getContext().packageManager
        grantRuntimePermissionMethod.invoke(packageManager, apkPackage, permission, userHandle)
    }

    override fun revokeRuntimePermission(apkPackage: String, permission: String) {
        Log.i(TAG, "revokeRuntimePermission apkPackage:$apkPackage,permission:$permission")
        val packageManager = BaseApplication.getContext().packageManager
        revokeRuntimePermissionMethod.invoke(packageManager, apkPackage, permission, userHandle)
    }

    private val revokeRuntimePermissionMethod: Method by lazy {
        val declaredMethod = PackageManager::class.java.getDeclaredMethod("revokeRuntimePermission", String::class.java, String::class.java, UserHandle::class.java)
        declaredMethod.isAccessible = true
        declaredMethod
    }
    private val grantRuntimePermissionMethod: Method by lazy {
        val declaredMethod = PackageManager::class.java.getDeclaredMethod("grantRuntimePermission", String::class.java, String::class.java, UserHandle::class.java)
        declaredMethod.isAccessible = true
        declaredMethod
    }

    private val userHandle: UserHandle by lazy {
        val myUserIdMethod = UserHandle::class.java.getDeclaredMethod("myUserId")
        val getUserHandleForUidMethod = UserHandle::class.java.getDeclaredMethod("getUserHandleForUid", Int::class.java)
        getUserHandleForUidMethod.invoke(null, myUserIdMethod.invoke(null)) as UserHandle
    }

    override fun removeAll() {
        Log.i(TAG, "removeAll")
        killApps(mOpkListenerMap.keys)
        mOpkListenerMap.clear()
        mAppMessengerMap.clear()
        ActivityProcessObserver.removeAll()
    }

    override fun onOpkProcessDied() {
        Log.d(TAG, "onOpkProcessDied")
        removeAll()
    }

    override fun onPageStateChanged(apkPackage: String, state: String) {
        Log.i(TAG, "onPageStateChanged apkPackage:$apkPackage,state:$state")
        mOpkListenerMap[apkPackage]?.onPageStateChanged(apkPackage, state)
    }

    override fun onTriggerCommand(apkPackage: String, command: String) {
        Log.i(TAG, "onTriggerCommand:$apkPackage,command:$command")
        mOpkListenerMap[apkPackage]?.onTriggerCommand(apkPackage, command)
    }

    override fun onServiceConnected(apkPackage: String) {
        Log.i(TAG, "onServiceConnected apkPackage:${apkPackage}")
        mOpkListenerMap[apkPackage]?.onServiceConnected(apkPackage)
    }

    override fun onServiceDisconnected(apkPackage: String) {
        Log.i(TAG, "onServiceDisconnected apkPackage:${apkPackage}")
        mOpkListenerMap[apkPackage]?.onServiceDisconnected(apkPackage)
    }

    override fun onRobotMessengerReady(apkPackage: String) {
        Log.i(TAG, "onRobotMessengerReady apkPackage:${apkPackage}")
        mOpkListenerMap[apkPackage]?.onRobotMessengerReady(apkPackage)
    }

    override fun onProcessDied(apkPackage: String) {
        Log.i(TAG, "onProcessDied apkPackage:${apkPackage}")
        mOpkListenerMap[apkPackage]?.onProcessDied(apkPackage)
    }

    override fun onProcessInVisible(apkPackage: String) {
        Log.i(TAG, "onProcessInVisible apkPackage:${apkPackage}")
        mOpkListenerMap[apkPackage]?.onProcessInVisible(apkPackage)
    }

    override fun onProcessVisible(apkPackage: String) {
        Log.i(TAG, "onProcessVisible apkPackage:${apkPackage}")
        mOpkListenerMap[apkPackage]?.onProcessVisible(apkPackage)
    }

    override fun onAppNotResponding(apkPackage: String) {
        Log.i(TAG, "onAppNotResponding apkPackage:${apkPackage}")
        mOpkListenerMap[apkPackage]?.onAppNotResponding(apkPackage)
    }

    override fun onTopActivityChanged(apkPackage: String, activityName: String) {
        Log.i(TAG, "onTopActivityChanged apkPackage:${apkPackage},activityName:$activityName")
        mOpkListenerMap.forEach { packageListenerEntryIt ->
            packageListenerEntryIt.value.onTopActivityChanged(apkPackage, activityName)
        }
    }

    override fun onApkInstallResult(apkPackage: String, success: Boolean, errorMsg: String, taskId: String) {
        Log.i(TAG, "onApkInstallResult apkPackage:${apkPackage},success:$success,errorMsg:$errorMsg,taskId:$taskId")
        mOpkListenerMap[apkPackage]?.onApkInstallResult(apkPackage, success, errorMsg, taskId)
    }

    private fun killApps(packages: Collection<String>) {
        val selfPackage: String = BaseApplication.getContext().packageName
        packages.forEach {
            if (it != selfPackage) {
                SystemUtils.forceStopPackage(it)
            }
        }
    }

}

/**
 * OPK 和 APK 通讯协议接口
 */
interface RNApkChangeListener : RNApkStateChangeListener {
    fun getCallbackId(): Int
    fun onDestroy()
}

/**
 * OPK 在主进程中的通讯接口 linstener binder 代理，负责把 apk 的状态以及传递的数据通过 binder 发送给 opk
 */
abstract class AbsRNApkChangeListenerBinderProxy() : RNApkChangeListener {

    private val TAG = AbsRNApkChangeListenerBinderProxy::class.java.simpleName

    private var listenerBinder: IRNApkControlListener? = null

    fun setApkControlListenerBinder(listener: IRNApkControlListener?): AbsRNApkChangeListenerBinderProxy {
        this.listenerBinder = listener
        return this
    }

    final override fun getCallbackId(): Int {
        return if (listenerBinder != null) {
            listenerBinder!!.callbackId
        } else {
            -1
        }
    }

    final override fun onPageStateChanged(apkPackage: String, state: String) {
        Log.i(TAG, "listenerBinder:$listenerBinder,onPageStateChanged apkPackage:$apkPackage state:$state")
        try {
            listenerBinder?.onPageStateChanged(apkPackage, state)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onTriggerCommand(apkPackage: String, command: String) {
        Log.i(TAG, "listenerBinder:$listenerBinder,onTriggerCommand apkPackage:$apkPackage command:$command")
        try {
            listenerBinder?.onTriggerCommand(apkPackage, command)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    final override fun onProcessDied(apkPackage: String) {
        Log.i(TAG, "listenerBinder:$listenerBinder,onProcessDied apkPackage:$apkPackage")
        try {
            listenerBinder?.onProcessDied(apkPackage)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onProcessInVisible(apkPackage: String) {
        Log.i(TAG, "listenerBinder:$listenerBinder,onProcessInVisible apkPackage:$apkPackage")
        try {
            listenerBinder?.onProcessInVisible(apkPackage)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onProcessVisible(apkPackage: String) {
        Log.i(TAG, "listenerBinder:$listenerBinder,onProcessVisible apkPackage:$apkPackage")
        try {
            listenerBinder?.onProcessVisible(apkPackage)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    final override fun onTopActivityChanged(apkPackage: String, activityName: String) {
        Log.i(TAG, "listenerBinder:$listenerBinder,onTopActivityChanged apkPackage:$apkPackage,activityName:$activityName")
        try {
            listenerBinder?.onTopActivityChanged(apkPackage, activityName)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    final override fun onAppNotResponding(apkPackage: String) {
        Log.i(TAG, "listenerBinder:$listenerBinder,onAppNotResponding apkPackage:$apkPackage")
        try {
            listenerBinder?.onAppNotResponding(apkPackage)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onServiceConnected(apkPackage: String) {
        try {
            listenerBinder?.onServiceConnected(apkPackage)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onServiceDisconnected(apkPackage: String) {
        try {
            listenerBinder?.onServiceDisconnected(apkPackage)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onRobotMessengerReady(apkPackage: String) {
        try {
            listenerBinder?.onRobotMessengerReady(apkPackage)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @CallSuper
    override fun onDestroy() {
        setApkControlListenerBinder(null)
    }

    override fun onApkInstallResult(apkPackage: String, success: Boolean, errorMsg: String, taskId: String) {
        listenerBinder?.onApkInstallResult(apkPackage, success, errorMsg, taskId)
    }
}

class DefaultRNApkChangeListenerBinderProxy() : AbsRNApkChangeListenerBinderProxy()

/**
 * 系统进程和 Activity 状态监听
 */
object ActivityProcessObserver : IProcessObserver, IActivityController {

    const val TAG = "ActivityProcessObserver"

    private var apkPackageListenerMap: MutableMap<String, OpkApkStateChangeListener> = ConcurrentHashMap()

    fun addNLPApkStateChangeListener(apkPackage: String, listener: OpkApkStateChangeListener) {
        Log.v(TAG, "addRNApkStateChangeListener")
        this.apkPackageListenerMap[apkPackage] = listener
    }

    fun removeNLPApkStateChangeListener(apkPackage: String) {
        this.apkPackageListenerMap.remove(apkPackage)
    }

    fun removeAll() {
        this.apkPackageListenerMap.clear();
    }

    private fun getAppPackage(pid: Int): Array<out String>? {
        val appProcessInfo = ApkObserve.instance.getAppProcessInfo(pid)
        if (appProcessInfo != null) {
            return appProcessInfo.pkgList
        }
        return null
    }

    private var lastTopActivity: ComponentName? = null

    private fun checkTopActivity() {
        if (apkPackageListenerMap.isNotEmpty()) {
            Log.i(TAG, "checkTopActivity")
            GlobalScope.launch {
                val runningTasks = SystemUtils.getActivityManager().getRunningTasks(1)
                if (!runningTasks.isNullOrEmpty()) {
                    val runningTaskInfo = runningTasks[0]
                    val topActivity = runningTaskInfo.topActivity
                    Log.i(TAG, "lastTopActivity:${lastTopActivity?.toString()}," +
                            "topActivity:${topActivity.toString()}")
                    if (lastTopActivity == null ||
                            topActivity.className != lastTopActivity?.className) {
                        lastTopActivity = topActivity
                        apkPackageListenerMap.values.forEach {
                            it.onTopActivityChanged(topActivity.packageName, topActivity.className)
                        }
                    }
                }
            }
        }
    }

    fun onOpkProcessDied() {
        Log.v(TAG, "onOpkProcessDied")
        apkPackageListenerMap.values.forEach {
            it.onOpkProcessDied()
        }
    }

    override fun onProcessStateChanged(pid: Int, uid: Int, procState: Int) {
        Log.v(TAG, "onProcessStateChanged")
        checkTopActivity()
    }

    override fun onForegroundActivitiesChanged(pid: Int, uid: Int, foregroundActivities: Boolean) {
        Log.v(TAG, "onForegroundActivitiesChanged")
        if (apkPackageListenerMap.isNotEmpty()) {
            val list = SystemUtils.getActivityManager().runningAppProcesses.filter {
                it.pid == pid
            }
            Log.i(TAG, "onForegroundActivitiesChanged pid:$pid,list:$list ,uid:$uid,foregroundActivities:$foregroundActivities")
            if (list.isNotEmpty()) {
                apkPackageListenerMap.values.forEach { listener ->
                    val runningAppProcessInfo = list[0]
                    val appPackage = runningAppProcessInfo.pkgList
                    appPackage?.forEach {
                        if (foregroundActivities) {
                            listener.onProcessVisible(it)
                        } else {
                            listener.onProcessInVisible(it)
                        }
                    }
                }
                checkTopActivity()
            }
        }
    }

    override fun onProcessDied(pid: Int, uid: Int) {
        Log.v(TAG, "onProcessDied")
        if (apkPackageListenerMap.isNotEmpty()) {
            val appPackage = getAppPackage(pid)
            Log.i(TAG, "onProcessDied pid:$pid,uid:$uid appPackage:$appPackage")
            appPackage?.apply {
                apkPackageListenerMap.values.forEach { listener ->
                    appPackage.forEach {
                        listener.onProcessDied(it)
                    }
                }
            }
        }
    }

    override fun systemNotResponding(msg: String?): Int {
        return 0
    }

    override fun apkInstallResult(apkPackage: String, success: Boolean, errorMsg: String, taskId: String) {
        if (apkPackageListenerMap.isNotEmpty()) {
            apkPackageListenerMap.values.forEach { listener ->
                listener.onApkInstallResult(apkPackage, success, errorMsg, taskId)
            }
        }
    }

    override fun appCrashed(processName: String?, pid: Int, shortMsg: String?, longMsg: String?, timeMillis: Long, stackTrace: String?): Boolean {
        onProcessDied(pid, 0)
        return false
    }

    override fun activityStarting(intent: Intent?, pkg: String?): Boolean {
        Log.v(TAG, "activityStarting")
        checkTopActivity()
        return false
    }

    override fun appEarlyNotResponding(processName: String?, pid: Int, annotation: String?): Int {
        // 等 appNotResponding 处理
        return 0
    }

    override fun appNotResponding(processName: String?, pid: Int, processStats: String?): Int {
        if (apkPackageListenerMap.isNotEmpty()) {
            val appPackage = getAppPackage(pid)
            Log.i(TAG, "onProcessappNotResponding pid:$pid,processName:$processName,appPackage:$appPackage,processStats:$processStats")
            appPackage?.apply {
                apkPackageListenerMap.values.forEach { listener ->
                    appPackage.forEach {
                        listener.onAppNotResponding(it)
                    }
                }
            }
        }
        return 0
    }

    override fun activityResuming(pkg: String?): Boolean {
        Log.v(TAG, "activityResuming")
        checkTopActivity()
        return false
    }

    override fun asBinder(): IBinder? {
        return null
    }

}