package com.ainirobot.platform.react.client

/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

import android.os.Message
import android.os.RemoteException
import android.util.Log
import com.ainirobot.platform.react.reactnative.component.uicomponent.recognitionview.RNSpeechHandler
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.IRNSpeechCallBack
import com.ainirobot.platform.speech.SpeechApiCost
import com.facebook.react.bridge.WritableNativeMap

class RNSpeechCallBack : IRNSpeechCallBack.Stub() {

    private val TAG = RNSpeechCallBack::class.java.getSimpleName()
    private val eventName = "onSpeeckCallback"
    private val mHandler = RNSpeechHandler("speech native thread").getHandler()

    @Throws(RemoteException::class)
    override fun onSpeechParResult(s: String) {
        CallBackHandler.instance.getHandler().post {
            Log.i(TAG, "onSpeechParResult :$s")
            val readableMap = WritableNativeMap()
            readableMap.putString("event", "onSpeechParResult")
            readableMap.putString("message", s)
            ReactNativeEventEmitter.triggerEvent(eventName, readableMap)
            mHandler.sendMessage(Message.obtain(mHandler, SpeechApiCost.MSG_PAR_RESULT, s))
        }

    }

    @Throws(RemoteException::class)
    override fun onRecognitionStart() {
        CallBackHandler.instance.getHandler().post {
            Log.i(TAG, "onSpeechRecognitionStart ")
            val readableMap = WritableNativeMap()
            readableMap.putString("event", "onSpeechRecognitionStart")
            ReactNativeEventEmitter.triggerEvent(eventName, readableMap)
            mHandler.sendMessage(Message.obtain(mHandler, SpeechApiCost.MSG_RECOGNITION_START, null))
        }

    }

    @Throws(RemoteException::class)
    override fun onRecognitionStop() {
        CallBackHandler.instance.getHandler().post {
            Log.i(TAG, "onSpeechRecognitionStop ")
            val readableMap = WritableNativeMap()
            readableMap.putString("event", "onSpeechRecognitionStop")
            ReactNativeEventEmitter.triggerEvent(eventName, readableMap)
            mHandler.sendMessage(Message.obtain(mHandler, SpeechApiCost.MSG_RECOGNITION_STOP, null))
        }

    }

    @Throws(RemoteException::class)
    override fun onQueryEnded(i: Int) {
        CallBackHandler.instance.getHandler().post {
            Log.d(TAG, "onQueryEnded()")
            val readableMap = WritableNativeMap()
            readableMap.putString("event", "onQueryEnded")
            readableMap.putInt("index", i)
            ReactNativeEventEmitter.triggerEvent(eventName, readableMap)
            mHandler.sendMessage(Message.obtain(mHandler, SpeechApiCost.MSG_QUERY_ENDED, i, 0))
        }

    }

    @Throws(RemoteException::class)
    override fun onQueryAsrResult(asrResult: String?) {
        CallBackHandler.instance.getHandler().post {
            Log.i(TAG, "asrResult = " + asrResult!!)
            val readableMap = WritableNativeMap()
            readableMap.putString("event", "onQueryAsrResult")
            readableMap.putString("message", asrResult)
            ReactNativeEventEmitter.triggerEvent(eventName, readableMap)
            mHandler.sendMessage(Message.obtain(mHandler, SpeechApiCost.MSG_QUERY_ASR_RESULT, asrResult))
        }

    }

    @Throws(RemoteException::class)
    override fun onRecognitionError(sid: String, code: Int, message: String) {
        CallBackHandler.instance.getHandler().post {
            Log.i(TAG, "onError sid=$sid, code=$code, message=$message")
            val readableMap = WritableNativeMap()
            readableMap.putString("event", "onSpeechRecognitionError")
            readableMap.putInt("code", code)
            readableMap.putString("sid", sid)
            readableMap.putString("message", message)
            ReactNativeEventEmitter.triggerEvent(eventName, readableMap)
            mHandler.sendMessage(Message.obtain(mHandler, SpeechApiCost.MSG_RECOGNITION_ERROR, code))
        }
    }

    @Throws(RemoteException::class)
    override fun onSpeechStreamData(data: String?) {
        CallBackHandler.instance.getHandler().post {
            Log.i(TAG, "onSpeechStreamData :$data")
            val readableMap = WritableNativeMap()
            readableMap.putString("event", "onSpeechStreamData")
            readableMap.putString("message", data)
            ReactNativeEventEmitter.triggerEvent(eventName, readableMap)
            mHandler.sendMessage(Message.obtain(mHandler, SpeechApiCost.MSG_SPEECH_STREAM_DATA, data))
        }
    }
}
