package com.ainirobot.platform.react.reactnative.component.common

import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.react.AppManger
import com.ainirobot.platform.react.OPKBeanV3
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNRobotSettingListener
import com.ainirobot.platform.utils.DeviceUtils
import com.facebook.react.bridge.*
import java.io.File
import java.util.*

/**
 * @date: 2019-09-16
 * @author: zyc
 * @desc: 获取当前系统设置信息　
 */
class SettingsBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    private val TAG = SettingsBridgeModule::class.java.simpleName
    private val mContext = BaseApplication.getContext()
    private val mKeyList = Collections.synchronizedList(ArrayList<String>())
    private val mHandler = Handler(Looper.getMainLooper())

    override fun getName(): String {
        return "Settings"
    }

    @ReactMethod
    fun putString(appId: String?, key: String, value: String, result: Promise) {
        Log.d(TAG, "Settings[$appId] : $key   $value")
        //非系统权限禁止调用
        val info = AppManger.getRPKByCharacter(appId)
        if (info == null || !info.isSystem) {
            Log.d(TAG, "Settings[$appId] : $key   $value permission denied")
            result.reject(Throwable("Permission denied"))
            return
        }

        try {
            RNClientManager.instance?.robotSettingManager?.setRobotString(key, value)
            result.resolve(true)
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun putInt(appId: String?, key: String, value: Int, result: Promise) {
        Log.d(TAG, "Settings[$appId] : $key   $value")
        //非系统权限禁止调用
        val info = AppManger.getRPKByCharacter(appId)
        if (info == null || !info.isSystem) {
            Log.d(TAG, "Settings[$appId] : $key   $value permission denied")
            result.reject(Throwable("Permission denied"))
            return
        }

        try {
            result.resolve(RNClientManager.instance?.robotSettingManager?.setRobotInt(key, value))
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun getInt(key: String, result: Promise) {
        try {
            result.resolve(RNClientManager.instance?.robotSettingManager?.getRobotInt(key))
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun getString(key: String, result: Promise) {
        try {
            result.resolve(RNClientManager.instance?.robotSettingManager?.getRobotString(key))
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun isOpen(key: String, result: Promise) {
        try {
            result.resolve(RNClientManager.instance?.robotSettingManager?.getRobotInt(key) == 1)
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun getSystemProperties(key: String, result: Promise) {
        try {
            result.resolve(DeviceUtils.getSystemProperties(key))
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun reloadJS() {
        Handler(BaseApplication.getContext().mainLooper).post {
            val file = File(Environment.getExternalStorageDirectory(), "index.android.bundle")
            Log.i("SettingsBridgeModule", "file path:" + file.absoluteFile)
        }
    }

    @ReactMethod
    fun isCharing(result: Promise) {
        try {
            val batteryStatus: Intent? = IntentFilter(Intent.ACTION_BATTERY_CHANGED).let { ifilter ->
                BaseApplication.getContext().registerReceiver(null, ifilter)
            }
            val status: Int = batteryStatus?.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1) ?: -1
            Log.i("SettingsBridgeModule", "isCharing:$status")
            val isCharging: Boolean = status != 0
            result.resolve(isCharging)
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun getIntByDefault(key: String, def: Int, result: Promise) {
        try {
            result.resolve(Settings.Global.getInt(mContext.contentResolver, key, def))
        } catch (e: Exception) {
            e.printStackTrace()
            result.reject(e)
        }
    }

    @ReactMethod
    fun addSettingsListener(keys: ReadableArray) {
        try {
            val values = keys.toArrayList()
            val oldSize = mKeyList.size;
            for (key in values) {
                if (!mKeyList.contains(key)) {
                    mKeyList.add(key.toString());
                    Log.d(TAG, "add register:" + key);
                }
            }
            if (oldSize < mKeyList.size) {
                Log.d(TAG, "register to main thread");
                RNClientManager.instance?.robotSettingManager?.unRegisterRobotSettingListener(mRobotSettingListener)
                RNClientManager.instance?.robotSettingManager?.registerRobotSettingListener(
                        mRobotSettingListener, mKeyList);
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun removeSettingsListener(keys: ReadableArray) {
        try {
            for (key in keys.toArrayList()) {
                if (mKeyList.contains(key)) {
                    mKeyList.remove(key);
                }
            }
            RNClientManager.instance?.robotSettingManager?.unRegisterRobotSettingListener(mRobotSettingListener)
            if (mKeyList.size > 0) {
                RNClientManager.instance?.robotSettingManager?.registerRobotSettingListener(
                        mRobotSettingListener, mKeyList);
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private val mRobotSettingListener = object : IRNRobotSettingListener.Stub() {
        override fun onRobotSettingChanged(key: String) {
            Log.i(TAG, "On settings change, key : $key")
            ReactNativeEventEmitter.triggerEvent("_settings_", key)
        }
    }

    @ReactMethod
    fun clearSettingsListener() {
        try {
            mKeyList.clear()
            RNClientManager.instance?.robotSettingManager?.unRegisterRobotSettingListener(mRobotSettingListener)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}
