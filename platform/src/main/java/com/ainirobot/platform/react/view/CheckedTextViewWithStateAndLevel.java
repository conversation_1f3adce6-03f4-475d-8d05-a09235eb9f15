package com.ainirobot.platform.react.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.CheckedTextView;

@SuppressLint("AppCompatCustomView")
public class CheckedTextViewWithStateAndLevel extends CheckedTextView {
	private int[] mDrawableState;
	private boolean mMergeState;
	private int mLevel;

	public CheckedTextViewWithStateAndLevel(Context context) {
		super(context);
	}

	public CheckedTextViewWithStateAndLevel(Context context, AttributeSet attrs) {
		super(context, attrs);
	}

	public CheckedTextViewWithStateAndLevel(Context context, AttributeSet attrs, int defStyleAttr) {
		super(context, attrs, defStyleAttr);
	}

	public void setImageState(int[] states, boolean mergeState) {
		mDrawableState = states;
		mMergeState = mergeState;
		refreshDrawableState();
		requestLayout();
	}

	public void setImageLevel(int level) {
		mLevel = level;
		final Drawable checkMarkDrawable = getCheckMarkDrawable();
		if (checkMarkDrawable != null) {
			checkMarkDrawable.setLevel(level);
		}
		for (Drawable d : getCompoundDrawablesRelative()) {
			if (d != null) {
				d.setLevel(level);
			}
		}
		final Drawable backgroundDrawable = getBackground();
		if (backgroundDrawable != null) {
			backgroundDrawable.setLevel(level);
		}

		final Drawable foregroundDrawable = getForeground();
		if (foregroundDrawable != null) {
			foregroundDrawable.setLevel(level);
		}
		requestLayout();
	}

	public int getImageLevel() {
		return mLevel;
	}

	@Override
	protected int[] onCreateDrawableState(int extraSpace) {
		if (mDrawableState == null) {
			return super.onCreateDrawableState(extraSpace);
		} else if (!mMergeState) {
			return mDrawableState;
		} else {
			return mergeDrawableStates(super.onCreateDrawableState(extraSpace + mDrawableState.length), mDrawableState);
		}
	}
}
