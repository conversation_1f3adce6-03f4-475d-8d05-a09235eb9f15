package com.ainirobot.platform.react.reactnative.component.uicomponent.external.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.FrameLayout;

public class BSRootView extends FrameLayout {

    private float WIDTH;
    private float HEIGHT;

    public BSRootView(Context context) {
        super(context);
    }

    public BSRootView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public BSRootView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public void setMeasureParams(int width, int height) {
        WIDTH = height;
        HEIGHT = width <= 1920 ? 1920 : 2560;
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);

        setScaleX(HEIGHT / WIDTH);
        setScaleY(WIDTH / HEIGHT);
        setTranslationX((HEIGHT - WIDTH) / 2);
        setTranslationY(-(HEIGHT - WIDTH) / 2);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        widthMeasureSpec = MeasureSpec.makeMeasureSpec((int) WIDTH, MeasureSpec.getMode(widthMeasureSpec));
        heightMeasureSpec = MeasureSpec.makeMeasureSpec((int) HEIGHT, MeasureSpec.getMode(heightMeasureSpec));
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }
}
