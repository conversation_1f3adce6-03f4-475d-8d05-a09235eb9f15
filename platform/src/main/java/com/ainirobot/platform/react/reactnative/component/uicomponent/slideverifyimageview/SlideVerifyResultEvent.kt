package com.ainirobot.platform.react.reactnative.component.uicomponent.slideverifyimageview

import android.util.Log
import com.facebook.react.bridge.Arguments
import com.facebook.react.uimanager.events.Event
import com.facebook.react.uimanager.events.RCTEventEmitter


class SlideVerifySuccessEvent(viewId: Int) : Event<SlideVerifySuccessEvent>(viewId) {

    override fun getEventName(): String {
        return  SlideVerifyImageViewManager.Companion.Events.EVENT_ACCESS.toString()
    }

    override fun dispatch(rctEventEmitter: RCTEventEmitter) {
        Log.i(SlideVerifyImageViewManager.TAG,"verify access dispatch")
        val map = Arguments.createMap()
        rctEventEmitter.receiveEvent(viewTag, eventName, map)
    }
}


class SlideVerifyFailureEvent(viewId: Int) : Event<SlideVerifyFailureEvent>(viewId) {

    override fun getEventName(): String {
        return SlideVerifyImageViewManager.Companion.Events.EVENT_FAILED.toString()
    }

    override fun dispatch(rctEventEmitter: RCTEventEmitter) {
        Log.i(SlideVerifyImageViewManager.TAG,"verify failed dispatch")
        val map = Arguments.createMap()
        rctEventEmitter.receiveEvent(viewTag, eventName, map)
    }
}

class SlideVerifyMaxFailureEvent(viewId: Int) : Event<SlideVerifyMaxFailureEvent>(viewId) {

    override fun getEventName(): String {
        return SlideVerifyImageViewManager.Companion.Events.EVENT_MAX_FAILED.toString()
    }

    override fun dispatch(rctEventEmitter: RCTEventEmitter) {
        Log.i(SlideVerifyImageViewManager.TAG,"verify exceed max times dispatch")
        val map = Arguments.createMap()
        rctEventEmitter.receiveEvent(viewTag, eventName, map)
    }
}

class StartSlideEvent(viewId: Int) : Event<StartSlideEvent>(viewId) {

    override fun getEventName(): String {
        return SlideVerifyImageViewManager.Companion.Events.EVENT_START_SLIDE.toString()
    }

    override fun dispatch(rctEventEmitter: RCTEventEmitter) {
        Log.i(SlideVerifyImageViewManager.TAG,"start slide dispatch")
        val map = Arguments.createMap()
        rctEventEmitter.receiveEvent(viewTag, eventName, map)
    }
}

