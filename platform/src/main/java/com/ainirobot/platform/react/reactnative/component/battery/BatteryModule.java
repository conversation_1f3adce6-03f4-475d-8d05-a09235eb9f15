package com.ainirobot.platform.react.reactnative.component.battery;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.facebook.react.bridge.LifecycleEventListener;

import javax.annotation.Nonnull;

public class BatteryModule extends ReactContextBaseJavaModule implements LifecycleEventListener {
    private static final String BATTERY_STATUS_ACTION = "com.ainirobot.platform.BATTERY_STATUS_CHANGED";
    private final ReactApplicationContext reactContext;
    private BroadcastReceiver batteryReceiver;
    private boolean isReceiverRegistered = false;

    public BatteryModule(@Nonnull ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
        this.reactContext.addLifecycleEventListener(this);
        initializeBatteryReceiver();
    }

    private void initializeBatteryReceiver() {
        batteryReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (reactContext.hasActiveCatalystInstance()) {
                    int batteryLevel = intent.getIntExtra("batteryLevel", 0);
                    boolean isCharging = intent.getBooleanExtra("isCharging", false);

                    WritableMap params = Arguments.createMap();
                    params.putInt("batteryLevel", batteryLevel);
                    params.putBoolean("isCharging", isCharging);

                    try {
                        reactContext
                            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                            .emit("batteryStatus", params);
                    } catch (Exception e) {
                        // Handle or log any errors that occur during event emission
                    }
                }
            }
        };
    }

    private void registerReceiver() {
        if (batteryReceiver != null && !isReceiverRegistered) {
            IntentFilter filter = new IntentFilter(BATTERY_STATUS_ACTION);
            reactContext.registerReceiver(batteryReceiver, filter);
            isReceiverRegistered = true;
        }
    }

    private void unregisterReceiver() {
        if (batteryReceiver != null && isReceiverRegistered) {
            reactContext.unregisterReceiver(batteryReceiver);
            isReceiverRegistered = false;
        }
    }

    @Override
    public void onHostResume() {
        registerReceiver();
    }

    @Override
    public void onHostPause() {
        // Optionally unregister here if you don't need updates when the app is in background
        // unregisterReceiver();
    }

    @Override
    public void onHostDestroy() {
        unregisterReceiver();
    }

    @Override
    public void onCatalystInstanceDestroy() {
        super.onCatalystInstanceDestroy();
        unregisterReceiver();
    }

    @Nonnull
    @Override
    public String getName() {
        return "BatteryModule";
    }
}
