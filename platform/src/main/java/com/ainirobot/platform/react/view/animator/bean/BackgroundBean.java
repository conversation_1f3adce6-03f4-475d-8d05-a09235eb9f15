/*
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.ainirobot.platform.react.view.animator.bean;

/**
 * 在此写用途
 *
 * @FileName: com.ainirobot.platform.ui.animator.bean.BackgroundBean.java
 * @param: res: 背景图片路径; scan: 背景填充方式[拉伸/横向/纵向]
 * @author: Orion
 * @date: 2018-11-29 15:13
 */
public class BackgroundBean {
    private String res;
    private String scan;
    private String decode;

    public String getRes() {
        return res;
    }

    public void setRes(String res) {
        this.res = res;
    }

    public String getScan() {
        return scan;
    }

    public void setScan(String scan) {
        this.scan = scan;
    }

    public String getDecode() {
        return decode;
    }

    public void setDecode(String decode) {
        this.decode = decode;
    }
}
