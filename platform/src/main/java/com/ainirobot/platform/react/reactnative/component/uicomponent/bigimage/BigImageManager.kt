package com.ainirobot.platform.react.reactnative.component.uicomponent.bigimage

import android.util.Log
import android.view.View
import com.facebook.react.bridge.ReadableMap
import com.facebook.react.common.MapBuilder
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.annotations.ReactProp
import com.facebook.react.uimanager.events.RCTEventEmitter

@ReactModule(name = BigImageManager.REACT_CLASS)
class BigImageManager: SimpleViewManager<BigImageView>() {
    private lateinit var mContext: ThemedReactContext
    companion object {
        const val REACT_CLASS = "BigImageView"
        const val TAG = "BigImageManager"
        const val HEIGHT = "height"
        const val SRC = "src"
    }

    override fun getName(): String {
        return REACT_CLASS
    }

    override fun createViewInstance(reactContext: ThemedReactContext): BigImageView {
        mContext = reactContext
        return BigImageView(reactContext)
    }

    @ReactProp(name = "data")
    fun setData(view: BigImageView, map: ReadableMap) {
        Log.d(TAG, map.toString())
        val height = map.getInt(HEIGHT)
        val src = map.getString(SRC)
        view.setData(src, height)
        view.setOnClickListener {
            mContext.getJSModule(RCTEventEmitter::class.java).receiveEvent(view.id, "onClick", null)
        }
    }

    override fun getExportedCustomDirectEventTypeConstants(): MutableMap<String, Any> {
        return MapBuilder.of(
                "onClick", MapBuilder.of("registrationName", "onClick")
        )
    }
}