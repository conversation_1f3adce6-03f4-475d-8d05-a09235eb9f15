package com.ainirobot.platform.react.reactnative.component.uicomponent.camera.event

import com.ainirobot.platform.bean.ResponseState
import com.facebook.react.bridge.Arguments
import com.facebook.react.uimanager.events.Event
import com.facebook.react.uimanager.events.RCTEventEmitter

/**
 * @date: 2019-02-27
 * @author: lumeng
 * @desc: 封装camera回调拍照事件
 */
class CaptureEvent(viewId:Int, private var state: ResponseState, private var originalImageFile:String, private var compressedFile: String) : Event<CaptureEvent>(viewId) {

    companion object {
        /**
         * 原生组件和js组件的evnent映射
         * 对应 CameraViewManager 中 addEventEmitters 方法里 CameraView 的 CaptureListener 的回调方法
         * 对应 JS 层组件的方法属性 _onCaptureState
         */
        const val EVENT_NAME = "_onCaptureState"
    }

    override fun getEventName(): String {
        return EVENT_NAME
    }

    /**
     * saveFile 为拍照完保存的图片的路径
     * state 为拍摄状态
     * @see ResponseState
     */
    override fun dispatch(rctEventEmitter: RCTEventEmitter) {
        val map = Arguments.createMap()
        map.putString("state",state.toString())
        map.putString("originalImageFile",originalImageFile)
        map.putString("compressedFile",compressedFile)
        rctEventEmitter.receiveEvent(viewTag, eventName, map)
    }
}
