package com.ainirobot.platform.react.reactnative.component.common

import android.util.Log
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNUpdateListener
import com.facebook.react.bridge.*
import org.json.JSONObject

class AppUpdateManagerBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String {
        return "AppUpdateManager"
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun isInitInstall(appId: String?): Boolean? {
        return RNClientManager.instance?.updateManager?.isInitInstall
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun isUpdating(appId: String?): Boolean? {
        return RNClientManager.instance?.updateManager?.isUpdating
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun checkUpdate(appId: String?): Boolean? {
        Log.d("AppUpdateManagerBridge", "Check update")
        setUpdateListener()
        return RNClientManager.instance?.updateManager?.checkUpdate()
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun setUpdateEnable(appId: String?, enable: Boolean) {
        Log.d("AppUpdateManagerBridge", "Set update enable : $enable    $appId")
        RNClientManager.instance?.updateManager?.setUpdateEnable(enable)
    }

    @ReactMethod
    fun setUpdateListener() {
        Log.d("AppUpdateManagerBridge", "set update listener : " + RNClientManager.instance?.updateManager)
        RNClientManager.instance?.updateManager?.setUpdateListener(object : IRNUpdateListener.Stub() {
            override fun onStart() {
                Log.d("AppUpdateManagerBridge", "On update start")
                ReactNativeEventEmitter.triggerEvent("updateStart", "")
            }

            override fun onConfigUpdate(appId: String?, newConfig: String) {
                Log.d("AppUpdateManagerBridge", "On config update : $appId  $newConfig")
                ReactNativeEventEmitter.triggerEvent("configUpdate_$appId", newConfig)
            }

            override fun onProgressUpdate(count: Int, index: Int, appName: String?, oper: String?) {
                Log.d("AppUpdateManagerBridge", "On progress update : $appName  $oper")
                val data = JSONObject()
                data.put("count", count)
                data.put("index", index)
                data.put("appName", appName)
                data.put("oper", oper)

                ReactNativeEventEmitter.triggerEvent("updateProgress", data.toString())
            }

            override fun onFinished(result: Boolean, message: String?) {
                Log.d("AppUpdateManagerBridge", "Update result : $result  $message")
                val data = JSONObject()
                data.put("result", result)
                data.put("message", message)

                ReactNativeEventEmitter.triggerEvent("updateResult", data.toString())
            }
        })
    }

    @ReactMethod
    fun retryUpdate(appId: String?) {
        RNClientManager.instance?.updateManager?.retryUpdate()
    }

    @ReactMethod
    fun startUpdate(appId: String?) {
        RNClientManager.instance?.updateManager?.startUpdate()
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun cancelUpdate(appId: String?) {
        RNClientManager.instance?.updateManager?.cancelUpdate()
    }

}
