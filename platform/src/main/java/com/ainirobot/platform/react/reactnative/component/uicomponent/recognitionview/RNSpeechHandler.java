package com.ainirobot.platform.react.reactnative.component.uicomponent.recognitionview;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import com.ainirobot.platform.speech.SpeechRegister;

public class <PERSON>NSpeechHandler extends HandlerThread {

    private static final String TAG = "RNSpeechHandler";
    private RNSpeechInternalHandler mHandler;

    public RNSpeechHandler(String name) {
        super(name);
        startThread();
    }

    private void startThread() {
        this.start();
        mHandler = new RNSpeechInternalHandler(this.getLooper());
    }

    public Handler getHandler() {
        return mHandler;
    }

    static class RNSpeechInternalHandler extends Handler {
        public RNSpeechInternalHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            SpeechRegister.getInstance().handleListeners(msg.what, msg.arg1, msg.obj);
        }
    }
}
