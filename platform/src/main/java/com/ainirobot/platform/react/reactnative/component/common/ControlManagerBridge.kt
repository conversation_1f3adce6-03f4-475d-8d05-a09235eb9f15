/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.react.reactnative.component.common

import com.ainirobot.platform.PlatformDef
import com.ainirobot.platform.react.client.RNClientManager
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import org.json.JSONException
import org.json.JSONObject


class ControlManagerBridge(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String {
        return "ControlManager"
    }

    @ReactMethod
    fun switchCharacter(characterName: String) {
        try {
            val json = JSONObject()
            json.put("name", characterName)
            RNClientManager.instance?.intentManager?.intentDispatch(0, PlatformDef.SWITCH_CHARACTER, "", json.toString())
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

}