package com.ainirobot.platform.react.reactnative;

import android.os.Handler;
import android.os.HandlerThread;

import java.util.Timer;
import java.util.TimerTask;
import android.util.Log;

public class DumpMemManager {
    private volatile static DumpMemManager mInstance;
    private static final String RN_PROCESS_NAME="com.ainirobot.moduleapp:sandbox";
    private boolean mIsAllowed;
    private Timer mDumpTimer;
    private DumpMemInfo mDumpMem;
    private int mInterval;
    private Handler mHandler;

    public static DumpMemManager getInstance() {
        if (mInstance == null) {
            synchronized (DumpMemManager.class) {
                if (mInstance == null) {
                    mInstance = new DumpMemManager();
                }
            }
        }
        return mInstance;
    }
    private DumpMemManager(){}

    public void init(){
        Log.v("caixj","DumpMemManger init");
        mIsAllowed=true;
        mDumpMem=new DumpMemInfo();
        HandlerThread ht=new HandlerThread("dump-mem");
        ht.start();
        mHandler=new Handler(ht.getLooper());
    }

    public void dumpMem(final String scene, final String view){
        Log.v("caixj","scene: "+scene+"  view: "+view);
        if(!mIsAllowed)
            return;
        cancelDumpTimer();
        System.gc();
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                startDumpMem(mInterval,scene,view);
            }
        },2000);
    }

    public void startDumpMem(int interval, final String scene, final String view){
        Log.v("caixj","scene: "+scene+"  view: "+view +" interval: "+interval);
        mIsAllowed=true;
        mInterval=interval;
        cancelDumpTimer();
        if(interval<1)
            interval=1;
        mDumpTimer=new Timer();
        mDumpTimer.schedule(new TimerTask() {
            @Override
            public void run() {
               mDumpMem.setCurrentApp(scene,view);
               mDumpMem.writeMemInfo(RN_PROCESS_NAME);
            }
        },0,interval*60*1000);
    }

    public void cancelDumpTimer() {
        Log.v("caixj","cancelDumpTimer");
        if (mDumpTimer != null) {
            mIsAllowed=false;
            mDumpTimer.cancel();
            mDumpTimer=null;
        }
    }
}
