package com.ainirobot.platform.react.reactnative.component.uicomponent.statusbar;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Bundle;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.R;
import com.ainirobot.platform.utils.SettingDataHelper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.ref.WeakReference;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Mini机型-StatusBar
 */
public class StatusBarMini extends BaseStatusBar {

    private static final String TAG = StatusBar.class.getSimpleName();

    private static volatile WeakReference<StatusBarMini> mStatusBarMiniRef;
    private TextView mTvSystemTime;
    private TextView mTvBatteryData;
    private ImageView mImBatteryCharge;
    private ImageView mImWifi;
    private ImageView mImPhoneSignal;
    private ImageView mImExposureStatus;
    private ImageView mRobotIcon; // 多机图标
    private TextView mMultiRobotNumber; // 机器台数
    private LinearLayout mRobotInfoLayout;

    private List<MultiRobotStatus> mRobotStatusList;

    public static StatusBarMini getInstance() {
        StatusBarMini instance = null;
        if (mStatusBarMiniRef != null) {
            instance = mStatusBarMiniRef.get();
        }
        if (instance == null) {
            synchronized (StatusBarMini.class) {
                if (mStatusBarMiniRef == null || mStatusBarMiniRef.get() == null) {
                    instance = new StatusBarMini();
                    mStatusBarMiniRef = new WeakReference<>(instance);
                }
            }
        }
        return instance;
    }

    @Override
    public View getStatusBarView() {
        return LayoutInflater.from(
                        new ContextThemeWrapper(BaseApplication.getApplication(), R.style.AppTheme))
                .inflate(R.layout.statusbar_mini, null, false);
    }

    @Override
    public View getStatusBarAnother() {
        return null;
    }

    @Override
    public void initStatusBarLayout() {
        mTvSystemTime = mStatusBarView.findViewById(R.id.tv_system_time);
        mTvBatteryData = mStatusBarView.findViewById(R.id.tv_battery_data);
        mImBatteryCharge = mStatusBarView.findViewById(R.id.icon_battery_charge);
        mImWifi = mStatusBarView.findViewById(R.id.icon_wifi);
        mImPhoneSignal = mStatusBarView.findViewById(R.id.icon_phone_signal);
        mImExposureStatus = mStatusBarView.findViewById(R.id.icon_exposure_status);
        mMultiRobotNumber = mStatusBarView.findViewById(R.id.multi_robot_number);
        mRobotIcon = mStatusBarView.findViewById(R.id.robot_icon);
        mRobotInfoLayout = mStatusBarView.findViewById(R.id.ll_robot_info);
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void updateBatteryInfo(Intent intent) {
        if (mImBatteryCharge == null || mTvBatteryData == null) {
            return;
        }
        int level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0);
        int plugged = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0);

        mImBatteryCharge.setImageLevel(level * 100);
        mImBatteryCharge.setImageState(plugged != 0 ? BATTERY_PLUGGED : null, true);
        mTvBatteryData.setText(level + "%");
    }

    @Override
    public void updateWifiConnectionInfo() {
        WifiInfo wifiInfo = getWifiInfo();
        if (isWifiConnected(wifiInfo)) {
            if (wifiInfo != null) {
                mImWifi.setImageLevel(WifiManager.calculateSignalLevel(wifiInfo.getRssi(), 4));
                mImPhoneSignal.setVisibility(View.GONE);
            } else {
                mImWifi.setImageLevel(4);
            }
        } else {
            mImWifi.setVisibility(View.GONE);
        }
    }

    @Override
    public void updateMobileConnectionInfo() {
        if (mTelephonyManager == null || mImPhoneSignal == null) {
            return;
        }
        if (isWifiConnected(getWifiInfo())) {
            mImPhoneSignal.setVisibility(View.GONE);
            return;
        }
        int simState = mTelephonyManager.getSimState();
        updateMobileNetworkTypeImage(simState);
        if (simState == TelephonyManager.SIM_STATE_READY) {
            mImPhoneSignal.setVisibility(View.VISIBLE);
            mImPhoneSignal.setImageLevel((mMobileSignalStrength != null) ? mMobileSignalStrength.getLevel() : 0);
        } else {
            mImPhoneSignal.setVisibility(View.VISIBLE);
            mImPhoneSignal.setImageState(SIM_STATE_NO_SERVICE, true);
        }
    }

    @Override
    public void updateTime() {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm", Locale.CHINA);
        Date date = new Date(System.currentTimeMillis());
        String curDate = formatter.format(date);
        mTvSystemTime.setText(curDate);
        DelayTask.submit(new Runnable() {
            @Override
            public void run() {
                if (mActivity.get() != null) {
                    mActivity.get().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            updateTime();
                        }
                    });
                }
            }
        }, 2000);
    }

    @Override
    public void updateExposureStatus(String data) {
        Log.d(TAG, "updateExposureStatus" + data);
        switch (data) {
            case "under_exposure":
                mImExposureStatus.setImageLevel(1);
                break;
            case "over_exposure":
                mImExposureStatus.setImageLevel(2);
                break;
            case "normal":
            default:
                mImExposureStatus.setImageLevel(0);
                break;
        }
    }

    void updateMobileNetworkTypeImage(int simState) {
        if (mTelephonyManager == null) {
            return;
        }
        if (simState != TelephonyManager.SIM_STATE_READY) {
            mImPhoneSignal.setVisibility(View.GONE);
            mImPhoneSignal.setImageState(null, true);
            return;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (!mTelephonyManager.isDataEnabled()) {
                mImPhoneSignal.setVisibility(View.VISIBLE);
                mImPhoneSignal.setImageState(SIM_STATE_NO_DATA, true);
                return;
            }
        } else {
            if (mTelephonyManager.getDataState() == TelephonyManager.DATA_DISCONNECTED) {
                mImPhoneSignal.setVisibility(View.VISIBLE);
                mImPhoneSignal.setImageState(SIM_STATE_NO_DATA, true);
                return;
            }
        }
        final int networkType = mTelephonyManager.getNetworkType();
        switch (networkType) {
            case TelephonyManager.NETWORK_TYPE_GPRS:
            case TelephonyManager.NETWORK_TYPE_EDGE:
            case TelephonyManager.NETWORK_TYPE_CDMA:
            case TelephonyManager.NETWORK_TYPE_1xRTT:
            case TelephonyManager.NETWORK_TYPE_IDEN:
                mImPhoneSignal.setVisibility(View.VISIBLE);
                mImPhoneSignal.setImageState(SIM_STATE_2G, true);
                break;
            case TelephonyManager.NETWORK_TYPE_UMTS:
            case TelephonyManager.NETWORK_TYPE_EVDO_0:
            case TelephonyManager.NETWORK_TYPE_EVDO_A:
            case TelephonyManager.NETWORK_TYPE_HSDPA:
            case TelephonyManager.NETWORK_TYPE_HSUPA:
            case TelephonyManager.NETWORK_TYPE_HSPA:
            case TelephonyManager.NETWORK_TYPE_EVDO_B:
            case TelephonyManager.NETWORK_TYPE_EHRPD:
            case TelephonyManager.NETWORK_TYPE_HSPAP:
                mImPhoneSignal.setVisibility(View.VISIBLE);
                mImPhoneSignal.setImageState(SIM_STATE_3G, true);
                break;
            case TelephonyManager.NETWORK_TYPE_LTE:
                mImPhoneSignal.setVisibility(View.VISIBLE);
                mImPhoneSignal.setImageState(SIM_STATE_4G, true);
                break;
            default:
                mImPhoneSignal.setVisibility(View.VISIBLE);
                mImPhoneSignal.setImageState(SIM_STATE_NO_SERVICE, true);
                break;
        }
    }

    public void handleMultipleRobotStatus(String eventData) {
        if (TextUtils.isEmpty(eventData)) {
            updateMultiRobotUI(0);
            return;
        }
        try {
            Type dataType = new TypeToken<List<MultiRobotStatus>>() {}.getType();
            List<MultiRobotStatus> mOtherRobotStatusList = new Gson().fromJson(eventData, dataType);
            int otherRobotNum = (mOtherRobotStatusList != null) ? mOtherRobotStatusList.size() : 0;
            System.out.println("++++++++otherRobotNum+++++++++" + otherRobotNum);
            updateMultiRobotUI(otherRobotNum);
        } catch (Exception e) {
            e.printStackTrace();
            // 可以添加日志记录
            updateMultiRobotUI(0);
        }
    }

    private void updateMultiRobotUI(final int otherRobotNum) {
        mStatusBarView.post(new Runnable() {
            @Override
            public void run() {
                if (otherRobotNum == 0) {
                    mRobotInfoLayout.setVisibility(View.GONE);
                } else {
                    mRobotInfoLayout.setVisibility(View.VISIBLE);
                    mMultiRobotNumber.setText(String.valueOf(otherRobotNum));
                }
            }
        });
    }

    @Override
    public void onStart(Activity activity) {
        super.onStart(activity);
        // 注册多机状态广播监听
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Definition.INTENT_MULTI_ROBOT_STATUS_UPDATE);
        BaseApplication.getContext().registerReceiver(mMultiRobotReceiver, intentFilter);

        int voiceMode = SettingDataHelper.getInstance().getRobotSettingAmbientLightSwitch();
        mImExposureStatus.setVisibility(voiceMode == 1 ? View.VISIBLE : View.GONE);
    }

    @Override
    public void onStop() {
        super.onStop();
        // 取消注册多机状态广播监听
        BaseApplication.getContext().unregisterReceiver(mMultiRobotReceiver);
    }

    private final BroadcastReceiver mMultiRobotReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null && Definition.INTENT_MULTI_ROBOT_STATUS_UPDATE.equals(intent.getAction())) {
                Bundle bundle = intent.getExtras();
                if (bundle != null) {
                    String eventData = bundle.getString("data");
                    System.out.println("++++++++eventData+++++++++" + eventData);
                    handleMultipleRobotStatus(eventData);
                } else {
                    // 增加日志记录
                    Log.d(TAG, "Received intent with no extras");
                }
            } else {
                // 增加日志记录
                Log.d(TAG, "Received unknown intent action: " + (intent != null ? intent.getAction() : "null"));
            }
        }
    };
}
