package com.ainirobot.platform.react.reactnative.component.uicomponent.recognitionview

import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import com.ainirobot.platform.PlatformDef
import com.ainirobot.platform.bean.RecognitionAnimationData
import com.facebook.react.bridge.ReadableArray
import com.facebook.react.common.MapBuilder
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.UIManagerModule
import com.facebook.react.uimanager.annotations.ReactProp

/**
 * @date: 2019-04-08
 * @author: haohong<PERSON>
 * @desc: 封装回显
 */
@ReactModule(name = RecognitionViewManager.REACT_CLASS)
class RecognitionViewManager : SimpleViewManager<RNRecognitionView>() {
    var TAG:String = "RecognitionViewManager"
    var animationDataType:String = PlatformDef.SKILL_CHAT_NAME
    var animationHeaderText:String=""
    var showGuideAnimation: Boolean = true
    var textStringArray: List<String>? = null
    var aniData :RecognitionAnimationData? = null
    var showBottomWaveAnimation:Boolean = true
    var hideView:Boolean = false
    companion object {
        const val REACT_CLASS = "RecognitionView"
    }

    override fun getName(): String {
        return REACT_CLASS
    }

    override fun createViewInstance(reactContext: ThemedReactContext): RNRecognitionView {
        return createRecognitionViewInstance(reactContext)
    }

    override fun getExportedCustomDirectEventTypeConstants(): MutableMap<String, Any> {
        return MapBuilder.of(
                RNRecognitionViewEvent.EVENT_NAME, MapBuilder.of("registrationName", RNRecognitionViewEvent.EVENT_NAME)
        )
    }

    override fun addEventEmitters(reactContext: ThemedReactContext, view: RNRecognitionView) {
        super.addEventEmitters(reactContext, view)
        Log.i(TAG, "addEventEmitters")

//        view?.setPreviewListener { state ->
//            Log.i(TAG, "addEventEmitters setPreviewListener")
//            reactContext
//                    .getNativeModule(UIManagerModule::class.java)
//                    .eventDispatcher
//                    .dispatchEvent(PreviewEvent(view.id, state))
//        }
        view?.setRNRecognitionViewListener(object : RNRecognitionView.RNRecognitionViewEventListener{
            override fun onClick(){
                Log.i(TAG, "RecognitionViewManager: onClick");
                //ReactNativeEventEmitter.triggerEvent("RNRECOGNITIONVIEW_EVENT", "{'msg':'onClick'}")
                reactContext
                        .getNativeModule(UIManagerModule::class.java)
                        ?.eventDispatcher
                        ?.dispatchEvent(RNRecognitionViewEvent(view.id))

            }
        })

    }

    private fun createRecognitionViewInstance(reactContext: ThemedReactContext): RNRecognitionView {
        val view = RNRecognitionView(reactContext);
        return view
    }

    @ReactProp(name = "showGuideAnimation")
    fun setshowGuideAnimation(view: RNRecognitionView, isShow: Boolean) {
        Log.i("showGuideAnimation","$isShow")
        showGuideAnimation = isShow
    }

    @ReactProp(name = "animationDataType")
    fun setanimationDataType(view: RNRecognitionView, text: String) {
        animationDataType = text

    }

    @ReactProp(name = "animationDataString")
    fun setanimationDataString(view: RNRecognitionView, textArray: ReadableArray) {
        textStringArray = (textArray.toArrayList() as List<String>)
    }

    @ReactProp(name = "animationHeaderText")
    fun setanimationHeaderText(view: RNRecognitionView, text: String) {
        animationHeaderText = text
    }

    @ReactProp(name = "showRecognitionView")
    fun setshowRecognitionView(view: RNRecognitionView, isShow: Boolean) {
        showBottomWaveAnimation = isShow
    }

    @ReactProp(name = "hide")
    fun setHideView(view: RNRecognitionView, hide: Boolean) {
        if(hide != hideView){
            view.clearRecognitionText()
        }
        hideView = hide
    }

    override fun onAfterUpdateTransaction(view: RNRecognitionView) {
        super.onAfterUpdateTransaction(view)
        Handler(Looper.getMainLooper()).post{
            view.setGuideAnimationConfig(true);
            if(textStringArray != null) {
                view.setQueryData(textStringArray, animationHeaderText)
            }else{
                var dataType = RecognitionAnimationData(animationDataType, animationHeaderText)
                view.setQueryDataForServer(dataType)
            }
            view.showGuideAnimation(showGuideAnimation);
            if(hideView == false) {
                view.setVisibility(View.VISIBLE)
                if (showBottomWaveAnimation == false) {
                    view.hideBottomWaveAnimation()
                } else {
                    view.showBottomWaveAnimation()
                }
            }else{
                view.setVisibility(View.INVISIBLE)
                view.hideBottomWaveAnimation()
            }
        }
    }

    override fun onDropViewInstance(view: RNRecognitionView) {
        super.onDropViewInstance(view)
        view.hideBottomWaveAnimation()
    }

}
