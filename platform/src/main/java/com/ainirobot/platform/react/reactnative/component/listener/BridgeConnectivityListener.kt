package com.ainirobot.platform.react.reactnative.component.listener

import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNConnectivityListener
import com.facebook.react.bridge.WritableNativeMap

class BridgeConnectivityListener : IRNConnectivityListener.Stub {

    companion object {
        fun obtain(callbackId:Int): BridgeConnectivityListener? {
            return BridgeConnectivityListener(callbackId)
        }
    }

    var id = -1

    private constructor(id: Int) {
        this.id = id
    }

    override fun onConnectStateChanged(networkType: String?, simState: String?,
                                       mobileType: String?, mobileConnect: Boolean) {
        triggerEvent("onMobileTypeChanged",networkType,simState,mobileType,mobileConnect)
    }

    fun triggerEvent(event: String,networkType: String?, simState: String?,
                     mobileType: String?, mobileConnect: Boolean) {
        if (id < 0) {
            return
        }
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putString("event", event)
        networkType?.apply { readableMap.putString("networkType", networkType) }
        simState?.apply { readableMap.putString("simState", simState) }
        mobileType?.apply { readableMap.putString("type", mobileType) }
        mobileConnect.apply { readableMap.putBoolean("mobileConnect", mobileConnect) }
        ReactNativeEventEmitter.triggerEvent("onConnectivityListener", readableMap)
    }
}