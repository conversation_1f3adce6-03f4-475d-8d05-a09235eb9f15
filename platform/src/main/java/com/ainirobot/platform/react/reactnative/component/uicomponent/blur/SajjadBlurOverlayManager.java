package com.ainirobot.platform.react.reactnative.component.uicomponent.blur;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.SystemClock;
import androidx.appcompat.widget.ContentFrameLayout;
import android.util.Log;
import android.view.View;

import com.facebook.react.ReactRootView;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.ViewGroupManager;
import com.facebook.react.uimanager.annotations.ReactProp;
import com.facebook.react.views.view.ReactViewGroup;

import javax.annotation.Nonnull;

public class SajjadBlurOverlayManager extends ViewGroupManager<SajjadBlurOverlayManager.BlurView> {

    private static final String REACT_CLASS = "RCTSajjadBlurOverlay";
    private final ReactApplicationContext reactContext;
    private int mRadius = 20;
    private float mBrightness = 0;
    private boolean hasFaceParticle = true;

    SajjadBlurOverlayManager(ReactApplicationContext reactContext) {
        this.reactContext = reactContext;
    }

    @Override
    public String getName() {
        return REACT_CLASS;
    }

    @Override
    protected BlurView createViewInstance(ThemedReactContext reactContext) {
        return new BlurView(reactContext);
    }

    public static class BlurView extends ReactViewGroup {

        private static Drawable storedBlurBg = null;

        public BlurView(Context context) {
            super(context);
        }

        public static void storeBlurBg(Drawable drawable) {
            Log.i("blur", "BlurView storeBlurBg drawable != null " + (drawable != null));
            storedBlurBg = drawable;
        }

        public static Drawable getStoredBlurBg() {
            Log.i("blur", "BlurView getStoredBlurBg != null " + (storedBlurBg != null));
            return storedBlurBg;
        }

        public void destory() {
            Log.i("blur", "BlurView destory");
            storedBlurBg = null;
        }
    }

    @Override
    protected void onAfterUpdateTransaction(@Nonnull final SajjadBlurOverlayManager.BlurView blurView) {
        super.onAfterUpdateTransaction(blurView);

        try {
            final Activity activity = reactContext.getCurrentActivity();

            if (activity == null) return;

            Drawable bg = blurView.getStoredBlurBg();
            if (bg == null) {
                View[] views = findViews(activity);
                Log.i("blur", "tryStartTask");
                BlurTask task = BlurTask.createNewBlurTask(blurView, views, reactContext, activity, this.hasFaceParticle, mRadius);
                if (task == null) {
                    Log.i("blur", "waitingTask");
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            boolean isEnd = false;
                            while (!isEnd) {
                                final Drawable blurBg = blurView.getStoredBlurBg();
                                if (blurBg == null) {
                                    SystemClock.sleep(10);
                                } else {
                                    Log.i("blur", "setBackgroundDrawable");
                                    blurView.post(new Runnable() {
                                        @Override
                                        public void run() {
                                            Log.i("blur", "drawable == null " + (blurBg == null));
                                            blurView.setBackgroundDrawable(blurBg);
                                        }
                                    });
                                    isEnd = true;
                                }
                            }
                        }
                    }).start();
                } else {
                    task.execute();
                }
            } else {
                Log.i("blur", "setBackgroundDrawable");
                Log.i("blur", "drawable == null " + (bg == null));
                blurView.setBackgroundDrawable(bg);
            }

            blurView.requestFocus();
        } catch (Exception e) {
            e.printStackTrace();
            Log.e("blur", "blurManager " + e);
        }
    }

    private View[] findViews(Activity activity) {
        ContentFrameLayout root = (ContentFrameLayout) activity.getWindow().findViewById(android.R.id.content);
        ReactRootView rnRoot = (ReactRootView) root.getChildAt(0);
        View[] views = new View[2];
        views[0] = rnRoot.getChildAt(0);
        for (int i = 1; i < rnRoot.getChildCount(); i++) {
            if (rnRoot.getChildAt(i) instanceof ReactViewGroup) {
                views[1] = rnRoot.getChildAt(i);
                break;
            }
        }

        return views;
    }

    @Override
    public void onDropViewInstance(@Nonnull SajjadBlurOverlayManager.BlurView view) {
        super.onDropViewInstance(view);
        view.destory();
    }

    @ReactProp(name = "radius")
    public void setRadius(BlurView view, int Radius) {
        mRadius = Radius;
    }

    @ReactProp(name = "hasFaceParticle")
    public void setHasFaceParticle(BlurView view, boolean hasFaceParticle) {
        Log.e("blur", "hasFaceParticle " + hasFaceParticle);
        this.hasFaceParticle = hasFaceParticle;
    }

    @ReactProp(name = "brightness")
    public void setBrightness(BlurView view, float brightness) {
        mBrightness = brightness;
    }
}
