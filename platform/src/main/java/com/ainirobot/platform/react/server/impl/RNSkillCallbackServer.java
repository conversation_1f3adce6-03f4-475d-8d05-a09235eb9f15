package com.ainirobot.platform.react.server.impl;

import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.speech.SkillCallback;
import com.ainirobot.platform.react.server.utils.RNServerUtils;
import com.ainirobot.platform.rn.IRNSpeechCallBack;

public class RNSkillCallbackServer extends SkillCallback {

    public static final String TAG = RNSkillCallbackServer.class.getSimpleName();

    private IRNSpeechCallBack mSpeechCallback;

    @Override
    public void onSpeechParResult(String s) {
        try {
//            Log.d(TAG, "onSpeechParResult s: " + s);
            if (mSpeechCallback == null) {
                Log.e(TAG, "onSpeechParResult speechCallback null");
                return;
            }
            mSpeechCallback.onSpeechParResult(s);
        } catch (RemoteException e) {
            RNServerUtils.handleRemoteException("onSpeechParResult", e);
        }
    }

    @Override
    public void onStart() {
        try {
//            Log.d(TAG, "onStart");
            if (mSpeechCallback == null) {
                Log.e(TAG, "onStart speechCallback null");
                return;
            }
            mSpeechCallback.onRecognitionStart();
        } catch (RemoteException e) {
            RNServerUtils.handleRemoteException("onRecognitionStart", e);
        }
    }

    @Override
    public void onStop() {
        try {
//            Log.d(TAG, "onStop");
            if (mSpeechCallback == null) {
                Log.e(TAG, "onStop speechCallback null");
                return;
            }
            mSpeechCallback.onRecognitionStop();
        } catch (RemoteException e) {
            RNServerUtils.handleRemoteException("onRecognitionStop", e);
        }
    }

    @Override
    public void onVolumeChange(int i) {
    }

    @Override
    public void onQueryEnded(int i) {
        try {
            if (mSpeechCallback == null) {
                Log.e(TAG, "onQueryEnded speechCallback null");
                return;
            }
            mSpeechCallback.onQueryEnded(i);
        } catch (RemoteException e) {
            RNServerUtils.handleRemoteException("onQueryEnded", e);
        }
    }

    @Override
    public void onQueryAsrResult(String asrResult) {
        try {
//            Log.d(TAG, "onQueryAsrResult asrResult: " + asrResult);
            if (mSpeechCallback == null) {
                Log.e(TAG, "onQueryAsrResult speechCallback null");
                return;
            }
            mSpeechCallback.onQueryAsrResult(asrResult);
        } catch (RemoteException e) {
            RNServerUtils.handleRemoteException("onQueryAsrResult", e);
        }
    }

    @Override
    public void onError(String sid, int code, String message) {
        try {
            Log.d(TAG, "onError");
            if (mSpeechCallback == null) {
                Log.e(TAG, "onError speechCallback null");
                return;
            }
            mSpeechCallback.onRecognitionError(sid, code, message);
        } catch (RemoteException e) {
            RNServerUtils.handleRemoteException("onRecognitionError", e);
        }
    }

    public String onGetMultipleModeInfos(int index) {
        return null;
    }

    @Override
    public void onSpeechStreamData(String data) throws RemoteException {
        try {
            if (mSpeechCallback == null) {
                Log.e(TAG, "onSpeechStreamData speechCallback null");
                return;
            }
            Log.d(TAG, "onSpeechStreamData :" + data);
            mSpeechCallback.onSpeechStreamData(data);
        } catch (RemoteException e) {
            RNServerUtils.handleRemoteException("onSpeechStreamData", e);
        }
    }

    public void updateSpeechCallback(IRNSpeechCallBack callBack) {
        Log.d(TAG, "updateSpeechCallback callBack: " + callBack);
        mSpeechCallback = callBack;
    }
}
