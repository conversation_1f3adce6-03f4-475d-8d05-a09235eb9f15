/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.platform.react.reactnative.component.skillcomponent

import com.ainirobot.platform.component.HeadTurnGroupComponent
import com.ainirobot.platform.react.server.utils.ComponentUtils
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactMethod

class HeadTurnGroupComponentBridge(reactContext: ReactApplicationContext) :
        BaseComponentBridge<HeadTurnGroupComponent>(reactContext) {

    override fun isHDMonopoly(): Boolean {
        // 主要用于小动作，rn层任务它独占
        return true
    }

    override fun getResouceType(): Array<ResouceType> {
        return arrayOf(ResouceType.HeadTurn)
    }

    override fun getName(): String {
        return ComponentUtils.COMPONENT_HEAD_TURN_GROUP
    }

    @ReactMethod
    override fun start(uid: String, param: String?) {
        super._start(uid, param)
    }

    @ReactMethod
    override fun stop(uid: String, timeout: Int?) {
        super._stop(uid, timeout)
    }

    @ReactMethod
    override fun updateParams(uid: String, intent: String, param: String) {
        super._updateParams(uid, intent, param)
    }

    @ReactMethod
    override fun setStatusListener(uid: String, statusListenerId: Int) {
        super._setStatusListener(uid, statusListenerId)
    }

    @ReactMethod
    override fun setFinishListener(uid: String, finishListenerId: Int) {
        super._setFinishListener(uid, finishListenerId)
    }

}

