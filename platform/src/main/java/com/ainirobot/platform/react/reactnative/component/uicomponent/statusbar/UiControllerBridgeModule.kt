package com.ainirobot.platform.react.reactnative.component.uicomponent.statusbar

import android.util.Log
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod

class UiControllerBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    companion object {
        const val TAG = "UiController"
    }

    override fun getName(): String {
        return "UiController"
    }

    @ReactMethod
    fun showStatusBar(isShow: Boolean) {
        Log.d(TAG, "showStatusBar isShow: $isShow")

        reactApplicationContext.runOnUiQueueThread {
            StatusBarManager.getStatusBar()?.showStatusBar(isShow)
        }
    }

    @ReactMethod
    fun showStatusBarByType(isShow: Boolean, type: Int) {
        Log.d(TAG, "showStatusBar isShow: $isShow, type: $type")

        reactApplicationContext.runOnUiQueueThread {
            StatusBarManager.getStatusBar()?.showStatusBarByType(isShow, type)
        }
    }
}
