package com.ainirobot.platform.react.reactnative.component.uicomponent.clipimage

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.media.MediaMetadataRetriever
import android.os.AsyncTask
import android.util.Log
import android.widget.ImageView
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.R
import com.ainirobot.platform.utils.CommonUtil
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.animation.GlideAnimation
import com.bumptech.glide.request.target.SimpleTarget
import java.util.HashMap

class ClipIconImageView(context: Context?) : ImageView(context) {

    companion object {
        const val TAG = "ClipIconImageView"
    }

    fun setImageViewUrl(src: String?, targetWidth: Int = 380, targetHeight: Int = 570) {
        //设置图片
        Glide
                .with(BaseApplication.getContext())
                .load(src)
                .asBitmap()
                .placeholder(R.drawable.chat_place_holder)
                .error(R.drawable.chat_error)
                .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                .skipMemoryCache(true)
                .crossFade(100)
                .into(object : SimpleTarget<Bitmap>() {

                    override fun onLoadStarted(placeholder: Drawable?) {
                        setImageDrawable(placeholder)
                    }

                    override fun onLoadFailed(e: Exception?, errorDrawable: Drawable?) {
                        setImageDrawable(errorDrawable)
                    }

                    override fun onResourceReady(resource: Bitmap, glideAnimation: GlideAnimation<in Bitmap>) {
                        val bitmap = changeBitmapWidthAndHeight(resource, targetWidth, targetHeight);
                        if (bitmap != null) {
                            setImageBitmap(bitmap)
                        }
                    }
                })
    }

    private fun changeBitmapWidthAndHeight(resource: Bitmap?, targetWidth: Int, targetHeight: Int): Bitmap? {
        if (resource == null || resource.isRecycled) {
            Log.d(TAG, "resource is recycle")
            setImageResource(R.drawable.chat_error)
            return null
        }
        //宽度和高度缩放
        val width = resource.width.toFloat()
        val height = resource.height.toFloat()
        val oriRatio = width / height
        val targetRatio = targetWidth / targetHeight.toFloat()
        Log.i(TAG, "width = " + width + ",height = " + height + "oriRatio = "
                + oriRatio + ",targetRatio = " + targetRatio)
        val scaleWidth: Int
        val scaleHeight: Int
        if (oriRatio > targetRatio) {
            //以高度进行缩放
            scaleHeight = targetHeight
            scaleWidth = (width * scaleHeight / height).toInt()
        } else {
            //以宽度进行缩放
            scaleWidth = targetWidth
            scaleHeight = (scaleWidth * height / width).toInt()
        }

        Log.i(TAG, "scaleWidth = $scaleWidth,scaleHeight = $scaleHeight")
        val scaleBitmap = CommonUtil.scaleBitmap(resource, scaleWidth, scaleHeight)
        //中间裁剪
        var startX = (scaleWidth - targetWidth) / 2
        var startY = (scaleHeight - targetHeight) / 2
        Log.i(TAG, "startX = $startX,startY = $startY")
        if (startX < 0) {
            startX = 0
        }
        if (startY < 0) {
            startY = 0
        }
        return Bitmap.createBitmap(
                scaleBitmap, startX, startY, targetWidth, targetHeight)
    }

    /**
     * 加载视频的第一帧
     */
    fun setImageViewVideoUrl(src: String?, targetWidth: Int, targetHeight: Int) {
        VideoClipAsyncTask(imageView = this, targetWidth = targetWidth, targetHeight = targetHeight).execute(src)
    }

    class VideoClipAsyncTask(imageView: ClipIconImageView, targetWidth: Int, targetHeight: Int) : AsyncTask<String, Void, Bitmap>() {

        var imageView:ClipIconImageView = imageView
        var targetWidth: Int = targetWidth
        var targetHeight: Int = targetHeight

        override fun doInBackground(vararg params: String?): Bitmap? {
            Log.d(TAG, "params[0]=${params[0]}")
            return getNetVideoBitmap(params[0])
        }

        private fun getNetVideoBitmap(videoUrl: String?): Bitmap? {
            var bitmap: Bitmap? = null
            val retriever = MediaMetadataRetriever()
            try {
                //根据url获取缩略图
                retriever.setDataSource(videoUrl, HashMap())
                //获得第一帧图片
                bitmap = retriever.frameAtTime
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            } finally {
                retriever.release()
            }
            return bitmap
        }

        override fun onPostExecute(result: Bitmap?) {
            val bitmap = this.imageView.changeBitmapWidthAndHeight(result, targetWidth, targetHeight)
            this.imageView.setImageBitmap(bitmap)
        }
    }
}
