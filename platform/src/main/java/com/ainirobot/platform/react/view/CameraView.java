/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.platform.react.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.ImageFormat;
import android.graphics.Matrix;
import android.graphics.Rect;
import android.graphics.SurfaceTexture;
import android.hardware.camera2.CameraAccessException;
import android.hardware.camera2.CameraCaptureSession;
import android.hardware.camera2.CameraCharacteristics;
import android.hardware.camera2.CameraDevice;
import android.hardware.camera2.CameraManager;
import android.hardware.camera2.CameraMetadata;
import android.hardware.camera2.CaptureFailure;
import android.hardware.camera2.CaptureRequest;
import android.hardware.camera2.TotalCaptureResult;
import android.media.Image;
import android.media.ImageReader;
import android.os.Environment;
import androidx.annotation.RequiresApi;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.util.Size;
import android.util.SparseIntArray;
import android.view.Surface;
import android.view.TextureView;
import android.widget.Toast;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.platform.R;
import com.ainirobot.platform.bean.ResponseState;
import com.ainirobot.platform.utils.ReceptionFileUtils;
import com.ainirobot.platform.utils.Utils;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Created by Orion on 2018/8/7.
 */

public class CameraView extends TextureView {

    private static final String TAG = "CameraView";
    private Context mContext;
    private String mCameraId = "0";//摄像头id（通常0代表后置摄像头，1代表前置摄像头）
    private static final int RESULT_CODE_CAMERA = 1;//判断是否有拍照权限的标识码
    private CameraDevice mCameraDevice;
    private CameraCaptureSession mPreviewSession;
    private CaptureRequest.Builder mCaptureRequestBuilder, captureRequestBuilder;
    private CaptureRequest mCaptureRequest;
    private ImageReader imageReader;
    private int mHeight = 1200, mWidth = 1200;
    private Size previewSize;
    private static final SparseIntArray ORIENTATIONS = new SparseIntArray();
    private int mDeviceOrientation = 0;
    private boolean mTakePictureMirror = false;
    private boolean mPreviewMirror = false;

    private CaptureListener mListener;

    public interface CaptureListener {
        //0开始 1成功 2失败（没有内存卡）
        void onCaptureState(ResponseState state, Bitmap bitmap, String saveFile);
    }

    public void setCaptureListener(CaptureListener listener) {
        mListener = listener;
    }

    private CaptureForRnListener mForRnListener;

    public interface CaptureForRnListener {
        void onCaptureState(ResponseState state, String originalImageFile, String compressedFile);
    }

    public void setCaptureForRnListener(CaptureForRnListener listener) {
        mForRnListener = listener;
    }

    private PreviewListener mPreviewListener;

    public interface PreviewListener {
        void onPreviewState(ResponseState state);
    }

    public void setPreviewListener(PreviewListener listener) {
        mPreviewListener = listener;
    }

    public CameraView(Context context) {
        super(context);
        init(context);
    }

    public CameraView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public CameraView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public CameraView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context);
    }

    private void init(Context context) {
        this.mContext = context;
        String model = RobotSettings.getProductModel();
        Log.d(TAG, "init ProductModel:" + model);
        if (ProductInfo.isMiniProduct() || ProductInfo.isSaiphPro() || ProductInfo.isMeissa2() || ProductInfo.isAlnilamPro() || ProductInfo.isCarryProduct()) {
            mTakePictureMirror = true;
        } else if (ProductInfo.isLaraForSale()) {
            mDeviceOrientation = 90;
            mTakePictureMirror = true;
            mPreviewMirror = true;
        } else if (ProductInfo.isMeissa1P5()) {
            mPreviewMirror = true;
        } else if (ProductInfo.ProductModel.CM_XIAOMI_BASE.model.equals(model)
                || ProductInfo.ProductModel.CM_XIAOMI_1p1.model.equals(model)) {
            mPreviewMirror = false;
            mDeviceOrientation = 90;
            mTakePictureMirror = true;
            mPreviewMirror = true;
        } else {
            mDeviceOrientation = 90;
            mTakePictureMirror = true;
            mPreviewMirror = true;
        }
        this.setSurfaceTextureListener(mSurfaceTextureListener);
    }

    /**
     * TextureView的监听
     */
    private SurfaceTextureListener mSurfaceTextureListener = new SurfaceTextureListener() {

        //可用
        @Override
        public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {
            Log.i(TAG, "onSurfaceTextureAvailable width:" + width + " height:" + height);
            openCamera();
        }


        @Override
        public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {
            Log.i(TAG, "onSurfaceTextureSizeChanged");
        }

        //释放
        @Override
        public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {
            Log.i(TAG, "onSurfaceTextureDestroyed");
            stopCamera();
            return true;
        }

        //更新
        @Override
        public void onSurfaceTextureUpdated(SurfaceTexture surface) {
            Log.i(TAG, "onSurfaceTextureUpdated");
        }
    };


    /**
     * 打开摄像头
     */
    @SuppressLint("MissingPermission")
    public void openCamera() {
        Log.i(TAG, "openCamera");

        CameraManager manager = (CameraManager) mContext.getSystemService(Context.CAMERA_SERVICE);
        //设置摄像头特性
        setCameraCharacteristics(manager);
        try {
            manager.openCamera(mCameraId, stateCallback, null);
        } catch (CameraAccessException e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置摄像头的参数
     */
    private void setCameraCharacteristics(CameraManager manager) {
        try {
            Log.i(TAG, "setCameraCharacteristics");
            // 获取指定摄像头的特性
            CameraCharacteristics characteristics = manager.getCameraCharacteristics(mCameraId);
//            // 获取摄像头支持的配置属性
//            StreamConfigurationMap map = characteristics
//                    .get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
//            // 获取摄像头支持的最大尺寸
//            Size largest = Collections.max(
//                    Arrays.asList(map.getOutputSizes(ImageFormat.JPEG)), new CompareSizesByArea());
//            // 创建一个ImageReader对象，用于获取摄像头的图像数据
//            imageReader = ImageReader.newInstance(largest.getWidth(), largest.getHeight(),
//                    ImageFormat.JPEG, 2);
//            //设置获取图片的监听
//            imageReader.setOnImageAvailableListener(imageAvailableListener, null);
//            // 获取最佳的预览尺寸
//            previewSize = chooseOptimalSize(map.getOutputSizes(
//                    SurfaceTexture.class), mWidth, mHeight, largest);
            // 创建一个ImageReader对象，用于获取摄像头的图像数据
            imageReader = ImageReader.newInstance(mWidth, mHeight, ImageFormat.JPEG, 2);
            // 设置获取图片的监听
            imageReader.setOnImageAvailableListener(imageAvailableListener, null);
            // 获取最佳的预览尺寸
            previewSize = new Size(mWidth, mHeight);
        } catch (CameraAccessException | NullPointerException e) {
            e.printStackTrace();
        }
    }

    private static Size chooseOptimalSize(Size[] choices
            , int width, int height, Size aspectRatio) {
        // 收集摄像头支持的大过预览Surface的分辨率
        List<Size> bigEnough = new ArrayList<>();
        int w = aspectRatio.getWidth();
        int h = aspectRatio.getHeight();
        for (Size option : choices) {
            if (option.getHeight() == option.getWidth() * h / w &&
                    option.getWidth() >= width && option.getHeight() >= height) {
                bigEnough.add(option);
            }
        }
        // 如果找到多个预览尺寸，获取其中面积最小的
        if (bigEnough.size() > 0) {
            return Collections.min(bigEnough, new CompareSizesByArea());
        } else {
            //没有合适的预览尺寸
            return choices[0];
        }
    }


    // 为Size定义一个比较器Comparator
    static class CompareSizesByArea implements Comparator<Size> {
        @Override
        public int compare(Size lhs, Size rhs) {
            // 强转为long保证不会发生溢出
            return Long.signum((long) lhs.getWidth() * lhs.getHeight() -
                    (long) rhs.getWidth() * rhs.getHeight());
        }
    }


    /**
     * 摄像头状态的监听
     */
    private CameraDevice.StateCallback stateCallback = new CameraDevice.StateCallback() {
        // 摄像头被打开时触发该方法
        @Override
        public void onOpened(CameraDevice cameraDevice) {
            Log.i(TAG, "onOpened");
            mCameraDevice = cameraDevice;
            // 开始预览
            takePreview();
        }

        // 摄像头断开连接时触发该方法
        @Override
        public void onDisconnected(CameraDevice cameraDevice) {
            Log.i(TAG, "onDisconnected");
            previewResult(ResponseState.RESULT_FAILURE);
            if (mCameraDevice != null) {
                mCameraDevice.close();
                mCameraDevice = null;
            }
        }

        // 打开摄像头出现错误时触发该方法
        @Override
        public void onError(CameraDevice cameraDevice, int error) {
            Log.i(TAG, "onError : error=" + error);
            previewResult(ResponseState.RESULT_FAILURE);
            if (mCameraDevice != null) {
                mCameraDevice.close();
            }
        }
    };

    /**
     * 开始预览
     */
    private void takePreview() {
        SurfaceTexture mSurfaceTexture = getSurfaceTexture();
        if (mSurfaceTexture == null) {
            return;
        }
        //设置TextureView的缓冲区大小
        mSurfaceTexture.setDefaultBufferSize(previewSize.getWidth(), previewSize.getHeight());
        //获取Surface显示预览数据
        Surface mSurface = new Surface(mSurfaceTexture);
        try {
            //创建预览请求
            mCaptureRequestBuilder = mCameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW);
            // 设置自动对焦模式
            mCaptureRequestBuilder.set(CaptureRequest.CONTROL_AF_MODE, CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE);
            //设置Surface作为预览数据的显示界面
            mCaptureRequestBuilder.addTarget(mSurface);

            if (mPreviewMirror) {
                setScaleX(-1f);//y轴镜像
            }

            //创建相机捕获会话，第一个参数是捕获数据的输出Surface列表，第二个参数是CameraCaptureSession的状态回调接口，当它创建好后会回调onConfigured方法，第三个参数用来确定Callback在哪个线程执行，为null的话就在当前线程执行
            mCameraDevice.createCaptureSession(Arrays.asList(mSurface, imageReader.getSurface()), new CameraCaptureSession.StateCallback() {
                @Override
                public void onConfigured(CameraCaptureSession session) {
                    Log.i(TAG, "onConfigured");
                    try {
                        //开始预览
                        mCaptureRequest = mCaptureRequestBuilder.build();
                        mPreviewSession = session;
                        //设置反复捕获数据的请求，这样预览界面就会一直有数据显示
                        mPreviewSession.setRepeatingRequest(mCaptureRequest, null, null);
                        previewResult(ResponseState.RESULT_SUCCESS);
                    } catch (CameraAccessException | IllegalStateException e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onConfigureFailed(CameraCaptureSession session) {
                    Log.i(TAG, "onConfigureFailed");
                    previewResult(ResponseState.RESULT_FAILURE);
                }
            }, null);
        } catch (CameraAccessException e) {
            e.printStackTrace();
            previewResult(ResponseState.RESULT_FAILURE);
        }

    }

    private void previewResult(ResponseState responseState) {
        Log.i(TAG, "previewResult : " + responseState);
        if (mPreviewListener != null)
            mPreviewListener.onPreviewState(responseState);
    }


    /**
     * 拍照
     */
    public void takePicture() {
        Log.i(TAG, "takePicture");
        try {
            if (mCameraDevice == null || mPreviewSession == null) {
                return;
            }
            // 创建拍照请求
            captureRequestBuilder = mCameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_STILL_CAPTURE);
            // 设置自动对焦模式
            captureRequestBuilder.set(CaptureRequest.CONTROL_AF_MODE, CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE);
            // 将imageReader的surface设为目标
            captureRequestBuilder.addTarget(imageReader.getSurface());

            // 获取设备方向 -- 机器人无陀螺仪无法获取设备方向
            // int rotation = 0;
            // 根据设备方向计算设置照片的方向 -- 高通845的CamX不支持图片旋转
            // captureRequestBuilder.set(CaptureRequest.JPEG_ORIENTATION, ORIENTATIONS.get(rotation));

            // 停止连续取景
            mPreviewSession.stopRepeating();
            //拍照
            CaptureRequest captureRequest = captureRequestBuilder.build();
            //设置拍照监听
            mPreviewSession.capture(captureRequest, captureCallback, null);
        } catch (CameraAccessException e) {
            e.printStackTrace();
        }
    }

    /**
     * 监听拍照结果
     */
    private CameraCaptureSession.CaptureCallback captureCallback = new CameraCaptureSession.CaptureCallback() {
        // 拍照成功
        @Override
        public void onCaptureCompleted(CameraCaptureSession session, CaptureRequest request, TotalCaptureResult result) {
            Log.i(TAG, "onCaptureCompleted");
            // 重设自动对焦模式
            captureRequestBuilder.set(CaptureRequest.CONTROL_AF_TRIGGER, CameraMetadata.CONTROL_AF_TRIGGER_CANCEL);
            // 设置自动曝光模式
            captureRequestBuilder.set(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON_AUTO_FLASH);
            try {
                //重新进行预览
                mPreviewSession.setRepeatingRequest(mCaptureRequest, null, null);
            } catch (CameraAccessException e) {
                e.printStackTrace();
            }

        }

        @Override
        public void onCaptureFailed(CameraCaptureSession session, CaptureRequest request, CaptureFailure failure) {
            super.onCaptureFailed(session, request, failure);
            Log.i(TAG, "onCaptureFailed");
        }
    };

    /**
     * 监听拍照的图片
     */
    private ImageReader.OnImageAvailableListener imageAvailableListener = new ImageReader.OnImageAvailableListener() {
        // 当照片数据可用时激发该方法
        @Override
        public void onImageAvailable(ImageReader reader) {
            Log.i(TAG, "onImageAvailable");
            //先验证手机是否有sdcard
            String status = Environment.getExternalStorageState();
            if (!status.equals(Environment.MEDIA_MOUNTED)) {
                Toast.makeText(mContext.getApplicationContext(),
                        Utils.getString(R.string.sdcard_disabled),
                        Toast.LENGTH_SHORT)
                        .show();
                return;
            }
            // 获取捕获的照片数据
            Image image = reader.acquireNextImage();
            ByteBuffer buffer = image.getPlanes()[0].getBuffer();
            byte[] data = new byte[buffer.remaining()];
            buffer.get(data);

            //            //手机拍照都是存到这个路径
            //            String filePath = Environment.getExternalStorageDirectory().getPath() + "/DCIM/Camera/";
            //            String picturePath = System.currentTimeMillis() + ".jpg";
            //            File file = new File(filePath, picturePath);
            //                //存到本地相册
            //                FileOutputStream fileOutputStream = new FileOutputStream(file);
            //                fileOutputStream.write(data);
            //                fileOutputStream.close();
            //                Log.i(TAG, "onImageAvailable beginSave");
            //                //显示图片
            //                BitmapFactory.Options options = new BitmapFactory.Options();
            //                options.inSampleSize = 4;
            //                Bitmap bitmap = BitmapFactory.decodeByteArray(data, 0, data.length, options);
            //                String saveFile = FileUtils.saveBitmap(bitmap);
            //                Log.i(TAG, "onImageAvailable saveFile:"+saveFile);
            //                if (mListener != null){
            //                    Bitmap showBitmap = BitmapFactory.decodeByteArray(data, 0, data.length, null);
            //                    mListener.onCaptureState(ResponseState.RESULT_SUCCESS, showBitmap, saveFile);
            //                }
            //                image.close();
            //            Log.i(TAG, "onImageAvailable end save saveFile:"+saveFile);
            Bitmap temp = BitmapFactory.decodeByteArray(data, 0, data.length);
//            Bitmap newBitmap = mTakePictureMirror ? convertBitmap(temp) : temp;
            Bitmap newBitmap = convertBitmap(temp);

            String savefile = null;
            if (mListener != null) {
                savefile = compressBitmap(newBitmap);
                mListener.onCaptureState(ResponseState.RESULT_SUCCESS, newBitmap, savefile);
            }
            // RN 层无法直接使用 bitmap，要通过图片文件来进行显示，
            // 所以需要保留原高清图片供显示使用，然后使用压缩过的图片给注册接口使用
            // 否则 RN 层图片显示会非常模糊
            if (mForRnListener != null) {
                if (TextUtils.isEmpty(savefile)) {
                    savefile = compressBitmap(newBitmap);
                }
                String originalImageFile = ReceptionFileUtils.saveBitmap(newBitmap);
                mForRnListener.onCaptureState(ResponseState.RESULT_SUCCESS, originalImageFile, savefile);
            }
            image.close();
        }

    };

    private Bitmap convertBitmap(Bitmap srcBitmap) {
        int width = srcBitmap.getWidth();
        int height = srcBitmap.getHeight();

        Canvas canvas = new Canvas();
        Matrix matrix = new Matrix();

        if (!mTakePictureMirror) {
            matrix.postScale(-1, 1);
        }
        matrix.postRotate(mDeviceOrientation);

        Bitmap newBitmap = Bitmap.createBitmap(srcBitmap, 0, 0, width, height, matrix, true);

        canvas.drawBitmap(newBitmap,
                new Rect(0, 0, width, height),
                new Rect(0, 0, width, height), null);

        return newBitmap;
    }

    private String compressBitmap(Bitmap bitmap) {
        Matrix matrix = new Matrix();
        matrix.postScale(0.25f, 0.25f);
        Bitmap newBitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
        String saveFile = ReceptionFileUtils.saveBitmap(newBitmap);
        return saveFile;
    }


//    @Override
//    public void onRequestPermissionsResult(int permsRequestCode, String[] permissions, int[] grantResults){
//        switch(permsRequestCode){
//            case RESULT_CODE_CAMERA:
//                boolean cameraAccepted = grantResults[0]==PackageManager.PERMISSION_GRANTED;
//                if(cameraAccepted){
//                    //授权成功之后，调用系统相机进行拍照操作等
//                    openCamera();
//                }else{
//                    //用户授权拒绝之后，友情提示一下就可以了
//                    Toast.makeText(mContext,"请开启应用拍照权限",Toast.LENGTH_SHORT).show();
//                }
//                break;
//        }
//    }

    /**
     * 启动拍照
     */
    public void startCamera() {
        Log.i(TAG, "startCamera isAvailable:" + isAvailable());
        if (isAvailable()) {
            if (mCameraDevice == null) {
                openCamera();
            }
        } else {
            setSurfaceTextureListener(mSurfaceTextureListener);
        }
    }

    /**
     * 停止拍照释放资源
     */
    public void stopCamera() {
        Log.i(TAG, "stopCamera mCameraDevice:" + mCameraDevice);
        if (mCameraDevice != null) {
            mCameraDevice.close();
            mCameraDevice = null;
        }

    }

}
