package com.ainirobot.platform.react.server.control;/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import androidx.annotation.Nullable;
import android.util.Log;

import com.ainirobot.platform.character.Character;
import com.ainirobot.platform.character.CharacterManager;
import com.ainirobot.platform.react.RNDef;
import com.ainirobot.platform.react.reactnative.character.ReactCharacter;

public class RNServerService extends Service {

    private static final String TAG = RNServerService.class.getSimpleName();

    @Override
    public void onCreate() {
        Log.d(TAG, "onCreate");
        super.onCreate();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "onStartCommand");
        return START_NOT_STICKY;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        Log.d(TAG, "onBind intent: " + intent.getAction());
        if (intent.getAction() != null
                && intent.getAction().equals(RNDef.RN_SERVER_ACTION_NAME)) {
            return new RNServer();
        }
        return null;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        Log.d(TAG, "onUnbind intent: " + intent.getAction());
        if (RNDef.RN_SERVER_ACTION_NAME.equals(intent.getAction())) {
            Character character = CharacterManager.getInstance().getDefault();
            if (character instanceof ReactCharacter) {
                boolean isSuspend = RNServerManager.getInstance().isSuspend();
                Log.d(TAG, "restart rn process isSuspend: " + isSuspend);
                if (!isSuspend) {
                    RNServerManager.getInstance().restartRNProcess();
                    reportStart();
                } else {
                    RNServerManager.getInstance().stopRNProcess();
                }
            }
        }
        return true;
    }

    private void reportStart() {
        if (RNServerManager.getInstance().isUpdate()) {
            RNServerManager.getInstance().setUpdate(false);
        } else {
            RNServerManager.getInstance().reportRestart();
        }
    }

    @Override
    public void onRebind(Intent intent) {
        Log.i(TAG, "Rebind " + intent.getAction());
        if (RNDef.RN_SERVER_ACTION_NAME.equals(intent.getAction())) {
            Character character = CharacterManager.getInstance().getDefault();
            if (character instanceof ReactCharacter) {
                boolean isSuspend = RNServerManager.getInstance().isSuspend();
                Log.d(TAG, "restart rn process isSuspend: " + isSuspend);
                if (isSuspend) {
                    RNServerManager.getInstance().stopRNProcess();
                }
            } else {
                RNServerManager.getInstance().stopRNProcess();
            }
        }
    }

    @Override
    public void onDestroy() {
        Log.i(TAG, "onDestroy");
        super.onDestroy();
    }
}
