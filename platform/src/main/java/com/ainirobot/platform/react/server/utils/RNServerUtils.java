package com.ainirobot.platform.react.server.utils;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.os.Process;
import android.util.Log;

import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.BuildConfig;
import com.ainirobot.platform.character.Character;
import com.ainirobot.platform.character.CharacterManager;
import com.ainirobot.platform.react.EveActivity;
import com.ainirobot.platform.react.FlashActivity;
import com.ainirobot.platform.react.reactnative.character.ReactCharacter;
import com.ainirobot.platform.react.reactnative.character.StartEvent;
import com.ainirobot.platform.react.server.control.ComponentManager;
import com.ainirobot.platform.react.server.control.RNServerManager;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.util.List;

public class RNServerUtils {

    private static final String TAG = RNServerUtils.class.getSimpleName();
    private static final String FULL_CONTROL_EVENT = "full_control_event";
    private static volatile long timestamp = System.currentTimeMillis();

    public static void handleRemoteException(String name, Exception e) {
        long currentTime = System.currentTimeMillis();
        Log.d(TAG, "handleRemoteException name: " + name + ", timestamp: " + timestamp
                + ", currentTime: " + currentTime + ", message: " + e.getMessage());
    }

    private static JsonObject buildFullControlCloseMsg() {
        JsonObject obj = new JsonObject();
        obj.addProperty("event", "close");
        obj.addProperty("value", "");

        return obj;
    }

    public static void resetAllComponents() {
        ComponentManager.getInstance().resetAllComponentListeners();
        ComponentManager.getInstance().resetAllComponents();
    }

    public static boolean killRNProcess() {
        Log.d(TAG, "killRNService");
        ComponentManager.getInstance().resetAllComponentListeners();
        ComponentManager.getInstance().resetAllComponents();
        RNServerManager.getInstance().getApiServer()
                .sendStatusReport(FULL_CONTROL_EVENT, new Gson().toJson(buildFullControlCloseMsg()));
        RNServerManager.getInstance().getSpeechApiServer().clearCallbacks();
        RNServerManager.getInstance().getDataServer().clearCallbacks();
        RNServerManager.getInstance().getApiServer().clearCallbacks();
        RNServerManager.getInstance().getDeviceServer().clearCallbacks();
        RNServerManager.getInstance().getRobotSettingServer().clearCallbacks();
        RNServerManager.getInstance().setCommandCallBack(null);
        RNServerManager.getInstance().setSpeechCallBack(null);
        RNServerManager.getInstance().getMsgServer().clearObserver();
        Context context = BaseApplication.getContext();
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (am == null) {
            Log.e(TAG, "killRNProcess am null");
            return false;
        }
        List<ActivityManager.RunningAppProcessInfo> processList = am.getRunningAppProcesses();

        int pid = getRNProcessPid(processList);
        if(pid > 0){
            Log.d(TAG, "killRNService sandbox");
            Process.killProcess(pid);
            return true;
        }else{
            Log.d(TAG, "killRNService sandbox fail cannot find process pid");
        }
        return false;
    }

    /**
     * 获取RN进程的PID
     * @param processList
     * @return
     */
    private static int getRNProcessPid(List<ActivityManager.RunningAppProcessInfo> processList){
        int pid = 0;
        if(processList != null){
            for (ActivityManager.RunningAppProcessInfo processInfo: processList) {
                String processName = processInfo.processName;
                Log.d(TAG,"getRNProcessPid processName: " + processName);
                if ("com.ainirobot.moduleapp:sandbox".equals(processName)) {
                    pid = processInfo.pid;
                    Log.d(TAG,"getRNProcessPid find pid: " + pid);
                    break;
                }
            }
        }
        return pid;
    }


    public static void startRNActivity() {
        StartEvent event = RNServerManager.getInstance().getStartEvent();
        if (event == null || event.getName().isEmpty()) {
            Log.e(TAG, "startRNService startEvent is null");
            return;
        }

        Character character = CharacterManager.getInstance().getDefault();
        boolean isSuspend = RNServerManager.getInstance().isSuspend();
        if (!(character instanceof ReactCharacter) || isSuspend) {
            Log.e(TAG, "startRNService not react or is suspend");
            return;
        }

        startEveActivity(event.getReqId(), event.getMsgId(), event.getName(), event.isCrash());
    }

    private static void startEveActivity(int reqId, String msgId, String name, boolean isCrash) {
        Context context = BaseApplication.getContext();
        Intent intentEve = new Intent();
        if (BuildConfig.DEBUG) {
            intentEve.setClass(context, FlashActivity.class);
        } else {
            intentEve.setClass(context, EveActivity.class);
        }
        intentEve.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intentEve.putExtra("extra_reqId", reqId);
        intentEve.putExtra("extra_msgId", msgId);
        intentEve.putExtra("extra_name", name);
        intentEve.putExtra("extra_crash", isCrash);
        context.startActivity(intentEve);
        Log.i(TAG, "start eve activity $name");
    }

}
