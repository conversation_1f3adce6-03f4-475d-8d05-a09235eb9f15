/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.react.server.impl;

import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.client.listener.ToneListener;
import com.ainirobot.coreservice.client.speech.SkillServerCheckListener;
import com.ainirobot.coreservice.client.speech.entity.ASRParams;
import com.ainirobot.coreservice.client.speech.entity.TTSParams;
import com.ainirobot.platform.react.server.utils.RNServerUtils;
import com.ainirobot.platform.rn.ISpeechApiRegistry;
import com.ainirobot.platform.rn.listener.IRNSkillServerCheckListener;
import com.ainirobot.platform.rn.listener.IRNTextListener;
import com.ainirobot.platform.rn.listener.IRNToneListener;
import com.ainirobot.platform.speech.SpeechApi;
import com.ainirobot.platform.speech.SpeechManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RNSpeechApiServer extends ISpeechApiRegistry.Stub {

    private static final String TAG = RNSpeechApiServer.class.getSimpleName();
    private SpeechApi mSpeechApi = SpeechApi.getInstance();
    private final Object TEXT_CALLBACK_LOCK = new Object();
    private final Object SKILL_CHECK_CALLBACK_LOCK = new Object();
    private final Object TONE_CALLBACK_LOCK = new Object();
    private final List<TextCallback> mTextCallbacks;
    private final List<ToneCallback> mToneCallbacks;
    private ServerCheckCallback mSkillCheckCallback;

    public RNSpeechApiServer() {
        mTextCallbacks = new ArrayList<>();
        mToneCallbacks = new ArrayList<>();
    }

    private TextCallback createTextCallback(IRNTextListener listener) {
        if (listener == null) {
            return null;
        }
        TextCallback callback = new TextCallback(listener);
        synchronized (TEXT_CALLBACK_LOCK) {
            mTextCallbacks.add(callback);
        }
        return callback;
    }

    private ToneCallback createToneCallback(IRNToneListener listener) {
        if (listener == null) {
            return null;
        }
        ToneCallback callback = new ToneCallback(listener);
        synchronized (TONE_CALLBACK_LOCK) {
            mToneCallbacks.add(callback);
        }
        return callback;
    }

    public void clearCallbacks() {
        Log.d(TAG, "clearCallbacks");
        synchronized (TEXT_CALLBACK_LOCK) {
            for (TextCallback callback : mTextCallbacks) {
                callback.stop();
            }
            mTextCallbacks.clear();
        }
        synchronized (TONE_CALLBACK_LOCK) {
            for (ToneCallback callback : mToneCallbacks) {
                callback.stop();
            }
            mToneCallbacks.clear();
        }
        synchronized (SKILL_CHECK_CALLBACK_LOCK) {
            if (mSkillCheckCallback != null) {
                mSkillCheckCallback.stop();
            }
        }
    }

    private void onTextFinish(TextCallback callback) {
        synchronized (TEXT_CALLBACK_LOCK) {
            mTextCallbacks.remove(callback);
        }
    }

    private void onToneFinish(ToneCallback callback) {
        synchronized (TONE_CALLBACK_LOCK) {
            mToneCallbacks.remove(callback);
        }
    }

    @Override
    public void playText(String text, IRNTextListener listener) {
        mSpeechApi.playText(text, createTextCallback(listener));
    }

    @Override
    public void playTextWithParams(String text, String params, IRNTextListener listener) {
        HashMap<String, String> ttsParams = new HashMap<>();
        if (!TextUtils.isEmpty(params)) {
            try {
                JSONObject json = new JSONObject(params);
                if (json.has(TTSParams.SPEECHVOLUME)) {
                    ttsParams.put(TTSParams.SPEECHVOLUME, json.optString(TTSParams.SPEECHVOLUME));
                }
                if (json.has(TTSParams.SPEAKERROLE)) {
                    ttsParams.put(TTSParams.SPEAKERROLE, json.optString(TTSParams.SPEAKERROLE));
                }
                if (json.has(TTSParams.SPEECHPIT)) {
                    ttsParams.put(TTSParams.SPEECHPIT, json.optString(TTSParams.SPEECHPIT));
                }
                if (json.has(TTSParams.SPEECHRATE)) {
                    ttsParams.put(TTSParams.SPEECHRATE, json.optString(TTSParams.SPEECHRATE));
                }
                if (json.has(TTSParams.SPEECHSPEED)) {
                    ttsParams.put(TTSParams.SPEECHSPEED, json.optString(TTSParams.SPEECHSPEED));
                }
                if (json.has(TTSParams.SPEECHLANGUAGE)) {
                    ttsParams.put(TTSParams.SPEECHLANGUAGE, json.optString(TTSParams.SPEECHLANGUAGE));
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        mSpeechApi.playText(text, ttsParams, createTextCallback(listener));
    }

    @Override
    public void stopTTS() {
        mSpeechApi.stopTTS();
    }

    @Override
    public void stopTone() {
        mSpeechApi.stopTone();
    }

    @Override
    public void cancelAudioOperation() {
        mSpeechApi.cancelAudioOperation();
    }

    @Override
    public void setRecognizeMode(boolean isContinue) {
        mSpeechApi.setRecognizeMode(isContinue);
    }

    @Override
    public void setRecognizeModeForce(boolean isContinue) {
        mSpeechApi.setRecognizeModeForce(isContinue);
    }

    @Override
    public void setRecognizeModeNew(boolean isContinue, boolean isCloseStreamData) throws RemoteException {
        mSpeechApi.setRecognizeModeNew(isContinue, isCloseStreamData);
    }

    @Override
    public void playStreamText(String streamSid, String textSid, String text, IRNTextListener listener) throws RemoteException {
        mSpeechApi.playStreamText(streamSid, textSid, text, createTextCallback(listener));
    }

    @Override
    public void onAgentActionFinish(String action, int code, String message) throws RemoteException {
        mSpeechApi.onAgentActionFinish(action, code, message);
    }

    @Override
    public void onAgentActionState(String action, int state, String data) throws RemoteException {
        mSpeechApi.onAgentActionState(action, state, data);
    }

    @Override
    public void queryByTextWithThinking(String text, boolean isShowThinking) throws RemoteException {
        mSpeechApi.queryByTextWithThinking(text, isShowThinking);
    }

    @Override
    public void setRecognizable(boolean enable) {
        mSpeechApi.setRecognizable(enable);
    }

    @Override
    public void queryByText(String text) {
        mSpeechApi.queryByText(text);
    }

    @Override
    public void setAngleCenterRange(float centerAngle, float rangeAngle) {
        mSpeechApi.setAngleCenterRange(centerAngle, rangeAngle);
    }

    @Override
    public int setCustomizeWakeUpWord(String wakeUpWordChinese, String wakeUpWordPinYin, String separator) {
        return mSpeechApi.setCustomizeWakeUpWord(wakeUpWordChinese, wakeUpWordPinYin, separator);
    }

    @Override
    public int closeCustomizeWakeUpWord() {
        return mSpeechApi.closeCustomizeWakeUpWord();
    }

    @Override
    public int getPinYinScore(String pinyin, String separator) {
        return mSpeechApi.getPinYinScore(pinyin, separator);
    }

    @Override
    public String queryPinYinFromChinese(String chineseWord) {
        return mSpeechApi.queryPinYinFromChinese(chineseWord);
    }

    @Override
    public String queryPinYinMappingTable(String pinyin) {
        return mSpeechApi.queryPinYinMappingTable(pinyin);
    }

    @Override
    public String queryUserSetWakeUpWord() {
        return mSpeechApi.queryUserSetWakeUpWord();
    }

    @Override
    public void playTone(String type, IRNToneListener listener) {
        mSpeechApi.playTone(type, createToneCallback(listener));
    }

    @Override
    public void playToneByLocalPath(String localPath, IRNToneListener listener) {
        mSpeechApi.playToneByLocalPath(localPath, createToneCallback(listener));
    }

    @Override
    public void setTTSParams(String ttsType, int value) throws RemoteException {
        mSpeechApi.setTTSParams(ttsType, value);
    }

    @Override
    public void setLangRec(String autoLangJson) throws RemoteException {
        mSpeechApi.setLangRec(autoLangJson);
    }

    @Override
    public void setASRParams(@ASRParams String asrType, String value) {
        mSpeechApi.setASRParams(asrType, value);
    }

    @Override
    public boolean setAsrExtendProperty(String propertyJson) {
        return SpeechManager.getInstance().setAsrExtendProperty(propertyJson);
    }

    @Override
    public void downloadTtsAudio(String ttsEntitiesJson) {
        mSpeechApi.downloadTtsAudio(ttsEntitiesJson);
    }

    @Override
    public String getSpokemanListByLanguage(String lang) throws RemoteException {
        return mSpeechApi.getSpokemanListByLanguage(lang);
    }


    @Override
    public void startApp(String appId) throws RemoteException {
        mSpeechApi.startApp(appId);
    }

    @Override
    public void moveToForeground(String appId) throws RemoteException {
        Log.d(TAG, "Move to foreground");
        mSpeechApi.moveToForeground(appId);
    }

    @Override
    public void moveToBack(String appId) throws RemoteException {
        mSpeechApi.moveToBack(appId);
    }

    @Override
    public void destroyApp(String appId) throws RemoteException {
        mSpeechApi.destroyApp(appId);
    }

    @Override
    public void setAppPath(String appId, String path) throws RemoteException {
        mSpeechApi.setAppPath(appId, path);
    }

    @Override
    public void sendAgentMessage(String type,int code,String message) throws RemoteException {
        mSpeechApi.sendAgentMessage(type, code, message);
    }

    @Override
    public void setAppVersion(String appId, String version) throws RemoteException {
        mSpeechApi.setAppVersion(appId, version);
    }

    @Override
    public void setSyncCustomNlpData(Map map) throws RemoteException {
        mSpeechApi.setSyncCustomNlpData(map);
    }

    @Override
    public String setAsyncCustomNlpData(String opt, String data) throws RemoteException {
        return mSpeechApi.setAsyncCustomNlpData(opt, data);
    }

    @Override
    public void resetNlpState() throws RemoteException {
        mSpeechApi.resetNlpState();
    }

    @Override
    public void setServerApp(List<String> appList) throws RemoteException {
        mSpeechApi.setServerApp(appList);
    }

    @Override
    public void setNLPDebug(boolean value) throws RemoteException {
        mSpeechApi.setNLPDebug(value);
    }

    @Override
    public void resetAngleCenterRange() {
        SpeechManager.getInstance().resetAngleCenterRange();
    }

    @Override
    public void closeStreamDataReceived(String paramJson){
        mSpeechApi.closeStreamDataReceived(paramJson);
    }

    @Override
    public boolean isRecognizeContinue() throws RemoteException {
        return mSpeechApi.isRecognizeContinue();
    }

    @Override
    public boolean isRecognizable() throws RemoteException {
        return mSpeechApi.isRecognizable();
    }

    @Override
    public void setSkillServerCheckCallBack(IRNSkillServerCheckListener listener) {
        synchronized (SKILL_CHECK_CALLBACK_LOCK) {
            if (listener == null) {
                mSkillCheckCallback = null;
            } else {
                mSkillCheckCallback = new ServerCheckCallback(listener);
            }
        }
        mSpeechApi.setSkillServerCheckCallback(mSkillCheckCallback);
    }

    @Override
    public void unRegisterServerCheck() {
        mSpeechApi.unRegisterServerCheck();
    }

    private class TextCallback extends TextListener {
        private IRNTextListener listener;

        TextCallback(IRNTextListener listener) {
            this.listener = listener;
        }

        public void stop() {
            listener = null;
        }

        @Override
        public void onStart() {
            try {
                if (listener != null) {
                    listener.onStart();
                }
            } catch (RemoteException e) {
                RNServerUtils.handleRemoteException("SpeechCallback onStart", e);
            }
        }

        @Override
        public void onStop() {
            try {
                if (listener != null) {
                    listener.onStop();
                    onTextFinish(this);
                }
            } catch (RemoteException e) {
                onTextFinish(this);
                RNServerUtils.handleRemoteException("SpeechCallback onStop", e);
            }
        }

        @Override
        public void onError() {
            try {
                if (listener != null) {
                    listener.onError();
                    onTextFinish(this);
                }
            } catch (RemoteException e) {
                onTextFinish(this);
                RNServerUtils.handleRemoteException("SpeechCallback onError", e);
            }
        }

        @Override
        public void onComplete() {
            try {
                if (listener != null) {
                    listener.onComplete();
                    onTextFinish(this);
                }
            } catch (RemoteException e) {
                onTextFinish(this);
                RNServerUtils.handleRemoteException("SpeechCallback onComplete", e);
            }
        }

        @Override
        public void onStreamComplete(String streamSid, String textSid) {
            try {
                if (listener != null) {
                    listener.onStreamComplete(streamSid, textSid);
                    onTextFinish(this);
                }
            } catch (RemoteException e) {
                onTextFinish(this);
                RNServerUtils.handleRemoteException("SpeechCallback onStreamComplete", e);
            }
        }
    }

    private class ToneCallback extends ToneListener {
        private IRNToneListener listener;

        ToneCallback(IRNToneListener listener) {
            this.listener = listener;
        }

        @Override
        public void onStart() {
            if (listener != null) {
                try {
                    listener.onStart();
                } catch (RemoteException e) {
                    RNServerUtils.handleRemoteException("ToneCallback onStart", e);
                }
            }
        }

        @Override
        public void onStop() {
            if (listener != null) {
                try {
                    listener.onStop();
                    onToneFinish(this);
                } catch (RemoteException e) {
                    RNServerUtils.handleRemoteException("ToneCallback onStop", e);
                }
            }
        }

        @Override
        public void onError() {
            if (listener != null) {
                try {
                    listener.onError();
                    onToneFinish(this);
                } catch (RemoteException e) {
                    RNServerUtils.handleRemoteException("ToneCallback onError", e);
                }
            }
        }

        @Override
        public void onComplete() {
            if (listener != null) {
                try {
                    listener.onComplete();
                    onToneFinish(this);
                } catch (RemoteException e) {
                    RNServerUtils.handleRemoteException("ToneCallback onComplete", e);
                }
            }
        }

        public void stop() {
            listener = null;
        }
    }

    private class ServerCheckCallback extends SkillServerCheckListener {
        private IRNSkillServerCheckListener listener;

        ServerCheckCallback(IRNSkillServerCheckListener listener) {
            this.listener = listener;
        }

        @Override
        public void onDelay(int timeMilliSeconds) {
            try {
                if (listener != null) {
                    listener.onDelay(timeMilliSeconds);
                }
            } catch (RemoteException e) {
                RNServerUtils.handleRemoteException("ServerCheckCallback onDelay", e);
            }
        }

        @Override
        public void onError(String message) {
            try {
                if (listener != null) {
                    listener.onError(message);
                }
            } catch (RemoteException e) {
                RNServerUtils.handleRemoteException("ServerCheckCallback onError", e);
            }
        }

        @Override
        public void onSuccess(int timeMilliSeconds) {
            try {
                if (listener != null) {
                    listener.onSuccess(timeMilliSeconds);
                }
            } catch (RemoteException e) {
                RNServerUtils.handleRemoteException("ServerCheckCallback onSuccess", e);
            }
        }

        public void stop() {
            listener = null;
        }
    }
}
