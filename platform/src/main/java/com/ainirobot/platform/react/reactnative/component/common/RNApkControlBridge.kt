package com.ainirobot.platform.react.reactnative.component.common

import android.util.Log
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.component.listener.BridgeRNControlListener
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod

class RNApkControlBridge(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    override fun getName(): String {
        return "NLPApkControl"
    }

    @ReactMethod
    fun addListener(apkPackage: String, callbackId: Int) {
        try {
            Log.v("caixj", "bridge addListener")
            RNClientManager.instance?.nlpApkControl?.addListener(apkPackage, BridgeRNControlListener.obtain(callbackId))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun removeListener(apkPackage: String) {
        try {
            RNClientManager.instance?.nlpApkControl?.removeListener(apkPackage)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun removeAll() {
        try {
            RNClientManager.instance?.nlpApkControl?.removeAll()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun forceStopPackage(apkPackage: String) {
        try {
            RNClientManager.instance?.nlpApkControl?.forceStopPackage(apkPackage)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun grantRuntimePermission(apkPackage: String, permission: String) {
        try {
            RNClientManager.instance?.nlpApkControl?.grantRuntimePermission(apkPackage, permission)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun revokeRuntimePermission(apkPackage: String, permission: String) {
        try {
            RNClientManager.instance?.nlpApkControl?.revokeRuntimePermission(apkPackage, permission)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun onRobotMessage(apkPackage: String, message: String) {
        try {
            RNClientManager.instance?.nlpApkControl?.onRobotMessage(apkPackage, message)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setApplicationState(apkPackage: String, isEnable: Boolean) {
        try {
            RNClientManager.instance?.nlpApkControl?.setApplicationState(apkPackage, isEnable)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun startService(packageName: String, serviceName: String, actionName: String) {
        Log.e(this.name, "not support startService.use OpenAppApi.openThirdPartyAppIfKillRnForResult instead");
    }
}