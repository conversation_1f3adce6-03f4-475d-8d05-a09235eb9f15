/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.react.view;


import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import com.ainirobot.platform.utils.AnimationUtil;

import java.lang.ref.WeakReference;
import java.util.List;

public class AnimationDecoratorImpl extends AnimatorListenerAdapter {

    private static final String TAG = AnimationDecoratorImpl.class.getSimpleName();
    private static final int DEFAULT_INTERVAL_TIME = 25 * 100;
    private static final int QUERY_LIMIT_TEXT_COUNT = 15;
    private static final String DEFAULT_HEADER_TEXT = "";
    private int mPosition = 0;
    private List<String> mDataList;
    private ScrollHandler mHandler;
    private int mDuration;
    private int mIntervalTime;
    private View mView;
    private boolean isStop = false;
    private AnimatorSet enterSet;
    private AnimatorSet shiftOutSet;
    private String headerText;
    private static final int MSG_ANIMATION_NEXT = 0;
    private int historySize = 0;

    public AnimationDecoratorImpl() {
        this(AnimationUtil.DEFAULT_DURATION_TIME);
    }

    public AnimationDecoratorImpl(int duration) {
        this(duration, DEFAULT_INTERVAL_TIME);
    }

    public AnimationDecoratorImpl(int duration, int intervalTime) {
        this.mDuration = duration;
        this.mIntervalTime = intervalTime;
        mHandler = new ScrollHandler(this);
    }

    public void setQueryList(List<String> dataList, String headerText) {
        this.mDataList = dataList;
        this.headerText = headerText;
        mPosition = 0;
        if (historySize == 1) {
            mHandler.sendEmptyMessage(MSG_ANIMATION_NEXT);
        }
        if(mView != null){
            startAnimation(mView);
        }
    }

    private static class ScrollHandler extends Handler {
        private WeakReference<AnimationDecoratorImpl> mAnimation;

        public ScrollHandler(AnimationDecoratorImpl animation) {
            mAnimation = new WeakReference<>(animation);
        }

        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_ANIMATION_NEXT:
                    if (mAnimation.get() != null) {
                        mAnimation.get().shiftOutAnimator();
                    }
                    break;
            }
        }
    }


    private void singleAnimation() {
        Log.d(TAG, "singleAnimation");
        enterSet = AnimationUtil.enterAnimator(mView);
        if (mDataList != null) {
            historySize = mDataList.size();
            if (mDataList.size() == 1) {
                return;
            }
        }
        mHandler.sendEmptyMessageDelayed(MSG_ANIMATION_NEXT, mIntervalTime);
    }

    private void shiftOutAnimator() {
        shiftOutSet = AnimationUtil.shiftOutAnimator(mView, this);
    }

    @Override
    public void onAnimationEnd(Animator animation) {
        Log.d(TAG, "onAnimationEnd");
        scheduleNext();
    }

    private void scheduleNext() {
        Log.d(TAG, "scheduleNext");
        Log.d(TAG, "mView getVisibility() ==== " + (mView.getVisibility() == View.VISIBLE));
        if (mView.getVisibility() != View.VISIBLE) {
            stopAnimation();
            return;
        }
        if (mDataList == null || mDataList.size() == 0) {
            return;
        }
        if (isStop) {
            return;
        }
        if (mPosition == mDataList.size()) {
            mPosition = 0;
        }
        transformText(mPosition);
        singleAnimation();
        mPosition++;
    }

    private StringBuffer stringBuffer = new StringBuffer();

    private void transformText(int index) {
        stringBuffer.setLength(0);
        if (TextUtils.isEmpty(headerText)) {
            headerText = DEFAULT_HEADER_TEXT;
        }
        if (mView instanceof TextView && index < mDataList.size()) {
            String text = mDataList.get(index);
            boolean containsDot = text.contains("'") || text.contains("“") || text.contains("\"") || text.contains("\'");
            if (TextUtils.isEmpty(headerText)) {
                if (text.length() > QUERY_LIMIT_TEXT_COUNT) {
                    int endIndex = (QUERY_LIMIT_TEXT_COUNT - 1) + (containsDot ? 1 : 0);
                    stringBuffer.append(text.substring(0, endIndex) + "...");
                } else {
                    stringBuffer.append(text);
                }
            } else {
                if (text.length() > (QUERY_LIMIT_TEXT_COUNT - 1)) {
                    stringBuffer.append(headerText + " \"" + text.substring(0, QUERY_LIMIT_TEXT_COUNT - 2 + (containsDot ? 1 : 0)) + "...\"");
                } else {
                    stringBuffer.append(headerText + " \"" + text + "\"");
                }
            }
            ((TextView) mView).setText(stringBuffer);
        }
    }

    public boolean isRunning() {
        return (enterSet != null && enterSet.isRunning()) && (shiftOutSet != null && shiftOutSet.isRunning());
    }

    public void startAnimation(View v) {
        Log.i(TAG, "startAnimation");
        stopAnimation();
        this.mView = v;
        if (mView != null) {
            isStop = false;
            scheduleNext();
        }
    }

    public void stopAnimation() {
        Log.i(TAG, "stopAnimation");
        mPosition = 0;
        isStop = true;
        if (mView != null) {
            ((TextView) mView).setText("");
        }
        mHandler.removeMessages(MSG_ANIMATION_NEXT);
        if (enterSet != null) {
            enterSet.removeAllListeners();
            enterSet.end();
            enterSet = null;
        }
        if (shiftOutSet != null) {
            shiftOutSet.removeAllListeners();
            shiftOutSet.end();
            shiftOutSet = null;
        }
    }

}

