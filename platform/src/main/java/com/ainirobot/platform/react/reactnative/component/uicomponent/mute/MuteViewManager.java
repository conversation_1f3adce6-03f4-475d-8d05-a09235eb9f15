package com.ainirobot.platform.react.reactnative.component.uicomponent.mute;

import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;

import javax.annotation.Nonnull;

public class MuteViewManager extends SimpleViewManager<MuteView> {
    @Nonnull
    @Override
    public String getName() {
        return "MuteView";
    }

    @Nonnull
    @Override
    protected MuteView createViewInstance(@Nonnull ThemedReactContext themedReactContext) {
        return new MuteView(themedReactContext);
    }
}
