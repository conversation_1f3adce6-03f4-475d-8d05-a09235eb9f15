package com.ainirobot.platform.react

import android.content.Context
import android.content.Intent
import android.os.Environment
import android.text.TextUtils
import android.util.Log
import com.ainirobot.platform.BaseApplication
import java.io.*
import java.util.concurrent.Executors

import kotlin.math.min

/**
 * rom opk check
 *
 * @version V1.0.0
 * @date 2019/8/30 16:50
 */
class RomOpkCheck private constructor() {

    companion object {
        private const val TAG = "RomOpkCheck"
        private const val BROADCAST_ACTION = "com.ainirobot.moduleapp.rom_opk_update"
        const val DEFAULT_APP_ID = "baoxiaomi_91d5a88c4eafda508216fb516dad4a80"
        private const val OPK_PATH = "path"
        private const val OPK_AUTO_START = "auto_start"
        private const val OPK_FORCE_START = "force_start"
        private val PATH = Environment.getExternalStorageDirectory().toString() + File.separator + "rndata" + File.separator
        val instance: RomOpkCheck by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) { RomOpkCheck() }
    }
    private val poolExecutor = Executors.newSingleThreadExecutor { r -> Thread(r, "check_rom_opk_thread") }

    fun checkOpkVersion(context: Context) {
        Log.i(TAG,"checkOpkVersion")
        if (poolExecutor.isShutdown) {
            Log.e(TAG, "check_rom_opk_thread pool already shut down," +
                    " don't invoke two time \"checkOpkVersion\" in process")
            return
        }
        poolExecutor.execute {
            val manager = context.assets
            try {
                val opkList = manager.list("opk")
                if (opkList == null || opkList.isEmpty()) {
                    Log.w(TAG, "checkOpkVersion: not find opk")
                    return@execute
                }
                searchOpkList(opkList, context)
                if (!poolExecutor.isShutdown) {
                    Log.i(TAG, " check finish, shutdown thread pool")
                    poolExecutor.shutdown()
                }
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    private fun searchOpkList(opkList: Array<String>, context: Context) {
        Log.i(TAG, "searchOpkList: opkList size=" + opkList.size)
        for (opk in opkList) {
            Log.i(TAG, "searchOpkList: opk=$opk")
            if (!isOpkFile(opk)) {
                continue
            }
            val newOpkVersion = parseVersion(opk)
            if (TextUtils.isEmpty(newOpkVersion)) {
                Log.e(TAG, "searchOpkList: opk name error, not find version name")
                return
            }
            Log.i(TAG, "searchOpkList: newOpkVersion=" + newOpkVersion!!)
            val oldOpkVersion = parseRomOpkVersionName()
            if (TextUtils.isEmpty(oldOpkVersion)) {
                Log.i(TAG, "searchOpkList: first install rom opk")
                copyOpk(context, opk)
                notifyUnzipOpk(context, opk)
                return
            }
            Log.i(TAG, "searchOpkList:  oldOpkVersion=" + oldOpkVersion!!)
            val result = compareVersion(oldOpkVersion, newOpkVersion)
            Log.i(TAG, "searchOpkList: compareVersion result=$result")
            if (result > 0) {
                copyOpk(context, opk)
                notifyUnzipOpk(context, opk)
            }
        }
    }

    private fun notifyUnzipOpk(context: Context, opkName: String) {
        Log.i(TAG, "notifyUnzipOpk: opkName=$opkName")
        val intent = Intent()
        intent.action = BROADCAST_ACTION
        intent.putExtra(OPK_PATH, PATH + opkName)
        intent.putExtra(OPK_AUTO_START, BaseApplication.getApplication().defaultReactNativeAutoSwitch)
        intent.putExtra(OPK_FORCE_START, BaseApplication.getApplication().isForceSwitchRomOpk)
        context.sendBroadcast(intent)
    }

    private fun isOpkFile(fileName: String): Boolean {
        return !TextUtils.isEmpty(fileName) && fileName.endsWith(".opk")
    }

    private fun parseVersion(fileName: String): String? {
        Log.i(TAG, "parseVersion: opkFileName=$fileName")
        val file = fileName.split("-".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        return if (file.size == 4) {
            file[1]
        } else null
    }

    private fun compareVersion(oldVersion: String, newVersion: String): Int {
        val oldV = oldVersion.split("\\.".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        val newV = newVersion.split("\\.".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        val min = min(oldV.size, newV.size)
        for (i in 0 until min) {
            val oldIndex = Integer.parseInt(oldV[i])
            val newIndex = Integer.parseInt(newV[i])
            Log.i(TAG, "compareVersion: oldIndex:$oldIndex newIndex:$newIndex")
            if (oldIndex == newIndex) {
                continue
            }
            return if (newIndex > oldIndex) 1 else -1
        }
        return newV.size.compareTo(oldV.size)
    }

    private fun parseRomOpkVersionName(): String? {
        val opkList = AppManger.getAllInstallList()
        Log.i(TAG, "parseRomOpkVersionName: opkList=" + opkList!!)
        val bean = opkList[BaseApplication.getApplication().defaultReactNativeAppId]
        if (bean == null) {
            Log.i(TAG, "parseRomOpkVersionName: not find opk info")
            return null
        }
        val (_, _, versionName) = bean.rpkBean
        return versionName
    }

    private fun copyOpk(context: Context, fileName: String) {
        val manager = context.assets
        var fileInput: InputStream? = null
        var fileOutput: FileOutputStream? = null
        try {
            fileInput = manager.open("opk/$fileName")
            val data = File(Environment.getExternalStorageDirectory().toString() + "/rndata")
            if (!data.exists()) {
                val result = data.mkdirs()
                Log.i(TAG, "copyOpk: create file result:$result")
            }
            val opk = File(data.absolutePath + File.separator + fileName)
            fileOutput = FileOutputStream(opk)
            fileInput.copyTo(fileOutput, 1024)
        } catch (e: IOException) {
            Log.w(TAG, "copy opk exception:" + e.printStackTrace())
        } finally {
            closeIo(fileInput)
            closeIo(fileOutput)
        }
    }

    private fun closeIo(io: Closeable?) {
        if (io != null) {
            try {
                io.close()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }
}
