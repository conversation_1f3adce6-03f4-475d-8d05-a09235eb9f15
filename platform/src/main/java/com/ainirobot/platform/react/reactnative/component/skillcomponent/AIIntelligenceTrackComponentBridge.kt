package com.ainirobot.platform.react.reactnative.component.skillcomponent

import com.ainirobot.platform.component.AIIntelligenceTrackComponent
import com.ainirobot.platform.react.server.utils.ComponentUtils
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactMethod

class AIIntelligenceTrackComponentBridge(reactContext: ReactApplicationContext) : BaseComponentBridge<AIIntelligenceTrackComponent>(reactContext) {

    override fun isHDMonopoly(): Boolean {
        return true
    }

    override fun getResouceType(): Array<ResouceType> {
        return arrayOf(ResouceType.Vision, ResouceType.Navigation, ResouceType.HeadTurn)
    }

    override fun getName(): String {
        return ComponentUtils.COMPONENT_AI_INTELLIGENCE_TRACK
    }

    @ReactMethod
    override fun start(uid: String, param: String?) {
        super._start(uid, param)
    }

    @ReactMethod
    override fun stop(uid: String, timeout: Int?) {
        super._stop(uid, timeout)
    }

    @ReactMethod
    override fun updateParams(uid: String, intent: String, param: String) {
        super._updateParams(uid, intent, param)
    }

    @ReactMethod
    override fun setStatusListener(uid: String, statusListenerId: Int) {
        super._setStatusListener(uid, statusListenerId)
    }

    @ReactMethod
    override fun setFinishListener(uid: String, finishListenerId: Int) {
        super._setFinishListener(uid, finishListenerId)
    }

}