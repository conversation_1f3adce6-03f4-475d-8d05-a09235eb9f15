package com.ainirobot.platform.react.reactnative.component.listener

import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNMsgListener
import com.facebook.react.bridge.WritableNativeMap

class BridgeMsgListener : IRNMsgListener.Stub {

    companion object {
        fun obtain(callbackId:Int): BridgeMsgListener? {
            return BridgeMsgListener(callbackId)
        }
    }

    var id = -1

    private constructor(id: Int) {
        this.id = id
    }

    override fun onMsgChanged() {
        triggerEvent("onMsgPushChanged")
    }

    fun triggerEvent(event: String) {
        if (id < 0) {
            return
        }
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putString("event", event)
        ReactNativeEventEmitter.triggerEvent("onMsgPushListener", readableMap)
    }
}