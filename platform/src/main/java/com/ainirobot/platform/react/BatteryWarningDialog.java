/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.react;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.CountDownTimer;
import android.provider.Settings;
import androidx.annotation.NonNull;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.platform.R;
import com.ainirobot.platform.utils.Utils;

public class BatteryWarningDialog extends Dialog {

    private static final String TAG = BatteryWarningDialog.class.getSimpleName();

    private TextView mBatteryRemainText;
    private TextView mBatteryDismissButton;
    private final View mDialogView;
    private BatteryWarningListener mListener;

    private CountDownTimer mCountDownTimer;
    private final long TIMEOUT = 5 * 1000;
    private void startCountDownTimer() {
        mCountDownTimer = new CountDownTimer(TIMEOUT, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                int second = (int) (millisUntilFinished / 1000);
                Log.d(TAG, " CountDownTimer second: " + second + " millisUntilFinished：" + millisUntilFinished );
                mBatteryDismissButton.setText(String.format(getContext().getString(R.string.has_know), second+1));
            }

            @Override
            public void onFinish() {
                dismiss();
            }
        };

    }

    private void cancelCountDownTimer() {
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
        }
    }

    public BatteryWarningDialog(@NonNull Context context) {
        super(context, R.style.OTADialog);
        mDialogView = LayoutInflater.from(context).inflate(R.layout.battery_dialog, null);
        setContentView(mDialogView);
        Window window = getWindow();
        if (window == null) return;
        WindowManager.LayoutParams p = window.getAttributes();  //获取对话框当前的参数值
        p.width = 950;
        p.height = WindowManager.LayoutParams.WRAP_CONTENT;
        window.setAttributes(p);
        setCanceledOnTouchOutside(false);

        initView();
        initListener();
        startCountDownTimer();
    }

    @Override
    public void show() {
        if (!(getContext() instanceof Activity)) {
            Window window = getWindow();
            if (window != null) {
                window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_DIALOG);
                window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            }
        }
        permission();
    }

    private void permission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(getContext())) {
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                        Uri.parse("package:" + getContext().getPackageName()));
                getContext().startActivity(intent);
                onStop();
            } else {
                // Already hold the SYSTEM_ALERT_WINDOW permission,
                Log.e(TAG, "has ACTION_MANAGE_OVERLAY_PERMISSION");
                super.show();
                mCountDownTimer.start();
            }
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        Log.i(TAG, "OTADialog onDismiss");
        if (mListener != null) {
            mListener.onStop();
        }
        cancelCountDownTimer();
    }

    private void initListener() {
        mBatteryDismissButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }

    private void initView() {
        mBatteryRemainText = (TextView) mDialogView.findViewById(R.id.battery_remaining_text);
        mBatteryDismissButton = (TextView) mDialogView.findViewById(R.id.battery_dismiss_button);
        setCanceledOnTouchOutside(false);
        setCancelable(false);
    }

    public BatteryWarningDialog setBatteryRemaining(int remaining) {
        mBatteryRemainText.setText(Utils.getString(R.string.battery_remaining_text, remaining + "", "%"));
        return this;
    }

    public BatteryWarningDialog setBatteryWarningListener(BatteryWarningListener listener) {
        mListener = listener;
        return this;
    }

    public static class BatteryWarningListener {
        public void onStop() {};
    }
}