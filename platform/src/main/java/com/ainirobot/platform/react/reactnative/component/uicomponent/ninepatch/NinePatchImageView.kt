package com.ainirobot.platform.react.reactnative.component.uicomponent.ninepatch

import android.content.Context
import android.util.Log
import android.widget.ImageView

/**
 * 加载drawable下的.9图片
 */
class NinePatchImageView(context: Context?) : ImageView(context) {

    /**
     * drawable下的.9图片资源的名字
     * 如:popup_bg.9.png，那么name=popup_bg
     */
    fun setBackgroundName(name: String = "") {
        val ref = resources.getIdentifier(name, "drawable",
                context.packageName)
        Log.d("NinePatchImageView", "ref = $ref")
        if (ref > 0) {
            setBackgroundResource(ref)
        }
    }
}