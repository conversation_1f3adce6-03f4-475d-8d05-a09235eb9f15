package com.ainirobot.platform.react.reactnative.component.common

import com.ainirobot.platform.utils.BitmapUtils
import com.ainirobot.platform.utils.FileUtils
import com.facebook.react.bridge.*

/**
 * @date: 2019-02-01
 * @author: ha<PERSON><PERSON><PERSON>
 * @desc: 通用工具的 BridgeModule
 */
class BitmapUtilBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String {
        return "BitmapUtil"
    }

    @ReactMethod
    fun mergeBitmap(sourcePath1: String, sourcePath2: String, sourcePath3: String, promise: Promise) {
        try {
            val source = BitmapUtils.mergeBitmap(sourcePath1, sourcePath2, sourcePath3)
            promise.resolve(source)
        } catch (e: Exception) {
            promise.reject("mergeBitmap", e.message)
        }
    }

    @ReactMethod
    fun copyFile(sourcePath1: String, sourcePath2: String, promise: Promise) {
        try {
            val isSuccess = FileUtils.copyFile(sourcePath1, sourcePath2)
            promise.resolve(isSuccess)
        } catch (e: Exception) {
            promise.reject("copyFile", e.message)
        }
    }
}
