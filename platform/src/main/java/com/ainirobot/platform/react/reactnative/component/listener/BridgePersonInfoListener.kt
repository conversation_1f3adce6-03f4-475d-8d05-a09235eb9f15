package com.ainirobot.platform.react.reactnative.component.listener

import com.ainirobot.coreservice.client.listener.Person
import com.ainirobot.coreservice.client.listener.PersonInfoListener
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeJson
import com.facebook.react.bridge.WritableNativeArray
import com.facebook.react.bridge.WritableNativeMap
import org.json.JSONObject

class BridgePersonInfoListener : PersonInfoListener {

    companion object {
        fun obtain(callbackId:Int): BridgePersonInfoListener? {
            return if(callbackId < 0){
                null
            } else {
                BridgePersonInfoListener(callbackId)
            }
        }
    }

    var id = -1
    private constructor(id:Int) {
        this.id = id
    }
    override fun onResult(status: Int, responseString: String) {
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putInt("status", status)
        readableMap.putString("data", responseString)
        readableMap.putString("event", "onResult")
        ReactNativeEventEmitter.triggerEvent("onPersonInfo", readableMap)
    }

    override fun onData(code: Int, data: List<Person>) {
        var readableMap = WritableNativeMap()
        var readableArray = WritableNativeArray()
        data.forEach {
            val personMap = ReactNativeJson.convertJsonToMap(JSONObject(it.toGson()))
            readableArray.pushMap(personMap)
        }
        readableMap.putInt("id", id)
        readableMap.putArray("persons", readableArray)
        readableMap.putInt("code", code)
        readableMap.putString("event", "onData")
        ReactNativeEventEmitter.triggerEvent("onPersonInfo", readableMap)
    }
}