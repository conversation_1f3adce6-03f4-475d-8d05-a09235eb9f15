package com.ainirobot.platform.react.view.captcha;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import androidx.annotation.NonNull;

import java.util.Random;

public class DefaultCaptchaStrategy extends CaptchaStrategy {

    public DefaultCaptchaStrategy(Context ctx) {
        super(ctx);
    }

    @Override
    public Path getBlockShape(int blockSize) {
        int gap = (int) (blockSize/4f);
        Path path = new Path();
        path.moveTo(0, 2 * gap);
        path.rCubicTo(0, (gap / 7) - gap, gap / 7, -gap, gap, -gap);
        path.rCubicTo(0, -gap, gap, -gap, gap, 0);
        path.rCubicTo(gap - (gap / 7), 0, gap, gap / 7, gap, gap);
        path.rCubicTo(gap, 0, gap, gap, 0, gap);
        path.rCubicTo(0, gap - (gap / 7), gap / 7, gap, -gap, gap);
        path.rCubicTo(0, -gap, -gap, -gap, -gap, 0);
        path.rCubicTo((gap / 7)- gap, 0, -gap,-gap / 7, -gap, -gap);
        path.rCubicTo(gap, 0, gap, -gap, 0, -gap);
        path.close();
        return path;
    }

    @Override
    public  @NonNull
    PositionInfo getBlockPostionInfo(int width, int height, int blockSize) {
        Random random = new Random();
        int left = random.nextInt(width - blockSize +1);
        //Avoid robot frequently and quickly click the start point to access the captcha.
        if (left < blockSize) {
            left = blockSize;
        }
        int top = random.nextInt(height - blockSize +1);
        if (top < 0) {
            top = 0;
        }
        return new PositionInfo(left, top);
    }

    @Override
    public @NonNull
    PositionInfo getPositionInfoForSwipeBlock(int width, int height, int blockSize) {
        Random random = new Random();
        int left = random.nextInt(width - blockSize+1);
        int top = random.nextInt(height - blockSize+1);
        if (top < 0) {
            top = 0;
        }
        return new PositionInfo(left, top);
    }

    @Override
    public Paint getBlockShadowPaint() {
        Paint shadowPaint = new Paint();
        shadowPaint.setColor(Color.parseColor("#000000"));
        shadowPaint.setAlpha(165);
        return shadowPaint;
    }

    @Override
    public Paint getBlockBitmapPaint() {
        Paint paint = new Paint();
        return paint;
    }


    @Override
    public void decoreateSwipeBlockBitmap(Canvas canvas, Path shape) {
        Paint paint = new Paint();
        paint.setColor(Color.parseColor("#FFFFFF"));
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(10);
//        paint.setPathEffect(new DashPathEffect(new float[]{20,20},10));
        Path path = new Path(shape);
        canvas.drawPath(path,paint);
    }
}
