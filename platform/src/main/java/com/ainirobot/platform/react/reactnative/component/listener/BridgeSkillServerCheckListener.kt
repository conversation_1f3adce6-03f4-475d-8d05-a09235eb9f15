package com.ainirobot.platform.react.reactnative.component.listener

import android.os.RemoteException
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNSkillServerCheckListener
import com.facebook.react.bridge.WritableNativeMap
/**
 * @date: 2019-06-26
 * @author: zyc
 * @desc: 心跳检测
 */

class BridgeSkillServerCheckListener : IRNSkillServerCheckListener.Stub{

    companion object {
        fun obtain(callbackId:Int): BridgeSkillServerCheckListener? {
            return if(callbackId < 0){
                null
            } else {
                BridgeSkillServerCheckListener(callbackId)
            }
        }
    }

    var id = -1
    private constructor(id:Int) {
        this.id = id
    }

    fun triggerEvent(event: String, param1: String?) {
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putString("event", event)
        if(param1!=null) {
            readableMap.putString("message", param1)
        }
        ReactNativeEventEmitter.triggerEvent("onSkillServer", readableMap)
    }

    @Throws(RemoteException::class)
    override fun onDelay(timeMilliSeconds: Int) {
        android.util.Log.e("BridgeSkillServerCheckListener ", "onDelay")
        triggerEvent("onDelay", timeMilliSeconds.toString())
    }

    @Throws(RemoteException::class)
    override fun onError(message: String) {
        android.util.Log.e("BridgeSkillServerCheckListener ", "onError")
        triggerEvent("onError", message)
    }

    @Throws(RemoteException::class)
    override fun onSuccess(timeMilliSeconds: Int) {
        android.util.Log.i("BridgeSkillServerCheckListener ", "onSuccess")
        triggerEvent("onSuccess", timeMilliSeconds.toString())
    }
}
