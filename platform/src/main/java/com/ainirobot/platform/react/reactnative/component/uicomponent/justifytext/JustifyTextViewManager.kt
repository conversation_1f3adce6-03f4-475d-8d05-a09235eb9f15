package com.ainirobot.platform.react.reactnative.component.uicomponent.justifytext

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.util.Log
import com.ainirobot.platform.bean.ChatNewBean
import com.ainirobot.platform.react.view.WrapperJustifyTextView
import com.ainirobot.platform.utils.GsonUtil
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReadableArray
import com.facebook.react.bridge.WritableMap
import com.facebook.react.common.MapBuilder
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.annotations.ReactProp
import com.facebook.react.uimanager.events.RCTEventEmitter
import java.util.*


@ReactModule(name = JustifyTextViewManager.REACT_CLASS)
class JustifyTextViewManager : SimpleViewManager<WrapperJustifyTextView>() {

    private lateinit var mContext: ThemedReactContext

    companion object {
        const val REACT_CLASS = "JustifyTextView"
        const val TAG = "JustifyTextViewManager"
        const val START_HIGH_LIGHT_ANIMATION = 0
        const val STOP_HIGH_LIGHT_ANIMATION = 1
        const val START_SCROLL_ANIMATION = 2
        const val STOP_SCROLL_ANIMATION = 3
        const val ALL_HIGH_LIGHT = 4
    }

    override fun getName(): String {
        return REACT_CLASS
    }

    override fun createViewInstance(reactContext: ThemedReactContext): WrapperJustifyTextView {
        mContext = reactContext
        return WrapperJustifyTextView(reactContext)
    }

    @ReactProp(name = "params")
    fun setParams(view: WrapperJustifyTextView, array: ReadableArray) {
        view.setMaxWidth(array.getInt(0))
        if (array.size() == 5) {
            view.setStartAndFinalTextColor(array.getString(3), array.getString(4))
            array.getDouble(2).toFloat().let { view.setText(array.getString(1), it) }
        } else {
            view.setText(array.getString(1))
        }

        Log.d(TAG, "setParams=$array");

        //手动测量宽高
        val event = Arguments.createMap()
        view.measure(0, 0)
        event.putInt("height", view.measuredHeight)
        event.putInt("lineCount", view.lineCount)
        event.putString("lineTextList", GsonUtil.toJson(view.lineTextList))
        event.putInt("answerTextDuration", view.answerTextDuration)
        jsReceiveEvent(view, event, "topChange")
    }

    private fun jsReceiveEvent(view: WrapperJustifyTextView, event: WritableMap, eventName: String) {
        mContext.getJSModule(RCTEventEmitter::class.java).receiveEvent(view.id, eventName, event)
    }

    override fun getCommandsMap(): MutableMap<String, Int>? {
        return MapBuilder.of(
                "startHighLight", START_HIGH_LIGHT_ANIMATION,
                "stopHighLight", STOP_HIGH_LIGHT_ANIMATION,
                "startScrollAnimation", START_SCROLL_ANIMATION,
                "stopScrollAnimation", STOP_SCROLL_ANIMATION,
                "startAllHighLight", ALL_HIGH_LIGHT
        )
    }

    override fun getExportedCustomDirectEventTypeConstants(): Map<String, Any>? {
        return MapBuilder.of(
                "onAnimationStart", MapBuilder.of("registrationName", "onAnimationStart"),
                "onAnimationRepeat", MapBuilder.of("registrationName", "onAnimationRepeat")
        )
    }

    override fun receiveCommand(view: WrapperJustifyTextView, commandId: Int, args: ReadableArray?) {
        val chat = ChatNewBean()
        Log.d(TAG, "commandId = $commandId")
        Log.d(TAG, "args = ${args?.toString()}")
        when (commandId) {
            START_HIGH_LIGHT_ANIMATION -> {
                if (args != null && args.size() == 3) {
                    val intArray = IntArray(args.size())
                    intArray[0] = args.getInt(0)
                    intArray[1] = args.getInt(1)
                    intArray[2] = args.getInt(2)
                    view.startHighLightAnimation(chat, intArray)
                }
            }
            STOP_HIGH_LIGHT_ANIMATION -> view.stopHighLightAnimation()
            START_SCROLL_ANIMATION -> {
                val chatBean = ChatNewBean()
                val type = args?.getInt(0)
                val isMuteReply = args?.getBoolean(1)
                if (type == 0) {
                    chatBean.type = ChatNewBean.ChatNewType.ANSWER_TEXT_PURE_GLOBAL
                } else {
                    chatBean.type = ChatNewBean.ChatNewType.ANSWER_TEXT_IMAGE_GLOBAL_SINGLE
                }

                val duration = args?.getArray(2)
                if (isMuteReply != null && duration != null && duration.size() == 3) {
                    val intArray = IntArray(duration.size())
                    intArray[0] = duration.getInt(0)
                    intArray[1] = duration.getInt(1)
                    intArray[2] = duration.getInt(2)
                    view.startScrollAnimation(chatBean, isMuteReply, intArray, object : AnimatorListenerAdapter() {

                        override fun onAnimationStart(animation: Animator?) {
                            Log.d(TAG, "onAnimationStart")
                            val event = Arguments.createMap()
                            jsReceiveEvent(view, event, "onAnimationStart")
                        }

                        override fun onAnimationRepeat(animation: Animator?) {
                            Log.d(TAG, "onAnimationRepeat")
                            val event = Arguments.createMap()
                            jsReceiveEvent(view, event, "onAnimationRepeat")
                        }
                    })
                }
            }
            STOP_SCROLL_ANIMATION -> view.stopScrollAnimation()
            ALL_HIGH_LIGHT -> view.setAllHighLight()
        }
    }


}