package com.ainirobot.platform.react

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.ainirobot.coreservice.client.Definition
import com.ainirobot.coreservice.client.RobotApi
import com.ainirobot.platform.PlatformDef
import com.ainirobot.platform.bi.annotation.OpkOpStatus
import com.ainirobot.platform.bi.annotation.OpkOpType
import com.ainirobot.platform.bi.wrapper.ReportControl
import com.ainirobot.platform.bi.wrapper.manager.bi.impl.RnDownloadOpkPoint
import com.ainirobot.platform.control.ControlManager
import com.ainirobot.platform.character.CharacterManager
import com.ainirobot.platform.react.reactnative.character.ReactCharacter
import com.ainirobot.platform.utils.FileHelper
import org.json.JSONException
import org.json.JSONObject
import java.io.File

class InstallCharacterReceiver: BroadcastReceiver() {
    override fun onReceive(context:Context?, intent:Intent?) {
        Log.d(TAG, intent.toString())
        if(intent!=null) {
            val rpkPath = intent.getStringExtra("path")
            val id = intent.getStringExtra("id")
            if(rpkPath!=null && id!=null) {
                RobotApi.getInstance().updateRnInstallStatus(0, id, Definition.STATUS_RN_INSTALL_START, "", null)
                val memorySize = FileHelper.getAvailableExternalMemorySize()
                val file = File(rpkPath)
                if  (file.exists() && file.length() * OPKHelper.DEFAULT_AVAILABLE_VALUE > memorySize) {
                    RobotApi.getInstance().updateRnInstallStatus(0, id, -107, "{\"code\":-107, \"message\":\"not rpk\"}", null)
                    Log.e(TAG, "sdcard no available free ${file.length() * 5} available: $memorySize")
                    return
                }

                val appBeanV2: AppBeanV2? = OPKHelper.getAppBeanForOPKFile(rpkPath, id)
                if(appBeanV2==null) {
                    RobotApi.getInstance().updateRnInstallStatus(0, id, Definition.STATUS_RN_INSTALL_FAILED, "{\"code\":1001, \"message\":\"not rpk\"}", null)
                    return
                }

                if(appBeanV2?.rpkBean?.appid==null || (!(appBeanV2?.bundleIndexFilePath != null || (appBeanV2?.bundleBizFilePath != null && appBeanV2?.bundlePlatformFilePath != null)))) {
                    RobotApi.getInstance().updateRnInstallStatus(0, id, Definition.STATUS_RN_INSTALL_FAILED, "{\"code\":1002, \"message\":\"error rpk\"}", null)
                    return
                }
                val coreTarget = if (appBeanV2.rpkBean.coreTarget == null) appBeanV2.rpkBean.coreVersion else appBeanV2.rpkBean.coreTarget
                ReportControl.getInstance().reportMsg(RnDownloadOpkPoint(1, appBeanV2.rpkBean.appid, appBeanV2.rpkBean.versionName, coreTarget!!))
                AppManger.addApp(appBeanV2)
                if(ReactCharacter.isReactRunning  && ReactCharacter.currentCharacter == appBeanV2?.rpkBean?.appid) {
                    switchCharacter(appBeanV2?.rpkBean?.appid)
                    OPKHelper.reportOpkOpCmd(id, appBeanV2.rpkBean.appid, OpkOpType.OPK_REGISTER, OpkOpStatus.OPK_OP_SUCCESS)
                    RobotApi.getInstance().updateRnInstallStatus(0, id, Definition.STATUS_RN_INSTALL_SUCCESS, "{\"code\":2001, \"message\":\"update success\"}", null)
                } else {
                    if (registerCharacter(appBeanV2.rpkBean.appid)) {
                        OPKHelper.reportOpkOpCmd(id, appBeanV2.rpkBean.appid, OpkOpType.OPK_REGISTER, OpkOpStatus.OPK_OP_SUCCESS)
                    } else {
                        OPKHelper.reportOpkOpCmd(id, appBeanV2.rpkBean.appid, OpkOpType.OPK_REGISTER, OpkOpStatus.RECEIVE_CMD)
                    }

                    RobotApi.getInstance().updateRnInstallStatus(0, id, Definition.STATUS_RN_INSTALL_SUCCESS, "{\"code\":2002, \"message\":\"install success\"}", null)
                }
            } else {
                RobotApi.getInstance().updateRnInstallStatus(0, id, Definition.STATUS_RN_INSTALL_FAILED, "{\"code\":1000, \"message\":\"error push\"}", null)
            }
        }
    }

    private fun registerCharacter(appid: String): Boolean {
        if (appid.isNullOrEmpty()) {
            Log.d(TAG, "registerCharacter appid is empty")
            return false

        }
        Log.i(TAG, "registerCharacter")
        val characterManager = CharacterManager.getInstance()
        val character = ReactCharacter(appid)
        return characterManager.registerCharacterFromRN(character.getName(), character)
    }

    private fun switchCharacter(characterName: String) {
        try {
            val json = JSONObject()
            json.put("name", characterName)

            ControlManager.handleRequest(0, PlatformDef.SWITCH_CHARACTER, "", json.toString());
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    companion object {

        /**
         * 保存相关数据
         */
        private const val TAG = "CharacterReceiver"
    }
}
