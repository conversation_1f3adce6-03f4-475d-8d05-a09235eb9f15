package com.ainirobot.platform.react.view;

import android.util.Log;

import static android.view.View.INVISIBLE;
import static android.view.View.VISIBLE;

public class BottomWaveManager implements SurfaceAnimation.OnFrameAnimationListener {

    private static final String TAG = "BottomWaveManager";
    /**
     * 底部wave动效是否显示标签
     */
    private boolean mBottomWaveShow = false;

    private SurfaceAnimation mSurfaceAnimation;

    public BottomWaveManager(SurfaceAnimation voiceNewWaveLayout) {
        this.mSurfaceAnimation = voiceNewWaveLayout;
        this.mSurfaceAnimation.setOnFrameAnimationListener(this);
    }

    @Override
    public void onFrameAnimationStart(Voice.VoiceType voiceType) {
        if (voiceType == Voice.VoiceType.START && mSurfaceAnimation.getVisibility() != VISIBLE) {
            //setDefaultRecognizeText();
        }
    }

    /**
     * 动画结束，进入监听
     *
     * @param voiceType
     */
    @Override
    public void onFrameAnimationFinished(Voice.VoiceType voiceType) {
        Log.d(TAG, "onFrameAnimationFinished mVoiceNewWaveLayout.visibility =========== " + Integer.toString(mSurfaceAnimation.getVisibility()));
        if (mSurfaceAnimation.getVisibility() == INVISIBLE || !mBottomWaveShow) {
            return;
        }

        if (voiceType == Voice.VoiceType.START) {
            mSurfaceAnimation.startAnimation(Voice.VoiceType.LISTEN);
        }
        if (voiceType == Voice.VoiceType.END) {
            mSurfaceAnimation.stopAnimation();
        }
    }

    /**
     * 设置为加载中
     *
     * @param voiceType
     */
    public void startVoiceByType(final Voice.VoiceType voiceType) {
        if (voiceType == Voice.VoiceType.LOADING && mSurfaceAnimation.getVoiceType() == Voice.VoiceType.SPEAK) {
            mSurfaceAnimation.startAnimation(Voice.VoiceType.LOADING);
        }
    }

    /**
     * 是否在倾听状态
     *
     * @return
     */
    private boolean isListenState() {
        return mSurfaceAnimation.getVoiceType() == Voice.VoiceType.LISTEN || mSurfaceAnimation.getVoiceType() == Voice.VoiceType.START;
    }

    /**
     * 是否显示底部动画
     *
     * @return
     */
    public boolean isShowBottomWave() {
        return mBottomWaveShow;
    }

    /**
     * 重置唤醒状态
     */
    public void resetWakeupState() {
        if (!isShowBottomWave()) {
            return;
        }
        Log.d(TAG, "resetWakeupState");
        Log.d(TAG, "mVoiceNewWaveLayout isShown ===========" + mSurfaceAnimation.isShown());
        mSurfaceAnimation.setDrawingFlag(true);
        startAnimation(Voice.VoiceType.START);
    }

    /**
     * 显示动画
     */
    public void showBottomWave(boolean bottomWaveShow) {
        this.mBottomWaveShow = bottomWaveShow;
        mSurfaceAnimation.setVisibility(bottomWaveShow ? VISIBLE : INVISIBLE);
        mSurfaceAnimation.setShowBottomWave(bottomWaveShow);
        if (mBottomWaveShow) {
            resetWakeupState();
        } else {
            mSurfaceAnimation.stopAnimation();
        }
    }

    /**
     * 播放停止动画
     */
    public void stopBottomWaveAnimation() {
        if (isShowBottomWave()) {
            startAnimation(Voice.VoiceType.END);
        }
    }

    public void startAnimation(Voice.VoiceType speak) {
        if (isShowBottomWave()) {
            mSurfaceAnimation.startAnimation(speak);
        }
    }
}
