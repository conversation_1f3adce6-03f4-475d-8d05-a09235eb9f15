package com.ainirobot.platform.react.reactnative.component.uicomponent.camerafilter

import android.util.Log
import com.facebook.react.bridge.Arguments
import com.facebook.react.uimanager.events.Event
import com.facebook.react.uimanager.events.RCTEventEmitter

/**
 * @date: 2020-04-21
 * @author: songben
 */

class CameraFilterOnInitSuccessEvent(viewId: Int) : Event<CameraFilterOnInitSuccessEvent>(viewId) {

    override fun getEventName(): String {
        return  CameraFilterViewManager.Companion.Events.EVENT_ONINITSUCCESS.toString()
    }

    override fun dispatch(rctEventEmitter: RCTEventEmitter) {
        Log.i(CameraFilterViewManager.TAG,"CameraFilterOnInitSuccessEvent dispatch")
        val map = Arguments.createMap()
        rctEventEmitter.receiveEvent(viewTag, eventName, map)
    }
}


class CameraFilterOnFailureEvent(viewId: Int, private val errCode: Int)
    : Event<CameraFilterOnFailureEvent>(viewId) {

    override fun getEventName(): String {
        return CameraFilterViewManager.Companion.Events.EVENT_ONFAILURE.toString()
    }

    override fun dispatch(rctEventEmitter: RCTEventEmitter) {
        Log.i(CameraFilterViewManager.TAG,"CameraFilterOnFailureEvent dispatch")
        val map = Arguments.createMap()
        map.putInt("errCode", errCode)
        rctEventEmitter.receiveEvent(viewTag, eventName, map)
    }
}

class CameraFilterOnSnapShotEvent(viewId: Int, private val path: String)
    : Event<CameraFilterOnSnapShotEvent>(viewId) {

    override fun getEventName(): String {
        return CameraFilterViewManager.Companion.Events.EVENT_ONSNAPSHOT.toString()
    }

    override fun dispatch(rctEventEmitter: RCTEventEmitter) {
        Log.i(CameraFilterViewManager.TAG,"CameraFilterOnSnapShotEvent dispatch")
        val map = Arguments.createMap()
        map.putString("path", path)
        rctEventEmitter.receiveEvent(viewTag, eventName, map)
    }
}

