package com.ainirobot.platform.react.reactnative.component.common

import android.os.SystemClock
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.component.listener.BridgeVolumeListener
import com.ainirobot.platform.react.reactnative.component.listener.BridgeCommandListener
import com.ainirobot.platform.react.reactnative.component.listener.BridgeConnectivityListener
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod

class DeviceBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    companion object {
        const val TAG = "DeviceBridgeModule"
    }
    override fun getName(): String {
        return "DeviceManager"
    }

    @ReactMethod
    fun startBluetoothDetect(type: String?) {
        RNClientManager.instance?.deviceManager?.startBluetoothDetect(type)
    }

    @ReactMethod
    fun getRobotName(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.deviceManager?.robotName)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun elapsedRealtime(): Double {
        return SystemClock.elapsedRealtime().toDouble()
    }

    @ReactMethod
    fun getCorpUuid(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.deviceManager?.corpUuid)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getVoiceCorpId(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.deviceManager?.voiceCorpId)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }


    @ReactMethod
    fun registerConnectivityChanged(callbackId: Int) {
        try {
            if(callbackId >= 0) {
                RNClientManager.instance?.deviceManager?.registerConnectivityChanged(BridgeConnectivityListener.obtain(callbackId))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun unregisterConnectivityChanged() {
        try {
            RNClientManager.instance?.deviceManager?.unregisterConnectivityChanged()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun toggleAirplaneMode() {
        try {
            RNClientManager.instance?.deviceManager?.toggleAirplaneMode()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun getCpuTemperature(callbackId: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.apiManager?.getCpuTemperature(BridgeCommandListener.obtain(callbackId)))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun getCpuUsage(promise: Promise) {
        try {
            val usage = RNClientManager.instance?.deviceManager?.cpuUsage
            promise.resolve(usage)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun isSurfaceShareUsed(): Boolean? {
        try {
            return RNClientManager.instance?.deviceManager?.isSurfaceShareUsed
        } catch (e: Exception) {
            e.printStackTrace()
            return false;
        }
    }

    @ReactMethod
    fun getStreamVolume(streamType: Int, promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.deviceManager?.getStreamVolume(streamType))
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun setStreamVolume(streamType: Int, index: Int, flags: Int) {
        try {
            RNClientManager.instance?.deviceManager?.setStreamVolume(streamType, index, flags)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun registerVolumeChanged(callbackId: Int) {
        try {
            if(callbackId >= 0) {
                RNClientManager.instance?.deviceManager?.registerVolumeChanged(callbackId, BridgeVolumeListener.obtain(callbackId))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun unregisterVolumeChanged(callbackId: Int) {
        try {
            RNClientManager.instance?.deviceManager?.unregisterVolumeChanged(callbackId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
