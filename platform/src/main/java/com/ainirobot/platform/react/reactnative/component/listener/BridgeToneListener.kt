package com.ainirobot.platform.react.reactnative.component.listener

import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNToneListener
import com.facebook.react.bridge.WritableNativeMap

class BridgeToneListener : IRNToneListener.Stub {

    companion object {
        fun obtain(callbackId:Int): BridgeToneListener? {
            return BridgeToneListener(callbackId)
        }
    }

    var id = -1

    private constructor(id: Int) {
        this.id = id
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
    }

    fun triggerEvent(event: String) {
        if (id < 0) {
            return
        }
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putString("event", event)
        ReactNativeEventEmitter.triggerEvent("onPlayTone", readableMap)
    }

    override fun onStart() {
        triggerEvent("onStart")
    }

    override fun onStop() {
        triggerEvent("onStop")
    }

    override fun onError() {
        triggerEvent("onError")
    }

    override fun onComplete() {
        triggerEvent("onComplete")
    }
}