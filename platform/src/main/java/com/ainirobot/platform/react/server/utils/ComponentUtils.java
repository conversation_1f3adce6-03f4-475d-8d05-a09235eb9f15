package com.ainirobot.platform.react.server.utils;/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

import android.util.ArraySet;
import android.util.Log;

import com.ainirobot.platform.component.AIIntelligenceTrackComponent;
import com.ainirobot.platform.component.AutoResetEstimateComponent;
import com.ainirobot.platform.component.BasicMotionComponent;
import com.ainirobot.platform.component.BodyFollowComponent;
import com.ainirobot.platform.component.ChargeComponent;
import com.ainirobot.platform.component.ChargeElevatorComponent;
import com.ainirobot.platform.component.ChargeStartComponent;
import com.ainirobot.platform.component.Component;
import com.ainirobot.platform.component.CruiseComponent;
import com.ainirobot.platform.component.FaceTrackComponent;
import com.ainirobot.platform.component.ForwardComponent;
import com.ainirobot.platform.component.GetCruiseRouteComponent;
import com.ainirobot.platform.component.GetDistanceWithPlaceComponent;
import com.ainirobot.platform.component.GetNearestPlaceComponent;
import com.ainirobot.platform.component.HeadTurnComponent;
import com.ainirobot.platform.component.HeadTurnGroupComponent;
import com.ainirobot.platform.component.JudgeInChargingPileComponent;
import com.ainirobot.platform.component.LeadingTrackComponent;
import com.ainirobot.platform.component.LeavePileComponent;
import com.ainirobot.platform.component.LightGroupComponent;
import com.ainirobot.platform.component.ModifyNameComponent;
import com.ainirobot.platform.component.MultiRobotNavigateComponent;
import com.ainirobot.platform.component.NavigationAdvanceComponent;
import com.ainirobot.platform.component.NavigationBackComponent;
import com.ainirobot.platform.component.NavigationCheckComponent;
import com.ainirobot.platform.component.NavigationComponent;
import com.ainirobot.platform.component.NavigationElevatorComponent;
import com.ainirobot.platform.component.NavigationTransferComponent;
import com.ainirobot.platform.component.PersonAppearComponent;
import com.ainirobot.platform.component.PersonDisappearComponent;
import com.ainirobot.platform.component.ProTrayLEDComponent;
import com.ainirobot.platform.component.ReceptionRegisterComponent;
import com.ainirobot.platform.component.RecognizeComponent;
import com.ainirobot.platform.component.RecoverPositiveComponent;
import com.ainirobot.platform.component.RegisterComponent;
import com.ainirobot.platform.component.ReservationCodeComponent;
import com.ainirobot.platform.component.ResetEstimateComponent;
import com.ainirobot.platform.component.RobotStandbyComponent;
import com.ainirobot.platform.component.SetCruiseRouteComponent;
import com.ainirobot.platform.component.SetNavigationConfigComponent;
import com.ainirobot.platform.component.SoundLocalizationComponent;
import com.ainirobot.platform.component.StandardFaceTrackComponent;
import com.ainirobot.platform.component.StatusComponent;
import com.ainirobot.platform.component.UvcCameraComponent;
import com.ainirobot.platform.component.WakeupAndPreWakeupStartCheckComponent;
import com.ainirobot.platform.react.server.control.ComponentManager;

import java.util.Set;

public class ComponentUtils {

    private static final String TAG  = ComponentUtils.class.getSimpleName();

    public static final String COMPONENT_WAKEUP_AND_PRE_WAKEUP_START_CHECK = "WakeupAndPreWakeupStartCheck";
    public static final String COMPONENT_STANDARD_FACE_TRACK = "StandardFaceTrack";
    public static final String COMPONENT_SOUND_LOCALIZATION = "SoundLocalizationComponent";
    public static final String COMPONENT_SET_CRUISE_ROUTE = "SetCruiseRoute";
    public static final String COMPONENT_ROBOT_STANDBY = "RobotStandby";
    public static final String COMPONENT_RESET_ESTIMATE = "ResetEstimate";
    public static final String COMPONENT_RESERVATION_CODE = "ReservationCode";
    public static final String COMPONENT_REGISTER = "Register";
    public static final String COMPONENT_RECOVER_POSITIVE = "RecoverPositive";
    public static final String COMPONENT_RECOGNIZE = "Recognize";
    public static final String COMPONENT_RECEPTION_REGISTER = "ReceptionRegister";
    public static final String COMPONENT_PERSON_APPEAR = "PersonAppear";
    public static final String COMPONENT_PERSON_DISAPPEAR = "PersonDisappear";
    public static final String COMPONENT_NAVIGATION = "Navigation";
    public static final String COMPONENT_NAVIGATION_BACK = "NavigationBack";
    public static final String COMPONENT_MODIFY_NAME = "ModifyName";
    public static final String COMPONENT_LEADING_TRACK = "LeadingTrack";
    public static final String COMPONENT_HEAD_TURN_GROUP = "HeadTurnGroup";
    public static final String COMPONENT_HEAD_TURN = "HeadTurn";
    public static final String COMPONENT_GET_DISTANCE_WITH_PLACE = "GetDistanceWithPlace";
    public static final String COMPONENT_GET_NEAREST_PLACE = "GetNearestPlace";
    public static final String COMPONENT_GET_CRUISE_ROUTE = "GetCruiseRoute";
    public static final String COMPONENT_FORWARD = "Forward";
    public static final String COMPONENT_FACE_TRACK = "FaceTrack";
    public static final String COMPONENT_CRUISE = "Cruise";
    public static final String COMPONENT_CHARGE = "Charge";
    public static final String COMPONENT_CHARGE_ELEVATOR = "ChargeElevator";
    public static final String COMPONENT_AUTO_RESET_ESTIMATE_COMPONENT = "AutoResetEstimate";
    public static final String COMPONENT_CHARGE_START = "ChargeStart";
    public static final String COMPONENT_BODY_FOLLOW = "BodyFollow";
    public static final String COMPONENT_STATUS = "Status";
    public static final String COMPONENT_BASIC_MOTION = "BasicMotion";
    public static final String COMPONENT_LIGHT_GROUP = "LightGroup";
    public static final String COMPONENT_NAVIGATION_CHECK = "NavigationCheck";
    public static final String COMPONENT_NAVIGATION_TRANSFER = "NavigationTransfer";
    public static final String COMPONENT_NAVIGATION_MULTI_ROBOT = "MultiRobotNavigation";
    public static final String COMPONENT_LEAVE_PILE="LeavePile";
    public static final String COMPONENT_JUDGECHARGING_PILE="JudgeChargingPile";
    public static final String COMPONENT_SET_NAVIGATION_CONFIG = "SetNavigationConfig";
    public static final String COMPONENT_NAVIGATION_ELEVATOR = "NavigationElevator";
    public static final String COMPONENT_NAVIGATION_ADVANCE = "NavigationAdvance";
    public static final String COMPONENT_UVC_CAMERA = "UvcCamera";
    public static final String COMPONENT_TRAY_LED = "TrayLED";
    public static final String COMPONENT_AI_INTELLIGENCE_TRACK = "AIIntelligenceTrack";

    public static Set<String> initAllComponent() {
        Set<String> componentSet = new ArraySet<>();
        componentSet.add(COMPONENT_WAKEUP_AND_PRE_WAKEUP_START_CHECK);
        componentSet.add(COMPONENT_STANDARD_FACE_TRACK);
        componentSet.add(COMPONENT_SOUND_LOCALIZATION);
        componentSet.add(COMPONENT_SET_CRUISE_ROUTE);
        componentSet.add(COMPONENT_ROBOT_STANDBY);
        componentSet.add(COMPONENT_RESET_ESTIMATE);
        componentSet.add(COMPONENT_RESERVATION_CODE);
        componentSet.add(COMPONENT_REGISTER);
        componentSet.add(COMPONENT_RECOVER_POSITIVE);
        componentSet.add(COMPONENT_RECOGNIZE);
        componentSet.add(COMPONENT_RECEPTION_REGISTER);
        componentSet.add(COMPONENT_PERSON_APPEAR);
        componentSet.add(COMPONENT_PERSON_DISAPPEAR);
        componentSet.add(COMPONENT_NAVIGATION);
        componentSet.add(COMPONENT_NAVIGATION_BACK);
        componentSet.add(COMPONENT_MODIFY_NAME);
        componentSet.add(COMPONENT_LEADING_TRACK);
        componentSet.add(COMPONENT_HEAD_TURN_GROUP);
        componentSet.add(COMPONENT_HEAD_TURN);
        componentSet.add(COMPONENT_GET_DISTANCE_WITH_PLACE);
        componentSet.add(COMPONENT_GET_NEAREST_PLACE);
        componentSet.add(COMPONENT_GET_CRUISE_ROUTE);
        componentSet.add(COMPONENT_FORWARD);
        componentSet.add(COMPONENT_FACE_TRACK);
        componentSet.add(COMPONENT_CRUISE);
        componentSet.add(COMPONENT_CHARGE);
        componentSet.add(COMPONENT_CHARGE_ELEVATOR);
        componentSet.add(COMPONENT_AUTO_RESET_ESTIMATE_COMPONENT);
        componentSet.add(COMPONENT_CHARGE_START);
        componentSet.add(COMPONENT_BODY_FOLLOW);
        componentSet.add(COMPONENT_STATUS);
        componentSet.add(COMPONENT_BASIC_MOTION);
        componentSet.add(COMPONENT_LIGHT_GROUP);
        componentSet.add(COMPONENT_NAVIGATION_CHECK);
        componentSet.add(COMPONENT_NAVIGATION_TRANSFER);
        componentSet.add(COMPONENT_NAVIGATION_MULTI_ROBOT);
        componentSet.add(COMPONENT_LEAVE_PILE);
        componentSet.add(COMPONENT_JUDGECHARGING_PILE);
        componentSet.add(COMPONENT_SET_NAVIGATION_CONFIG);
        componentSet.add(COMPONENT_NAVIGATION_ELEVATOR);
        componentSet.add(COMPONENT_UVC_CAMERA);
        componentSet.add(COMPONENT_TRAY_LED);
        componentSet.add(COMPONENT_NAVIGATION_ADVANCE);
        componentSet.add(COMPONENT_AI_INTELLIGENCE_TRACK);
        return componentSet;
    }

    public static Component initialComponent(String componentName) {
        Log.d(TAG, "initialComponent componentName: " + componentName);
        switch (componentName) {
            case COMPONENT_WAKEUP_AND_PRE_WAKEUP_START_CHECK:
                return new WakeupAndPreWakeupStartCheckComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_STANDARD_FACE_TRACK:
                return new StandardFaceTrackComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_SOUND_LOCALIZATION:
                return new SoundLocalizationComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_SET_CRUISE_ROUTE:
                return new SetCruiseRouteComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_ROBOT_STANDBY:
                return new RobotStandbyComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_RESET_ESTIMATE:
                return new ResetEstimateComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_RESERVATION_CODE:
                return new ReservationCodeComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_REGISTER:
                return new RegisterComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_RECOVER_POSITIVE:
                return new RecoverPositiveComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_RECOGNIZE:
                return new RecognizeComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_RECEPTION_REGISTER:
                return new ReceptionRegisterComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_PERSON_APPEAR:
                return new PersonAppearComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_PERSON_DISAPPEAR:
                return new PersonDisappearComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_NAVIGATION:
                return new NavigationComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_NAVIGATION_BACK:
                return new NavigationBackComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_MODIFY_NAME:
                return new ModifyNameComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_LEADING_TRACK:
                return new LeadingTrackComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_HEAD_TURN_GROUP:
                return new HeadTurnGroupComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_HEAD_TURN:
                return new HeadTurnComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_GET_DISTANCE_WITH_PLACE:
                return new GetDistanceWithPlaceComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_GET_NEAREST_PLACE:
                return new GetNearestPlaceComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_GET_CRUISE_ROUTE:
                return new GetCruiseRouteComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_FORWARD:
                return new ForwardComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_FACE_TRACK:
                return new FaceTrackComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_CRUISE:
                return new CruiseComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_CHARGE:
                return new ChargeComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_CHARGE_ELEVATOR:
                return new ChargeElevatorComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_AUTO_RESET_ESTIMATE_COMPONENT:
                return new AutoResetEstimateComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_CHARGE_START:
                return new ChargeStartComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_BODY_FOLLOW:
                return new BodyFollowComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_STATUS:
                return new StatusComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_BASIC_MOTION:
                return new BasicMotionComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_LIGHT_GROUP:
                return new LightGroupComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_NAVIGATION_CHECK:
                return new NavigationCheckComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_NAVIGATION_TRANSFER:
                return new NavigationTransferComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_NAVIGATION_MULTI_ROBOT:
                return new MultiRobotNavigateComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_LEAVE_PILE:
                return new LeavePileComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_JUDGECHARGING_PILE:
                return new JudgeInChargingPileComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_SET_NAVIGATION_CONFIG:
                return new SetNavigationConfigComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_NAVIGATION_ELEVATOR:
                return new NavigationElevatorComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_UVC_CAMERA:
                return new UvcCameraComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_TRAY_LED:
                return new ProTrayLEDComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_NAVIGATION_ADVANCE:
                return new NavigationAdvanceComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            case COMPONENT_AI_INTELLIGENCE_TRACK:
                return new AIIntelligenceTrackComponent(TAG,
                        ComponentManager.getInstance().getThread().getLooper());
            default:
                return null;
        }
    }
}
