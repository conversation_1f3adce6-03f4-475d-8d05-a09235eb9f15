/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.react.view;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import androidx.annotation.ColorInt;
import androidx.annotation.ColorRes;
import androidx.annotation.Nullable;
import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ainirobot.platform.bean.ChatNewBean;
import com.ainirobot.platform.utils.DimenUtils;
import com.ainirobot.platform.utils.GsonUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 思路：首先测量正常的textView的行数，除去最后一行，其他一行生成一个JustifyTextView
 * JustifyTextView：两端对齐
 * 动画：行自动高亮，同时开启自动滚动
 */
public class WrapperJustifyTextView extends LinearLayout {

    private static final String TAG = "WrapperJustifyTextView";
    static String CHN_NUMBER[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
    static String CHN_UNIT[] = {"", "十", "百", "千"};          //权位
    static String CHN_UNIT_SECTION[] = {"", "万", "亿", "万亿"}; //节权位

    private int mLineCount;
    private int mLineHeight;
    private float mTextSize = 70.0f;
    private int mTextColor = Color.parseColor("#4BFFFFFF");
    private int mTextFinalColor = Color.parseColor("#FFFFFFFF");
    private int mMaxWidth;
    private AnimatorSet mScrollAnimator;
    private AnimatorSet mAnimator;
    private String mText;
    private int mLineLength;

    private List<String> mLineTextList;
    private int mOrientation;

    public WrapperJustifyTextView(Context context) {
        super(context);
        init();
    }

    public WrapperJustifyTextView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public WrapperJustifyTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public void setText(CharSequence text, float textSize) {
        // 将文案中的 \\n 转换为实际的换行符 \n
        String processedText = text.toString().replace("\\n", "\n");
        mText = processedText;
        mTextSize = textSize;
        calculateSingleTextView(processedText);
        generateJustifyTextView(processedText);
    }

    public void setText(CharSequence text) {
        // 将文案中的 \\n 转换为实际的换行符 \n
        String processedText = text.toString().replace("\\n", "\n");
        mText = processedText;
        calculateSingleTextView(processedText);
        generateJustifyTextView(processedText);
    }

    public void setTextColor(int color) {
        mTextColor = color;
        for (int i = 0; i < getChildCount(); i++) {
            TextView textView = (TextView) getChildAt(i);
            textView.setTextColor(color);
        }
    }

    public void setStartAndFinalTextColor(String startColor,String endColor) {
        mTextColor = Color.parseColor(startColor);
        mTextFinalColor = Color.parseColor(endColor);
    }

    public void setMaxWidth(int mMaxWidth) {
        this.mMaxWidth = mMaxWidth;
        ViewGroup.LayoutParams layoutParams = getLayoutParams();
        if (layoutParams != null) {
            layoutParams.width = mMaxWidth;
            requestLayout();
        }
    }

    public void setTextColorLine(@ColorInt int color, int line) {
        for (int i = 0; i < getChildCount(); i++) {
            if (i == line) {
                TextView textView = (TextView) getChildAt(i);
                textView.setTextColor(color);
            }
        }
    }

    public int getLineCount() {
        return mLineCount;
    }

    public List<String> getLineTextList() {
        Log.d(TAG, "getLineTextList " + GsonUtil.toJson(mLineTextList));
        return mLineTextList;
    }

    public void setTextSize(float textSize) {
        if (textSize > 0){
            mTextSize = textSize;
            Log.d(TAG, "outside setTextSize  px : "+mTextSize);
            generateJustifyTextView(mText);
        }else {
            Log.e(TAG,"setTextSize textSize can't be 0 px !");
        }
    }

    private void init() {
        setOrientation(VERTICAL);
        // 横屏适配
        mOrientation = getResources().getConfiguration().orientation;
    }

    private void generateJustifyTextView(CharSequence text) {
        Log.d(TAG, "generateJustifyTextView called mTextSize : "+mTextSize);
        removeAllViews();
        if (TextUtils.isEmpty(text)) {
            return;
        }
        for (int i = 0; i < mLineTextList.size(); i++) {
            String lineText = mLineTextList.get(i);
            // 跳过空行，避免显示多余的空白
            if (TextUtils.isEmpty(lineText.trim())) {
                continue;
            }
            
            TextView textView;
            //最后一行不处理
//            if (i == mLineTextList.size() - 1) {
//                textView = new TextView(getContext());
//            } else {
//                textView = new JustifyTextView(getContext());
//            }
            textView = new TextView(getContext());

            if (mOrientation != Configuration.ORIENTATION_LANDSCAPE){
                textView.setLineSpacing(10, 1);
            }
            textView.setText(lineText);
            textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, mTextSize);
            textView.setTextColor(mTextColor);
            addView(textView);
            if (i != 0) {
                LayoutParams layoutParams = (LayoutParams) textView.getLayoutParams();
                layoutParams.topMargin = (mOrientation != Configuration.ORIENTATION_LANDSCAPE ? 10 : 15);
                Log.d(TAG, "generateJustifyTextView topMargin:"+layoutParams.topMargin);
            }
            textView.requestLayout();
        }
        //手动测量宽高
        measure(0, 0);
        mLineHeight = getMeasuredHeight() / mLineCount;
        Log.d(TAG, "post lineHeight = " + mLineHeight);

    }

    /**
     * 计算一个textView
     * 总共的行数；每一行的文字，开头和结尾
     *
     * @param text
     */
    private void calculateSingleTextView(CharSequence text) {
        if (text == null
                || text.length() == 0) {
            return;
        }
        int widthMeasureSpec = MeasureSpec.makeMeasureSpec(mMaxWidth - 10, MeasureSpec.AT_MOST);
        int heightMeasureSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.AT_MOST);
        TextView targetTextView = getTargetTextView(text);
        targetTextView.measure(widthMeasureSpec, heightMeasureSpec);
        mLineCount = targetTextView.getLineCount();

        //每一行的text数据
        mLineTextList = new ArrayList<>();
        Layout layout = targetTextView.getLayout();
        int start = 0;
        int end;
        String textStr = text.toString();
        for (int i = 0; i < targetTextView.getLineCount(); i++) {
            end = layout.getLineEnd(i);
            String lineText = textStr.substring(start, end);
            // 去掉行末的换行符
            if (lineText.endsWith("\n")) {
                lineText = lineText.substring(0, lineText.length() - 1);
            }
            mLineTextList.add(lineText);
            start = end;
        }
        Log.d(TAG, "lineCount = " + mLineCount);
        Log.d(TAG, "mLineTextList = " + mLineTextList.toString());

    }

    private TextView getTargetTextView(CharSequence text) {
        Log.d(TAG, "getTargetTextView setTextSize : "+mTextSize);
        TextView textView = new TextView(getContext());
        textView.setText(text);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, mTextSize);
        return textView;
    }

    public void startScrollAnimation(final ChatNewBean bean
            , boolean isReply, int[] duration, final Animator.AnimatorListener listener) {
        if (bean == null) {
            return;
        }
        Log.i(TAG, "duration = " + Arrays.toString(duration));
        Log.i(TAG, "oneLineHeight = " + mLineHeight);
        Log.i(TAG, "mLineCount = " + mLineCount);
        //纯文本高亮第三行后开始自动滚动，视频或图片第二行后自动滚动
        int delay = 0;
        int multiple = 0;
        switch (bean.getType()) {
            case ANSWER_TEXT_PURE_GLOBAL:
                if (isReply) {
                    multiple = 1;
                } else {
                    multiple = 2;
                }
                break;
            case ANSWER_TEXT_IMAGE_GLOBAL_SINGLE:
            case ANSWER_TEXT_GLOBAL_VIDEO:
                //图片和视频不会有静默回复
                multiple = 2;
                break;
        }
        if (multiple >= mLineTextList.size()) {
            multiple = mLineTextList.size() - 1;
        }
        for (int i = 0; i <= multiple; i++) {
            delay += getCurrentLineDuration(i, duration);
        }
        stopScrollAnimation();
        mScrollAnimator = new AnimatorSet();

        List<Animator> list = new ArrayList<>();
        for (int i = multiple; i < mLineCount; i++) {
            ValueAnimator animator = ValueAnimator.ofInt(0, 1);
            Log.d(TAG, "startAnimationScroll i = " + i + ",duration=" + getCurrentLineDuration(i, duration));
            animator.setDuration(getCurrentLineDuration(i, duration));
            animator.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationStart(Animator animation) {
                    if (listener != null) {
                        listener.onAnimationRepeat(animation);
                    }
                }
            });
            list.add(animator);
        }
        mScrollAnimator.setStartDelay(delay);
        mScrollAnimator.playSequentially(list);
        mScrollAnimator.start();
        if (listener != null) {
            listener.onAnimationStart(null);
        }
    }

    public void startHighLightAnimation(ChatNewBean answer, int[] duration) {
        if (answer == null) {
            return;
        }
        Log.i(TAG, "duration = " + duration);
        stopHighLightAnimation();
        setTextColor(mTextColor);
        startTextHeightAnimation(getLineCount(), duration);
    }

    private void startTextHeightAnimation(int lines, int[] duration) {
        Log.i(TAG, "startTextHeightAnimation");
        if (mAnimator != null) {
            mAnimator.cancel();
        }
        mAnimator = new AnimatorSet();
        //初始化第一行高亮
        realTextHigh(0);
        List<Animator> list = new ArrayList<>();
        for (int i = 0; i < lines; i++) {
            ValueAnimator animator = ValueAnimator.ofInt(0, 1);
            animator.setDuration(getCurrentLineDuration(i, duration));
            Log.d(TAG, "startAnimationTextHeight i =" + i + ",duration=" + getCurrentLineDuration(i, duration));
            final int finalI = i;
            animator.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationStart(Animator animation) {
                    if (finalI != 0) {
                        setTextColorLine(mTextFinalColor, finalI - 1);
                        realTextHigh(finalI);
                    }
                }
            });
            list.add(animator);
        }
        mAnimator.playSequentially(list);
        mAnimator.start();
    }

    public int getAnswerTextDuration() {
        int[] durationArray = {215, 71, 210};
        int duration = durationArray[0] * (getChineseCount(this.mText))
                + durationArray[1] * getEnglishCount(this.mText)
                + durationArray[2] * getDigitCount(this.mText);
        Log.d(TAG, "getAnswerTextDuration chinese count = " + getChineseCount(this.mText) +
                ",english count=" + getEnglishCount(this.mText) + ",digit count=" + getDigitCount(this.mText));
        return duration;
    }

    private int getCurrentLineDuration(int line, int durationArray[]) {
        if (mLineTextList == null || mLineTextList.size() <= 1) {
            return 0;
        }
        String text = mLineTextList.get(line);
        if (TextUtils.isEmpty(text)) {
            return 1;
        }
        int duration = durationArray[0] * (getChineseCount(text))
                + durationArray[1] * getEnglishCount(text)
                + durationArray[2] * getDigitCount(text);
        Log.d(TAG, "chinese count = " + getChineseCount(text) +
                ",english count=" + getEnglishCount(text) + ",digit count=" + getDigitCount(text));
        return duration;
    }

    private static int getChineseCount(String text) {
        if (TextUtils.isEmpty(text)) {
            return 0;
        }
        int count = 0;
        String regEx = "[\u4e00-\u9fa5]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(text);
        while (m.find()) {
            count++;
        }
        return count;
    }

    public static int getDigitCount(String str) {
        if (str == null) {
            return 0;
        }
        int count = 0;
        //考虑14:00这种特殊情况
        String regex = "^(20|21|22|23|[0-1]\\d):[0-5]\\d";
        Pattern pattern = Pattern.compile(regex);
        Matcher m = pattern.matcher(str);
        while (m.find()) {
            String group = m.group();
            String[] times = group.split(":");
            if (times != null) {
                int hour = Integer.parseInt(times[0]);
                int minute = Integer.parseInt(times[1]);
                if (hour > 10) {
                    count += 3;
                } else {
                    count += 2;
                }
                if (minute > 10) {
                    count += 3;
                } else if (minute == 0) {
                    count += 0;
                } else {
                    count += 2;
                }
                System.out.println("WrapperJustifyTextView hour=" + hour + ",minute=" + minute);
            }
            str = str.replace(group, " ");
        }
        if (count != 0) {
            System.out.println("WrapperJustifyTextView count=" + count + ",str=" + str);
        }
        Pattern p = Pattern.compile("[0-9]+");
        Matcher matcher = p.matcher(str);
        List<Integer> digitList = new ArrayList<>();
        while (matcher.find()) {
            try {
                digitList.add(Integer.parseInt(matcher.group()));
            } catch (Exception e) {

            }
        }
        List<String> bigList = new ArrayList<>();
        for (Integer data : digitList) {
            bigList.add(numberToChn(data));
        }
        System.out.println("WrapperJustifyTextView bigList=" + bigList.toString());
        for (String data : bigList) {
            count += data.length();
        }
        return count;
    }

    public static String numberToChn(int num) {
        StringBuffer returnStr = new StringBuffer();
        Boolean needZero = false;
        int pos = 0;           //节权位的位置
        if (num == 0) {
            //如果num为0，进行特殊处理。
            returnStr.insert(0, CHN_NUMBER[0]);
        }
        while (num > 0) {
            int section = num % 10000;
            if (needZero) {
                returnStr.insert(0, CHN_NUMBER[0]);
            }
            String sectionToChn = SectionNumToChn(section);
            //判断是否需要节权位
            sectionToChn += (section != 0) ? CHN_UNIT_SECTION[pos] : CHN_UNIT_SECTION[0];
            returnStr.insert(0, sectionToChn);
            needZero = ((section < 1000 && section > 0) ? true : false); //判断section中的千位上是不是为零，若为零应该添加一个零。
            pos++;
            num = num / 10000;
        }
        return returnStr.toString();
    }

    /**
     * 将四位的section转换为中文数字
     *
     * @param section
     * @return
     */
    public static String SectionNumToChn(int section) {
        StringBuffer returnStr = new StringBuffer();
        int unitPos = 0;       //节权位的位置编号，0-3依次为个十百千;

        Boolean zero = true;
        while (section > 0) {

            int v = (section % 10);
            if (v == 0) {
                if ((section == 0) || !zero) {
                    zero = true; /*需要补0，zero的作用是确保对连续的多个0，只补一个中文零*/
                    //chnStr.insert(0, chnNumChar[v]);
                    returnStr.insert(0, CHN_NUMBER[v]);
                }
            } else {
                zero = false; //至少有一个数字不是0
                StringBuffer tempStr = new StringBuffer(CHN_NUMBER[v]);//数字v所对应的中文数字
                tempStr.append(CHN_UNIT[unitPos]);  //数字v所对应的中文权位
                returnStr.insert(0, tempStr);
            }
            unitPos++; //移位
            section = section / 10;
        }
        return returnStr.toString();
    }

    private static int getEnglishCount(String text) {
        if (TextUtils.isEmpty(text)) {
            return 0;
        }
        text = text.replaceAll("[\u4e00-\u9fa5]", "");
        text = text.replaceAll("[0-9]", "");
        text = text.replaceAll("/n", ",");

        text = text.replaceAll(" ", "");

        int count = 0;
        Pattern pattern = Pattern.compile("[a-zA-Z]+");
        Matcher matcher = pattern.matcher(text);
        while (matcher.find()) {
            String matchText = matcher.group();
            count += matchText.length();
        }

        return count + (text.length() - count) * 4;
    }

    public static void main(String[] args) {
        System.out.print(getDigitCount("14:02你好啊，我是豹小秘hello world23"));
    }

    //每次只显示一条高亮文字
    private void realTextHigh(final int line) {
        //文字颜色渐变效果
        //ValueAnimator animator = ValueAnimator.ofArgb(0x4BFFFFFF, 0xFFFFFFFF);
        ValueAnimator animator = ValueAnimator.ofArgb(mTextColor, mTextFinalColor);
        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                int value = (int) animation.getAnimatedValue();
                setTextColorLine(value, line);
            }
        });
        animator.setDuration(100);
        animator.start();
    }

    public void setAllHighLight() {
        //setTextColor(R.color.white);
        setTextColor(mTextFinalColor);
    }

    public void stopHighLightAnimation() {
        if (mAnimator != null) {
            mAnimator.cancel();
        }
    }

    public void stopScrollAnimation() {
        if (mScrollAnimator != null) {
            mScrollAnimator.cancel();
        }
    }

    /**
     * <AUTHOR>
     * @Date 3/18/14
     */
    @SuppressLint("AppCompatCustomView")
    private static class JustifyTextView extends TextView {

        private int mLineY;
        private int mViewWidth;

        public JustifyTextView(Context context) {
            super(context);
        }

        @Override
        protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
            super.onLayout(changed, left, top, right, bottom);
        }

        @Override
        protected void onDraw(Canvas canvas) {
            TextPaint paint = getPaint();
            paint.setColor(getCurrentTextColor());
            paint.drawableState = getDrawableState();
            mViewWidth = getMeasuredWidth();
            String text = getText().toString();
            mLineY = 0;
            mLineY += getTextSize();
            Layout layout = getLayout();

            if (layout == null) {
                return;
            }

            Paint.FontMetrics fm = paint.getFontMetrics();

            int textHeight = (int) (Math.ceil(fm.descent - fm.ascent));
            textHeight = (int) (textHeight * layout.getSpacingMultiplier() + layout.getSpacingAdd());

            for (int i = 0; i < layout.getLineCount(); i++) {
                int lineStart = layout.getLineStart(i);
                int lineEnd = layout.getLineEnd(i);
                float width = StaticLayout.getDesiredWidth(text, lineStart, lineEnd, getPaint());
                String line = text.substring(lineStart, lineEnd);
                if (needScale(line) && i < layout.getLineCount()) {
                    drawScaledText(canvas, lineStart, line, width);
                } else {
                    canvas.drawText(line, 0, mLineY, paint);
                }
                mLineY += textHeight;
            }

        }

        private void drawScaledText(Canvas canvas, int lineStart, String line, float lineWidth) {
            float x = 0;
            if (isFirstLineOfParagraph(lineStart, line)) {
                String blanks = "  ";
                canvas.drawText(blanks, x, mLineY, getPaint());
                float bw = StaticLayout.getDesiredWidth(blanks, getPaint());
                x += bw;

                line = line.substring(3);
            }

            int gapCount = line.length() - 1;
            int i = 0;
            if (line.length() > 2 && line.charAt(0) == 12288 && line.charAt(1) == 12288) {
                String substring = line.substring(0, 2);
                float cw = StaticLayout.getDesiredWidth(substring, getPaint());
                canvas.drawText(substring, x, mLineY, getPaint());
                x += cw;
                i += 2;
            }

            float d = (mViewWidth - lineWidth) / gapCount;
            for (; i < line.length(); i++) {
                String c = String.valueOf(line.charAt(i));
                float cw = StaticLayout.getDesiredWidth(c, getPaint());
                canvas.drawText(c, x, mLineY, getPaint());
                x += cw + d;
            }
        }

        private boolean isFirstLineOfParagraph(int lineStart, String line) {
            return line.length() > 3 && line.charAt(0) == ' ' && line.charAt(1) == ' ';
        }

        private boolean needScale(String line) {
            if (line == null || line.length() == 0) {
                return false;
            } else {
                return line.charAt(line.length() - 1) != '\n';
            }
        }

    }
}
