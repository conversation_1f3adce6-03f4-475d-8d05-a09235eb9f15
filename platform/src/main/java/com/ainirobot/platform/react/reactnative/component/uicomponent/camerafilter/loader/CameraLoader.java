package com.ainirobot.platform.react.reactnative.component.uicomponent.camerafilter.loader;

public abstract class CameraLoader {

    protected OnPreviewFrameListener mOnPreviewFrameListener;

    public abstract void onResume();

    public abstract void onPause();

    public abstract float getRadio();

    public void setOnPreviewFrameListener(OnPreviewFrameListener onPreviewFrameListener) {
        mOnPreviewFrameListener = onPreviewFrameListener;
    }

    public interface OnPreviewFrameListener {
        void onStatusUpdate(int status);
        void onError(int error, String message);
        void onPreviewFrame(byte[] data, int width, int height);
        void onPreviewChange(int width, int height);
    }
}
