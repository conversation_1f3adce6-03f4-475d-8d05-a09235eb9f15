package com.ainirobot.platform.react.reactnative.component.uicomponent.bigimage;

import android.app.Activity;
import android.content.Context;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.R;
import com.ainirobot.platform.react.view.photoview.PhotoView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.facebook.react.uimanager.ThemedReactContext;

import java.util.Objects;

public class BigImageView extends LinearLayout {

    private static final String TAG = "BigImageView";
    private PhotoView mPhotoView;
    private int mWidth = 1200;

    public BigImageView(Context context) {
        super(context);
        preInit((ThemedReactContext) context);
        init();
    }

    public BigImageView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public BigImageView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        setGravity(Gravity.CENTER);
        setClipChildren(false);
        setClipToPadding(false);
    }

    private void preInit(ThemedReactContext reactContext) {
//        WindowManager windowManager =
//                (WindowManager)reactContext.getSystemService(Context.WINDOW_SERVICE);
        WindowManager windowManager = ((Activity) Objects
                .requireNonNull(reactContext.getCurrentActivity())).getWindowManager();
        DisplayMetrics outMetrics = new DisplayMetrics();
        windowManager.getDefaultDisplay().getRealMetrics(outMetrics);
        mWidth = outMetrics.widthPixels;
    }

    public void setData(String src, int height) {
        if (TextUtils.isEmpty(src)) {
            Log.d(TAG, "data is empty");
            return;
        }
        loadRelativeView();
        setImageViewWidthHeight(height, mWidth);
        Glide
                .with(BaseApplication.getContext())
                .load(src)
                .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                .error(R.drawable.chat_error)
                .crossFade(500)
                .into(mPhotoView);
    }

    public void setOnClickListener(final OnClickListener listener) {
        mPhotoView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    listener.onClick(v);
                }
            }
        });
    }

    private void loadRelativeView() {
        mPhotoView = new PhotoView(getContext());
        mPhotoView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        LayoutParams layoutParams = new LayoutParams
                (ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.gravity = Gravity.CENTER_VERTICAL | Gravity.CENTER_HORIZONTAL;
        mPhotoView.setLayoutParams(layoutParams);
        addView(mPhotoView);
    }

    private void setImageViewWidthHeight(int height, int targetWidth) {
        ViewGroup.LayoutParams layoutParams = mPhotoView.getLayoutParams();
        layoutParams.width = targetWidth;
        layoutParams.height = height;
        Log.i(TAG, "width = " + targetWidth + ",height = " + height);
        mPhotoView.requestLayout();
    }


}
