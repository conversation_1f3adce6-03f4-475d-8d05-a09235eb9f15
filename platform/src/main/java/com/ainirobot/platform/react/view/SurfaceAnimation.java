/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.react.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.PixelFormat;
import android.graphics.PorterDuff;
import android.graphics.RectF;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.SystemClock;
import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import android.util.AttributeSet;
import android.util.Log;
import android.view.KeyEvent;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;

public class SurfaceAnimation extends SurfaceView implements SurfaceHolder.Callback, Runnable {

    private static final String TAG = "SurfaceAnimation";
    private static final long INTERVAL_TIME = 40;
    private SurfaceHolder mHolder;
    private boolean isDrawing = false;
    private boolean isSurfaceCreated = false;
    private List<String> mFilePathListRGB = new ArrayList<>();
    private HandlerThread handlerThread = new HandlerThread("surfaceview");
    private OnFrameAnimationListener mListener;
    private Voice.VoiceType mVoiceType;
    private RnVoiceLoader mVoiceLoader;
    private Handler mWorkHandler;
    private Context mContext;
    private boolean mLoop;
    private Matrix mDrawMatrix;
    private int mScaleType;
    private boolean mIsShowBottomWave;


    @IntDef({SCALE_TYPE_FIT_XY, SCALE_TYPE_FIT_START, SCALE_TYPE_FIT_CENTER, SCALE_TYPE_FIT_END,
            SCALE_TYPE_CENTER, SCALE_TYPE_CENTER_CROP, SCALE_TYPE_CENTER_INSIDE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface ScaleType {

    }

    private static final int SCALE_TYPE_MATRIX = 0;

    public static final int SCALE_TYPE_FIT_XY = 1;

    public static final int SCALE_TYPE_FIT_START = 2;

    public static final int SCALE_TYPE_FIT_CENTER = 3;

    public static final int SCALE_TYPE_FIT_END = 4;

    public static final int SCALE_TYPE_CENTER = 5;

    public static final int SCALE_TYPE_CENTER_CROP = 6;

    public static final int SCALE_TYPE_CENTER_INSIDE = 7;


    public SurfaceAnimation(Context context) {
        this(context, null);
    }

    public SurfaceAnimation(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    private void init() {
        mHolder = getHolder();
        mHolder.addCallback(this);
        mDrawMatrix = new Matrix();
        mVoiceLoader = RnVoiceLoader.getInstance();
        mScaleType = SCALE_TYPE_FIT_CENTER;
        setZOrderOnTop(true);
        mHolder.setFormat(PixelFormat.TRANSLUCENT);
        handlerThread.start();
        mIsShowBottomWave = false;
    }
    
    public void setShowBottomWave(boolean mIsShowBottomWave) {
        this.mIsShowBottomWave = mIsShowBottomWave;
        Log.v(TAG, "setShowBottomWave");
    }
    
    public void setMatrix(@NonNull Matrix matrix) {
        if (matrix == null) {
            throw new NullPointerException("matrix can not be null");
        }
        mDrawMatrix = matrix;
       mScaleType = SCALE_TYPE_MATRIX;
    }

    private void setScaleType(int type) {
        if (type < SCALE_TYPE_FIT_XY || type > SCALE_TYPE_CENTER_INSIDE) {
            throw new IllegalArgumentException("Illegal ScaleType");
        }
        if (mScaleType != type) {
            mScaleType = type;
        }
    }

    private int mLastFrameWidth = -1;
    private int mLastFrameHeight = -1;
    private int mLastFrameScaleType = -1;
    private int mLastSurfaceWidth;
    private int mLastSurfaceHeight;

    private void configureDrawMatrix(Bitmap bitmap) {
        final int srcWidth = bitmap.getWidth();
        final int dstWidth = this.getWidth();
        final int srcHeight = bitmap.getHeight();
        final int dstHeight = this.getHeight();
        final boolean nothingChanged =
                srcWidth == mLastFrameWidth
                        && srcHeight == mLastFrameHeight
                        && mLastFrameScaleType == mScaleType
                        && mLastSurfaceWidth == dstWidth
                        && mLastSurfaceHeight == dstHeight;
        if (nothingChanged) {
            return;
        }
        mLastFrameScaleType = mScaleType;
        mLastFrameHeight = bitmap.getHeight();
        mLastFrameWidth = bitmap.getWidth();
        mLastSurfaceHeight = this.getHeight();
        mLastSurfaceWidth = this.getWidth();
        if (mScaleType == SCALE_TYPE_MATRIX) {
            return;
        } else if (mScaleType == SCALE_TYPE_CENTER) {
            mDrawMatrix.setTranslate(
                    Math.round((dstWidth - srcWidth) * 0.5f),
                    Math.round((dstHeight - srcHeight) * 0.5f));
        } else if (mScaleType == SCALE_TYPE_CENTER_CROP) {
            float scale;
            float dx = 0, dy = 0;
            if (dstHeight * srcWidth > dstWidth * srcHeight) {
                scale = (float) dstHeight / (float) srcHeight;
                dx = (dstWidth - srcWidth * scale) * 0.5f;
            } else {
                scale = (float) dstWidth / (float) srcWidth;
                dy = (dstHeight - srcHeight * scale) * 0.5f;
            }
            mDrawMatrix.setScale(scale, scale);
            mDrawMatrix.postTranslate(dx, dy);
        } else if (mScaleType == SCALE_TYPE_CENTER_INSIDE) {
            float scale;
            float dx;
            float dy;
            if (srcWidth <= dstWidth && srcHeight <= dstHeight) {
                scale = 1.0f;
            } else {
                scale = Math.min((float) dstWidth / (float) srcWidth,
                        (float) dstHeight / (float) srcHeight);
            }
            dx = Math.round((dstWidth - srcWidth * scale) * 0.5f);
            dy = Math.round((dstHeight - srcHeight * scale) * 0.5f);

            mDrawMatrix.setScale(scale, scale);
            mDrawMatrix.postTranslate(dx, dy);
        } else {
            RectF srcRect = new RectF(0, 0, bitmap.getWidth(), bitmap.getHeight());
            RectF dstRect = new RectF(0, 0, this.getWidth(), this.getHeight());
            mDrawMatrix.setRectToRect(srcRect, dstRect, MATRIX_SCALE_ARRAY[mScaleType - 1]);
        }
    }

    private static final Matrix.ScaleToFit[] MATRIX_SCALE_ARRAY = {
            Matrix.ScaleToFit.FILL,
            Matrix.ScaleToFit.START,
            Matrix.ScaleToFit.CENTER,
            Matrix.ScaleToFit.END
    };

    public void startAnimation(Voice.VoiceType voiceType) {
        
        if (!mIsShowBottomWave) {
            return;
        }

        Log.d(TAG,"voiceType =======" + voiceType);
        Log.d(TAG,"mVoiceType ========== " + mVoiceType);

        this.mVoiceType = voiceType;
        Log.v(TAG, "startAnimation: " + voiceType.getName());

        long delay = 0;
        this.mLoop = !voiceType.isOneShot();
        List<String> pathListRGB = mVoiceLoader.getPathList(voiceType);
        if (pathListRGB == null || pathListRGB.size() == 0) {
            Log.v(TAG, "pathListRGB do not empty");
            return;
        }
         isDrawing = true;
        setVisibility(VISIBLE);
        mFilePathListRGB.clear();
        mFilePathListRGB.addAll(pathListRGB);
        mWorkHandler = new NoLeakHandler(handlerThread.getLooper());
        if (!isSurfaceCreated) {
            Log.d(TAG, "SurfaceView is not created.wait 1000");
            delay = 1000;
        }
        setLayerType(LAYER_TYPE_HARDWARE, null);
        mWorkHandler.postDelayed(this, delay);
    }

    public Voice.VoiceType getVoiceType() {
        return mVoiceType;
    }

    public void stopAnimation() {
        Log.d(TAG,"stopAnimation");
        mFilePathListRGB.clear();
        setVisibility(INVISIBLE);
        setLayerType(LAYER_TYPE_NONE, null);
        isDrawing = false;
        if (mWorkHandler != null) {
            mWorkHandler.removeCallbacks(this);
        }
    }

    public void setOnFrameAnimationListener(OnFrameAnimationListener listener) {
        mListener = listener;
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        isSurfaceCreated = true;
        isDrawing = true;
        Log.d(TAG, "surfaceCreated");
    }

    public void setDrawingFlag(boolean drawingFlag) {
        isDrawing = drawingFlag;
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {

    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        stopAnimation();
        RnVoiceLoader.getInstance().clearBitmapCache();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            stopAnimation();
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void run() {
        Log.v(TAG, "run: mLoop = " + mLoop + " isDrawing = " + isDrawing + " type = " + mVoiceType.getName());
        Log.v(TAG, "mIsShowBottomWave = " + mIsShowBottomWave);
        do {
            realDrawThread();
        } while (mLoop && isDrawing && mIsShowBottomWave);
    }

    @Override
    protected void onDetachedFromWindow() {
        Log.v(TAG, "onDetachedFromWindow");
        super.onDetachedFromWindow();
        isDrawing = false;
        if (null != mWorkHandler) {
            mWorkHandler.removeCallbacks(this);
        }
    }

    private void realDrawThread() {
        if (!isShown() || !mIsShowBottomWave) {
            return;
        }
        post(new Runnable() {
            @Override
            public void run() {
                notifyStart();
            }
        });
        drawBitmap();
        post(new Runnable() {
            @Override
            public void run() {
                notifyFinished();
            }
        });
    }

    private void drawBitmap() {
        for (int i = 0; i < mFilePathListRGB.size(); i++) {
            if (isDrawing) {
                try {
                    long now = SystemClock.uptimeMillis();
                    draw(mFilePathListRGB.get(i));
                    long intervalTime = INTERVAL_TIME - (SystemClock.uptimeMillis() - now) > 0 ? INTERVAL_TIME - (SystemClock.uptimeMillis() - now) : 0;
                    Thread.sleep(Math.max(0, (intervalTime)));
                } catch (Exception e) {
//                    e.printStackTrace();
                }
            } else {
                break;
            }
        }
    }


    private void draw(String path) {
        Canvas canvas = null;
        try {
            canvas = mHolder.lockCanvas();
        } catch (Exception e) {
        }
        //Log.i(TAG,"mCanvas = "+canvas);

        if (canvas != null) {
            Bitmap diskBitmap = mVoiceLoader.getDiskBitmap(path);
            if (diskBitmap != null) {
                canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR);
                configureDrawMatrix(diskBitmap);
                canvas.drawBitmap(diskBitmap, mDrawMatrix, null);
            }
            mHolder.unlockCanvasAndPost(canvas);
        }
    }


    private void notifyStart() {
        if (mListener != null) {
            mListener.onFrameAnimationStart(mVoiceType);
        }
    }

    private void notifyFinished() {
        if (mListener != null) {
            mListener.onFrameAnimationFinished(mVoiceType);
        }
    }

    public interface OnFrameAnimationListener {

        void onFrameAnimationStart(Voice.VoiceType voiceType);

        void onFrameAnimationFinished(Voice.VoiceType voiceType);

    }
    
    private static class NoLeakHandler extends Handler {
        public NoLeakHandler(Looper looper) {
            super(looper);
        }
    }

    @Override
    protected void onVisibilityChanged(@NonNull View changedView, int visibility) {
        super.onVisibilityChanged(changedView, visibility);
        Log.v(TAG, "visible change "+ changedView.toString() + "visibility =" + visibility);
    }
}
