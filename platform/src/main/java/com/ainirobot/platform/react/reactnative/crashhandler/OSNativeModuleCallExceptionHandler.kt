package com.ainirobot.platform.react.reactnative.crashhandler

import android.annotation.SuppressLint
import android.app.Dialog
import android.graphics.Outline
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.util.Log
import android.view.*
import android.widget.TextView
import com.ainirobot.base.OrionBase
import com.ainirobot.platform.R
import com.ainirobot.platform.react.AppManger
import com.ainirobot.platform.react.Constant
import com.ainirobot.platform.react.EveActivity
import com.ainirobot.platform.react.OPKBeanV3
import com.ainirobot.platform.react.client.ClientUtils
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.character.ReactCharacter
import com.ainirobot.platform.react.reactnative.character.StartEvent
import com.ainirobot.platform.utils.DeviceUtils
import com.ainirobot.platform.utils.SystemUtils
import com.facebook.react.bridge.NativeModuleCallExceptionHandler
import com.facebook.react.common.JavascriptException
import org.json.JSONObject
import org.simple.eventbus.EventBus
import java.io.File
import java.util.*

/***
 * <AUTHOR>
 * @date 2019/4/19
 */
class OSNativeModuleCallExceptionHandler(private val mIsAutoStart: Boolean) : NativeModuleCallExceptionHandler {

    private var mCrashDialog: Dialog? = null;

    /**
     * 是否显示崩溃弹窗
     */
    private val isShowCrashDialog: Boolean = true;

    /**
     * 崩溃弹窗显示时长
     */
    private val mCrashDialogTimeout: Long = 60 * 1000;

    private var mCrashTimer: Timer? = null;

    private val mMainHandler = object : Handler(Looper.getMainLooper()) {
        override fun dispatchMessage(msg: Message) {
            try {
                super.dispatchMessage(msg)
            } catch (e: Exception) {
                e.printStackTrace()
            }

        }
    }

    @SuppressLint("LongLogTag")
    override fun handleException(e: Exception) {
        Log.i(TAG, " handleException " + Log.getStackTraceString(e))

        //停止所有的机器人能力
        try {
            RNClientManager.instance?.speechApiManager?.stopTTS()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }

        var opkBean = getAppInfo(e);
        Log.i(TAG, "The app that crashed is : " + opkBean?.appid + "   " + opkBean?.appName + "  " + opkBean?.versionName)

        if (isAllowShowCrashDialog(opkBean)) {
            showCrashDialog(opkBean);
        } else {
            ClientUtils.killProcess(android.os.Process.myPid())
        }

        reportCrash(e, opkBean);
    }

    /**
     * 上报JS崩溃到数据平台
     */
    private fun reportCrash(e: Exception, opkBean: OPKBeanV3?) {
        if (!OrionBase.isInitialized()) {
            return
        }
        try {
            opkBean?.let {
                OrionBase.setJsRole(it.appid)
                OrionBase.setJsScene(DeviceUtils.getScenesName())
                val jsonObject = JSONObject()
                jsonObject.put("CoreVersion", it.coreTarget)
                jsonObject.put("BizVersion", it.versionName)
                jsonObject.put("AppId", it.appid)
                OrionBase.setOptJson(jsonObject.toString())
                //将异常给SDK
                OrionBase.logCrash(Thread.currentThread(), e)
            }
        } catch (e1: Exception) {
            e1.printStackTrace()
        }
    }

    /**
     * 显示崩溃弹窗
     */
    private fun showCrashDialog(opkBean: OPKBeanV3?) {
        if (mCrashDialog != null
                && mCrashDialog!!.isShowing) {
            return;
        }

        Log.d(TAG, "Show crash dialog : " + opkBean)

        if (opkBean == null) {
            return;
        }

        val context = EveActivity.getActivity().get();
        context!!.runOnUiThread {
            mCrashDialog = Dialog(context, R.style.Dialog)

            val view = LayoutInflater.from(context).inflate(R.layout.platform_crash_dialog, null, false)
            view.outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View?, outline: Outline?) {
                    view!!.clipToOutline = true
                    outline!!.setRoundRect(0, 0, view.width, view.height, 60f)
                }
            }
            view.findViewById(R.id.reboot).setOnClickListener {
                mCrashDialog?.dismiss()
                cancelCrashTimer()
            }

            mCrashDialog?.setOnDismissListener {
                //重新启动RN环境
                ClientUtils.killProcess(android.os.Process.myPid())
            }

            val content = context.getString(R.string.crash_content, opkBean.appName, opkBean.appid);
            (view.findViewById(R.id.crash_content) as TextView).setText(content)

            mCrashDialog?.setContentView(view)
            val params = mCrashDialog?.window?.attributes
            params?.gravity = Gravity.CENTER;
            params?.width = 950
            params?.height = WindowManager.LayoutParams.WRAP_CONTENT
            mCrashDialog?.window?.attributes = params

            mCrashDialog?.setCanceledOnTouchOutside(false)

            mCrashDialog?.show()

            startCrashTimer()
        }
    }

    /**
     * 是否允许崩溃弹窗
     *
     * User版本，屏蔽官方应用弹窗
     *
     */
    private fun isAllowShowCrashDialog(appInfo: OPKBeanV3?): Boolean {
        if (appInfo == null) {
            return false;
        }

        return isShowCrashDialog && !(SystemUtils.isUser() && appInfo.isSystem);
    }

    /**
     * 启动崩溃定时器
     */
    private fun startCrashTimer() {
        mCrashTimer = Timer()
        mCrashTimer?.schedule(object : TimerTask() {
            override fun run() {
                mCrashDialog?.dismiss()
            }
        }, mCrashDialogTimeout)
    }

    private fun cancelCrashTimer() {
        if (mCrashTimer != null) {
            mCrashTimer?.cancel()
            mCrashTimer = null
        }
    }

    /**
     * 获取发生崩溃的opk的信息
     */
    private fun getAppInfo(e: Exception): OPKBeanV3? {
        if (e is JavascriptException) {
            var frame = e.details?.getMap(0)

            //opk路径
            var file = frame?.getString("file");

            if (file != null) {
                var appId = File(file).parentFile?.name
                if (AppManger.isAppValid(appId)) {
                    return AppManger.getRPKByCharacter(appId)
                }
            }
        }

        return AppManger.getRPKByCharacter(ReactCharacter.currentCharacter)
    }

    /**
     * 当需要时则重启环境
     */
    private fun restartEnvironmentIfNeed() {
        if (mIsAutoStart) {
            mMainHandler.post {
                //需要再主线程中重启
                //重启ReactNative环境
                EventBus.getDefault().post(StartEvent(true, -1, "", ""), Constant.EVENT_BUS_RELOAD_REACT_NATIVE_ENVIRONMENT)
            }
        }
    }

    companion object {

        private val TAG = OSNativeModuleCallExceptionHandler::class.java.simpleName
    }
}
