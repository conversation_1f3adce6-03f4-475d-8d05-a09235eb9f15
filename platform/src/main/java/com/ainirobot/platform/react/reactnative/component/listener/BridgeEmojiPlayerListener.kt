package com.ainirobot.platform.react.reactnative.component.listener

import android.util.Log
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.facebook.react.bridge.WritableNativeMap

class BridgeEmojiPlayerListener {
    companion object {
        fun obtain(callbackId:Int): BridgeEmojiPlayerListener? {
            return if(callbackId < 0){
                null
            } else {
                BridgeEmojiPlayerListener(callbackId)
            }
        }
    }

    var id = 0
    private constructor(id:Int) {
        this.id = id
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
    }

    fun triggerEvent(event: String) {
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putString("event", event)
        Log.i("BridgeEmojiPlayerListener","triggerEvent $id")
        ReactNativeEventEmitter.triggerEvent("onEmojiPlayer", readableMap)
    }

    fun onPlayerTouched() {
        triggerEvent("onEmojiPlayerTouched")
    }

    fun onPlayFinished() {
        triggerEvent("onEmojiPlayerFinished")
    }
}