/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.react.server.impl;

import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.bi.wrapper.ReportControl;
import com.ainirobot.platform.bi.wrapper.manager.bi.impl.RnSwitchOpkPoint;
import com.ainirobot.platform.data.ModuleDataMgr;
import com.ainirobot.platform.react.RNReportUtils;
import com.ainirobot.platform.react.client.RNClientManager;
import com.ainirobot.platform.react.reactnative.character.ReactCharacter;
import com.ainirobot.platform.rn.IDataRegistry;
import com.ainirobot.platform.rn.listener.IRNDataListener;
import com.ainirobot.platform.rn.listener.IRNRobotStatusListener;
import com.ainirobot.platform.utils.FileUtils;
import com.ainirobot.platform.utils.LocalUtils;
import com.ainirobot.platform.utils.SystemUtils;
import com.google.gson.Gson;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;

public class RNDataServer extends IDataRegistry.Stub {

    private static final String TAG = RNDataServer.class.getSimpleName();

    private final Object CALLBACK_LOCK = new Object();
    private Callback mCallback;

    @Override
    public String getConfigs(String moduleCode) {
        String strRet = "";
        try {
            ArrayList<String> arrayList = ModuleDataMgr.getInstance().getConfigFiles(moduleCode);
            if (arrayList != null && arrayList.size() > 0) {
                Gson gson = new Gson();
                strRet = gson.toJson(arrayList);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return strRet;
    }

    @Override
    public String getConfigContents(String moduleCode) {
        String strRet = "";
        try {
            ArrayList<String> arrayList = ModuleDataMgr.getInstance().getConfigFiles(moduleCode);
            if (arrayList != null && arrayList.size() > 0) {
                strRet = combineConfigFiles(arrayList);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return strRet;
    }

    private static String combineConfigFiles(ArrayList<String> arrCfgFiles) {
        if (arrCfgFiles.size() == 0) {
            return null;
        }

        //String strContent = null;
        StringBuilder strContent = new StringBuilder();
        Log.i(TAG, "start *************************** ");
        strContent.append("[");
        for (String path : arrCfgFiles) {
            if (TextUtils.isEmpty(path) ||
                    !FileUtils.checkFileExist(path)) {
                Log.i(TAG, "parserFile: path --> " + path);
                continue;
            }
            Log.i(TAG, "parserFile: path exist --> " + path);
            try {
                File jsonFile = new File(path);
                InputStream is = new FileInputStream(jsonFile);
                InputStreamReader input = new InputStreamReader(is, "UTF-8");
                BufferedReader br = new BufferedReader(input);
                String line;
                while ((line = br.readLine()) != null) {
                    strContent.append(line);
                    strContent.append('\n');
                }
                br.close();
                //if (arrCfgFiles.get(arrCfgFiles.size() - 1).compareTo(path) != 0) {
                    Log.i(TAG, "##############  add ");
                    strContent.append(",");
                //}

            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }


        if(!TextUtils.isEmpty(strContent) &&
                strContent.lastIndexOf(",") == strContent.length()-1){
            strContent.deleteCharAt(strContent.lastIndexOf(","));
            Log.i(TAG, "##############  delete ");
        }

        strContent.append("]");
        Log.i(TAG, "end  *************************** ");
        return strContent.toString();
    }

    @Override
    public String getResFile(String moduleCode, String filePath) {
        String strRet = "";
        try {
         //   Log.i(TAG, "getResFile, begin:"+filePath);
            strRet = ModuleDataMgr.getInstance().getResFile(moduleCode, filePath);
            if (TextUtils.isEmpty(strRet)) {
                strRet = "";
            }
            Log.i(TAG, "getResFile, end:"+filePath);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return strRet;
    }

    @Override
    public String[] getResFileBatch(String moduleCode, String filePaths) {
        String[] strRet = null;
        try {
            //Log.i(TAG, "getResFileBatch, begin:"+filePaths);
            strRet = ModuleDataMgr.getInstance().getResFileBatch(moduleCode, filePaths);
            //Log.i(TAG, "getResFileBatch, end:"+filePaths);
        } catch (Exception ex) {
            Log.i(TAG, "getResFileBatch, Exception:"+filePaths);
            ex.printStackTrace();
        }
        return strRet;
    }

    @Override
    public String getCropName() {
        String corpName = LocalUtils.getGlobalSettings(BaseApplication.getContext(),
                "corp_name", "");
        if (!TextUtils.isEmpty(corpName)) {
            return corpName;
        }

        return "";
    }

    @Override
    public void registerUpdateSkillDataListener(IRNDataListener listener) {
        synchronized (CALLBACK_LOCK) {
            if (listener == null) {
                mCallback = null;
            } else {
                mCallback = new Callback(listener);
            }
        }
        if (SystemUtils.isMainProcess(BaseApplication.getContext())) {
            RobotApi.getInstance().registerStatusListener(Definition.STATUS_MODULE_DATA_UPDATE,
                    mCallback);
        } else {
            if (RNClientManager.Companion.getInstance() != null
                    && RNClientManager.Companion.getInstance().getApiManager() != null) {
                try {
                    RNClientManager.Companion.getInstance().getApiManager()
                            .registerStatusListener(Definition.STATUS_MODULE_DATA_UPDATE,
                                    new IRNRobotStatusListener.Stub() {
                                        @Override
                                        public void onStatusUpdate(String type, String data) {
                                            if (mCallback != null) {
                                                mCallback.onStatusUpdate(type, data);
                                            }
                                        }
                                    });
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    public String forceFetchModuledata(String moduleCode) {
        String strRet = "";
        try {
            strRet = ModuleDataMgr.getInstance().forceFetchModuledata(moduleCode);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return strRet;
    }

    @Override
    public String getCurrentCharcater() {
        String current = "";
        try {
            current = ReactCharacter.Companion.getCurrentCharacter();
        } catch (Exception ex) {

        }
        return current;
    }

    @Override
    public void reportRNSwitch(int result) {
        ReportControl.getInstance().reportMsg(new RnSwitchOpkPoint(result,
                RNReportUtils.getInstance().getVersionName(),
                RNReportUtils.getInstance().getCoreTarget(),
                RNReportUtils.getInstance().getAppId()));
    }

    public void clearCallbacks() {
        Log.d(TAG, "clearCallbacks");
        synchronized (CALLBACK_LOCK) {
            if (mCallback != null) {
                mCallback.stop();
            }
        }
    }

    private class Callback extends StatusListener {
        private IRNDataListener listener;

        Callback(IRNDataListener listener) {
            this.listener = listener;
        }

        public void stop() {
            listener = null;
        }

        @Override
        public void onStatusUpdate(String type, String data) {
            Log.i(TAG, "StatusListener data update type = " + type + ",data = " + data);
            try {
                if (null != listener) {
                    listener.onDataUpdate(data);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
