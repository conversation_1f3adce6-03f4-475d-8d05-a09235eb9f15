package com.ainirobot.platform.react.reactnative.component.uicomponent.clipimage

import android.util.Log
import com.facebook.react.bridge.ReadableMap
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.annotations.ReactProp

@ReactModule(name = ClipIconImageViewManager.REACT_CLASS)
class ClipIconImageViewManager: SimpleViewManager<ClipIconImageView>() {

    companion object {
        const val REACT_CLASS = "ClipIconImageView"
        const val TAG = "ClipIconImageManager"
    }

    override fun getName(): String {
        return REACT_CLASS
    }

    override fun createViewInstance(reactContext: ThemedReactContext): ClipIconImageView {
        return ClipIconImageView(reactContext)
    }

    @ReactProp(name = "params")
    fun setParams(view: ClipIconImageView, params: ReadableMap) {
        val targetWidth = params.getInt("targetWidth")
        val targetHeight = params.getInt("targetHeight")
        if (params.hasKey("videoUrl")) {
            val videoUrl = params.getString("videoUrl")
            if (videoUrl != null) {
                view.setImageViewVideoUrl(videoUrl, targetWidth, targetHeight)
                Log.d(TAG, "targetWidth=$targetWidth,targetHeight=$targetHeight，videoUrl=$videoUrl,")
                return
            }
        }
        if (params.hasKey("url")) {
            val url = params.getString("url")
            view.setImageViewUrl(url, targetWidth, targetHeight)
            Log.d(TAG, "targetWidth=$targetWidth,targetHeight=$targetHeight，url=$url")
        }
    }
}