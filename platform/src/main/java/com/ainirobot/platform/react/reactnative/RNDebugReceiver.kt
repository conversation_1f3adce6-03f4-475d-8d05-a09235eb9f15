package com.ainirobot.platform.react.reactnative

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.ainirobot.platform.PlatformDef
import com.ainirobot.platform.control.ControlManager
import com.ainirobot.platform.react.Constant
import com.ainirobot.platform.react.client.CallBackHandler
import com.ainirobot.platform.react.client.RNCommandCallBack
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.react.server.control.RNServerManager
import com.facebook.react.bridge.WritableNativeMap
import org.json.JSONObject
import org.simple.eventbus.EventBus
import java.util.*

/***
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-28
 * 用于接收外部开启RN环境的debug状态
 */
class RNDebugReceiver : BroadcastReceiver() {

    companion object {

        const val REQ_TAKE_SNAPSHOT = "take_snapshot";
        const val REQ_TOP_SNAPSHOT = "stop_snapshot";

        const val ACTION_DENUG = "com.ainirobot.moduleapp.debug";
        const val ACTION_REMOTE = "com.ainirobot.moduleapp.remote.debug";
        const val ACTION_DELETE = "com.ainirobot.moduleapp.delete.bundle";

        var DEBUG: Boolean = false
        var mTimer: Timer? = null;

        const val TAG = "RNDebugReceiver"

        fun setDebugEnable(debug: Boolean) {
            if (DEBUG) {
                return
            }

            DEBUG = !DEBUG
            Log.d(TAG, "Set debug mode : " + debug)
            EventBus.getDefault().post(DEBUG, Constant.EVENT_BUS_DEBUG_CHARACTER_ENVIRONMENT)
        }

        fun setRemoteDebug(remote: Boolean) {
            Log.d(TAG, "Set remote debug : " + remote)
            if (remote) {
                //开启远程调试时自动开启Debug模式
                setDebugEnable(true);
            } else {
                //非Debug模式禁止关闭远程调试操作
                if (!DEBUG) {
                    return;
                }
            }
            EventBus.getDefault().post(remote, Constant.EVENT_BUS_REMOTE_DEBUG_ENVIRONMENT)
        }
    }

    fun deleteTempBundle() {
        Log.d(TAG, "Delete tmp bundle")
        EventBus.getDefault().post(null, Constant.EVENT_BUS_DELETE_BUNDLE_ENVIRONMENT)
    }

    override fun onReceive(context: Context, intent: Intent?) {
        if (intent != null) {
            when (intent.action) {
                ACTION_DENUG -> {
                    val debug = intent.getStringExtra("debug");
                    setDebugEnable(debug.toBoolean());
                };
                ACTION_DELETE -> deleteTempBundle();
                ACTION_REMOTE -> {
                    val remote = intent.getStringExtra("remote");
                    setRemoteDebug(remote.toBoolean())
                };
            }

        }
    }
}
