package com.ainirobot.platform.react.reactnative.component.uicomponent.mute;

import android.app.Service;
import android.content.Context;
import android.database.ContentObserver;
import android.graphics.PixelFormat;
import android.media.AudioManager;
import android.os.Handler;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import com.ainirobot.platform.R;

import static android.view.WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;
import static android.view.WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
import static android.view.WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL;

public class MuteView extends RelativeLayout {

    private static final String TAG = "MuteView";
    private View muteView = null;
    private WindowManager wm;
    private WindowManager.LayoutParams wmParams;
    private SettingsContentObserver mSettingsContentObserver;

    public MuteView(Context context) {
        super(context);
        initView();
    }

    public MuteView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public MuteView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    private void initView() {

        int volume = ((AudioManager) getContext().
                getSystemService(Service.AUDIO_SERVICE)).
                getStreamVolume(AudioManager.STREAM_MUSIC);

        Log.d(TAG, "initView volume: " + volume);

        initWindowsParamForMute();
        if (volume == 0) {
            showMuteView();
        }
        registerVolumeChangeReceiver();
    }

    private void initWindowsParamForMute() {
        wm = (WindowManager) getContext().getSystemService(Context.WINDOW_SERVICE);
        wmParams = new WindowManager.LayoutParams();
        wmParams.type = WindowManager.LayoutParams.TYPE_APPLICATION;
        wmParams.format = PixelFormat.RGBA_8888;
        wmParams.flags = FLAG_LAYOUT_IN_SCREEN | FLAG_NOT_TOUCH_MODAL | FLAG_NOT_FOCUSABLE;
        wmParams.gravity = Gravity.CENTER_HORIZONTAL | Gravity.TOP;
        wmParams.width = 100;
        wmParams.height = 100;
    }

    private void registerVolumeChangeReceiver() {
        mSettingsContentObserver = new SettingsContentObserver(getContext(), new Handler());
        getContext().getContentResolver().registerContentObserver
            (android.provider.Settings.System.CONTENT_URI, true, mSettingsContentObserver);
    }

    private void showMuteView() {
        Log.d(TAG, "showMuteView muteView = " + muteView);
        if (muteView == null) {
            muteView = LayoutInflater.from(getContext()).inflate(R.layout.mute, null);
            wm.addView(muteView, wmParams);
        }
    }

    private void hideMuteView() {
        Log.d(TAG, "hideMuteView muteView = " + muteView);
        if (muteView != null) {
            wm.removeView(muteView);
            muteView = null;
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        getContext().getContentResolver().unregisterContentObserver(mSettingsContentObserver);
    }

    public class SettingsContentObserver extends ContentObserver {
        Context context;
        AudioManager manager;
        int lastMusicVol;

        SettingsContentObserver(Context c, Handler handler) {
            super(handler);
            context = c;
            manager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            lastMusicVol = manager.getStreamVolume(AudioManager.STREAM_MUSIC);

            Log.d(TAG,"lastMusicVol: " + lastMusicVol);
            if(lastMusicVol != 0){
                hideMuteView();
            }
        }

        @Override
        public boolean deliverSelfNotifications() {
            return super.deliverSelfNotifications();
        }

        @Override
        public void onChange(boolean selfChange) {
            super.onChange(selfChange);
            int curMusicVol = manager.getStreamVolume(AudioManager.STREAM_MUSIC);
            boolean change = (lastMusicVol != curMusicVol);
            if (change) {
                Log.d(TAG, "music vol change from " + lastMusicVol + " to " + curMusicVol);
                lastMusicVol = curMusicVol;
                if (manager.getStreamVolume(AudioManager.STREAM_MUSIC) == 0) {
                    showMuteView();
                } else {
                    hideMuteView();
                }
            }

        }

    }
}
