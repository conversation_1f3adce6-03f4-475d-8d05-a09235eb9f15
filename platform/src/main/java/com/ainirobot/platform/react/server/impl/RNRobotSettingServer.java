/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.react.server.impl;

import static com.ainirobot.coreservice.client.ProductInfo.isMeissa2;
import static com.ainirobot.coreservice.client.ProductInfo.isMeissaPlus;
import static com.ainirobot.coreservice.client.ProductInfo.isMiniProduct;
import static com.ainirobot.coreservice.client.RobotSettings.ROBOT_LOW_POWER_LEVEL;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingListener;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.rn.IRNRobotSettingRegistry;
import com.ainirobot.platform.rn.listener.IRNRobotSettingListener;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class RNRobotSettingServer extends IRNRobotSettingRegistry.Stub {

    // MINI 的默认配置是10%
    public static final int DEFAULT_LOW_POWER_LEVEL_MINI = 10;
    public static final int DEFAULT_LOW_POWER_LEVEL_OTHER = 5;

    private RobotSettingApi mApi = RobotSettingApi.getInstance();
    private final Object CALLBACK_LOCK = new Object();
    private List<Callback> mCallbackList;

    public RNRobotSettingServer() {
        mCallbackList = new ArrayList<>();
    }

    @Override
    public int getRobotInt(String key) {
        return mApi.getRobotInt(key);
    }

    @Override
    public float getRobotFloat(String key) {
        return mApi.getRobotFloat(key);
    }

    @Override
    public String getRobotString(String key) {
        return mApi.getRobotString(key);
    }

    @Override
    public void setRobotInt(String key, int value) {
        mApi.setRobotInt(key, value);
    }

    @Override
    public void setRobotFloat(String key, float value) {
        mApi.setRobotFloat(key, value);
    }

    @Override
    public void setRobotString(String key, String value) {
        mApi.setRobotString(key, value);
    }

    @Override
    public void registerRobotSettingListener(IRNRobotSettingListener listener, List<String> keyList) {
        if (listener == null || keyList == null || keyList.size() <= 0) {
            return;
        }
        synchronized (CALLBACK_LOCK) {
            String[] keys = keyList.toArray(new String[0]);
            Callback callback = new Callback(listener);
            Log.d("RNRobotSettingServer", "registerRobotSettingListener");
            mApi.registerRobotSettingListener(callback, keyList.toArray(keys));
            mCallbackList.add(callback);
        }
    }

    @Override
    public void unRegisterRobotSettingListener(IRNRobotSettingListener listener) {
        if (listener == null) {
            return;
        }
        synchronized (CALLBACK_LOCK) {
            Callback callback = null;
            Iterator<Callback> iterator = mCallbackList.iterator();
            while (iterator.hasNext()) {
                Callback internalCallback = iterator.next();
                if (listener == internalCallback.listener) {
                    callback = internalCallback;
                    iterator.remove();
                    break;
                }
            }
            if (callback == null) {
                return;
            }
            mApi.unRegisterRobotSettingListener(callback);
        }
    }

    @Override
    public int getRobotGlobalInt(String key) {
        if (TextUtils.equals(key, ROBOT_LOW_POWER_LEVEL)) {
            int level = Settings.Global.getInt(BaseApplication.getApplication().getContentResolver(), ROBOT_LOW_POWER_LEVEL, -1);
            if (level == -1){
                try {
                    return Integer.parseInt(getRobotString(ROBOT_LOW_POWER_LEVEL));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return isMiniProduct() || isMeissaPlus() || isMeissa2() ? DEFAULT_LOW_POWER_LEVEL_MINI : DEFAULT_LOW_POWER_LEVEL_OTHER;
            }
            return level;
        }
        return 0;
    }

    private class Callback extends RobotSettingListener {
        private IRNRobotSettingListener listener;

        Callback(IRNRobotSettingListener listener) {
            this.listener = listener;
        }

        @Override
        public void onRobotSettingChanged(String key) {
            try {
                if (null != listener) {
                    listener.onRobotSettingChanged(key);
                }
            } catch (Exception e) {
                e.printStackTrace();
                synchronized (CALLBACK_LOCK) {
                    mCallbackList.remove(this);
                    mApi.unRegisterRobotSettingListener(this);
                }
            }
        }
    }

    public void clearCallbacks() {
        Iterator<Callback> iterator = mCallbackList.iterator();
        synchronized (CALLBACK_LOCK) {
            while (iterator.hasNext()) {
                Callback callback = iterator.next();
                mApi.unRegisterRobotSettingListener(callback);
            }
            mCallbackList.clear();
        }
    }
}
