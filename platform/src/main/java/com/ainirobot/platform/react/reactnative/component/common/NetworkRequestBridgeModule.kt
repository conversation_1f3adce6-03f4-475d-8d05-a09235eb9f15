package com.ainirobot.platform.react.reactnative.component.common

import android.os.IBinder
import android.util.Log
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.rn.CallbackRegistry
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod

class NetworkRequestBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    private val TAG = NetworkRequestBridgeModule::class.java.simpleName

    override fun getName(): String {
        return "NetworkApi"
    }

    @ReactMethod
    fun sendRequest(requestParams: String?, promise: Promise) {
        Log.d(TAG, "requestParams:$requestParams")
        try {
            RNClientManager.instance?.okhttpRequestManager?.sendRequest(requestParams, object : CallbackRegistry.Stub() {
                override fun onSuccess(response: String) {
                    promise.resolve(response)
                }

                override fun onFailure(error: String) {
                    promise.reject("REQUEST_FAILED", error)
                }

                override fun asBinder(): IBinder {
                    return super.asBinder()
                }
            })
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject("REQUEST_FAILED", e.message)
        }
    }


    @ReactMethod
    fun startExecution(param: String?, promise: Promise) {
        if (param.isNullOrEmpty()) {
            promise.reject("INVALID_PARAM", "Parameter 'param' is required and cannot be null or empty")
            return
        }
        try {
            RNClientManager.instance?.okhttpRequestManager?.startExecution(param, object : CallbackRegistry.Stub() {
                override fun onSuccess(response: String) {
                    promise.resolve(response)
                }
                override fun onFailure(error: String) {
                    promise.reject("REQUEST_FAILED", error)
                }
            })
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject("REQUEST_FAILED", e.message)
        }
    }


    @ReactMethod
    fun getIpconfig(promise: Promise) {
        try {
            RNClientManager.instance?.okhttpRequestManager?.getIpconfig(object : CallbackRegistry.Stub() {
                override fun onSuccess(response: String) {
                    promise.resolve(response)
                }
                override fun onFailure(error: String) {
                    promise.reject("REQUEST_FAILED", error)
                }
            })
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject("REQUEST_FAILED", e.message)
        }
    }

    @ReactMethod
    fun getPing(param: String?, promise: Promise) {
        if (param.isNullOrEmpty()) {
            promise.reject("INVALID_PARAM", "Parameter 'param' is required and cannot be null or empty")
            return
        }
        try {
            RNClientManager.instance?.okhttpRequestManager?.getPing(param, object : CallbackRegistry.Stub() {
                override fun onSuccess(response: String) {
                    promise.resolve(response)
                }
                override fun onFailure(error: String) {
                    promise.reject("REQUEST_FAILED", error)
                }
            })
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject("REQUEST_FAILED", e.message)
        }
    }
}
