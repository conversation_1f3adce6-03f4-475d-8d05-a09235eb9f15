package com.ainirobot.platform.react.reactnative.component.listener

import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNEmergencyListener
import com.facebook.react.bridge.WritableNativeMap

class BridgeEmergencyListener : IRNEmergencyListener.Stub {

    companion object {
        fun obtain(callbackId:Int): BridgeEmergencyListener? {
            return BridgeEmergencyListener(callbackId)
        }
    }

    var id = -1

    private constructor(id: Int) {
        this.id = id
    }

    override fun onEmergencyStatusChanged(data: String?) {
        triggerEvent("onEmergencyStatusChanged", data)
    }

    fun triggerEvent(event: String, data: String?) {
        if (id < 0) {
            return
        }
        var readableMap = WritableNativeMap()
        readableMap.putInt("id", id)
        readableMap.putString("event", event)
        data?.apply { readableMap.putString("data", data) }
        ReactNativeEventEmitter.triggerEvent("onEmergencyStatusChangedListener", readableMap)
    }
}