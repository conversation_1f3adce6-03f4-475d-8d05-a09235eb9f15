package com.ainirobot.platform.react.reactnative.component.common

import android.util.Log
import com.ainirobot.platform.bi.WakeUpIdWrapper
import com.ainirobot.platform.bi.wrapper.ReportControl
import com.ainirobot.platform.bi.wrapper.manager.bijs.BiJsReport
import com.ainirobot.platform.react.client.RNClientManager
import com.facebook.react.bridge.*
import com.google.gson.Gson
import org.json.JSONException
import org.json.JSONObject

open class ReportBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    private val TAG = "ReportBridgeModule"
    private val EVENT = "event"
    private val DATA = "data"

    override fun getName(): String {
        return "Reports"
    }

    @ReactMethod
    fun reportMsg(jsonMap : ReadableMap) {
        val json = Gson().toJson(jsonMap.toHashMap())
        Log.d("ReportBridgeModule", "reportMsg json $json")
        ReportControl.getInstance().reportMsg(BiJsReport(json))
    }

    @ReactMethod
    fun reportEWMsg(jsonMap : ReadableMap) {
        val json = Gson().toJson(jsonMap.toHashMap())
        Log.d(TAG, "reportEWMsg json: $json")
        var jsonObject: JSONObject?
        try {
            jsonObject = JSONObject(json)
            val event = jsonObject.optString(EVENT)
            val value = jsonObject.optString(DATA)
            try {
                RNClientManager.instance?.apiManager?.sendStatusReport(event, value)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        } catch (e: JSONException) {
            Log.e(TAG, "reportEWMsg fail, data format exception json=$json")
        }
    }

    @ReactMethod
    fun generateWakeupID() {
        RNClientManager.instance?.deviceManager?.generateWakeupId()
    }

    @ReactMethod
    fun getWakeupId(promise: Promise) {
        try {
            promise.resolve(RNClientManager.instance?.deviceManager?.wakeupId)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }
}