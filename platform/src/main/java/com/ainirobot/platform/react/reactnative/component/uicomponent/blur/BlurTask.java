package com.ainirobot.platform.react.reactnative.component.uicomponent.blur;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Rect;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.renderscript.Allocation;
import android.renderscript.Element;
import android.renderscript.RenderScript;
import android.renderscript.ScriptIntrinsicBlur;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.PixelCopy;
import android.view.Surface;
import android.view.View;
import android.view.WindowManager;

import com.ainirobot.platform.R;
import com.ainirobot.platform.utils.BitmapUtils;
import com.facebook.react.bridge.ReactApplicationContext;

import java.lang.ref.WeakReference;
import java.lang.reflect.Method;

public class BlurTask extends AsyncTask<Void, Void, Drawable> {

    private WeakReference<SajjadBlurOverlayManager.BlurView> blurView;
    private WeakReference<View[]> copyViews;
    private WeakReference<Activity> activity;
    private WeakReference<ReactApplicationContext> ctx;
    private int radius;
    private boolean hasFaceParticle;
    private Bitmap b1;
    private static BlurTask blurTask = null;
    // only retain a weak reference to the activity

    public static BlurTask createNewBlurTask(SajjadBlurOverlayManager.BlurView blurView, View[] copyViews, ReactApplicationContext ctx, Activity activity, boolean hasFaceParticle, int radius) {
        if (blurTask == null) {
            Log.i("blur", "BlurTask createNewBlurTask");
            blurTask = new BlurTask(blurView, copyViews, ctx, activity, hasFaceParticle, radius);
            return blurTask;
        }
        Log.i("blur", "BlurTask createNewBlurTask has created!");
        return null;
    }

    public static void forceStop() {
        Log.i("blur", "BlurTask forceStop");
        try {
            if (blurTask != null) {
                blurTask.cancel(true);
                blurTask = null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private BlurTask(SajjadBlurOverlayManager.BlurView blurView, View[] copyViews, ReactApplicationContext ctx, Activity activity, boolean hasFaceParticle, int radius) {
        this.blurView = new WeakReference<>(blurView);
        this.copyViews = new WeakReference<>(copyViews);
        this.activity = new WeakReference<>(activity);
        this.ctx = new WeakReference<>(ctx);
        this.radius = radius;
        this.hasFaceParticle = hasFaceParticle;
    }

    protected void onPreExecute() {
        try {
            if (copyViews.get() == null) {
                b1 = screenshots();
            } else {
                if (hasFaceParticle) {
                    Bitmap topBitmap = BitmapUtils.drawableToBitmap(activity.get().getResources().getDrawable(R.drawable.emoji_small_bg));
                    b1 = getScreenShotBitmap(convertViewToBitmap(copyViews.get()[0]), copyViews.get()[1], topBitmap);
                } else {
                    b1 = convertViewToBitmap(copyViews.get()[1]);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    protected Drawable doInBackground(Void... param) {
        return screenShot(ctx.get(), b1, radius);
    }

    protected void onPostExecute(final Drawable result) {
        try {
            try {
                SajjadBlurOverlayManager.BlurView.storeBlurBg(result);
                Log.i("blur", "drawable == null " + (result == null));
                if (blurView.get() != null) {
                    Log.i("blur", "blurView != null");
                    Log.i("blur", "setBackgroundDrawable");
                    blurView.get().setBackgroundDrawable(result);
                } else {
                    Log.i("blur", "blurView = null");
                }
                blurTask = null;
            } catch (Exception e) {
                e.printStackTrace();
                Log.i("blur", "error  " + e.toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.i("blur", "error  " + e.toString());
        }
    }

    private Drawable screenShot(
            ReactApplicationContext reactContext,
            Bitmap b1,
            int radius) {
        try {
            b1 = blurBitmap(reactContext, b1, radius);
            return new BitmapDrawable(reactContext.getResources(), b1);
        } catch (Exception e) {
            e.printStackTrace();
            Log.i("blur", "error  " + e.toString());
            return null;
        }
    }

    public static Bitmap blurBitmap(Context context, Bitmap source, float radius) {

        if (context == null || context.getApplicationInfo() == null) {
            return source;
        }

        //创建一个缩小后的bitmap
        Bitmap inputBitmap = Bitmap.createScaledBitmap(
                source, source.getWidth() / 2, source.getHeight() / 2, false);
        //创建将在ondraw中使用到的经过模糊处理后的bitmap
        Bitmap outputBitmap = Bitmap.createBitmap(inputBitmap);

        //创建RenderScript，ScriptIntrinsicBlur固定写法
        RenderScript rs = RenderScript.create(context);
        ScriptIntrinsicBlur blurScript = ScriptIntrinsicBlur.create(rs, Element.U8_4(rs));

        //根据inputBitmap，outputBitmap分别分配内存
        Allocation tmpIn = Allocation.createFromBitmap(rs, inputBitmap);
        Allocation tmpOut = Allocation.createFromBitmap(rs, outputBitmap);

        //设置模糊半径取值0-25之间，不同半径得到的模糊效果不同
        blurScript.setRadius(radius);
        blurScript.setInput(tmpIn);
        blurScript.forEach(tmpOut);

        //得到最终的模糊bitmap
        tmpOut.copyTo(outputBitmap);
        return outputBitmap;
    }

    private Bitmap mergeBitmap(Bitmap backBitmap, Bitmap frontBitmap) {
        if (backBitmap == null || backBitmap.isRecycled() || frontBitmap == null || frontBitmap.isRecycled()) {
            Log.e("BlurTask", "backBitmap=" + backBitmap + ";frontBitmap=" + frontBitmap);
            return null;
        }
        Bitmap bitmap = backBitmap.copy(Bitmap.Config.ARGB_8888, true);
        Canvas canvas = new Canvas(bitmap);
        Rect baseRect = new Rect(0, 0, backBitmap.getWidth(), backBitmap.getHeight());
        Rect frontRect = new Rect(0, 0, frontBitmap.getWidth(), frontBitmap.getHeight());
        canvas.drawBitmap(frontBitmap, frontRect, baseRect, null);
        return bitmap;
    }

    private Bitmap getScreenShotBitmap(Bitmap content, View layoutView, Bitmap topBitmap) {
        if (content == null || layoutView == null) {
            return null;
        }

        layoutView.setDrawingCacheEnabled(true);
        layoutView.buildDrawingCache();
        Bitmap layout = convertViewToBitmap(layoutView);
        //拼接
        return mergeBitmap(mergeBitmap(content, layout), topBitmap);
    }

    private Bitmap convertViewToBitmap(View view) {
        if (view == null || view.getWidth() == 0 || view.getHeight() == 0) {
            return null;
        }
        Bitmap bitmap;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            bitmap = Bitmap.createBitmap(view.getWidth(), view.getHeight(), Bitmap.Config.ARGB_8888);
            int[] location = new int[2];
            view.getLocationInWindow(location);
            Activity activity = this.activity.get();

            if (activity == null || activity.getWindow() == null) {
                return null;
            }

            PixelCopy.request(activity.getWindow(),
                    new Rect(location[0], location[1], location[0] + view.getWidth(), location[1] + view.getHeight()),
                    bitmap, new PixelCopy.OnPixelCopyFinishedListener() {
                        @Override
                        public void onPixelCopyFinished(int copyResult) {

                        }
                    }, new Handler(Looper.getMainLooper()));
        } else {
            bitmap = Bitmap.createBitmap(view.getWidth(), view.getHeight(), Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bitmap);
            view.draw(canvas);
        }
        return bitmap;
    }

//    /**
//     *
//     * @param rs RenderScript Context
//     * @param image screenshot bitmap
//     * @param Radius integer between 1 to 24
//     * @param brightness -255..255 0 is default
//     * @return blurred Bitmap
//     */
//    private static Bitmap blur(RenderScript rs, Bitmap image, int Radius, float brightness, float factor) {
//        Bitmap outputBitmap;
//        if(Radius > 0 && Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
//            outputBitmap = Bitmap.createBitmap(image.getWidth(),image.getHeight(), Bitmap.Config.ARGB_8888);
//            ScriptIntrinsicBlur theIntrinsic = ScriptIntrinsicBlur.create(rs, Element.U8_4(rs));
//            Allocation tmpIn = Allocation.createFromBitmap(rs, image);
//            Allocation tmpOut = Allocation.createFromBitmap(rs, outputBitmap);
//            theIntrinsic.setRadius(Radius/factor);
//            theIntrinsic.setInput(tmpIn);
//            theIntrinsic.forEach(tmpOut);
//            tmpOut.copyTo(outputBitmap);
//        } else {
//            outputBitmap = image;
//        }
//        if(brightness!=0){
//            ColorMatrix cm = new ColorMatrix(new float[]
//                    {
//                            (float) 1, 0, 0, 0, brightness,
//                            0, (float) 1, 0, 0, brightness,
//                            0, 0, (float) 1, 0, brightness,
//                            0, 0, 0, 1, 0
//                    });
//            Canvas canvas = new Canvas(outputBitmap);
//            Paint paint = new Paint();
//            paint.setColorFilter(new ColorMatrixColorFilter(cm));
//            canvas.drawBitmap(outputBitmap, 0, 0, paint);
//        }
//        return outputBitmap;
//    }

    public Bitmap screenshots() {
        WindowManager mWindowManager = (WindowManager) ctx.get().getApplicationContext().getSystemService(Context.WINDOW_SERVICE);
        Display mDisplay = mWindowManager.getDefaultDisplay();
        DisplayMetrics mDisplayMetrics = new DisplayMetrics();
        Matrix mDisplayMatrix = new Matrix();
        mDisplay.getRealMetrics(mDisplayMetrics);
        float[] dims = {mDisplayMetrics.widthPixels, mDisplayMetrics.heightPixels};
        float degrees = getDegreesForRotation(mDisplay.getRotation());
//        Log.d(TAG, "getDegreesForRotation degrees : " + degrees);
        boolean requiresRotation = (degrees > 0);
        //如果屏幕发生旋转，通过matrix旋转回来
        if (requiresRotation) {
            mDisplayMatrix.reset();
            mDisplayMatrix.preRotate(-degrees);
            mDisplayMatrix.mapPoints(dims);
            dims[0] = Math.abs(dims[0]);
            dims[1] = Math.abs(dims[1]);
        }
        Bitmap bitmap = null;
        try {
            Class<?> clazz = Class.forName("android.view.SurfaceControl");
            Method method = clazz.getDeclaredMethod("screenshot", int.class, int.class);
            bitmap = (Bitmap) method.invoke(null, (int) dims[0], (int) dims[1]);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (bitmap != null) {
            bitmap.setHasAlpha(false);
        }
        return bitmap;
    }

    /**
     * @return the current display rotation in degrees
     */
    private static float getDegreesForRotation(int value) {
        switch (value) {
            case Surface.ROTATION_90:
                return 360f - 90f;
            case Surface.ROTATION_180:
                return 360f - 180f;
            case Surface.ROTATION_270:
                return 360f - 270f;
            default:
                return 0f;
        }
    }
}