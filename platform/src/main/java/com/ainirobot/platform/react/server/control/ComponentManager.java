package com.ainirobot.platform.react.server.control;/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

import android.os.HandlerThread;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.platform.component.Component;
import com.ainirobot.platform.react.server.utils.ComponentUtils;
import com.ainirobot.platform.react.server.utils.RNServerUtils;
import com.ainirobot.platform.rn.listener.IRNFinishListener;
import com.ainirobot.platform.rn.listener.IRNStatusListener;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

public class ComponentManager {

    private static final String TAG = ComponentManager.class.getSimpleName();
    private static final long STOP_TIMEOUT = 50;
    private static ComponentManager sComponentManager;

    private Set<String> mComponentNameList;
    private Map<String, Map<String, Component>> mComponentNameMap;
    private HandlerThread mThread;
    private final Object LOCK = new Object();

    private ComponentManager() {
        mThread = new HandlerThread(TAG);
        mThread.start();
        initComponentList();
    }

    public static ComponentManager getInstance() {
        if (sComponentManager == null) {
            sComponentManager = new ComponentManager();
        }
        return sComponentManager;
    }

    public void start(String componentName, String uid, String param) {
        Log.d(TAG, "start componentName: " + componentName + ", uid: " + uid
                + ", param: " + param);
        if (TextUtils.isEmpty(componentName) || TextUtils.isEmpty(uid)) {
            Log.e(TAG, "start error, componentName or uid null");
            return;
        }

        if (!mComponentNameList.contains(componentName)) {
            Log.e(TAG, "start error, invalid componentName: " + componentName);
            return;
        }

        Component component;

        if (!mComponentNameMap.containsKey(componentName)) {
            Log.d(TAG, "start component not contains");
            Component newComponent = ComponentUtils.initialComponent(componentName);
            if (newComponent == null) {
                Log.e(TAG, "start new component failed");
                return;
            }
            synchronized (LOCK){
                Log.d(TAG, "start put");
                Map<String, Component> componentUidMap = new ConcurrentHashMap<>();
                componentUidMap.put(uid, newComponent);
                mComponentNameMap.put(componentName, componentUidMap);
            }
            component = newComponent;
        } else if (mComponentNameMap.containsKey(componentName)
                && !mComponentNameMap.get(componentName).containsKey(uid)) {
            Log.d(TAG, "start uid not contains");
            Component newComponent = ComponentUtils.initialComponent(componentName);
            if (newComponent == null) {
                Log.e(TAG, "start new component failed");
                return;
            }
            Map<String, Component> componentUidMap = mComponentNameMap.get(componentName);
            synchronized (LOCK){
                if (componentUidMap == null){
                    componentUidMap = new ConcurrentHashMap<>();
                }
                componentUidMap.put(uid, newComponent);
                mComponentNameMap.put(componentName, componentUidMap);
                component = newComponent;
            }
        } else {
            Map<String, Component> componentUidMap = mComponentNameMap.get(componentName);
            component = componentUidMap.get(uid);
        }
        component.mUid = uid;
        component.start(param);
    }

    public void setStatusListener(String componentName, final String uid, final IRNStatusListener statusListener) {
        Log.d(TAG, "setStatusListener componentName: " + componentName + ", uid: " + uid);
        if (TextUtils.isEmpty(componentName) || TextUtils.isEmpty(uid)) {
            Log.e(TAG, "setStatusListener error, componentName or uid null");
            return;
        }

        if (!mComponentNameList.contains(componentName)) {
            Log.e(TAG, "setStatusListener error, invalid componentName: " + componentName);
            return;
        }

        Component component;

        if (!mComponentNameMap.containsKey(componentName)) {
            Log.d(TAG, "setStatusListener componentName not contains");
            Component newComponent = ComponentUtils.initialComponent(componentName);
            if (newComponent == null) {
                Log.e(TAG, "setStatusListener new component failed");
                return;
            }
            synchronized (LOCK){
                Log.d(TAG, "setStatusListener put");
                Map<String, Component> componentUidMap = new ConcurrentHashMap<>();
                componentUidMap.put(uid, newComponent);
                mComponentNameMap.put(componentName, componentUidMap);
            }
            component = newComponent;
        } else if (mComponentNameMap.containsKey(componentName)
                && !mComponentNameMap.get(componentName).containsKey(uid)) {
            Log.d(TAG, "setStatusListener uid not contains");
            Component newComponent = ComponentUtils.initialComponent(componentName);
            if (newComponent == null) {
                Log.e(TAG, "setStatusListener new component failed");
                return;
            }
            Map<String, Component> componentUidMap = mComponentNameMap.get(componentName);
            synchronized (LOCK){
                if (componentUidMap == null){
                    componentUidMap = new ConcurrentHashMap<>();
                }
                componentUidMap.put(uid, newComponent);
                mComponentNameMap.put(componentName, componentUidMap);
                component = newComponent;
            }
        } else {
            Map<String, Component> componentUidMap = mComponentNameMap.get(componentName);
            component = componentUidMap.get(uid);
        }
        component.mUid = uid;
        component.setStatusListener(new Component.ComponentStatusListener() {
            @Override
            public void onStatusUpdate(int status, String data, String extraData) {
                Log.e(TAG, "onStatusUpdate listener uid: " + uid
                        + ", status: " + status + ", data: " + data);
                try {
                    if (statusListener != null) {
                        statusListener.onStatusUpdate(status, data, extraData);
                    }
                } catch (RemoteException e) {
                    RNServerUtils.handleRemoteException("ComponentCallback onStatusUpdate", e);
                }
            }
        });
    }

    public void setComponentListener(final String componentName, final String uid,
                                     final IRNFinishListener finishListener) {
        Log.d(TAG, "setComponentListener componentName: " + componentName + ", uid: " + uid);
        if (TextUtils.isEmpty(componentName) || TextUtils.isEmpty(uid)) {
            Log.e(TAG, "setComponentListener error, componentName or uid null");
            return;
        }

        if (!mComponentNameList.contains(componentName)) {
            Log.e(TAG, "setComponentListener error, invalid componentName: " + componentName);
            return;
        }

        Component component;

        if (!mComponentNameMap.containsKey(componentName)) {
            Log.d(TAG, "setComponentListener componentName not contains");
            Component newComponent = ComponentUtils.initialComponent(componentName);
            if (newComponent == null) {
                Log.e(TAG, "setComponentListener new component failed");
                return;
            }
            synchronized (LOCK){
                Log.d(TAG, "setComponentListener put");
                Map<String, Component> componentUidMap = new ConcurrentHashMap<>();
                componentUidMap.put(uid, newComponent);
                mComponentNameMap.put(componentName, componentUidMap);
            }
            component = newComponent;
        } else if (mComponentNameMap.containsKey(componentName)
                && !mComponentNameMap.get(componentName).containsKey(uid)) {
            Log.d(TAG, "setComponentListener uid not contains");
            Component newComponent = ComponentUtils.initialComponent(componentName);
            if (newComponent == null) {
                Log.e(TAG, "setComponentListener new component failed");
                return;
            }
            Map<String, Component> componentUidMap = mComponentNameMap.get(componentName);
            synchronized (LOCK){
                if (componentUidMap == null){
                    componentUidMap = new ConcurrentHashMap<>();
                }
                componentUidMap.put(uid, newComponent);
                mComponentNameMap.put(componentName, componentUidMap);
                component = newComponent;
            }
        } else {
            Map<String, Component> componentUidMap = mComponentNameMap.get(componentName);
            component = componentUidMap.get(uid);
        }
        component.mUid = uid;
        component.setComponentListener(new Component.ComponentListener() {
            @Override
            public void onFinish(int result, String message, String extraData) {
                Log.e(TAG, "setComponentListener listener uid: " + uid
                        + ", result: " + result + ", message: " + message);
                try {
                    if (finishListener != null) {
                        finishListener.onFinish(result, message, extraData);
                    }
                } catch (RemoteException e) {
                    RNServerUtils.handleRemoteException("ComponentCallback onFinish", e);
                } finally {
                    Map<String, Component> uidMap = mComponentNameMap.get(componentName);
                    if (uidMap == null || uidMap.isEmpty()) {
                        Log.e(TAG, "setComponentListener onFinish componentName: "
                                + componentName + " uidMap null or not contains");
                        mComponentNameMap.remove(componentName);
                    } else {
                        synchronized (LOCK){
                            Log.d(TAG, "setComponentListener onFinish remove uid:"+uid);
                            uidMap.remove(uid);
                            if (uidMap.isEmpty()) {
                                mComponentNameMap.remove(componentName);
                            }
                        }
                    }
                }
            }
        });
    }

    public void updateParams(String componentName, String uid, String intent, String param) {
        Log.d(TAG, "updateParams componentName: " + componentName + ", uid: " + uid);
        Component component = getComponent(componentName, uid);
        if (component == null) {
            Log.e(TAG, "updateParams component null");
            return;
        }
        component.mUid = uid;
        component.updateParams(intent, param);
    }

    public void stop(String componentName, String uid, long timeout) {
        Log.d(TAG, "stop componentName: " + componentName + ", uid: " + uid);
        Map<String, Component> uidMap = mComponentNameMap.get(componentName);
        if (uidMap == null || uidMap.isEmpty() || !uidMap.containsKey(uid)) {
            Log.e(TAG, "stop component componentName: " + componentName
                    + " uidMap null or not contains");
            mComponentNameMap.remove(componentName);
        } else {
            Component component = uidMap.get(uid);
            if (component != null) {
                component.stop(timeout);
            }
            synchronized (LOCK){
                Log.d(TAG, "stop remove uid:"+uid);
                uidMap.remove(uid);
                if (uidMap.isEmpty()) {
                    mComponentNameMap.remove(componentName);
                }
            }
        }
    }

    private void initComponentList() {
        Log.d(TAG, "initComponentList");
        mComponentNameList = ComponentUtils.initAllComponent();
        mComponentNameMap = new ConcurrentHashMap<>();

    }

    public void resetAllComponents() {
        Log.d(TAG, "resetAllComponents");
        if (mComponentNameMap == null || mComponentNameMap.isEmpty()) {
            Log.e(TAG, "resetAllComponents name map null");
            mComponentNameList = ComponentUtils.initAllComponent();
            mComponentNameMap = new ConcurrentHashMap<>();
            return;
        }

        Set<String> nameSet = mComponentNameMap.keySet();
        Log.d(TAG, "resetAllComponents nameSet before size: " + nameSet.size());
        for (String componentName : nameSet) {
            Map<String, Component> uidMap = mComponentNameMap.get(componentName);
            if (uidMap != null) {
                Log.d(TAG, "resetAllComponents uidMap componentName: " + componentName
                        + ", before size: " + uidMap.size());
                for (String uid : uidMap.keySet()) {
                    Component component = uidMap.get(uid);
                    if (component != null) {
                        component.stop(STOP_TIMEOUT);
                        uidMap.remove(uid);
                    }
                }
                Log.d(TAG, "resetAllComponents uidMap componentName: " + componentName
                        + ", after size: " + uidMap.size());
            }
            mComponentNameMap.remove(componentName);
        }
        Log.d(TAG, "resetAllComponents nameSet after size: " + nameSet.size());
    }

    public void resetAllComponentListeners() {
        Log.d(TAG, "resetAllComponentListeners");
        if (mComponentNameMap == null || mComponentNameMap.isEmpty()) {
            Log.e(TAG, "resetAllComponentListeners name map null");
            return;
        }

        Set<String> nameSet = mComponentNameMap.keySet();
        for (String componentName : nameSet) {
            Map<String, Component> uidMap = mComponentNameMap.get(componentName);
            if (uidMap != null) {
                for (String uid : uidMap.keySet()) {
                    Component component = uidMap.get(uid);
                    if (component != null) {
                        component.setComponentListener(null);
                        component.setStatusListener(null);
                    }
                }
            }
        }
    }

    private Component getComponent(String componentName, String uid) {
        Map<String, Component> uidMap = mComponentNameMap.get(componentName);
        if (uidMap == null || uidMap.isEmpty()) {
            return null;
        }
        return uidMap.get(uid);
    }

    public HandlerThread getThread() {
        return mThread;
    }
}
