/*
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.ainirobot.platform.react.view.animator;

/**
 * 在此写用途
 *
 * @FileName: com.ainirobot.platform.ui.animator.AnimatorConfig.java
 * @params: REFRESH_RATE_DEF: 默认刷新频率; LayerType: drawable 层类型; Properties: 动画运动属性
 * @author: Orion
 * @date: 2018-11-29 19:27
 */
public class AnimatorConfig {
    public static final int REFRESH_RATE_DEF = 40; // 单位: ms

    public static enum Properties {
        right2left("right2left"),
        left2right("left2right"),
        down2up("down2up"),
        up2down("up2down"),
        vertical("vertical"),
        horizontal("horizontal"),
        fit("fit");

        private String name;

        Properties(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    public static enum LayerType {
        property("property"),
        json("json"),
        frame("frame");

        private String mName;

        LayerType(String name) {
            mName = name;
        }

        public String getName() {
            return mName;
        }
    }

}
