package com.ainirobot.platform.react.reactnative.component.common

import android.util.Base64
import android.util.Log
import com.ainirobot.platform.appmanager.config.impl.SystemConfig
import com.ainirobot.platform.react.AppManger
import com.ainirobot.platform.react.OPKBeanV3
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.character.ReactCharacter
import com.ainirobot.platform.react.reactnative.component.listener.BridgeCommandListener
import com.ainirobot.platform.utils.FileHelper
import com.ainirobot.platform.utils.GsonUtil
import com.ainirobot.platform.utils.LocalUtils
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import org.json.JSONArray
import org.json.JSONObject
import kotlin.system.exitProcess

/**
 * @date: 2019-06-21
 * @author: zyc
 * @desc: 当前版本信息
 */
class AppManagerBridgeModule(reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String {
        return "AppManager"
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun getAppJson(): String? {
        return getAppJsonByAppId(ReactCharacter.currentCharacter)
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun getAppJsonByAppId(appId: String?): String? {
        try {
            val rpkBean = AppManger.getRPKByCharacter(appId)

            if (rpkBean == null) {
                Log.v("AppManagerBridgeModule", "getAppJson no data")
                return ""
            }
            Log.v("AppManagerBridgeModule", " getAppJson ${GsonUtil.toJson(rpkBean)}")
            return GsonUtil.toJson(rpkBean)
        } catch (e: Exception) {
            e.printStackTrace()
            return ""
        }
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun getOpkPath(): String? {
        return getOpkPathByAppId(ReactCharacter.currentCharacter)
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun getOpkPathByAppId(appId: String?): String? {
        val rpkBean = AppManger.getRPKByCharacter(appId)

        return try {
            val opkPath = when (rpkBean!!.opkLoad) {
                "v3", "v2" -> {
                    AppManger.getAppBizPathByCharacter(appId)
                }

                "v1", null -> {
                    AppManger.getAppIndexPathByCharacter(appId)
                }

                else -> {
                    AppManger.getAppIndexPathByCharacter(appId)
                }
            }
            Log.v("AppManagerBridgeModule", "getOpkPath $opkPath")
            opkPath
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun getOpkExtraPath(): String? {
        return getOpkExtraPathByAppId(ReactCharacter.currentCharacter)
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun getOpkExtraPathByAppId(appId: String?): String? {
        return try {
            val opkPath = getOpkPathByAppId(appId)
            val pathArray = opkPath!!.split("/")
            Log.v("AppManagerBridgeModule", "getOpkExtraPath ${pathArray.last()}")
            opkPath.replace(pathArray.last(), "extra")
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun getAppIcon(appId: String?): String? {
        return try {
            val opkPath = when (appId) {
                null -> {
                    getOpkPath()
                }

                else -> {
                    getOpkPathByAppId(appId)
                }
            }

            val pathArray = opkPath!!.split("/")
            Log.v("AppManagerBridgeModule", "getAppIcon ${pathArray.last()}")
            opkPath.replace(pathArray.last(), "launcherIcon.png")
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun getAppConfig(appId: String?): String? {
        try {
            Log.v("AppManagerBridgeModule", "getAppConfig : $appId")
            val rpkBean = AppManger.getRPKByCharacter(appId)

            if (rpkBean == null) {
                Log.v("AppManagerBridgeModule", "getAppConfig no data : $appId")
                return ""
            }

            return when (rpkBean.type) {
                "host" -> {
                    getHostConfig(rpkBean)
                }

                "plugin" -> {
                    getPluginConfig(rpkBean)
                }

                else -> {
                    FileHelper.loadJsonFromFile(rpkBean.configPath)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return ""
        }
    }

    private fun getPluginConfig(info: OPKBeanV3): String? {
        Log.d(
            "AppManagerBridgeModule",
            "Get plugin config : ${info.appName}  ${info.appid}  ${info.configPath}"
        )
        if (info.configPath == null) {
            return ""
        }
        val config = JSONObject(FileHelper.loadJsonFromFile(info.configPath))
        val appConfig = config.getJSONObject("app_config")
        val data = String(Base64.decode(appConfig.getString("cfg_data"), Base64.DEFAULT))
        appConfig.put("cfg_data", data)
        Log.d("AppManagerBridgeModule", "Get plugin config : $appConfig")
        return appConfig.toString()
    }

    private fun getHostConfig(info: OPKBeanV3): String? {
        val config = JSONObject()
        val pluginConfigs = JSONArray()
        config.put("portal", LocalUtils.get(reactApplicationContext, "portal"))
        info.plugin?.forEach {
            try {
                val pluginConfig = JSONObject(FileHelper.loadJsonFromFile(it.configPath))
                if (pluginConfig.has("sys_config")) {
                    val sysConfig = pluginConfig.getJSONObject("sys_config")
                    sysConfig.put("appId", it.appid)
                    pluginConfigs.put(sysConfig)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        config.put("pluginConfig", pluginConfigs)

        Log.d("AppManagerBridgeModule", "Get host config : $config")
        return config.toString()
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun getSystemId(): String {
        return SystemConfig.APP_ID
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun setAppConfig(appId: String?, key: String, value: String) {
        try {
            RNClientManager.instance?.updateManager?.setAppConfig(appId, key, value)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun restartApp(appId: String?) {
        Log.v("AppManagerBridgeModule", "Restart app")
        //      AppManger.reload()
//        Handler(Looper.getMainLooper()).post {
//            EveActivity.getActivity().get()!!.recreate()
//        }

//        EveActivity.getActivity().get()!!.recreate()

        exitProcess(0)
    }

    @ReactMethod
    fun startApp(appId: String?) {
        try {
            RNClientManager.instance?.speechApiManager?.startApp(appId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun destroyApp(appId: String?) {
        try {
            RNClientManager.instance?.speechApiManager?.destroyApp(appId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun moveToForeground(appId: String?) {
        try {
            Log.v("AppManagerBridgeModule", "Move to foreground")
            RNClientManager.instance!!.speechApiManager!!.moveToForeground(appId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun moveToBack(appId: String?) {
        try {
            RNClientManager.instance?.speechApiManager?.moveToBack(appId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setAppPath(appId: String?, path: String) {
        try {
            Log.v("AppManagerBridgeModule", "Set app path")
            RNClientManager.instance?.speechApiManager?.setAppPath(appId, path)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setAppVersion(appId: String?, version: String) {
        try {
            RNClientManager.instance?.speechApiManager?.setAppVersion(appId, version)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun setAsyncCustomNlpData(opt: String?, data: String?): String? {
        try {
            Log.v("AppManagerBridgeModule", "setAsyncCustomNlpData")
            return RNClientManager.instance?.speechApiManager?.setAsyncCustomNlpData(opt, data)
        } catch (e: Exception) {
            e.printStackTrace()
            return "AppManagerBridgeModule setAsyncCustomNlpData error"
        }
    }

    @ReactMethod
    fun setServerApp(appList: List<String>) {
        try {
            RNClientManager.instance?.speechApiManager?.setServerApp(appList)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun getAppToken(appId: String, callbackId: Int, promise: Promise) {
        try {
            val rpkBean = AppManger.getRPKByCharacter(appId)
            if (null == rpkBean) {
                promise.reject(Exception("appId is null"))
            } else {
                promise.resolve(
                    RNClientManager.instance?.apiManager?.getAppToken(
                        "opk", appId,
                        rpkBean.versionName, BridgeCommandListener.obtain(callbackId)
                    )
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject(e)
        }
    }

    @ReactMethod
    fun finishAgentAction(action: String, code: Int, message: String?) {
        try {
            RNClientManager.instance?.speechApiManager?.onAgentActionFinish(action, code, message)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun updateAgentActionState(action: String, state: Int, data: String?) {
        try {
            RNClientManager.instance?.speechApiManager?.onAgentActionState(action, state, data)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
