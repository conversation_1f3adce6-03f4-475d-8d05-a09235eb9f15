package com.ainirobot.platform.react.reactnative.component

import android.view.View
import com.ainirobot.platform.react.reactnative.component.common.PersonDataBridgeModule
import com.ainirobot.platform.react.reactnative.component.skillcomponent.*
import com.ainirobot.platform.react.reactnative.component.uicomponent.statusbar.UiControllerBridgeModule
import com.ainirobot.platform.react.reactnative.openapp.OpenAppApiBridgeModule
import com.facebook.react.ReactPackage
import com.facebook.react.bridge.NativeModule
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.uimanager.ReactShadowNode
import com.facebook.react.uimanager.ViewManager
import java.util.*
import kotlin.collections.ArrayList

/**
 * @date: 2019-01-04
 * @author: lumeng
 * @desc: 管理各个BridgeModule注册
 *
 * Java 类型      -> JS 类型
 * Boolean       -> Bool
 * Integer       -> Number
 * Double        -> Number
 * Float         -> Number
 * String        -> String
 * Callback      -> function
 * ReadableMap   -> Object
 * ReadableArray -> Array
 */

class ComponentBridgeModuleReactPackage : ReactPackage {

    override fun createNativeModules(reactContext: ReactApplicationContext): ArrayList<NativeModule> {
        val modules = ArrayList<NativeModule>()
        modules.add(RobotApiBridgeModule(reactContext))
        modules.add(SkillApiBridgeModule(reactContext))
        modules.add(PersonDisappearComponentBridge(reactContext))
        modules.add(PersonAppearComponentBridge(reactContext))
        modules.add(NavigationComponentBridge(reactContext))
        modules.add(LeadingTrackComponentBridge(reactContext))
        modules.add(WakeupAndPreWakeupStartCheckComponentBridge(reactContext))
        modules.add(StandardFaceTrackComponentBridge(reactContext))
        modules.add(ReservationCodeComponentBridge(reactContext))
        modules.add(ReceptionRegisterComponentBridge(reactContext))
        modules.add(AutoResetEstimateComponentBridge(reactContext))
        modules.add(FaceTrackComponentBridge(reactContext))
        modules.add(ForwardComponentBridge(reactContext))
        modules.add(GetDistanceWithPlaceComponentBridge(reactContext))
        modules.add(GetNearestPlaceComponentBridge(reactContext))
        modules.add(ModifyNameComponentBridge(reactContext))
        modules.add(RecognizeComponentBridge(reactContext))
        modules.add(RecoverPositiveComponentBridge(reactContext))
        modules.add(RegisterComponentBridge(reactContext))
        modules.add(ResetEstimateComponentBridge(reactContext))
        modules.add(SoundLocalizationComponentBridge(reactContext))
        modules.add(HeadTurnComponentBridge(reactContext))
        modules.add(HeadTurnGroupComponentBridge(reactContext))
        modules.add(SetCruiseRouteComponentBridge(reactContext))
        modules.add(GetCruiseRouteComponentBridge(reactContext))
        modules.add(CruiseComponentBridge(reactContext))
        modules.add(ChargeComponentBridge(reactContext))
        modules.add(RobotStandbyComponentBridge(reactContext))
        modules.add(LightApiBridgeModule(reactContext))
        modules.add(VideoApiBridgeModule(reactContext))
        modules.add(PersonDataBridgeModule(reactContext))
        modules.add(ChargeStartComponentBridge(reactContext))
        modules.add(NavigationBackComponentBridge(reactContext))
        modules.add(OpenAppApiBridgeModule(reactContext))
        modules.add(BodyFollowComponentBridge(reactContext))
        modules.add(StatusComponentBridge(reactContext))
        modules.add(UiControllerBridgeModule(reactContext))
        modules.add(BasicMotionComponentBridge(reactContext))
        modules.add(LightGroupComponentBridge(reactContext))
        modules.add(MsgBridgeModule(reactContext))
        modules.add(NavigationCheckComponentBridge(reactContext))
        modules.add(NavigationTransferComponentBridge(reactContext))
        modules.add(LeavePileComponentBridge(reactContext))
        modules.add(JudgeInChargingPileComponentBridge(reactContext))
        modules.add(SetNavigationConfigComponentBridge(reactContext))
        modules.add(NavigationElevatorComponentBridge(reactContext))
        modules.add(UvcCameraComponentBridge(reactContext))
        modules.add(ProTrayLedComponentBridge(reactContext))
        modules.add(NavigationAdvanceComponentBridge(reactContext))
        modules.add(AIIntelligenceTrackComponentBridge(reactContext))
        return modules
    }

    override fun createViewManagers(reactContext: ReactApplicationContext): MutableList<ViewManager<View, ReactShadowNode<*>>> {
        return Collections.emptyList()
    }

}
