package com.ainirobot.platform.react.reactnative.component.uicomponent.imageframe;

import android.content.res.Resources;
import android.graphics.drawable.BitmapDrawable;
import androidx.core.view.ViewCompat;
import android.util.Log;

import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.common.MapBuilder;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.UIManagerModule;
import com.facebook.react.uimanager.annotations.ReactProp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;


public class ImageFrameManager extends SimpleViewManager<ImageFrameCustomView> {

    public static String EVENT_FRAME_END = "_onPlayFinish";

    private ThemedReactContext reactContext;
    private int fps = 25;
    private boolean isLoop = true;
    private boolean isOpenLruCache = true;
    private int animType = 0;
    private int[] resIds = new int[0];
    private String[] resStrs = new String[0];
    private boolean isLocal = false;

    private boolean isImageError = false;

    @Override
    public String getName() {
        return "ImageFrame";
    }

    @Override
    protected ImageFrameCustomView createViewInstance(ThemedReactContext reactContext) {
        this.reactContext = reactContext;
        ImageFrameCustomView view = new ImageFrameCustomView(reactContext);
        return view;
    }

    @Override
    protected void addEventEmitters(@Nonnull ThemedReactContext reactContext, @Nonnull ImageFrameCustomView view) {
        super.addEventEmitters(reactContext, view);
    }

    @Nullable
    @Override
    public Map<String, Object> getExportedCustomDirectEventTypeConstants() {
        Map<String, Object> map = new HashMap<>();
        map.put(EVENT_FRAME_END, MapBuilder.of("registrationName", EVENT_FRAME_END));
        return map;
    }

    @Override
    protected void onAfterUpdateTransaction(@Nonnull final ImageFrameCustomView view) {
        super.onAfterUpdateTransaction(view);

        if (isImageError) return;

        FrameBuild builder = isLocal?
                new ImageFrameHandler.ResourceHandlerBuilder(reactContext.getResources(), resIds) :
                new ImageFrameHandler.UrlHandlerBuilder(resStrs);

        ImageFrameHandler handler = builder.setFps(fps).setLoop(isLoop).openLruCache(isOpenLruCache).build();

        handler.setOnImageLoaderListener(new ImageFrameHandler.OnImageLoadListener() {
            @Override
            public void onImageLoad(BitmapDrawable drawable) {
                ViewCompat.setBackground(view, drawable);
            }

            @Override
            public void onPlayFinish() {
                reactContext
                        .getNativeModule(UIManagerModule.class)
                        .getEventDispatcher()
                        .dispatchEvent(new FrameEndEvent(view.getId(), animType));
            }
        });
        view.startImageFrame(handler);
    }

    private void loadRes(final ImageFrameCustomView imageFrame, ThemedReactContext reactContext, ArrayList<String> uris) {
        resIds = new int[uris.size()];
        Resources res = reactContext.getResources();
        final String packageName = reactContext.getPackageName();
        for (int i = 0; i < resIds.length; i++) {
            int imageResId = res.getIdentifier(uris.get(i), "drawable", packageName);
            resIds[i] = imageResId;
            Log.d("ImageFrame", "imageResId=" + imageResId  + " package: " + packageName);
        }
    }

    private void loadUrl(final ImageFrameCustomView imageFrame, ArrayList<String> uris) {
        resStrs = new String[uris.size()];
        for (int i = 0; i < resStrs.length; i++) {
            resStrs[i] = uris.get(i);
        }
    }

    /**
     * sets the speed of the animation.
     *
     * @param view
     * @param framesPerSecond
     */
    @ReactProp(name = "framesPerSecond")
    public void setFramesPerSecond(final ImageFrameCustomView view, Integer framesPerSecond) {
//    view.setFramesPerSecond(framesPerSecond);
        fps = framesPerSecond;
    }

    /**
     * @param view
     * @param images an array of ReadableMap's {uri: "http://...."} return value of the resolveAssetSource(....)
     */
    @ReactProp(name = "images")
    public void setImages(final ImageFrameCustomView view, ReadableArray images) {
        ArrayList<String> uris = new ArrayList<>();
        try {
            for (int index = 0; index < images.size(); index++) {
                ReadableMap map = images.getMap(index);
              //  Log.d("ImageFrame", "setImages: " + map.getString("uri"));

                uris.add(map.getString("uri"));
            }

            if (isLocal = isLocalRes(uris)) {
                loadRes(view, this.reactContext, uris);
            } else {
                loadUrl(view, uris);
            }
        } catch (Exception e) {
            isImageError = true;
            Log.e("ImageFrameManager", "image src error: " + e);
        }
    }

    private boolean isLocalRes(ArrayList<String> uris) {
        if (uris == null || uris.isEmpty()) {
            return false;
        }

        for (String url: uris) {
            if (url.startsWith("http") || url.startsWith("file")) {
                return false;
            }
        }

        return true;
    }

    /**
     * sets if animations is looped indefinitely.
     *
     * @param view
     * @param loop
     */
    @ReactProp(name = "loop")
    public void setLoop(final ImageFrameCustomView view, Boolean loop) {
//    view.setLoop(loop);

        isLoop = loop;
    }

    @ReactProp(name = "type")
    public void setAnimType(final ImageFrameCustomView view, int type) {
//    view.setLoop(loop);

        animType = type;
    }


}