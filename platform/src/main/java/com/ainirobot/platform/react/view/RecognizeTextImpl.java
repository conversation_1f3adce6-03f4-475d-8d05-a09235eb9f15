/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.react.view;

import android.text.TextUtils;

public class RecognizeTextImpl implements IRecognizeText {

    private String tempText;
    private String historyText;
    private StringBuffer sbTrans = new StringBuffer();
    private static final int ASR_LIMIT_TEXT_COUNT = 12;

    private IRecognizeText mRecognizeText;

    public RecognizeTextImpl(IRecognizeText recognizeText) {
        this.mRecognizeText = recognizeText;
    }

    private String transText(String text) {
        sbTrans.setLength(0);
        text = text.replaceAll("[\\[\\]]", "");
        sbTrans.append(text);
        int lowerCase = containLowerCase(sbTrans.toString());
        int textLength = sbTrans.length() - lowerCase / 2;
        if (textLength > ASR_LIMIT_TEXT_COUNT) {
            return sbTrans.substring(textLength - ASR_LIMIT_TEXT_COUNT, sbTrans.length());
        }
        return sbTrans.toString();
    }

    private int containLowerCase(String str) {
        int cntL = 0;
        for (int i = 0; i < str.length(); ++i) {
            char ch = str.charAt(i);
            if (ch >= 'a' && ch <= 'z') {
                cntL++;
            }
        }
        return cntL;
    }

    @Override
    public void onRecognizeText(boolean isFinal, String result) {
        if (TextUtils.isEmpty(result)) {
            return;
        }
        if (!TextUtils.isEmpty(result)) {
            tempText = transText(result);
        }
        if (tempText.length() == 1) {
            historyText = tempText;
            return;
        }
        if (!isFinal && TextUtils.equals(historyText, tempText)) {
            return;
        }
        if (mRecognizeText != null) {
            mRecognizeText.onRecognizeText(isFinal, tempText);
        }
        historyText = tempText;
    }

}
