package com.ainirobot.platform.react.reactnative.component.common

import android.util.Log
import com.ainirobot.coreservice.client.actionbean.GongFuBean
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.reactnative.component.listener.BridgeActionListener
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.google.gson.Gson

/**
 * @date: 2019-07-02
 * @author: lumeng
 * @email: <EMAIL>
 * @desc: dance bridge
 */
class DanceOperationBridge(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String {
        return "DanceOperation"
    }

    @ReactMethod
    fun playMotion(callbackId:Int, headAction:String, footAction:String) {
        Log.i(name, "playMoveMotion")
        try {
            val bean = GongFuBean()
            bean.headActionJson = headAction
            bean.footActionJson = footAction
            RNClientManager.instance?.apiManager!!.playTrick(Gson().toJson(bean), BridgeActionListener.obtain(callbackId))
        }catch (e:Throwable){
            e.printStackTrace()
        }
    }

    @ReactMethod
    fun stopMotion() {
        Log.i(name, "stopMotion")
        try {
            RNClientManager.instance?.apiManager!!.stopPlayTrick()
        }catch (e:Throwable){
            e.printStackTrace()
        }
    }


    @ReactMethod
    fun playLightEffect(lightEffectTypeStr: String) {
       // Log.i(name, "LightOperation play lightEffectTypeStr:$lightEffectTypeStr")
        try {
            RNClientManager.instance?.lightManager!!.playEffect(lightEffectTypeStr)
        }catch (e:Throwable){
            e.printStackTrace()
        }
    }

}