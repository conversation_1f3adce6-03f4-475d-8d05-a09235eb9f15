package com.ainirobot.platform.react.reactnative.component.uicomponent.ninepatch

import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.annotations.ReactProp

@ReactModule(name = NinePatchImageManager.REACT_CLASS)
class NinePatchImageManager: SimpleViewManager<NinePatchImageView>() {

    companion object {
        const val REACT_CLASS = "NinePatchImageView"
        const val TAG = "NinePatchImageManager"
    }

    override fun getName(): String {
        return REACT_CLASS
    }

    override fun createViewInstance(reactContext: ThemedReactContext): NinePatchImageView {
        return NinePatchImageView(reactContext)
    }

    @ReactProp(name = "name")
    fun setBackgroundName(view: NinePatchImageView, name: String) {
        if (name.contains(".9.png")) {
            try {
                val drawable = NinePatchUtils.decodeDrawableFromOtherResour(view.context, name);
                if (drawable != null) {
                    view.background = drawable
                }
            }catch (e: Exception) {

            }
        } else {
            view.setBackgroundName(name)
        }
    }
}