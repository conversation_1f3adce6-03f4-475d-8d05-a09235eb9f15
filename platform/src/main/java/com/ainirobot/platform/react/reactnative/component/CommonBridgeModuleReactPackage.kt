package com.ainirobot.platform.react.reactnative.component

import android.view.View
import com.ainirobot.platform.react.reactnative.component.common.BitmapUtilBridgeModule
import com.ainirobot.platform.react.reactnative.component.common.*
import com.facebook.react.ReactPackage
import com.facebook.react.bridge.NativeModule
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.uimanager.ReactShadowNode
import com.facebook.react.uimanager.ViewManager
import java.util.*
import kotlin.collections.ArrayList

/**
 * @date: 2019-01-04
 * @author: lumeng
 * @desc: 管理各个BridgeModule注册
 *
 * Java 类型      -> JS 类型
 * Boolean       -> Bool
 * Integer       -> Number
 * Double        -> Number
 * Float         -> Number
 * String        -> String
 * Callback      -> function
 * ReadableMap   -> Object
 * ReadableArray -> Array
 */

class CommonBridgeModuleReactPackage : ReactPackage {

    override fun createNativeModules(reactContext: ReactApplicationContext): ArrayList<NativeModule> {
        val modules = ArrayList<NativeModule>()
        modules.add(InitialBridgeModule(reactContext))
        modules.add(SettingsBridgeModule(reactContext))
        modules.add(CommonUtilBridgeModule(reactContext))
        modules.add(BitmapUtilBridgeModule(reactContext))
        modules.add(ControlManagerBridge(reactContext))
        modules.add(ReportBridgeModule(reactContext))
        modules.add(ModuleDataUnitBridgeModule(reactContext))
        modules.add(AppManagerBridgeModule(reactContext))
        modules.add(AppUpdateManagerBridgeModule(reactContext))
        modules.add(DanceOperationBridge(reactContext))
        modules.add(SendBroadcastBridgeModule(reactContext))
        modules.add(DeviceBridgeModule(reactContext))
        modules.add(AndroidSettingsBridgeModule(reactContext))
        modules.add(RobotSettingApiBridgeModule(reactContext))
        modules.add(RNApkControlBridge(reactContext))
        modules.add(MapBridgeModule(reactContext))
        modules.add(DumpMemInfoBridge(reactContext))
        modules.add(CountdownAlarmManagerBridgeModule(reactContext))
        modules.add(CallButtonBridgeModule(reactContext))
        modules.add(TimeoutModule(reactContext))
        modules.add(ProductInfoBridgeModule(reactContext))
        modules.add(NetworkRequestBridgeModule(reactContext))//网络请求
        modules.add(WebViewBridgeModule(reactContext))
        return modules
    }

    override fun createViewManagers(reactContext: ReactApplicationContext): MutableList<ViewManager<View, ReactShadowNode<*>>> {
        return Collections.emptyList()
    }

}
