package com.ainirobot.platform.react.reactnative.component.uicomponent.statusbar;

import com.ainirobot.coreservice.client.ProductInfo;

public class StatusBarManager {
    private static final String TAG = StatusBarManager.class.getSimpleName();

    public static IStatusBar getStatusBar() {
        if (ProductInfo.isCarryProduct()) {
            return StatusBarCarry.getInstance();
        }

        if (ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()) {
            return StatusBarMini.getInstance();
        }

        return StatusBar.getInstance();
    }
}
