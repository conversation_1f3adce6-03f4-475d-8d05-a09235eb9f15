package com.ainirobot.platform.react.reactnative.component.skillcomponent

import com.ainirobot.platform.component.LeadingTrackComponent
import com.ainirobot.platform.react.server.utils.ComponentUtils
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactMethod

/**
 * @date: 2019-03-30
 * @author: lumeng
 * @desc: 引领追踪组件
 */
class LeadingTrackComponentBridge(reactContext: ReactApplicationContext) : BaseComponentBridge<LeadingTrackComponent>(reactContext) {

    override fun isHDMonopoly(): Boolean {
        // 270度的头，会占用云台，占用完之后就不再占用，要根据状态来判断，这个在rn层做判断
        return true
    }

    override fun getResouceType(): Array<ResouceType> {
        return arrayOf(ResouceType.Vision,ResouceType.HeadTurn)
    }

    override fun getName(): String {
        return ComponentUtils.COMPONENT_LEADING_TRACK
    }

    @ReactMethod
    override fun start(uid: String, param: String?) {
        super._start(uid, param)
    }

    @ReactMethod
    override fun stop(uid: String, timeout: Int?) {
        super._stop(uid, timeout)
    }

    @ReactMethod
    override fun updateParams(uid: String, intent: String, param: String) {
        super._updateParams(uid, intent, param)
    }

    @ReactMethod
    override fun setStatusListener(uid: String, statusListenerId: Int) {
        super._setStatusListener(uid, statusListenerId)
    }

    @ReactMethod
    override fun setFinishListener(uid: String, finishListenerId: Int) {
        super._setFinishListener(uid, finishListenerId)
    }

}
