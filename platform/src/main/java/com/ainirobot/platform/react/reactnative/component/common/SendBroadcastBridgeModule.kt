package com.ainirobot.platform.react.reactnative.component.common

import android.content.Intent
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.ReadableMap
import org.json.JSONException

class SendBroadcastBridgeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    override fun getName(): String = "Broadcast"

    @ReactMethod
    fun sendBroadcast(action: String, map: ReadableMap) {

        try {
            val intent = Intent(action)
            val hashMap = map.toHashMap()
            hashMap.iterator().forEach {
                when (it.value) {
                    is Int -> intent.putExtra(it.key, it.value as Int)
                    is IntArray -> intent.putExtra(it.key, it.value as IntArray)
                    is String -> intent.putExtra(it.key, it.value as String)
                    is Boolean -> intent.putExtra(it.key, it.value as Boolean)
                    is BooleanArray -> intent.putExtra(it.key, it.value as BooleanArray)
                    is Short -> intent.putExtra(it.key, it.value as Short)
                    is ShortArray -> intent.putExtra(it.key, it.value as ShortArray)
                    is Long -> intent.putExtra(it.key, it.value as Long)
                    is LongArray -> intent.putExtra(it.key, it.value as LongArray)
                    is Float -> intent.putExtra(it.key, it.value as Float)
                    is FloatArray -> intent.putExtra(it.key, it.value as FloatArray)
                    is Double -> intent.putExtra(it.key, it.value as Double)
                    is DoubleArray -> intent.putExtra(it.key, it.value as DoubleArray)
                }
            }
            reactApplicationContext.sendBroadcast(intent)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }
}