package com.ainirobot.platform.react.reactnative.component.uicomponent.statusbar;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.PixelFormat;
import android.net.ConnectivityManager;
import android.net.wifi.SupplicantState;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.telephony.PhoneStateListener;
import android.telephony.SignalStrength;
import android.telephony.TelephonyManager;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.R;
import com.ainirobot.platform.data.RobotInfo;

import java.lang.ref.WeakReference;

/**
 * 适配不同机型的StatusBar-抽象类
 * 可继承BaseStatusBar,实现不同布局的StatusBar
 */
public abstract class BaseStatusBar implements IStatusBar {

    private static final String TAG = BaseStatusBar.class.getSimpleName();
    public static final int STATUS_BAR_BACKGROUND_LIGHT = 1;
    public static final int STATUS_BAR_BACKGROUND_DEEP = 2;
    protected static final String ACTION_SIM_STATE_CHANGED = "android.intent.action.SIM_STATE_CHANGED";
    static final int[] SIM_STATE_NO_SERVICE = new int[]{R.attr.state_mobile_type_no_service};
    static final int[] SIM_STATE_2G = new int[]{R.attr.state_mobile_type_2g};
    static final int[] SIM_STATE_3G = new int[]{R.attr.state_mobile_type_3g};
    static final int[] SIM_STATE_4G = new int[]{R.attr.state_mobile_type_4g};
    static final int[] SIM_STATE_5G = new int[]{R.attr.state_mobile_type_5g};
    static final int[] SIM_STATE_NO_DATA = new int[]{R.attr.state_mobile_type_no_data};
    static final int[] BATTERY_PLUGGED = new int[]{R.attr.state_battery_plugged};
    protected WindowManager mWindowManager;
    protected TelephonyManager mTelephonyManager;
    protected WifiManager mWifiManager;
    protected View mStatusBarView; // 根据ui要求，需要适配两套顶部状态栏。icon深色，背景白色。根据需求适配。
    protected View mStatusBarAnotherrView; // 根据ui要求，需要适配两套顶部状态栏。icon白色，背景深色
    private int mCurrStatusBarMode = -1;
    private static final WindowManager.LayoutParams sLayoutParams = new WindowManager.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE, PixelFormat.TRANSLUCENT);
    protected SignalStrength mMobileSignalStrength = null;
    protected WeakReference<Activity> mActivity;
    protected boolean mIsShow = false; // 显示StatusBar标记
    private BroadcastReceiver mExposureReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String data = intent.getStringExtra("data");
            updateExposureStatus(data);
        }
    };

    @Override
    public void onCreate() {
        Log.d(TAG, "onCreate");

        mStatusBarView = getStatusBarView(); // 先初始化view，再初始化系统功能监听
        mStatusBarAnotherrView = getStatusBarAnother();
        if (mStatusBarView == null) {
            throw new IllegalStateException("StatusBarView is not initialized");
        }
        initStatusBarLayout();
        init();
        if (!mIsShow) {
            mWindowManager.addView(mStatusBarView, sLayoutParams);
            mIsShow = true;
        }

    }

    @Override
    public void onStart(Activity activity) {
        IntentFilter filter = new IntentFilter(RobotInfo.STATUS_EXPOSURE);
        BaseApplication.getContext().registerReceiver(mExposureReceiver, filter);

        mActivity = new WeakReference<>(activity);
        updateMobileConnectionInfo();
        updateWifiConnectionInfo();
        updateTime();
    }

    @Override
    public void onStop() {
        Log.d(TAG, "trigger onStop");
        try {
            BaseApplication.getContext().unregisterReceiver(mExposureReceiver);
        } catch (Exception e) {
            Log.e(TAG, "unregisterReceiver mExposureReceiver exception", e);
        }
    }

    @Override
    public void showStatusBar(boolean isShow) {
        Log.d(TAG, "showStatusBar isShow: " + isShow);
        if (mWindowManager == null || mIsShow == isShow) {
            return;
        }
        mIsShow = isShow;
        if (isShow) {
            mWindowManager.addView(mStatusBarView, sLayoutParams);
        } else {
            mWindowManager.removeView(mStatusBarView);
        }
    }

    @Override
    public synchronized void showStatusBarByType(boolean isShow, int type) {
        Log.d(TAG, "showStatusBar isShow: " + isShow + " type: " + type);
        if (mWindowManager == null) {
            return;
        }
        if (mStatusBarAnotherrView == null) {
            throw new IllegalStateException("StatusBarViewAnother is not initialized");
        }
        if (isShow) {
            handleStatusBarShow(type);
        } else {
            mWindowManager.removeView(mStatusBarView);
            mWindowManager.removeView(mStatusBarAnotherrView);
        }
        mCurrStatusBarMode = type;
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "trigger onDestroy:" + mIsShow);
        if (mIsShow) {
            if (mStatusBarView != null && mStatusBarView.isAttachedToWindow()) {
                mWindowManager.removeView(mStatusBarView);
            }
            mIsShow = false;
        }
        mStatusBarView = null;

        if (mStatusBarAnotherrView != null && mStatusBarAnotherrView.isAttachedToWindow()) {
            mWindowManager.removeView(mStatusBarAnotherrView);
        }
        mStatusBarAnotherrView = null;
    }

    /**
     * 初始化公共数据
     * 1. statusbar窗口及布局参数、手机卡信号、wifi信号
     * 2. 注册网络 电池 手机网卡信号等系统服务，在回调中处理bar的数据状态显示
     */
    private void init() {
        mWindowManager = (WindowManager) BaseApplication.getApplication().getSystemService(Context.WINDOW_SERVICE);
        mWifiManager = (WifiManager) BaseApplication.getApplication().getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        mTelephonyManager = (TelephonyManager) BaseApplication.getContext().getSystemService(Context.TELEPHONY_SERVICE);

        sLayoutParams.gravity = Gravity.END | Gravity.TOP;
        sLayoutParams.packageName = BaseApplication.getContext().getPackageName();
        sLayoutParams.setTitle("RobotStatusBar");
        sLayoutParams.alpha = 1.0f;
        sLayoutParams.dimAmount = 0f;

        final IntentFilter filter = new IntentFilter();
        filter.addAction(WifiManager.WIFI_STATE_CHANGED_ACTION);
        filter.addAction(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION);
        filter.addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION);
        filter.addAction(WifiManager.RSSI_CHANGED_ACTION);
        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        filter.addAction(ACTION_SIM_STATE_CHANGED);
        mBroadcastReceiver.onReceive(BaseApplication.getContext(), BaseApplication.getApplication().registerReceiver(mBroadcastReceiver, filter));
        mBroadcastReceiver.onReceive(BaseApplication.getContext(), BaseApplication.getApplication().registerReceiver(mBroadcastReceiver, new IntentFilter(Intent.ACTION_BATTERY_CHANGED))); // update
        mTelephonyManager.listen(mPhoneStateListener, PhoneStateListener.LISTEN_CELL_INFO | PhoneStateListener.LISTEN_SERVICE_STATE
                | PhoneStateListener.LISTEN_SIGNAL_STRENGTHS);
    }

    public abstract View getStatusBarView(); // 如果需要两个StatusBar切换就实现getStatusBarAnother，否则返回null即可。
    public abstract View getStatusBarAnother();
    public abstract void initStatusBarLayout();
    public abstract void updateBatteryInfo(Intent intent);
    public abstract void updateWifiConnectionInfo();
    public abstract void updateMobileConnectionInfo();
    public abstract void updateTime();
    public abstract void updateExposureStatus(String data);

    protected final BroadcastReceiver mBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null && intent.getAction() != null) {
                switch (intent.getAction()) {
                    case Intent.ACTION_BATTERY_CHANGED:
                        updateBatteryInfo(intent);
                        updateWifiConnectionInfo();
                        updateMobileConnectionInfo();
                        break;
                    case ACTION_SIM_STATE_CHANGED:
                    case ConnectivityManager.CONNECTIVITY_ACTION:
                    case WifiManager.WIFI_STATE_CHANGED_ACTION:
                    case WifiManager.NETWORK_STATE_CHANGED_ACTION:
                    case WifiManager.SCAN_RESULTS_AVAILABLE_ACTION:
                    case WifiManager.RSSI_CHANGED_ACTION:
                        updateWifiConnectionInfo();
                        updateMobileConnectionInfo();
                        break;
                    default:
                        break;
                }
            }
        }
    };

    protected final PhoneStateListener mPhoneStateListener = new PhoneStateListener() {

        public void onDataConnectionStateChanged(int state, int networkType) {
            super.onDataConnectionStateChanged(state, networkType);
        }
        // 设备服务状态变化时触发
        public void onServiceStateChanged(android.telephony.ServiceState serviceState) {
            updateMobileConnectionInfo();
        }

        public void onCellInfoChanged(java.util.List<android.telephony.CellInfo> cellInfo) {
            updateMobileConnectionInfo();
        }
        // 网络信号变化时触发
        public void onSignalStrengthsChanged(android.telephony.SignalStrength signalStrength) {
            mMobileSignalStrength = signalStrength;
            updateMobileConnectionInfo();
        }
    };

    protected WifiInfo getWifiInfo() {
        if (mWifiManager != null && mWifiManager.getConnectionInfo() != null) {
            return mWifiManager.getConnectionInfo();
        } else {
            WifiManager wifiManager = (WifiManager) BaseApplication.getApplication().getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            return wifiManager.getConnectionInfo();
        }
    }

    protected boolean isWifiConnected(WifiInfo wifiInfo) {
        return (wifiInfo != null) && ((wifiInfo.getSupplicantState() == SupplicantState.COMPLETED)) && (wifiInfo.getNetworkId() != -1);
    }

    private void handleStatusBarShow(int type) {
        if (type != mCurrStatusBarMode) {
            if (mCurrStatusBarMode == STATUS_BAR_BACKGROUND_DEEP) {
                mWindowManager.removeView(mStatusBarView);
                mWindowManager.addView(mStatusBarAnotherrView, sLayoutParams);
            } else {
                mWindowManager.removeView(mStatusBarAnotherrView);
                mWindowManager.addView(mStatusBarView, sLayoutParams);
            }
        }
    }
}
