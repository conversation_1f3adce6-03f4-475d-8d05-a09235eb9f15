package com.ainirobot.platform.react.server.control;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.ainirobot.base.OrionBase;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.platform.bi.wrapper.ReportControl;
import com.ainirobot.platform.bi.wrapper.manager.bi.impl.OpkRestartPoint;
import com.ainirobot.platform.bi.wrapper.manager.bi.impl.RnHeartBeatTimeoutPoint;
import com.ainirobot.platform.light.LightManager;
import com.ainirobot.platform.msg.MsgController;
import com.ainirobot.platform.react.AppManger;
import com.ainirobot.platform.react.OPKBeanV3;
import com.ainirobot.platform.react.reactnative.character.ReactCharacter;
import com.ainirobot.platform.react.reactnative.character.StartEvent;
import com.ainirobot.platform.react.server.impl.MapManager;
import com.ainirobot.platform.react.server.impl.RNApiServer;
import com.ainirobot.platform.react.server.impl.RNCallButtonManagerServer;
import com.ainirobot.platform.react.server.impl.RNCallbackServer;
import com.ainirobot.platform.react.server.impl.RNComponentServer;
import com.ainirobot.platform.react.server.impl.RNDataServer;
import com.ainirobot.platform.react.server.impl.RNDeviceServer;
import com.ainirobot.platform.react.server.impl.RNIntentManagerServer;
import com.ainirobot.platform.react.server.impl.RNNLPApkControlSever;
import com.ainirobot.platform.react.server.impl.RNOkHttpRequestServer;
import com.ainirobot.platform.react.server.impl.RNUpdateManagerServer;
import com.ainirobot.platform.react.server.impl.RNPersonManagerServer;
import com.ainirobot.platform.react.server.impl.RNRobotSettingServer;
import com.ainirobot.platform.react.server.impl.RNSkillCallbackServer;
import com.ainirobot.platform.react.server.impl.RNSpeechApiServer;
import com.ainirobot.platform.react.server.utils.RNServerUtils;
import com.ainirobot.platform.rn.IRNCommandCallBack;
import com.ainirobot.platform.rn.IRNSpeechCallBack;

public class RNServerManager {

    private static final String TAG = "RNServerManager";
    private volatile static RNServerManager sRNServerManager;
    private RNSpeechApiServer mSpeechApiServer;
    private RNComponentServer mComponentServer;
    private RNApiServer mApiServer;
    private RNDataServer mDataServer;
    private RNDeviceServer mDeviceServer;
    private RNPersonManagerServer mPersonServer;
    private RNCallbackServer mCallbackServer;
    private RNSkillCallbackServer mSkillCallbackServer;
    private RNIntentManagerServer mIntentManagerServer;
    private RNRobotSettingServer mRobotSettingServer;
    private RNUpdateManagerServer mRNUpdateManagerServer;
    private RNNLPApkControlSever mNLPApkControlSever;

    private MapManager mMapServer;
    private RNCallButtonManagerServer mRNCallButtonManagerServer;
    private RNOkHttpRequestServer mRNOkHttpRequestServer;
    private StartEvent mStartEvent;
    private boolean mIsSuspend;
    private Handler mHandler;
    private final Object mRNServerLock = new Object();
    private Runnable mStartRunnable = new Runnable() {
        @Override
        public void run() {
            Log.d(TAG, "rnStartRunnable run");
            OrionBase.resetCPUMemoryWarnCount();

            if (RobotApi.getInstance().isActive()) {
                mStartTime = System.currentTimeMillis();
                RNServerUtils.startRNActivity();
                getMapServer().reload();
            } else {
                Log.i(TAG, "not start RNActivity, because don't have permission");
            }
        }
    };

    // 1分钟
    private static final long RN_HEART_BEAT_TIMEOUT = 60_000L;
    // 时间偏差
    private static final long RN_HEART_BEAT_TIMEOUT_DEVIATION = 3000L;
    private static final int RN_HEART_BEAT_TIMEOUT_COUNT = 5;

    private long lastTimeMillis = 0;
    private long timeOutCount = 0;
    private boolean allowRnHeartBeat = true;

    /**
     * RN两次启动最小间隔
     */
    private long MIN_INTERVAL = 500;

    /**
     * 触发RN启动的时间
     */
    private long mStartTime = 0;

    private Runnable mTimeoutAction = new Runnable() {

        @Override
        public void run() {
            String opkVersion = "";
            String appid = "";
            OPKBeanV3 opkBeanV3 = AppManger.INSTANCE.getRPKByCharacter(ReactCharacter.Companion.getCurrentCharacter());
            if (opkBeanV3 != null) {
                opkVersion = opkBeanV3.getVersionName();
                appid = opkBeanV3.getAppid();
            }
            ReportControl.getInstance().reportMsg(new RnHeartBeatTimeoutPoint(opkVersion, appid, String.valueOf(System.currentTimeMillis() - lastTimeMillis)));
            timeOutCount++;
            Log.d(TAG, String.format("rnHeartBeat mTimeoutAction 上报 timeOutCount:%d,opkVersion:%s,appid:%s", timeOutCount, opkVersion, appid));
            if (timeOutCount >= RN_HEART_BEAT_TIMEOUT_COUNT) {
                // 超过5个周期，停止接收心跳，停止 delay action
                Log.d(TAG, "rnHeartBeat timeout stop allowRnHeartBeat");
                mHandler.removeCallbacks(mTimeoutAction);
                allowRnHeartBeat = false;
            } else {
                // 小于5个周期继续开启检测
                Log.d(TAG, "rnHeartBeat timeout restartRnHeartBeat");
                restartRnHeartBeat();
                timeOutCount = 0;
            }
        }
    };
    private boolean isUpdate;

    private RNServerManager() {
        mHandler = new Handler(Looper.getMainLooper());
    }

    public static RNServerManager getInstance() {
        if (sRNServerManager == null) {
            synchronized (RNServerManager.class) {
                if (sRNServerManager == null) {
                    sRNServerManager = new RNServerManager();
                }
            }
        }
        return sRNServerManager;
    }


    public boolean rnHeartBeat() {
        if (allowRnHeartBeat) {
            long currentTimeMillis = System.currentTimeMillis();
            if (lastTimeMillis == 0) {
                Log.d(TAG, "rnHeartBeat start delay runnable");
                // start rnHeartBeat
                lastTimeMillis = currentTimeMillis;
                restartRnHeartBeat();
            } else {
                Log.d(TAG, String.format("rnHeartBeat currentTimeMillis:%d,lastTimeMillis:%d,timegap:%d", currentTimeMillis, lastTimeMillis, currentTimeMillis - lastTimeMillis));
                if (currentTimeMillis - lastTimeMillis - RN_HEART_BEAT_TIMEOUT > RN_HEART_BEAT_TIMEOUT_DEVIATION) {
                    // 超时执行上报一次
                    mTimeoutAction.run();
                } else {
                    restartRnHeartBeat();
                }
                lastTimeMillis = currentTimeMillis;
            }
        }
        return allowRnHeartBeat;
    }

    private void restartRnHeartBeat() {
        mHandler.removeCallbacks(mTimeoutAction);
        mHandler.postDelayed(mTimeoutAction, RN_HEART_BEAT_TIMEOUT);
    }

    private void stopRnHeartBeat() {
        mHandler.removeCallbacks(mTimeoutAction);
        lastTimeMillis = 0;
        timeOutCount = 0;
        allowRnHeartBeat = true;
    }

    public void restartRNProcess() {
        Log.d(TAG, "restartRNProcess delay");
        long interval = System.currentTimeMillis() - mStartTime;
        synchronized (mRNServerLock) {
            if (interval < MIN_INTERVAL) {
                Log.d(TAG, "restartRNProcess failed, The interval is too short : " + interval);
                return;
            }

            stopRnHeartBeat();
            RNServerUtils.killRNProcess();
            mHandler.removeCallbacks(mStartRunnable);
            mHandler.postDelayed(mStartRunnable, 500);
//            if (isKill) {
//                mHandler.postDelayed(mStartRunnable, 500);
//            } else {
//                mStartRunnable.run();
//            }
        }
    }

    public void startRNProcess() {
        Log.d(TAG, "startRNProcess");
        synchronized (mRNServerLock) {
            long interval = System.currentTimeMillis() - mStartTime;
            if (interval < MIN_INTERVAL) {
                Log.d(TAG, "startRNProcess failed, The interval is too short : " + interval);
                return;
            }

            stopRnHeartBeat();
            RNServerUtils.resetAllComponents();

            mHandler.removeCallbacks(mStartRunnable);
//            mHandler.postDelayed(mStartRunnable, 500);
//            mHandler.post(mStartRunnable);
            mHandler.postDelayed(mStartRunnable, 500);
        }
    }

    public void reportRestart() {
        String opkVersion = "";
        String appid = "";
        OPKBeanV3 opkBeanV3 = AppManger.INSTANCE.getRPKByCharacter(ReactCharacter.Companion.getCurrentCharacter());
        if (opkBeanV3 != null) {
            opkVersion = opkBeanV3.getVersionName();
            appid = opkBeanV3.getAppid();
        }

        ReportControl.getInstance().reportMsg(new OpkRestartPoint(opkVersion, appid));
    }

    public void stopRNProcess() {
        Log.d(TAG, "stopRNProcess");
        stopRnHeartBeat();
        mHandler.removeCallbacks(mStartRunnable);
        RNServerUtils.killRNProcess();
        mStartTime = 0;
    }

    public void updateStartEvent(StartEvent event) {
        Log.d(TAG, "updateStartEvent $event");
        mStartEvent = event;
    }

    public StartEvent getStartEvent() {
        return mStartEvent;
    }

    public void suspend() {
        Log.d(TAG, "suspend isSuspend: " + true);
        mIsSuspend = true;
        stopRnHeartBeat();
        mHandler.removeCallbacks(mStartRunnable);
        RNServerUtils.resetAllComponents();

        if (mCallbackServer != null) {
            mCallbackServer.suspend();
        }
    }

    public void recovery() {
        Log.d(TAG, "suspend isSuspend: " + false);
        mIsSuspend = false;
        restartRnHeartBeat();
        if (mCallbackServer != null) {
            mCallbackServer.recovery();
        }
    }

    public void setSuspend(boolean isSuspend) {
        Log.d(TAG, "setSuspend isSuspend: " + isSuspend);
        mIsSuspend = isSuspend;
    }

    public boolean isSuspend() {
        return mIsSuspend;
    }

    public void setCommandCallBack(IRNCommandCallBack commandCallBack) {
        getCallbackServer().updateCallback(commandCallBack);
    }

    public void setSpeechCallBack(IRNSpeechCallBack speechCallBack) {
        getSkillCallbackServer().updateSpeechCallback(speechCallBack);
    }

    public RNApiServer getApiServer() {
        if (mApiServer == null) {
            synchronized (mRNServerLock) {
                if (mApiServer == null) {
                    mApiServer = new RNApiServer();
                }
            }
        }
        return mApiServer;
    }

    public MapManager getMapServer() {
        if (mMapServer == null) {
            synchronized (mRNServerLock) {
                if (mMapServer == null) {
                    mMapServer = new MapManager();
                }
            }
        }
        return mMapServer;
    }

    public RNCallButtonManagerServer getRNCallButtonManagerServer() {
        if (mRNCallButtonManagerServer == null) {
            synchronized (mRNServerLock) {
                if (mRNCallButtonManagerServer== null) {
                    mRNCallButtonManagerServer = new RNCallButtonManagerServer();
                }
            }
        }
        return mRNCallButtonManagerServer;
    }


    public RNOkHttpRequestServer getRNOkHttpRequestServer() {
        if (mRNOkHttpRequestServer== null) {
            synchronized (mRNServerLock) {
                if (mRNOkHttpRequestServer== null) {
                    mRNOkHttpRequestServer = new RNOkHttpRequestServer();
                }
            }
        }
        return mRNOkHttpRequestServer;
    }

    public RNSpeechApiServer getSpeechApiServer() {
        if (mSpeechApiServer == null) {
            synchronized (mRNServerLock) {
                if (mSpeechApiServer == null) {
                    mSpeechApiServer = new RNSpeechApiServer();
                }
            }
        }
        return mSpeechApiServer;
    }

    public RNComponentServer getComponentServer() {
        if (mComponentServer == null) {
            synchronized (mRNServerLock) {
                if (mComponentServer == null) {
                    mComponentServer = new RNComponentServer();
                }
            }
        }
        return mComponentServer;
    }

    public LightManager getLightServer() {
        return LightManager.getInstance();
    }

    public RNPersonManagerServer getPersonServer() {
        if (mPersonServer == null) {
            synchronized (mRNServerLock) {
                if (mPersonServer == null) {
                    mPersonServer = new RNPersonManagerServer();
                }
            }
        }
        return mPersonServer;
    }

    public RNDataServer getDataServer() {
        if (mDataServer == null) {
            synchronized (mRNServerLock) {
                if (mDataServer == null) {
                    mDataServer = new RNDataServer();
                }
            }
        }
        return mDataServer;
    }

    public RNDeviceServer getDeviceServer() {
        if (mDeviceServer == null) {
            synchronized (mRNServerLock) {
                if (mDeviceServer == null) {
                    mDeviceServer = new RNDeviceServer();
                }
            }
        }
        return mDeviceServer;
    }

    public RNCallbackServer getCallbackServer() {
        if (mCallbackServer == null) {
            synchronized (mRNServerLock) {
                if (mCallbackServer == null) {
                    mCallbackServer = new RNCallbackServer();
                }
            }
        }
        return mCallbackServer;
    }

    public RNSkillCallbackServer getSkillCallbackServer() {
        if (mSkillCallbackServer == null) {
            synchronized (mRNServerLock) {
                if (mSkillCallbackServer == null) {
                    mSkillCallbackServer = new RNSkillCallbackServer();
                }
            }
        }
        return mSkillCallbackServer;
    }

    public RNIntentManagerServer getRNIntentManagerServer() {
        if (mIntentManagerServer == null) {
            synchronized (RNIntentManagerServer.class) {
                if (mIntentManagerServer == null) {
                    mIntentManagerServer = new RNIntentManagerServer();
                }
            }
        }
        return mIntentManagerServer;
    }

    public RNRobotSettingServer getRobotSettingServer() {
        if (mRobotSettingServer == null) {
            synchronized (RNRobotSettingServer.class) {
                if (mRobotSettingServer == null) {
                    mRobotSettingServer = new RNRobotSettingServer();
                }
            }
        }
        return mRobotSettingServer;
    }

    public RNUpdateManagerServer getRNPackageManagerServer() {
        if (mRNUpdateManagerServer == null) {
            synchronized (RNUpdateManagerServer.class) {
                if (mRNUpdateManagerServer == null) {
                    mRNUpdateManagerServer = new RNUpdateManagerServer();
                }
            }
        }
        return mRNUpdateManagerServer;
    }

    public RNNLPApkControlSever getNLPApkControlServer() {
        if (mNLPApkControlSever == null) {
            synchronized (RNNLPApkControlSever.class) {
                if (mNLPApkControlSever == null) {
                    mNLPApkControlSever = new RNNLPApkControlSever();
                }
            }
        }
        return mNLPApkControlSever;
    }


    public MsgController getMsgServer() {
        return MsgController.getController();
    }

    public boolean isUpdate() {
        return isUpdate;
    }

    public void setUpdate(boolean update) {
        isUpdate = update;
    }

    public boolean isInStandByOpkMode() {
        int flag = getRobotSettingServer().getRobotInt(Definition.ROBOT_SETTING_STANDBY_OPK_MODE_SWITCH);
        return flag == 1;
    }

    /**
     * 是否在充电
     *
     * @return
     */
    public boolean isCharging() {
        boolean isCharging = RobotApi.getInstance().getChargeStatus();
        return isCharging;
    }

}