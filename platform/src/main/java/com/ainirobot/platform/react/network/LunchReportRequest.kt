package com.ainirobot.platform.react.network

import org.json.JSONObject
import retrofit2.Call
import retrofit2.http.GET
import retrofit2.http.Query

/***
 ** <AUTHOR>
 ** @email <EMAIL>
 ** @date 2019-07-18
 **/
interface LunchReportRequest {
    @GET("/api/v1/robot/launch/report")
    fun launchReport(@Query("msg_id") msg_id : String,
                     @Query("return_code") return_code : Int,
                     @Query("return_msg") return_msg: String,
                     @Query("machine_sn") machine_sn: String,
                     @Query("rn_app_id") rn_app_id: String,
                     @Query("method_name") method_name : String,
                     @Query("ota_version") ota_version : String) : Call<JSONObject>
}