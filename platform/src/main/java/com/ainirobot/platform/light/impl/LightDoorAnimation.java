package com.ainirobot.platform.light.impl;

import com.ainirobot.coreservice.client.actionbean.LedLightBean;

/**
 * Created by Orion on 2018/2/24.
 */

public class LightDoorAnimation extends LedLightBean {

    private int baseTarget;

    public LightDoorAnimation(int baseTarget, int singleTime, int rgbBase,
                              int rgbMove) {
        this.type = 10;
        this.baseTarget = baseTarget;
        this.singleTime = singleTime;
        this.rgbBase = rgbBase;
        this.rgbMove = rgbMove;
    }

    public int getBaseTarget() {
        return baseTarget;
    }

    public void setBaseTarget(int baseTarget) {
        this.baseTarget = baseTarget;
    }

    @Override
    public String toString() {
        return "LightDoorAnimation{" +
                "type=" + type +
                ", target=" + target +
                ", singleTime=" + singleTime +
                ", rgbBase=" + rgbBase +
                ", rgbMove=" + rgbMove +
                '}';
    }
}
