package com.ainirobot.platform.light;

public class ProTrayLedBean {

    private int mFloorId = 1;
    private boolean mSwitch = false;

    public ProTrayLedBean(int mFloorId, boolean mSwitch) {
        this.mFloorId = mFloorId;
        this.mSwitch = mSwitch;
    }

    public int getFloorId() {
        return mFloorId;
    }

    public void setFloorId(int mFloorId) {
        this.mFloorId = mFloorId;
    }

    public boolean isSwitch() {
        return mSwitch;
    }

    public void setSwitch(boolean mSwitch) {
        this.mSwitch = mSwitch;
    }

    @Override
    public String toString() {
        return "ProTrayLedBean{" +
                "mFloorId=" + mFloorId +
                ", mSwitch=" + mSwitch +
                '}';
    }
}
