/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.light;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.light.impl.LightAnimation;
import com.ainirobot.platform.light.impl.LightColor;
import com.ainirobot.platform.light.impl.LightDoorAnimation;
import com.ainirobot.platform.light.impl.LightMultipleColor;
import com.ainirobot.platform.light.impl.LightNavigationAnimation;
import com.ainirobot.platform.rn.ILightRegistry;
import com.google.gson.Gson;

/**
 * <AUTHOR>
 */
public class LightManager extends ILightRegistry.Stub {

    private static final String TAG = LightManager.class.getSimpleName();
    private static volatile LightManager sInstance;

    private int mReqId = 0;
    private Gson mGson;

    private LightManager() {
        init();
    }

    public void init() {
        mGson = new Gson();
    }

    public static LightManager getInstance() {
        if (sInstance == null) {
            synchronized (LightManager.class) {
                if (sInstance == null) {
                    sInstance = new LightManager();
                }
            }
        }
        return sInstance;
    }

    @Override
    public int playEffect(String lightEffect) {
        Log.d(TAG, "playEffect lightEffect: " + lightEffect);

            switch (lightEffect) {
                case LightConstants.LIGHT_EFFECT_BLUE_BREATH:
                    setBlueBreathLight();
                    break;
                case LightConstants.LIGHT_EFFECT_BLUE_LIGHT:
                    setBlueLight();
                    break;
                case LightConstants.LIGHT_EFFECT_GREEN_BREATH:
                    setGreenBreathLight();
                    break;
                case LightConstants.LIGHT_EFFECT_GREEN_LIGHT:
                    setGreenLight();
                    break;
                case LightConstants.LIGHT_EFFECT_RED_BREATH:
                    setRedBreathLight();
                    break;
                case LightConstants.LIGHT_EFFECT_RED_LIGHT:
                    setRedLight();
                    break;
                case LightConstants.LIGHT_EFFECT_BLUE_NAVI_BREATH:
                    setBlueNavigationBreathLight();
                    break;
                default:
                    break;
            }

        return 0;
    }

    public int playColor(String color) {
        if (TextUtils.isEmpty(color)) {
            return -1;
        }
        playColor(mGson.fromJson(color, LightColor.class));
        return 0;
    }

    public void playColor(LightColor color) {
     //   Log.d(TAG, "playColor color: " + color);
        if (color == null) {
            return;
        }
        color.setType(1);
        if ((color.getBaseTarget() & LightConstants.LIGHT_TARGET_FOOT) == LightConstants.LIGHT_TARGET_FOOT) {
            color.setTarget(Definition.LAMP_TARGET_FOOT);
            RobotApi.getInstance().setLedLight(mReqId, color);
        }
        if ((color.getBaseTarget() & LightConstants.LIGHT_TARGET_POWER_INSIDE)
                == LightConstants.LIGHT_TARGET_POWER_INSIDE) {
            color.setTarget(Definition.LAMP_TARGET_POWER_INSIDE);
            RobotApi.getInstance().setLedLight(mReqId, color);
        }
        if ((color.getBaseTarget() & LightConstants.LIGHT_TARGET_POWER_OUTSIDE)
                == LightConstants.LIGHT_TARGET_POWER_OUTSIDE) {
            color.setTarget(Definition.LAMP_TARGET_POWER_OUTSIDE);
            RobotApi.getInstance().setLedLight(mReqId, color);
        }
    }

    @Override
    public int playMultipleColor(String color) {
        if (TextUtils.isEmpty(color)) {
            return -1;
        }
        playMultipleColor(mGson.fromJson(color, LightMultipleColor.class));
        return 0;
    }

    public void playMultipleColor(LightMultipleColor color) {
        Log.d(TAG, "playMultipleColor color: " + color);
        if (color == null) {
            return;
        }
        color.setTarget(Definition.LAMP_TARGET_FOOT);
        color.setType(6);
        RobotApi.getInstance().setLedLight(mReqId, color);
    }

    @Override
    public int playAnimation(String animation) {
        if (TextUtils.isEmpty(animation)) {
            return -1;
        }
        playAnimation(mGson.fromJson(animation, LightAnimation.class));
        return 0;
    }

    public void playAnimation(LightAnimation animation) {
        Log.d(TAG, "playAnimation animation: " + animation);
        if (animation == null) {
            return;
        }
        animation.setTarget(Definition.LAMP_TARGET_FOOT);
        animation.setType(2);
        RobotApi.getInstance().setLedLight(mReqId, animation);
    }

    @Override
    public int playNavigationAnimation(String animation) {
        if (TextUtils.isEmpty(animation)) {
            return -1;
        }
        playNavigationAnimation(mGson.fromJson(animation, LightNavigationAnimation.class));
        return 0;
    }

    public void playNavigationAnimation(LightNavigationAnimation animation) {
        Log.d(TAG, "playNavigationAnimation animation: " + animation);
        if (animation == null) {
            return;
        }
        animation.setTarget(Definition.LAMP_TARGET_FOOT);
        animation.setType(7);
        RobotApi.getInstance().setLedLight(mReqId, animation);
    }

    @Override
    public int playDoorAnimation(String animation) {
        if (TextUtils.isEmpty(animation)) {
            return -1;
        }
        playDoorAnimation(mGson.fromJson(animation, LightDoorAnimation.class));
        return 0;
    }

    public void playDoorAnimation(LightDoorAnimation animation) {
        Log.d(TAG, "playDoorAnimation animation: " + animation);
        if (animation == null) {
            return;
        }
        animation.setType(10);
        if ((animation.getBaseTarget() & LightConstants.LIGHT_TARGET_FOOT) == LightConstants.LIGHT_TARGET_FOOT) {
            animation.setTarget(Definition.LAMP_TARGET_FOOT);
            RobotApi.getInstance().setLedLight(mReqId, animation);
        }
        if ((animation.getBaseTarget() & LightConstants.LIGHT_TARGET_POWER_INSIDE)
                == LightConstants.LIGHT_TARGET_POWER_INSIDE) {
            animation.setTarget(Definition.LAMP_TARGET_POWER_INSIDE);
            RobotApi.getInstance().setLedLight(mReqId, animation);
        }
        if ((animation.getBaseTarget() & LightConstants.LIGHT_TARGET_POWER_OUTSIDE)
                == LightConstants.LIGHT_TARGET_POWER_OUTSIDE) {
            animation.setTarget(Definition.LAMP_TARGET_POWER_OUTSIDE);
            RobotApi.getInstance().setLedLight(mReqId, animation);
        }
    }

    /**
     * set greeen light effect
     */
    private void setGreenLight() {
        playColor(new LightColor(LightConstants.LIGHT_TARGET_ALL,
                Integer.parseInt("37E737", 16)));
    }

    /**
     * set blue light effect
     */
    private void setBlueLight() {
        playColor(new LightColor(LightConstants.LIGHT_TARGET_ALL,
                Integer.parseInt("5493FF", 16)));
    }

    /**
     * set red light effect
     */
    private void setRedLight() {
        playColor(new LightColor(LightConstants.LIGHT_TARGET_ALL, Integer.parseInt("D31812", 16)));
    }

    /**
     * set red breath light effect
     */
    private void setRedBreathLight() {
        playAnimation(new LightAnimation(Integer.parseInt("D32812", 16),
                Integer.parseInt("991E0E", 16), 0, 0, -1,
                1300, Integer.parseInt("D32812", 16)));
    }

    /**
     * set blue breath light effect
     */
    private void setBlueBreathLight() {
        playAnimation(new LightAnimation(Integer.parseInt("5493FF", 16),
                Integer.parseInt("111d33", 16), 0, 0, -1,
                1300, Integer.parseInt("5493FF", 16)));
    }

    /**
     * set greeen breath light effect
     */
    private void setGreenBreathLight() {
        playAnimation(new LightAnimation(Integer.parseInt("37E737", 16),
                Integer.parseInt("061A06", 16), 0, 0, -1,
                1300, Integer.parseInt("37E737", 16)));
    }

    /**
     * set navigation breath light effect
     */
    private void setBlueNavigationBreathLight() {
        playNavigationAnimation(new LightNavigationAnimation(Integer.parseInt("5493FF", 16),
                Integer.parseInt("111d33", 16), 1300, 0));
    }

    //按场景控制胸口灯，锁骨灯和底盘灯
    @Override
    public int setSceneLedEffect(String effect) {
        if (TextUtils.isEmpty(effect)) {
            return -1;
        }
        int ledEffect = 0;
        if (ProductInfo.isSlimProduct()) {
            switch (effect) {
                case LightConstants.LIGHT_EFFECT_TURN_LEFT:
                    if (ProductInfo.isSlimDoor()) {
                        ledEffect = Definition.LED_EFFECT_SLIM_CLAVICLE_TURNLEFT;
                        RobotApi.getInstance().setBottomLedEffect(mReqId, ledEffect, null);
                    } else {
                        ledEffect = Definition.TRAY_LED_EFFECT_BLUEMETEORREVERSE;
                        RobotApi.getInstance().setTrayLedEffect(mReqId, ledEffect, null);
                        RobotApi.getInstance().setBottomLedEffect(mReqId, Definition.LED_EFFECT_BLUENORMAL, null);
                    }
                    break;
                case LightConstants.LIGHT_EFFECT_TURN_RIGHT:
                    if (ProductInfo.isSlimDoor()) {
                        ledEffect = Definition.LED_EFFECT_SLIM_CLAVICLE_TURNRIGHT;
                        RobotApi.getInstance().setBottomLedEffect(mReqId, ledEffect, null);
                    } else {
                        ledEffect = Definition.TRAY_LED_EFFECT_BLUEMETEORFORWARD;
                        RobotApi.getInstance().setTrayLedEffect(mReqId, ledEffect, null);
                        RobotApi.getInstance().setBottomLedEffect(mReqId, Definition.LED_EFFECT_BLUENORMAL, null);
                    }
                    break;
                case LightConstants.LIGHT_EFFECT_BLUE_LIGHT:
                    ledEffect = Definition.LED_EFFECT_BLUENORMAL;
                    if (!ProductInfo.isSlimDoor()) {
                        RobotApi.getInstance().setTrayLedEffect(mReqId, ledEffect, null);
                    }
                    RobotApi.getInstance().setBottomLedEffect(mReqId, ledEffect, null);
                    break;
                case LightConstants.LIGHT_EFFECT_BLUE_NAVI_BREATH:
                    ledEffect = Definition.LED_EFFECT_BLUEBREATH;
                    if (!ProductInfo.isSlimDoor()) {
                        RobotApi.getInstance().setTrayLedEffect(mReqId, ledEffect, null);
                    }
                    RobotApi.getInstance().setBottomLedEffect(mReqId, ledEffect, null);
                    break;
            }
        } else {
            ledEffect = this.getLedEffectByType(effect);
            if(ledEffect > 0){
                RobotApi.getInstance().setClavicleLedEffect(mReqId, ledEffect, null);
                RobotApi.getInstance().setBottomLedEffect(mReqId, ledEffect, null);
            }
        }
        return 0;
    }

    /**
     * 根据类型返回锁骨灯对应值
     * @param type
     * @return
     */
    private int getLedEffectByType(String type) {
        int result = -1;
        switch (type) {
            case LightConstants.LIGHT_EFFECT_BLUE_BREATH:
                result = Definition.LED_EFFECT_BLUEBREATH;
                break;
            case LightConstants.LIGHT_EFFECT_BLUE_LIGHT:
                result = Definition.LED_EFFECT_BLUENORMAL;
                break;
            case LightConstants.LIGHT_EFFECT_GREEN_BREATH:
                result = Definition.LED_EFFECT_GREENBREATH;
                break;
            case LightConstants.LIGHT_EFFECT_GREEN_LIGHT:
                result = Definition.LED_EFFECT_GREENNORMAL;
                break;
            case LightConstants.LIGHT_EFFECT_RED_BREATH:
                result = Definition.LED_EFFECT_REDFLASH;
                break;
            case LightConstants.LIGHT_EFFECT_RED_LIGHT:
                result = Definition.LED_EFFECT_REDNORMAL;
                break;
            case LightConstants.LIGHT_EFFECT_YELLOW_BREATH:
                result = Definition.LED_EFFECT_YELLOWBREATH;
                break;
            case LightConstants.LIGHT_EFFECT_YELLOW_LIGHT:
                result = Definition.LED_EFFECT_YELLOWNORMAL;
                break;
            case LightConstants.LIGHT_EFFECT_YELLOW_FLASH:
                result = Definition.LED_EFFECT_YELLOWFLASH;
                break;
            case LightConstants.LIGHT_EFFECT_TURN_LEFT:
                result = Definition.LED_EFFECT_TURNLEFT;
                break;
            case LightConstants.LIGHT_EFFECT_TURN_RIGHT:
                result = Definition.LED_EFFECT_TURNRIGHT;
                break;
            case LightConstants.LIGHT_EFFECT_ALL_OFF:
                result = Definition.LED_EFFECT_ALLOFF;
                break;
            default:
                break;
        }
        return result;
    }

}
