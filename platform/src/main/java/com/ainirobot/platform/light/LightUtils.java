package com.ainirobot.platform.light;/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.LedLightBean;
import com.ainirobot.platform.light.impl.LightMultipleColor;

import org.json.JSONException;
import org.json.JSONObject;

public class LightUtils {

    public static String getLightColorParam(int target, int color) {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.JSON_LAMP_TYPE, Definition.LAMP_TYPE_COLOR);
            params.put(Definition.JSON_LAMP_TARGET, target);
            params.put(Definition.JSON_LAMP_COLOR_RGB_VALUE, color);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return params.toString();
    }

    public static String getLightAnimationParam(int target, int startColor, int endColor,
                                          int startTime, int endTime, int loopTimes, int duration,
                                          int finalColor) {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.JSON_LAMP_TYPE, Definition.LAMP_TYPE_BREATH);
            params.put(Definition.JSON_LAMP_TARGET, target);
            params.put(Definition.JSON_LAMP_RGB_START, startColor);
            params.put(Definition.JSON_LAMP_RGB_END, endColor);
            params.put(Definition.JSON_LAMP_START_TIME, startTime);
            params.put(Definition.JSON_LAMP_END_TIME, endTime);
            params.put(Definition.JSON_LAMP_REPEAT, loopTimes);
            params.put(Definition.JSON_LAMP_ON_TIME, duration);
            params.put(Definition.JSON_LAMP_RGB_FREEZE, finalColor);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return params.toString();
    }

    public static String getLightNavigationAnimationParam(int target, int startColor, int endColor,
                                                    int startTime) {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.JSON_LAMP_TYPE, Definition.LAMP_TYPE_NAVIGATION);
            params.put(Definition.JSON_LAMP_TARGET, target);
            params.put(Definition.JSON_LAMP_RGB_START, startColor);
            params.put(Definition.JSON_LAMP_RGB_END, endColor);
            params.put(Definition.JSON_LAMP_START_TIME, startTime);
            params.put(Definition.JSON_LAMP_END_TIME, 0);
            params.put(Definition.JSON_LAMP_REPEAT, -1);
            params.put(Definition.JSON_LAMP_ON_TIME, 0);
            params.put(Definition.JSON_LAMP_RGB_FREEZE, 0);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return params.toString();
    }

    /**
     * 仅针对KTV豹小递生效的灯效控制接口
     */
    public static String getDoorLightAnimationParam(int target, int startColor, int endColor,
                                                    int startTime, int endTime, int loopTimes, int duration,
                                                    int finalColor, int singleTime, int rgbBase, int rgbMove, int rgbMiddle) {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.JSON_LAMP_TYPE, Definition.LAMP_TYPE_DOOR);
            params.put(Definition.JSON_LAMP_TARGET, target);
            params.put(Definition.JSON_LAMP_RGB_START, startColor);
            params.put(Definition.JSON_LAMP_RGB_END, endColor);
            params.put(Definition.JSON_LAMP_START_TIME, startTime);
            params.put(Definition.JSON_LAMP_END_TIME, endTime);
            params.put(Definition.JSON_LAMP_REPEAT, loopTimes);
            params.put(Definition.JSON_LAMP_ON_TIME, duration);
            params.put(Definition.JSON_LAMP_RGB_FREEZE, finalColor);
            params.put(Definition.JSON_LAMP_SIGLE_TIME, singleTime);
            params.put(Definition.JSON_LAMP_RGB_BASE, rgbBase);
            params.put(Definition.JSON_LAMP_RGB_MOVE, rgbMove);
            params.put(Definition.JSON_LAMP_RGB_MIDDLE, rgbMiddle);

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return params.toString();
    }

    public static LedLightBean getLedLightBean(int target, LightMultipleColor customizeColor) {
        LedLightBean bean = new LedLightBean();
        bean.setTarget(target);
        bean.setRgbSet(customizeColor.getRgbSet());
        bean.setType(customizeColor.getType());
        return bean;
    }

}
