package com.ainirobot.platform.light.impl;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.LedLightBean;

/**
 * Created by Orion on 2018/2/24.
 */

public class LightAnimation extends LedLightBean {

    public LightAnimation(int rgbStart, int rgbEnd,
                       int startTime, int endTime, int onTime,
                       int repeat, int rgbFreeze) {
        this.type = 2;
        this.target = Definition.LAMP_TARGET_FOOT;
        this.rgbStart = rgbStart;
        this.rgbEnd = rgbEnd;
        this.startTime = startTime;
        this.endTime = endTime;
        this.onTime = onTime;
        this.repeat = repeat;
        this.rgbFreeze = rgbFreeze;
    }

    @Override
    public String toString() {
        return "LightAnimation{" +
                "type=" + type +
                ", target=" + target +
                ", rgbStart=" + rgbStart +
                ", rgbEnd=" + rgbEnd +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", onTime=" + onTime +
                ", repeat=" + repeat +
                ", rgbFreeze=" + rgbFreeze +
                '}';
    }
}
