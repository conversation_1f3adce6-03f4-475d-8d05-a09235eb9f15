/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.light;

/**
 * Created by Orion on 2019/07/27.
 */

public class LightConstants {

    /**
     * Light target type
     */
    public static final int LIGHT_TARGET_FOOT = 1;
    public static final int LIGHT_TARGET_POWER_INSIDE = 2;
    public static final int LIGHT_TARGET_POWER_OUTSIDE = 4;
    public static final int LIGHT_TARGET_ALL = LIGHT_TARGET_FOOT | LIGHT_TARGET_POWER_INSIDE
            | LIGHT_TARGET_POWER_OUTSIDE;

    /**
     * Light effect type
     */
    /**
     * 绿色常量
     */
    public static final String LIGHT_EFFECT_GREEN_LIGHT = "light_effect_green_light";
    /**
     * 绿色呼吸
     */
    public static final String LIGHT_EFFECT_GREEN_BREATH = "light_effect_green_breath";
    /**
     * 蓝色常量
     */
    public static final String LIGHT_EFFECT_BLUE_LIGHT = "light_effect_blue_light";
    /**
     * 蓝色呼吸
     */
    public static final String LIGHT_EFFECT_BLUE_BREATH = "light_effect_blue_breath";
    /**
     * 红色常量
     */
    public static final String LIGHT_EFFECT_RED_LIGHT = "light_effect_red_light";
    /**
     * 红色呼吸
     */
    public static final String LIGHT_EFFECT_RED_BREATH = "light_effect_red_breath";
    /**
     * 蓝色导航
     */
    public static final String LIGHT_EFFECT_BLUE_NAVI_BREATH = "light_effect_navi_breath";
    /**
     * 黄色呼吸
     */
    public static final String LIGHT_EFFECT_YELLOW_BREATH = "light_effect_yellow_breath";
    /**
     * 黄色常亮
     */
    public static final String LIGHT_EFFECT_YELLOW_LIGHT = "light_effect_yellow_light";
    /**
     * 黄色闪烁
     */
    public static final String LIGHT_EFFECT_YELLOW_FLASH = "light_effect_yellow_flash";
    /**
     * 左转
     */
    public static final String LIGHT_EFFECT_TURN_LEFT = "light_effect_turn_left";
    /**
     * 右转
     */
    public static final String LIGHT_EFFECT_TURN_RIGHT = "light_effect_turn_right";
    /**
     * 关闭
     */
    public static final String LIGHT_EFFECT_ALL_OFF = "light_effect_all_off";
}
