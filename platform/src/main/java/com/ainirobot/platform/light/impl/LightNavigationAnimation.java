package com.ainirobot.platform.light.impl;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.LedLightBean;

/**
 * Created by Orion on 2018/2/24.
 */

public class LightNavigationAnimation extends LedLightBean {

    public LightNavigationAnimation(int rgbStart, int rgbEnd,
                          int startTime, int rgbValue) {
        this.type = 7;
        this.target = Definition.LAMP_TARGET_FOOT;
        this.rgbStart = rgbStart;
        this.rgbEnd = rgbEnd;
        this.startTime = startTime;
        this.rgbValue = rgbValue;
    }
}
