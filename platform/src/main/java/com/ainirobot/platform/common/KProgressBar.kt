package com.ainirobot.platform.common

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View

class KProgressBar : View {

    private val pathProgressBg = Path() //进度条背景路径
    private val pathProgressFg = Path() //进度条前景路径

    private val paintProgress = Paint(Paint.ANTI_ALIAS_FLAG) //绘制进度条的画笔

    private val pathMeasure = PathMeasure()
    var colorProgressBg: Int = 0x0F000000.toInt() //进度条背景
    var colorProgressFg: Int = 0xAF61D800.toInt() //进度条前景

    var progress = 0.0f //进度条进度
        set(value) {
            field = if (value < 0) 0.0f else if (value > 1.0f) 1.0f else value
            invalidate()
        }

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attributeSet: AttributeSet?) : this(context, attributeSet, 0)

    constructor(context: Context, attributeSet: AttributeSet?, defStyleAttr: Int) : super(context, attributeSet, defStyleAttr) {
        change()
    }

    fun change(): KProgressBar {
        paintProgress.strokeCap = Paint.Cap.ROUND //设置线头为圆角
        paintProgress.style = Paint.Style.STROKE //设置绘制样式为线条
        paintProgress.strokeJoin = Paint.Join.ROUND //设置拐角为圆角
        return this
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        val height = h.toFloat()
        paintProgress.strokeWidth = height
        //进度条绘制在控件中央,宽度为控件宽度(progressHeight/2是为了显示出左右两边的圆角)
        pathProgressBg.moveTo(height / 2, h / 2f)
        pathProgressBg.lineTo(w - height / 2, h / 2f)
        //将进度条路径设置给PathMeasure
        pathMeasure.setPath(pathProgressBg, false)
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        //绘制进度条
        drawProgress(canvas)
    }

    private fun drawProgress(canvas: Canvas) {
        pathProgressFg.reset()
        paintProgress.color = colorProgressBg
        //绘制进度背景
        canvas.drawPath(pathProgressBg, paintProgress)
        //计算进度条的进度
        val stop: Float = pathMeasure.length * progress
        //得到与进度对应的路径
        pathMeasure.getSegment(0.0f, stop, pathProgressFg, true)
        paintProgress.color = colorProgressFg
        //绘制进度
        canvas.drawPath(pathProgressFg, paintProgress)
    }
}