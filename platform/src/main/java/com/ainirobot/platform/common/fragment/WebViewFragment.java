package com.ainirobot.platform.common.fragment;

import static com.facebook.react.bridge.UiThreadUtil.runOnUiThread;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.res.AssetManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.GeolocationPermissions;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.ainirobot.coreservice.client.ApiListener;
import com.ainirobot.coreservice.client.speech.SkillApi;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.R;
import com.ainirobot.platform.common.KProgressBar;
import com.ainirobot.platform.common.WebViewPool;
import com.ainirobot.platform.nlp.state.NlpStateManager;
import com.ainirobot.platform.speech.SpeechApi;
import com.google.gson.Gson;

import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

public class WebViewFragment extends Fragment {
    private static final String TAG = "WebViewFragment";
    private static final long RECONNECT_INTERVAL = 5000;
    private final SkillApi skillApi = new SkillApi();
    private Timer timer;
    private static final String ARG_URL = "url";
    private static final String ARG_SCALE = "scale";

    private static final String TEST_SPECIAL_DOMAIN = "test-agentpoi.orionstar.com";
    private static final String PRO_SPECIAL_DOMAIN = "agentpoi.orionstar.com";

    private WebView webView;
    private View progressBar;
    private ImageView backBtn, forwardBtn, refreshBtn, closeBtn;
    private WebViewCallback callback;
    private WebViewPool webViewPool;
    private boolean isErrorPage = false;
    private long pageLoadStartTime = 0;
    private float currentAppliedScale = 1.0f; 

    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    private Context context;

    public static WebViewFragment newInstance(String url, float scale) {
        WebViewFragment fragment = new WebViewFragment();
        Bundle args = new Bundle();
        args.putString(ARG_URL, url);
        args.putFloat(ARG_SCALE, scale);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        this.context = context;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        connectSpeechServer();
        Log.d(TAG, "onCreate");
    }


    private void connectSpeechServer() {
        ApiListener listener = new ApiListener() {
            @Override
            public void handleApiDisabled() {

            }

            @Override
            public void handleApiConnected() {
                SpeechApi.getInstance().setSpeechApi(skillApi);
                NlpStateManager.Companion.get().resetNlpLauncher();
            }

            @Override
            public void handleApiDisconnected() {
                reconnect();
            }
        };
        skillApi.addApiEventListener(listener);
        skillApi.connectApi(BaseApplication.getContext());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView");
        View view = inflater.inflate(R.layout.fragment_webview, container, false);
        initViews(view);
        setupWebView();
        return view;
    }


    private void reconnect() {
        cancelTimer();
        timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.e(TAG, "Reconnect to speech");
                if (skillApi.isApiConnectedService()) {
                    Log.e(TAG, "Already connected to speech");
                    cancelTimer();
                    return;
                }
                skillApi.connectApi(BaseApplication.getContext());
            }
        }, 0, RECONNECT_INTERVAL);
    }

    private void initViews(View view) {
        Log.d(TAG, "initViews");
        webView = view.findViewById(R.id.web_view);
        progressBar = view.findViewById(R.id.progress_bar);
        backBtn = view.findViewById(R.id.back_btn);
        forwardBtn = view.findViewById(R.id.forward_btn);
        refreshBtn = view.findViewById(R.id.refresh_btn);
        closeBtn = view.findViewById(R.id.close_btn);

        setupClickListeners();
    }

    private void cancelTimer() {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
    }

    private void setupClickListeners() {
        Log.d(TAG, "Setting up click listeners");

        closeBtn.setOnClickListener(v -> {
            Log.d(TAG, "Close button clicked");
            if (callback != null) {
                Log.d(TAG, "callback");
                callback.onClose(true);
            }
        });

        backBtn.setOnClickListener(v -> {
            Log.d(TAG, "Back button clicked, canGoBack: " + (webView != null && webView.canGoBack()));
            if (webView != null && webView.canGoBack()) {
                webView.goBack();
            }
        });

        forwardBtn.setOnClickListener(v -> {
            Log.d(TAG, "Forward button clicked, canGoForward: " + (webView != null && webView.canGoForward()));
            if (webView != null && webView.canGoForward()) {
                webView.goForward();
            }
        });

        refreshBtn.setOnClickListener(v -> {
            Log.d(TAG, "Refresh button clicked");
            if (webView != null) {
                webView.reload();
            }
        });
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void setupWebView() {
        try {
            Log.d(TAG, "Setting up WebView");
            webViewPool = WebViewPool.getInstance(requireContext());

            WebSettings settings = webView.getSettings();
            settings.setJavaScriptEnabled(true);
            settings.setJavaScriptCanOpenWindowsAutomatically(true);
            settings.setDomStorageEnabled(true);
            settings.setAllowFileAccess(true);
            settings.setAllowContentAccess(true);
            settings.setUseWideViewPort(true);
            settings.setLoadWithOverviewMode(true);
            settings.setGeolocationEnabled(true);
            settings.setDatabaseEnabled(true);
            settings.setSupportZoom(true);
            settings.setDisplayZoomControls(false);
            settings.setCacheMode(WebSettings.LOAD_DEFAULT);
            settings.setAppCacheEnabled(true);
            settings.setAppCachePath(requireContext().getCacheDir().getAbsolutePath());
            settings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NORMAL);
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
            settings.setLoadsImagesAutomatically(true);
            settings.setUserAgentString("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            webView.setWebViewClient(createWebViewClient());
            webView.setWebChromeClient(createWebChromeClient());
            webView.removeJavascriptInterface("Android");
            webView.addJavascriptInterface(new WebViewInterface(), "Android");
            String url = getArguments().getString(ARG_URL);
            float currentScale = getArguments().getFloat(ARG_SCALE);
            if (url != null) {
                Log.d(TAG, "Loading initial URL: " + url + " with scale: " + currentScale);
                java.net.URL urlObj = new java.net.URL(url);
                if (!TEST_SPECIAL_DOMAIN.equals(urlObj.getHost()) || !PRO_SPECIAL_DOMAIN.equals(urlObj.getHost())) {
                    // 如果currentScale是30，表示30%缩放，直接使用
                    webView.setInitialScale((int)currentScale);
                }
                loadUrl(url, currentScale);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting up WebView: " + e.getMessage());
            if (callback != null) {
                callback.onError("Failed to setup WebView: " + e.getMessage());
            }
        }
    }


    private WebViewClient createWebViewClient() {
        return new WebViewClient() {
            private boolean isRedirect = false;

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                if (url.startsWith("mailto:")) {
                    return true;
                }
                return false;
            }

            @Override
            public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                Log.d(TAG, "onPageStarted: " + url);
                super.onPageStarted(view, url, favicon);
                // 记录开始时间
                isErrorPage = false;
                isRedirect = false;
                pageLoadStartTime = System.currentTimeMillis();
                mainHandler.post(() -> {
                    if (progressBar != null) {
                        progressBar.setVisibility(View.VISIBLE);
                    }
                });
                if (callback != null) {
                    callback.onPageStarted(url);
                }
            }


            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                if (isRedirect || url.startsWith("file:///android_asset/")) {
                    return;
                }
                long loadEndTime = System.currentTimeMillis();
                long loadDuration = loadEndTime - pageLoadStartTime;
                Log.d(TAG, "onPageFinished: " + url + ", loadTime: " + loadDuration + "ms"+", isRedirect: " + isRedirect);
                mainHandler.post(() -> {
                    if (progressBar != null) {
                        progressBar.setVisibility(View.GONE);
                    }
                });
                try {
                    java.net.URL urlObj = new java.net.URL(url);
                    if (!TEST_SPECIAL_DOMAIN.equals(urlObj.getHost()) || !PRO_SPECIAL_DOMAIN.equals(urlObj.getHost())) {
                        // 获取传入的缩放比例
                        float currentScale = getArguments().getFloat(ARG_SCALE);
                        // 将百分比转换为小数形式（30% -> 0.3）
                        float viewportScale = currentScale / 100.0f;
                        // 注入meta标签，使用转换后的缩放比例
                        view.evaluateJavascript(
                                "var meta = document.querySelector('meta[name=\"viewport\"]');" +
                                        "if (!meta) {" +
                                        "  meta = document.createElement('meta');" +
                                        "  meta.name = 'viewport';" +
                                        "  document.head.appendChild(meta);" +
                                        "}" +
                                        "meta.content = 'width=device-width, initial-scale=" + viewportScale + "';",
                                null
                        );
                    }
                } catch (MalformedURLException e) {
                    throw new RuntimeException(e);
                }
                String jsCode = readJavaScriptFile(context, "webviewJs_old.js");
                view.evaluateJavascript(jsCode, result -> {
                    Log.d(TAG, "evaluateJavascript result: " + result);
                });
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                Log.d(TAG, "shouldOverrideUrlLoading！");
                // 1. 空安全检查
                if (request == null) {
                    Log.w(TAG, "shouldOverrideUrlLoading, delegate to super");
                    return super.shouldOverrideUrlLoading(view, request);
                }
                // 2. 获取 Uri 并检查
                Uri uri = request.getUrl();
                if (uri == null) {
                    Log.w(TAG, "shouldOverrideUrlLoading=====Null Uri in WebResourceRequest");
                    return super.shouldOverrideUrlLoading(view, request);
                }
                // 3. 转换为字符串并记录日志
                String url = uri.toString();
                Log.d(TAG, "shouldOverrideUrlLoading: " + url);

                // 4. 协议过滤
                if (!url.startsWith("http:") && !url.startsWith("https:")) {
                    Log.d(TAG, "shouldOverrideUrlLoading=====Non-HTTP(s) protocol, delegate to system");
                    return super.shouldOverrideUrlLoading(view, request);
                }

                // 5. 处理重定向逻辑
                isRedirect = true;
                if (view != null) {
                    view.loadUrl(url);
                }
                return true;
            }


            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                Log.d(TAG, "onReceivedError: " + request.getUrl() + ", " + error.getErrorCode());
                if (request.isForMainFrame()) {
                    isErrorPage = true;
                    mainHandler.post(() -> {
                        if (progressBar != null) {
                            progressBar.setVisibility(View.VISIBLE);
                        }
                    });
                    // 根据系统语言选择不同的错误页面
                    String language = Locale.getDefault().toString();
                    if (language.startsWith("zh_CN")) {
                        view.loadUrl("file:///android_asset/error_domestic.html");
                    } else {
                        view.loadUrl("file:///android_asset/error_oversea.html");
                    }
                }
            }
        };
    }

    private WebChromeClient createWebChromeClient() {
        return new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                if (progressBar instanceof KProgressBar) {
                    mainHandler.post(() -> {
                        ((KProgressBar) progressBar).setProgress(newProgress);
                    });
                }
            }

            @Override
            public void onGeolocationPermissionsShowPrompt(String origin, GeolocationPermissions.Callback callback) {
                callback.invoke(origin, true, false);
            }
        };
    }

    public void goBack() {
        runOnUiThread(() -> {
            if (webView != null && webView.canGoBack()) {
                webView.goBack();
            }
        });
    }

    public void goForward() {
        runOnUiThread(() -> {
            if (webView != null && webView.canGoForward()) {
                webView.goForward();
            }
        });
    }

    public void reload() {
        runOnUiThread(() -> {
            if (webView != null) {
                webView.reload();
            }
        });
    }

    public void loadUrl(String url,float scale) {
        if (webView != null) {
            Log.d(TAG, "Loading loadUrl URL: " + url);
            try {
                mainHandler.post(() -> {
                    // 显示进度条
                    if (progressBar != null) {
                        progressBar.setVisibility(View.VISIBLE);
                    }
                });

                // 更新参数中的URL
                Bundle args = getArguments();
                if (args != null) {
                    args.putString(ARG_URL, url);
                    args.putFloat(ARG_SCALE, scale);
                }

                // 重置状态
                isErrorPage = false;
                pageLoadStartTime = System.currentTimeMillis();
                // 加载新URL
                webView.loadUrl(url);
            } catch (Exception e) {
                Log.e(TAG, "Error loading URL: " + e.getMessage());
                if (callback != null) {
                    callback.onError("Failed to load URL: " + e.getMessage());
                }
            }
        } else {
            Log.e(TAG, "WebView is null when trying to load URL: " + url);
        }
    }

    /**
     * 模拟点击网页中的元素
     * @param index 要点击的元素索引
     */
    public void simulateClick(int index) {
        if (webView != null) {
            try {
                Log.d(TAG, "simulateClick: index=" + index);
                // 准备点击数据
                Map<String, Object> data = new HashMap<>();
                data.put("type", "click");
                data.put("index", index);
                String json = new Gson().toJson(data);

                // 执行JavaScript点击操作
                webView.evaluateJavascript("javascript:receiveMessage('" + json + "')", null);
            } catch (Exception e) {
                Log.e(TAG, "Error simulating click: " + e.getMessage(), e);
                if (callback != null) {
                    callback.onError("Failed to simulate click: " + e.getMessage());
                }
            }
        } else {
            Log.e(TAG, "WebView is null when trying to simulate click");
        }
    }
    private String readJavaScriptFile(Context context, String fileName) {
        try {
            AssetManager assetManager = context.getAssets();
            InputStream input = assetManager.open(fileName);
            int size = input.available();
            byte[] buffer = new byte[size];
            input.read(buffer);
            input.close();
            return new String(buffer, "UTF-8");
        } catch (IOException e) {
            Log.e(TAG, "Error reading JavaScript file: " + e.getMessage(), e);
            return "";
        }
    }

    private class WebViewInterface {
        @JavascriptInterface
        public void onMessage(String message) {
            mainHandler.post(() -> {
                Log.d(TAG, "onWebViewMessage: " + message);
                if (callback != null) {
                    if (message.startsWith("dom:")) {
                        if (isErrorPage) {
                            callback.onMessage("webview_dom", "<Page Error, Content is Null>");
                        } else {
                            callback.onMessage("webview_dom", message.substring(4));
                        }
                    }
                    else if (message.startsWith("anchor:")) {
                        String textContent = message.substring(7)
                                .replaceAll("[\\n\\r]", " ")
                                .replaceAll("\\s+", " ")
                                .trim();
                        callback.onMessage("anchor", textContent);
                    }
                }
            });
        }

        @JavascriptInterface
        public void onExecuteFinish() {
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onExecuteFinish();
                }
            });
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume");
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "onPause");
    }

    @Override
    public void onDestroyView() {
        Log.d(TAG, "onDestroyView");
        if (webView != null) {
            webView.stopLoading();
            webView.clearHistory();
            ((ViewGroup) webView.getParent()).removeView(webView);
            webView.destroy();
            webView = null;
        }
        callback = null;
        super.onDestroyView();
    }

    public void setCallback(WebViewCallback callback) {
        this.callback = callback;
    }

    public interface WebViewCallback {
        void onPageStarted(String url);
        void onPageFinished(String url);
        void onClose(boolean updatePage);
        void onMessage(String tag, String message);
        void onError(String error);
        void onExecuteFinish();
    }
}