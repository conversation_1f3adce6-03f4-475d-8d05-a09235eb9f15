package com.ainirobot.platform.common;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;
import android.webkit.WebSettings;
import android.webkit.WebView;
import java.util.LinkedList;
import java.util.Queue;

public class WebViewPool {
    private static final String TAG = "WebViewPool";
    private static WebViewPool instance;
    private final Context appContext;
    private final Queue<WebView> webViewPool;
    private static final int POOL_SIZE = 3;

    private WebViewPool(Context context) {
        this.appContext = context.getApplicationContext();
        this.webViewPool = new LinkedList<>();
        prewarmPool();
    }

    public static WebViewPool getInstance(Context context) {
        if (instance == null) {
            synchronized (WebViewPool.class) {
                if (instance == null) {
                    instance = new WebViewPool(context);
                }
            }
        }
        return instance;
    }

    public void prewarmPool() {
        while (webViewPool.size() < POOL_SIZE) {
            webViewPool.offer(createWebView());
        }
    }

    public WebView obtainWebView() {
        WebView webView = webViewPool.poll();
        if (webView == null) {
            webView = createWebView();
        }
        return webView;
    }

    public void recycleWebView(WebView webView) {
        if (webView != null) {
            resetWebView(webView);
            if (webViewPool.size() < POOL_SIZE) {
                webViewPool.offer(webView);
            } else {
                destroyWebView(webView);
            }
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private WebView createWebView() {
        WebView webView = new WebView(appContext);

        WebSettings settings = webView.getSettings();
        settings.setJavaScriptEnabled(true);
        settings.setJavaScriptCanOpenWindowsAutomatically(true);
        settings.setDomStorageEnabled(true);
        settings.setAllowFileAccess(true);
        settings.setAllowContentAccess(true);
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);
        settings.setGeolocationEnabled(true);
        settings.setDatabaseEnabled(true);
        settings.setSupportZoom(true);
        settings.setDisplayZoomControls(false);
        settings.setCacheMode(WebSettings.LOAD_DEFAULT);
        settings.setAppCacheEnabled(true);
        settings.setAppCachePath(appContext.getCacheDir().getAbsolutePath());
        settings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NORMAL);
        settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        settings.setLoadsImagesAutomatically(true);
        settings.setUserAgentString("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

        webView.setLayerType(WebView.LAYER_TYPE_HARDWARE, null);

        return webView;
    }

    private void resetWebView(WebView webView) {
        try {
            webView.stopLoading();
            webView.clearHistory();
            webView.clearCache(false);
            webView.clearFormData();
            webView.loadUrl("about:blank");
            webView.removeJavascriptInterface("Android");
        } catch (Exception e) {
            Log.e(TAG, "Error resetting WebView: " + e.getMessage());
        }
    }

    private void destroyWebView(WebView webView) {
        try {
            webView.stopLoading();
            webView.clearHistory();
            webView.clearCache(true);
            webView.loadUrl("about:blank");
            webView.removeAllViews();
            webView.destroyDrawingCache();
            webView.destroy();
        } catch (Exception e) {
            Log.e(TAG, "Error destroying WebView: " + e.getMessage());
        }
    }


    public void release() {
        for (WebView webView : webViewPool) {
            destroyWebView(webView);
        }
        webViewPool.clear();
        instance = null;
    }
}