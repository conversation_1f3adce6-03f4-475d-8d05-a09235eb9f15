/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.data;

import com.ainirobot.platform.PlatformDef;
import com.ainirobot.platform.bean.IntentInfo;

import java.util.HashMap;

public class IntentManager {

    private static IntentManager sInstance = new IntentManager();

    // hash map of pattern and IntentInfo
    private HashMap<String, IntentInfo> mIntentInfos;

    private IntentManager() {
        loadData();
    }

    private void loadData() {
        mIntentInfos = new HashMap<>();
        mIntentInfos.put(PlatformDef.SET_WORK_MODE,
                new IntentInfo(PlatformDef.SET_WORK_MODE, IntentInfo.TYPE_PLATFORM));
        mIntentInfos.put(PlatformDef.SWITCH_APP_PLATFORM,
                new IntentInfo(PlatformDef.SWITCH_APP_PLATFORM, IntentInfo.TYPE_PLATFORM));
        mIntentInfos.put(PlatformDef.LAUNCH_RN, new IntentInfo(PlatformDef.LAUNCH_RN,
                IntentInfo.TYPE_PLATFORM));
        mIntentInfos.put(PlatformDef.CLOSE_RN, new IntentInfo(PlatformDef.CLOSE_RN,
                IntentInfo.TYPE_PLATFORM));
        mIntentInfos.put(PlatformDef.LIST_RN_OPK, new IntentInfo(PlatformDef.LIST_RN_OPK,
                IntentInfo.TYPE_PLATFORM));
        mIntentInfos.put(PlatformDef.UNINSTALL_RN, new IntentInfo(PlatformDef.UNINSTALL_RN,
                IntentInfo.TYPE_PLATFORM));
        mIntentInfos.put(PlatformDef.SWITCH_CHARACTER,
                new IntentInfo(PlatformDef.SWITCH_CHARACTER,
                        IntentInfo.TYPE_SWITCH_CHARACTER));
    }

    /**
     * 判断intent是否切换角色指令
     *
     * @param intent 指令标识
     * @return boolean
     */
    public boolean isSwitchCharacter(String intent) {
        IntentInfo info = mIntentInfos.get(intent);
        if (info == null) {
            return false;
        }
        return info.getType() == IntentInfo.TYPE_SWITCH_CHARACTER;
    }

    /**
     * 判断intent是否切换平台指令
     *
     * @param intent 指令标识
     * @return boolean
     */
    public boolean isPlatformIntent(String intent) {
        IntentInfo info = mIntentInfos.get(intent);
        if (info == null) {
            return false;
        }
        return info.getType() == IntentInfo.TYPE_PLATFORM;
    }

    public IntentInfo getIntentInfo(String intent) {
        return mIntentInfos.get(intent);
    }

    public static IntentManager getInstance() {
        return sInstance;
    }
}
