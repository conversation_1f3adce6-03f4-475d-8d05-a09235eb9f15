package com.ainirobot.platform.data.provider;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.provider.BaseColumns;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.data.provider.BaseDataHelper;
import com.ainirobot.coreservice.data.provider.Column.Constraint;
import com.ainirobot.coreservice.data.provider.Column.DataType;
import com.ainirobot.coreservice.data.provider.SQLiteTable;
import com.ainirobot.platform.react.AppBeanV2;
import com.ainirobot.platform.react.OPKBeanV3;
import com.ainirobot.platform.react.reactnative.character.Constant;
import com.ainirobot.platform.utils.FileHelper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 用来存储App信息
 */
public class AppInfoHelper extends BaseDataHelper {

    private static final String TAG = "AppInfoHelper";

    public static final SQLiteTable TABLE = new SQLiteTable(OpkField.TABLE_NAME,
            DataProvider.AUTHORITY, true) {

        @Override
        public void create(SQLiteDatabase db) {
            super.create(db);
        }

        @Override
        public void update(Context context, SQLiteDatabase db, int oldVersion, int newVersion) {
            Log.d(TAG, "Update db , old : " + oldVersion + "  new : " + newVersion);
            if (newVersion == 2) {
                importData(context, db);
            }

            if (oldVersion < 3) {
                //AppInfo 表增加 supportModel 字段；
                addSupportModelToAppInfoTable(db);
                importData(context, db);
                Log.d(TAG, "onUpgrade: Version-3 Upgrade done");
            }

            if (oldVersion < 4) {
                //AppInfo 表增加 osType 字段；
                addOSTypeToAppInfoTable(db);
                importData(context, db);
                Log.d(TAG, "onUpgrade: Version-4 Upgrade done");
            }
        }

        @Override
        public void importData(Context context, SQLiteDatabase db) {
            Log.d(TAG, "Import data : " + getCreateString());
            String rpkInstallList = FileHelper
                    .loadJsonFromFile(context.getFilesDir().getAbsolutePath() + "/" + Constant.RPK_INSTALL_DIR + "/" + Constant.RPK_INSTALL_NAME);
            HashMap<String, AppBeanV2> opkList = new Gson().fromJson(rpkInstallList, new TypeToken<HashMap<String, AppBeanV2>>() {
            }.getType());
            if (opkList == null) {
                return;
            }
            for (AppBeanV2 bean : opkList.values()) {
                insertOpkInfo(db, bean);
            }
        }

        private void insertOpkInfo(SQLiteDatabase db, AppBeanV2 bean) {
            Log.d(TAG, "Insert opk info : " + bean.getRpkBean());
            OPKBeanV3 appInfo = bean.getRpkBean();

            db.insertWithOnConflict(getTableName(), OpkField.APP_ID, getContentValues(appInfo),
                    SQLiteDatabase.CONFLICT_IGNORE);
        }

        /**
         * 版本3，修改 AppInfo 表
         * 增加 supportModel 字段
         *
         * @param db
         */
        private void addSupportModelToAppInfoTable(SQLiteDatabase db) {
            // 检查列是否存在
            boolean columnExists = false;
            Cursor cursor = db.rawQuery("PRAGMA table_info(" + AppInfoHelper.OpkField.TABLE_NAME + ")", null);
            while (cursor.moveToNext()) {
                String columnName = cursor.getString(cursor.getColumnIndex("name"));
                if (AppInfoHelper.OpkField.SUPPORT_MODEL.equals(columnName)) {
                    columnExists = true;
                    break;
                }
            }
            cursor.close();

            // 如果列不存在，则添加列
            if (!columnExists) {
                String chassisTable = "alter table " + AppInfoHelper.OpkField.TABLE_NAME +
                        " add column " + AppInfoHelper.OpkField.SUPPORT_MODEL + " text";
                Log.d(TAG, "addSupportModelToAppInfoTable=" + chassisTable);
                db.execSQL(chassisTable);
            } else {
                Log.d(TAG, "Column supportModel already exists in table " + AppInfoHelper.OpkField.TABLE_NAME);
            }
        }

        /**
         * 修改 AppInfo 表
         * 增加 osType 字段
         *
         * @param db
         */
        private void addOSTypeToAppInfoTable(SQLiteDatabase db) {
            // 检查列是否存在
            boolean columnExists = false;
            Cursor cursor = db.rawQuery("PRAGMA table_info(" + AppInfoHelper.OpkField.TABLE_NAME + ")", null);
            while (cursor.moveToNext()) {
                String columnName = cursor.getString(cursor.getColumnIndex("name"));
                if (OpkField.OS_TYPE.equals(columnName)) {
                    columnExists = true;
                    break;
                }
            }
            cursor.close();

            // 如果列不存在，则添加列
            if (!columnExists) {
                String chassisTable = "alter table " + AppInfoHelper.OpkField.TABLE_NAME +
                        " add column " + OpkField.OS_TYPE + " text";
                Log.d(TAG, "addOSTypeToAppInfoTable=" + chassisTable);
                db.execSQL(chassisTable);
            } else {
                Log.d(TAG, "Column osType already exists in table " + AppInfoHelper.OpkField.TABLE_NAME);
            }
        }
    };


static {
        TABLE.addColumn(OpkField.APP_ID, Constraint.UNIQUE, DataType.TEXT)
                .addColumn(OpkField.APP_NAME, DataType.TEXT)
                .addColumn(OpkField.VERSION_CODE, DataType.INTEGER)
                .addColumn(OpkField.VERSION, DataType.TEXT)
                .addColumn(OpkField.CORE_VERSION, DataType.TEXT)
                .addColumn(OpkField.CORE_TARGET, DataType.TEXT)
                .addColumn(OpkField.OPK_LOAD, DataType.TEXT)
                .addColumn(OpkField.PATH, DataType.TEXT)
                .addColumn(OpkField.CONFIG_PATH, DataType.TEXT)
                .addColumn(OpkField.CONFIG_ID, DataType.TEXT)
                .addColumn(OpkField.TYPE, DataType.TEXT)
                .addColumn(OpkField.SUPPORT_MODEL, DataType.TEXT)
                .addColumn(OpkField.OS_TYPE, DataType.TEXT)
                .addColumn(OpkField.INSTALL_TIME, DataType.TEXT)
                .addColumn(OpkField.UPDATE_TIME, DataType.TIMESTAMP);
    }


    public AppInfoHelper(Context context) {
        super(context);
    }

    @Override
    public Uri getContentUri() {
        return TABLE.getContentUri();
    }

    public void insert(OPKBeanV3 info) {
        ContentValues values = getContentValues(info);
        insert(values);
    }

    public boolean delete(String appId) {
        if (TextUtils.isEmpty(appId)) {
            return false;
        }
        int row = delete(OpkField.APP_ID + "=?", new String[]{appId});
        return row > 0;
    }

    public void update(OPKBeanV3 info) {
        ContentValues values = getContentValues(info);
        update(values, OpkField.APP_ID + "=?", new String[]{info.getAppid()});
    }

    public boolean isExist(String appId) {
        Cursor cursor = query(OpkField.APP_ID + "=?", new String[]{appId});
        return cursor.moveToFirst();
    }

    public void replace(OPKBeanV3 info) {
        if (isExist(info.getAppid())) {
            update(info);
        } else {
            insert(info);
        }
    }

    public List<OPKBeanV3> getAllOpkInfo() {
        List<OPKBeanV3> data = new ArrayList<>();
        Cursor cursor = query(null, null);
        if (cursor == null) {
            return data;
        }
        while (cursor.moveToNext()) {

            String appId = cursor.getString(cursor.getColumnIndex(OpkField.APP_ID));
            String appName = cursor.getString(cursor.getColumnIndex(OpkField.APP_NAME));
            int versionCode = cursor.getInt(cursor.getColumnIndex(OpkField.VERSION_CODE));
            String version = cursor.getString(cursor.getColumnIndex(OpkField.VERSION));
            String coreVersion = cursor.getString(cursor.getColumnIndex(OpkField.CORE_VERSION));
            String configId = cursor.getString(cursor.getColumnIndex(OpkField.CONFIG_ID));
            String coreTarget = cursor.getString(cursor.getColumnIndex(OpkField.CORE_TARGET));
            String opkLoad = cursor.getString(cursor.getColumnIndex(OpkField.OPK_LOAD));
            String path = cursor.getString(cursor.getColumnIndex(OpkField.PATH));
            String configPath = cursor.getString(cursor.getColumnIndex(OpkField.CONFIG_PATH));
            String type = cursor.getString(cursor.getColumnIndex(OpkField.TYPE));
            String supportModel = cursor.getString(cursor.getColumnIndex(OpkField.SUPPORT_MODEL));
            String osType = cursor.getString(cursor.getColumnIndex(OpkField.OS_TYPE));


            OPKBeanV3 info = new OPKBeanV3(appId, versionCode, version, coreVersion, coreTarget,
                    opkLoad, 0, null, null, type, path, configId, configPath,
                    appName, null, false, supportModel, osType);
            data.add(info);
        }
        cursor.close();
        return data;
    }

    private static ContentValues getContentValues(OPKBeanV3 info) {
        ContentValues values = new ContentValues();
        values.put(OpkField.APP_ID, info.getAppid());
        values.put(OpkField.APP_NAME, info.getAppName());
        values.put(OpkField.TYPE, info.getType());
        values.put(OpkField.SUPPORT_MODEL, info.getSupportModel());
        values.put(OpkField.OS_TYPE, info.getOsType());
        values.put(OpkField.VERSION, info.getVersionName());
        values.put(OpkField.VERSION_CODE, info.getVersionCode());
        values.put(OpkField.CORE_VERSION, info.getCoreVersion());
        values.put(OpkField.CORE_TARGET, info.getCoreTarget());
        values.put(OpkField.CONFIG_ID, info.getConfigId());
        values.put(OpkField.CONFIG_PATH, info.getConfigPath());
        values.put(OpkField.OPK_LOAD, info.getOpkLoad());
        values.put(OpkField.PATH, info.getPath());
        values.put(OpkField.INSTALL_TIME, info.getInstallTime());
        values.put(OpkField.UPDATE_TIME, System.currentTimeMillis());
        return values;
    }

    public static final class OpkField implements BaseColumns {
        static final String TABLE_NAME = "AppInfo";

        static final String APP_ID = "appId";
        static final String APP_NAME = "appName";
        static final String VERSION_CODE = "versionCode";
        static final String VERSION = "version";
        static final String CORE_VERSION = "coreVersion";
        static final String CORE_TARGET = "coreTarget";
        static final String OPK_LOAD = "opkLoad";
        static final String INSTALL_TIME = "installTime";
        static final String PATH = "path";
        static final String CONFIG_PATH = "configPath";
        static final String CONFIG_ID = "configId";
        static final String TYPE = "type";
        static final String UPDATE_TIME = "time";
        static final String SUPPORT_MODEL = "supportModel";
        static final String OS_TYPE = "osType";
    }

}
