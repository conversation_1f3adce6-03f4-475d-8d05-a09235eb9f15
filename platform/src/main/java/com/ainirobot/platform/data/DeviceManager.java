/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.data;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothClass;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.util.ArrayMap;
import android.util.Log;

import com.ainirobot.platform.bi.wrapper.ReportControl;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.bi.wrapper.manager.bi.impl.BluetoothPoint;
import com.ainirobot.platform.data.listener.ConnectivityListener;
import com.ainirobot.platform.utils.TelephonyUtils;
import com.google.gson.Gson;

import java.util.Collection;

public class DeviceManager {

    private static final String TAG = DeviceManager.class.getSimpleName();
    private static final int MSG_START_DETECT_BLUETOOTH = 0x1;
    private static final int MSG_BLUETOOTH_DISCOVERY_STARTED = 0x2;
    private static final int MSG_BLUETOOTH_DISCOVERY_FINISHED = 0x3;
    private static final int MSG_BLUETOOTH_FOUND = 0x4;
    private static final int MSG_AIRPLANE_MODE_CLOSE = 0x5;

    private static volatile DeviceManager sDeviceManager;
    private DeviceHandler mHandler;
    private boolean mIsStarted;
    private String mType;
    private ArrayMap<String, BluetoothBean> mBluetoothBeans;
    private Gson mGson;
    private ConnectivityListener mListener;

    private DeviceManager() {
        HandlerThread thread = new HandlerThread("DeviceManager");
        thread.start();
        mHandler = new DeviceHandler(thread.getLooper());
        mBluetoothBeans = new ArrayMap<>();
        mGson = new Gson();
        IntentFilter filter = new IntentFilter(BluetoothDevice.ACTION_FOUND);
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED);
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED);
        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        filter.setPriority(Integer.MAX_VALUE);
        BaseApplication.getContext().registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                Log.d(TAG, "onReceive intent: " + intent);
                if (intent == null || intent.getAction() == null) {
                    return;
                }
                Message message;
                switch (intent.getAction()) {
                    case BluetoothDevice.ACTION_FOUND:
                        mHandler.removeMessages(MSG_BLUETOOTH_FOUND);
                        message = new Message();
                        message.what = MSG_BLUETOOTH_FOUND;
                        message.obj = intent;
                        mHandler.sendMessage(message);
                        break;
                    case BluetoothAdapter.ACTION_DISCOVERY_STARTED:
                        mHandler.removeMessages(MSG_BLUETOOTH_DISCOVERY_STARTED);
                        mHandler.sendEmptyMessage(MSG_BLUETOOTH_DISCOVERY_STARTED);
                        break;
                    case BluetoothAdapter.ACTION_DISCOVERY_FINISHED:
                        mHandler.removeMessages(MSG_BLUETOOTH_DISCOVERY_FINISHED);
                        mHandler.sendEmptyMessage(MSG_BLUETOOTH_DISCOVERY_FINISHED);
                        break;
                    case ConnectivityManager.CONNECTIVITY_ACTION:
                        updateCurrentMobileSubType();
                        break;
                    default:
                        break;
                }
            }
        }, filter);
    }

    public void init() {
        BluetoothManager bluetoothManager = (BluetoothManager) BaseApplication
                .getContext().getSystemService(Context.BLUETOOTH_SERVICE);
        if (bluetoothManager == null) {
            return;
        }
        BluetoothAdapter adapter = bluetoothManager.getAdapter();
        boolean openSuccess = adapter.enable();
        Log.d(TAG, "init openSuccess: " + openSuccess);
        TelephonyUtils.setAirPlaneModeByRadio(false);
    }

    private class DeviceHandler extends Handler {
        private DeviceHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            Log.d(TAG, "handleMessage what: " + msg.what + ", mIsStarted: " + mIsStarted);
            switch (msg.what) {
                case MSG_START_DETECT_BLUETOOTH:
                    if (mIsStarted) {
                        Log.e(TAG, "startBluetoothDetect has started");
                        return;
                    }
                    startBluetoothTask((String) msg.obj);
                    break;
                case MSG_BLUETOOTH_DISCOVERY_STARTED:
                    break;
                case MSG_BLUETOOTH_DISCOVERY_FINISHED:
                    Collection<BluetoothBean> beans = mBluetoothBeans.values();
                    Log.d(TAG, "finished type: " + mType
                            + ", beans: " + mGson.toJson(beans));
                    if (beans.size() <= 0) {
                        return;
                    }
                    ReportControl.getInstance().reportMsg(new BluetoothPoint(mGson.toJson(beans), mType));
                    break;
                case MSG_BLUETOOTH_FOUND:
                    Intent intent = (Intent) msg.obj;
                    BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                    short rssi = intent.getShortExtra(BluetoothDevice.EXTRA_RSSI, Short.MIN_VALUE);
                    String name = device.getName() == null ? "" : device.getName();
                    String address = device.getAddress();
                    int deviceClass = device
                            .getBluetoothClass().getMajorDeviceClass();
                    Log.v(TAG, "found name: " + name + ", address: " + address
                            + ", class: " + deviceClass + ", rssi: " + rssi);
                    if (BluetoothClass.Device.Major.PHONE != deviceClass) {
                        return;
                    }
                    BluetoothBean bean = new BluetoothBean(address, name, rssi);
                    mBluetoothBeans.put(address, bean);
                    break;
                case MSG_AIRPLANE_MODE_CLOSE:
                    TelephonyUtils.setAirPlaneModeByRadio(false);
                    break;
                default:
                    break;
            }
        }
    }

    public static DeviceManager getInstance() {
        if (sDeviceManager == null) {
            synchronized (DeviceManager.class) {
                if (sDeviceManager == null) {
                    sDeviceManager = new DeviceManager();
                }
            }
        }
        return sDeviceManager;
    }

    public void registerConnectivityChanged(ConnectivityListener listener) {
        mListener = listener;
        updateCurrentMobileSubType();
    }

    public void unregisterConnectivityChanged() {
        mListener = null;
    }

    public void updateCurrentMobileSubType() {
        if (mListener != null) {
            mListener.onConnectStateChanged(TelephonyUtils.getActiveNetworkType(),
                    TelephonyUtils.getSimState(), TelephonyUtils.getMobileSubType(),
                    TelephonyUtils.isMobileConnect());
        }
    }

    public void toggleAirplaneMode() {
        Log.d(TAG, "toggleAirplaneMode");
        mHandler.removeMessages(MSG_AIRPLANE_MODE_CLOSE);
        TelephonyUtils.setAirPlaneModeByRadio(true);
        mHandler.sendEmptyMessageDelayed(MSG_AIRPLANE_MODE_CLOSE, 10000);
    }

    public void startBluetoothDetect(String type) {
        Log.d(TAG, "startBluetoothDetect type: " + type);
        mHandler.removeMessages(MSG_START_DETECT_BLUETOOTH);
        Message message = new Message();
        message.what = MSG_START_DETECT_BLUETOOTH;
        message.obj = type;
        mHandler.sendMessage(message);
    }

    private void startBluetoothTask(String type) {
        BluetoothManager bluetoothManager = (BluetoothManager) BaseApplication
                .getContext().getSystemService(Context.BLUETOOTH_SERVICE);
        if (bluetoothManager == null) {
            return;
        }
        mType = type;
        mBluetoothBeans.clear();
        BluetoothAdapter adapter = bluetoothManager.getAdapter();
        if (!adapter.isEnabled()) {
            boolean openSuccess = adapter.enable();
            Log.d(TAG, "starBluetoothTask open success: " + openSuccess);
        }
        boolean discoverySuccess = adapter.startDiscovery();
        if (discoverySuccess) {
            mIsStarted = true;
        }
        Log.d(TAG, "starBluetoothTask discovery success: " + discoverySuccess);
    }

    public class BluetoothBean {

        private String address;
        private String name;
        private short rssi;

        BluetoothBean(String address, String name, short rssi) {
            this.address = address;
            this.name = name;
            this.rssi = rssi;
        }

        @Override
        public String toString() {
            return "name: " + name + ", address: " + address + ", rssi: " + rssi;
        }
    }
}
