package com.ainirobot.platform.data;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.platform.PlatformDef;
import com.ainirobot.platform.control.ControlManager;

import org.json.JSONException;
import org.json.JSONObject;

public class HwStateManager {
    private static final String TAG = "HWAbnormalReporter";
    private static HwStateManager mInstance;
    private int mReqId = 0;
    private boolean mIsBlocking = false;

    private HwStateManager() {
    }

    public static HwStateManager getInstance() {
        if (mInstance == null) {
            mInstance = new HwStateManager();
        }
        return mInstance;
    }

    public void init() {
        Log.d(TAG, "init registerStatusListener");
        RobotApi.getInstance().registerStatusListener(Definition.STATUS_HEAD, new StatusListener() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "onStatusUpdate type=" + type + " data=" + data);
                if (TextUtils.equals(type, Definition.STATUS_HEAD) && !TextUtils.isEmpty(data)) {
                    switch (data) {
                        case PlatformDef.STATUS_HW_CONNECTED:
                            ControlManager.handleRequest(mReqId, PlatformDef.HW_HEAD_CONNECT, "", "");
                            break;

                        case PlatformDef.STATUS_HW_DISCONNECTED:
                            ControlManager.handleRequest(mReqId, PlatformDef.HW_HEAD_DISCONNECTED, "", "");
                            break;
                    }
                }
            }
        });

        RobotApi.getInstance().registerStatusListener(Definition.HW_WHEEL_MOTOR_BLOCKED, new StatusListener() {
            @Override
            public void onStatusUpdate(String type, String data) {
                try {
                    JSONObject json = new JSONObject(data);
                    if (!json.has("board") || json.has("state")) {
                        return;
                    }
                } catch (JSONException e) {
                    return;
                }

                if (!mIsBlocking) {
                    Log.d(TAG, Definition.HW_WHEEL_MOTOR_BLOCKED + ":" + data);
                    mIsBlocking = true;
                    RobotApi.getInstance().stopMove(0, null);
                    ControlManager.handleRequest(mReqId, PlatformDef.WHEEL_MOTOR_BLOCKED,
                            PlatformDef.WHEEL_MOTOR_BLOCKED, "");
                    DelayTask.submit(this, new Runnable() {
                        @Override
                        public void run() {
                            mIsBlocking = false;
                        }
                    }, 3000);
                }
            }
        });
    }
}
