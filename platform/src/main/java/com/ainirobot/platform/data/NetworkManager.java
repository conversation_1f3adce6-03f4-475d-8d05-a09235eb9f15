package com.ainirobot.platform.data;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.telephony.PhoneStateListener;
import android.util.Log;

import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.bi.wrapper.ReportControl;
import com.ainirobot.platform.bi.wrapper.manager.bi.impl.SimStatePoint;
import com.ainirobot.platform.utils.TelephonyUtils;

public class NetworkManager extends PhoneStateListener {

    private static final String TAG = NetworkManager.class.getSimpleName();
    private static final int MSG_SIM_STATE_CHANGE = 0x1;
    private final static String ACTION_SIM_STATE_CHANGED
            = "android.intent.action.SIM_STATE_CHANGED";

    private static volatile NetworkManager sNetworkManager;
    private NetworkHandler mHandler;

    public static NetworkManager getInstance() {
        if (sNetworkManager == null) {
            synchronized (NetworkManager.class) {
                if (sNetworkManager == null) {
                    sNetworkManager = new NetworkManager();
                }
            }
        }
        return sNetworkManager;
    }

    private NetworkManager() {
        HandlerThread thread = new HandlerThread("NetworkManager");
        thread.start();
        mHandler = new NetworkHandler(thread.getLooper());
    }

    public void init() {
        IntentFilter filter = new IntentFilter(ACTION_SIM_STATE_CHANGED);
        filter.setPriority(Integer.MAX_VALUE);
        mHandler.sendEmptyMessage(MSG_SIM_STATE_CHANGE);
        BaseApplication.getContext().registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                Log.d(TAG, "onReceive intent: " + intent);
                if (intent == null || intent.getAction() == null) {
                    return;
                }
                switch (intent.getAction()) {
                    case ACTION_SIM_STATE_CHANGED:
                        mHandler.sendEmptyMessage(MSG_SIM_STATE_CHANGE);
                        break;
                    default:
                        break;
                }
            }
        }, filter);
    }

    private static class NetworkHandler extends Handler {
        private NetworkHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            Log.d(TAG, "handleMessage what: " + msg.what);
            switch (msg.what) {
                case MSG_SIM_STATE_CHANGE:
                    String simStateStr = TelephonyUtils.getSimState();
                    Log.d(TAG, "sim state: " + simStateStr);
                    ReportControl.getInstance().reportMsg(new SimStatePoint(simStateStr));
                    DeviceManager.getInstance().updateCurrentMobileSubType();
                    break;
                default:
                    break;
            }
        }
    }
}
