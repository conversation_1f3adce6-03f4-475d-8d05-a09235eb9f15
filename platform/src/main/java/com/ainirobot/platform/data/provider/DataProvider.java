package com.ainirobot.platform.data.provider;

import android.content.ContentValues;
import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.net.Uri;
import androidx.annotation.NonNull;

import com.ainirobot.coreservice.data.provider.BaseProvider;
import com.ainirobot.coreservice.data.provider.SQLiteTable;

import java.util.ArrayList;
import java.util.List;

public class DataProvider extends BaseProvider {
    private static final String TAG = "DataProvider";

    public static final String AUTHORITY = "com.ainirobot.robotplatform.dataprovider";

    private static final List<SQLiteTable> TABLES = new ArrayList<SQLiteTable>() {{
        add(AppInfoHelper.TABLE);
    }};

    @Override
    public List<SQLiteTable> getTables() {
        return TABLES;
    }

    @Override
    public SQLiteOpenHelper getDBHelper() {
        return new DBHelper(getContext());
    }

    @Override
    public int update(@NonNull Uri uri, ContentValues values, String selection, String[] selectionArgs) {
        return super.update(uri, values, selection, selectionArgs, true);
    }

    static class DBHelper extends SQLiteOpenHelper {
        private static final String DB_NAME = "platformData.db";

        private static final int VERSION = 4;

        private Context mContext;

        private DBHelper(Context context) {
            super(context, DB_NAME, null, VERSION);
            mContext = context;
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            for (SQLiteTable table : TABLES) {
                table.create(db);
                table.importData(mContext, db);
            }
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            for (SQLiteTable table : TABLES) {
                table.update(mContext, db, oldVersion, newVersion);
            }
        }

        @Override
        public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {

        }
    }
}
