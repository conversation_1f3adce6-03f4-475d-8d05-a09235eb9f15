/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.data;


import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.actionbean.CruiseRouteBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingListener;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.coreservice.utils.ZipUtils;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.R;
import com.ainirobot.platform.bean.Location;
import com.ainirobot.platform.bean.MapInfoBean;
import com.ainirobot.platform.react.view.FloatDialogManager;
import com.ainirobot.platform.speech.SpeechApi;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 机器人信息
 */
public class RobotInfo {
    private static final String TAG = "RobotInfo";
    public static final String STATUS_EXPOSURE = "com.ainirobot.status.EXPOSURE";
    private static RobotInfo sInstance;
    private static final String FAILED = "failed";

    private static final long RETRY_INTERVAL = 5 * 1000;

    private RobotApi mApi;
    private Gson mGson;

    private static final String H401 = "H401";

    /**
     * 待命位置名称
     */
    public static final String RECEPTION_POINT = "接待点";

    /**
     * 接待点位置范围
     */
    private static final double RECEPTION_RANGE = 1d;

    /**
     * 当前地图名称
     */
    private volatile String mapName;

    /**
     * 是否已定位
     */
    private volatile boolean isPoseEstimate = false;

    /**
     * 当前机器人坐标点
     */
    private AtomicReference<Pose> currentPose;

    /**
     * 所有位置点
     */
    private volatile AtomicReference<Map<String, Pose>> locations;

    /**
     * 巡逻路线
     */
    private AtomicReference<CruiseRouteBean> cruiseRoute;

    /**
     * 所有楼层的楼层信息
     */
    private volatile AtomicReference<List<MultiFloorInfo>> multiFloorInfos;

    /**
     * 头部版本
     * <p>
     * H401 支持270度旋转
     */
    private String headVersion;

    /**
     * 是否数据被修改
     */
    private boolean isDataChange = true;

    /**
     * 返回点
     */
    private BackPoint backPoint;

    /**
     * 当前语言 zh-CN
     */
    private String language;

    private boolean isCharging;
    private boolean isLoadingPose;

    private static RobotInfo getInstance() {
        if (sInstance == null) {
            synchronized (RobotInfo.class) {
                if (sInstance == null) {
                    sInstance = new RobotInfo();
                }
            }
        }
        return sInstance;
    }

    private RobotInfo() {
        this.mApi = RobotApi.getInstance();
        this.mGson = new Gson();
        this.currentPose = new AtomicReference<>();
        this.cruiseRoute = new AtomicReference<>();
        this.locations = new AtomicReference<>();
        this.multiFloorInfos = new AtomicReference<>();
        initMonitor();
    }

    private void initMonitor() {
        Log.d(TAG, "Init monitor");
        SpeechApi.getInstance().sendAgentMessage("anchor", 100, "");
        SpeechApi.getInstance().sendAgentMessage("message", 100, "");
        setConfigMonitor();
        setPoseEstimateMonitor();
        setPoseMonitor();
        setLanguageMonitor();
        setMultiFloorMonitor();
        setNaviSensorExceptionMonitor();
        setExposureStatusMonitor();

        loadHeadVersion();
    }

    private void setExposureStatusMonitor() {
        RobotApi.getInstance().registerStatusListener(Definition.STATUS_EXPOSURE, new StatusListener() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Intent intent = new Intent(RobotInfo.STATUS_EXPOSURE);
                intent.putExtra("data", data);
                Log.d(TAG, "status_exposure sendBroadcast" + data);
                BaseApplication.getContext().sendBroadcast(intent);
            }
        });
    }

    /**
     * 导航传感器异常监听
     */
    private void setNaviSensorExceptionMonitor() {
        mApi.registerStatusListener(Definition.STATUS_NAVI_SENSOR_EXCEPTION, new StatusListener() {
            @Override
            public void onStatusUpdate(String type, String data) {
                handleNaviSensorException(type, data);
            }
        });
    }

    private void handleNaviSensorException(String type, String data) {
        if (!Definition.STATUS_NAVI_SENSOR_EXCEPTION.equals(type)) {
            Log.d(TAG, "type is not support");
            return;
        }
        try {
            //正常
            if (Definition.NAVI_SENSOR_STATE_NORMAL.equals(data)
                    && FloatDialogManager.getInstance().isShowingSensorErrorDialog()) {
                Log.d(TAG, "setNaviSensor: sensor return to normal");
                FloatDialogManager.getInstance().removeAll();
            }

            //提示可能出现问题
            if (Definition.NAVI_SENSOR_STATE_SLIGHT.equals(data)) {
                Log.d(TAG, "setNaviSensor: sensor warning,show toast");
                //显示底部toast
                FloatDialogManager.getInstance().showFloatDialog(
                        FloatDialogManager.FloatDialogType.TYPE_RESPONSE_SENSOR_ERROR,
                        BaseApplication.getContext().getString(R.string.radar_expration_please_wait),
                        30 * 1000,
                        FloatDialogManager.DIALOG_TYPE_BOTTOM);
            }

            //异常
            if (Definition.NAVI_SENSOR_STATE_ERROR.equals(data)
                    && FloatDialogManager.getInstance().isShowingSensorErrorDialog()) {
                Log.d(TAG, "setNaviSensor: sensor error ！");
                FloatDialogManager.getInstance().removeAll();
            }
        } catch (Exception e) {
            Log.d(TAG, "setNaviSensor onStatusUpdate e :" + e.getLocalizedMessage());
        }
    }

    private void setMultiFloorMonitor() {
        loadMultiFloorInfo();
        mApi.registerStatusListener(Definition.STATUS_MULTI_FLOOR_CONFIG_UPDATE, new StatusListener() {
            @Override
            public void onStatusUpdate(String type, String data) {
                loadMultiFloorInfo(data);
            }
        });
    }

    private void loadMultiFloorInfo() {
        mApi.queryMultiFloorConfig(Definition.DEBUG_REQ_ID, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                loadMultiFloorInfo(message);
            }
        });
    }

    public void sendAgentMapInfo() {
        mApi.getMapInfo(0, null, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                if (result == Definition.RESULT_OK && !TextUtils.isEmpty(message)) {
                    Type type = new TypeToken<MapInfoBean>() {}.getType();
                    MapInfoBean mapInfoBean = new Gson().fromJson(message, type);
                    Map<String, Object> sendAgentMessage = new HashMap<>();
                    sendAgentMessage.put("map_id", mapInfoBean.getMapUuid());
                    sendAgentMessage.put("map_name", mapInfoBean.getMapName());
                    Log.d(TAG, "getMapInfo:onResult:  " + mGson.toJson(sendAgentMessage));
                    SpeechApi.getInstance().sendAgentMessage( "robot_current_mapInfo", 100,  mGson.toJson(sendAgentMessage));
                }
            }
        });
    }

    private void loadMultiFloorInfo(final String message) {
        Log.d(TAG, "updateMultiFloorInfo: " + message);
        Type type = new TypeToken<ArrayList<MultiFloorInfo>>() {
        }.getType();
        try {
            List<MultiFloorInfo> placeList = new Gson().fromJson(message, type);
            multiFloorInfos.set(placeList);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setLanguageMonitor() {
        language = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);
        RobotSettingApi.getInstance().registerRobotSettingListener(new RobotSettingListener() {
            @Override
            public void onRobotSettingChanged(String key) {
                language = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);
            }
        }, Definition.ROBOT_LANGUAGE);
    }

    /**
     * 设置定位信息监控
     */
    private void setPoseEstimateMonitor() {
        loadPoseEstimate();

        //监听定位信息变化
        mApi.registerStatusListener(Definition.STATUS_POSE_ESTIMATE, new StatusListener() {
            @Override
            public void onStatusUpdate(String type, String data) {
                try {
                    Log.d(TAG, "On pose estimate change : " + data);
                    JSONObject json = new JSONObject(data);
                    isPoseEstimate = json.getBoolean("isPoseEstimate");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 设置Navigation配置文件监听
     */
    private void setConfigMonitor() {
        //查询地图名称
        loadMapName();

        //监听配置文件变化
        mApi.registerStatusListener(Definition.REPORT_NAVI_CONFIG, new StatusListener() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "On config change : " + data);
                isDataChange = true;
                if (Definition.NAVI_UPDATE_MAP_INFO.equals(data)) {
                    loadMapName();
                }
//                else if (Definition.NAVI_UPDATE_PLACE.equals(data)) {
//                    loadAllLocations();
//                }
            }
        });
    }

    /**
     * 设置坐标点变化监控
     */
    private void setPoseMonitor() {
        mApi.registerStatusListener(Definition.STATUS_POSE, new StatusListener() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Pose pose = mGson.fromJson(data, Pose.class);
                currentPose.set(pose);
            }
        });
    }

    /**
     * 获取定位信息
     */
    private void loadPoseEstimate() {
        mApi.isRobotEstimate(Definition.DEBUG_REQ_ID, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                try {
                    Log.d(TAG, "Load pose estimate : " + message);
                    isPoseEstimate = Boolean.parseBoolean(message);
                    return;
                } catch (Exception e) {
                    e.printStackTrace();
                }

                //定位信息获取失败后重试
                retry(new Runnable() {
                    @Override
                    public void run() {
                        loadPoseEstimate();
                    }
                });
            }
        });
    }

    /**
     * 获取地图名称
     */
    private void loadMapName() {
        mApi.getMapName(Definition.DEBUG_REQ_ID, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                if (result == Definition.RESULT_OK) {
                    if (TextUtils.isEmpty(message)) {
                        Log.d(TAG, "Load map name failed : not exits");
                        return;
                    }

                    if (!message.equals(mapName)) {
                        mapName = message;
                        loadAllLocations();
                    }
                    isDataChange = false;
                    Log.d(TAG, "Load map name : " + mapName);
                    //根据地图名称，更新巡逻路线信息
                    loadCruiseRoute();
                    sendAgentMapInfo();
                    return;
                }

                //地图名称获取失败后重试
                retry(new Runnable() {
                    @Override
                    public void run() {
                        loadMapName();
                    }
                });
            }
        });
    }


    /**
     * 加载巡逻路线
     */
    private void loadCruiseRoute() {
        if (TextUtils.isEmpty(mapName)) {
            cruiseRoute.set(null);
            return;
        }

        JSONObject object = new JSONObject();
        try {
            object.put("map_name", mapName);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        mApi.getNaviCruiseRoute(Definition.DEBUG_REQ_ID, object.toString(), new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                try {
                    if (FAILED.equals(message)) {
                        cruiseRoute.set(null);
                        Log.d(TAG, "Load cruise route failed : not exits");
                        return;
                    }

                    CruiseRouteBean route = mGson.fromJson(message, CruiseRouteBean.class);
                    if (route != null) {
                        Log.d(TAG, "Load cruise route : " + route.toString());
                        cruiseRoute.set(route);
                        return;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    cruiseRoute.set(null);
                }

                //巡逻路线加载失败后重试
                retry(new Runnable() {
                    @Override
                    public void run() {
                        loadCruiseRoute();
                    }
                });
            }
        });
    }

    /**
     * 加载所有坐标点
     */
    private void loadAllLocations() {
        if (TextUtils.isEmpty(mapName)) {
            cruiseRoute.set(null);
            return;
        }
        isLoadingPose = true;
        JsonObject obj = new JsonObject();
        obj.addProperty("mapName", mapName);
        obj.addProperty("noCache", true);
        mApi.getPlaceListByMapName(Definition.DEBUG_REQ_ID, mGson.toJson(obj), new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Type type = new TypeToken<ArrayList<Location>>() {
                }.getType();
                try {
                    Gson gson = new Gson();
                    message = ZipUtils.unzipMapData(gson, message);
                    List<Location> placeList = gson.fromJson(message, type);
                    Map<String, Pose> tmpLocations = new HashMap<>();
                    if (placeList != null) {
                        for (Location location : placeList) {
                            Pose pose = new Pose();
                            pose.setX((float) location.getX());
                            pose.setY((float) location.getY());
                            pose.setTheta((float) location.getTheta());
                            pose.setName(location.getName());
                            tmpLocations.put(location.getName(), pose);
                        }

                        Log.d(TAG, "Load locations : " + tmpLocations.toString());
                        locations.set(tmpLocations);
                        isLoadingPose = false;
                        return;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                //加载失败后重试
                retry(new Runnable() {
                    @Override
                    public void run() {
                        loadAllLocations();
                    }
                });
            }
        });
    }

    private void loadHeadVersion() {
        mApi.getCanRotateSupport(Definition.DEBUG_REQ_ID, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                if (result == Definition.RESULT_UNSUPPORT) {
                    return;
                }
                Log.d(TAG, "Get head version : " + message);
                try {
                    JSONObject jsonObject = new JSONObject(message);
                    headVersion = jsonObject.getString(Definition.JSON_CAN_BOARD_APP_VERSION);
                    return;
                } catch (Exception e) {
                    e.printStackTrace();
                }

                //加载失败后重试
                retry(new Runnable() {
                    @Override
                    public void run() {
                        loadHeadVersion();
                    }
                });
            }
        });
    }

    public static void init() {
        if (sInstance == null) {
            sInstance = new RobotInfo();
        } else {
            sInstance.initMonitor();
        }
    }

    public static void reload() {
        if (sInstance == null) {
            init();
            return;
        }
        Log.d(TAG, "Reload : " + sInstance.isDataChange);
//        if (mInstance.isDataChange) {
//
//        }
        sInstance.loadMapName();
        sInstance.loadPoseEstimate();
        sInstance.loadHeadVersion();
        sInstance.loadMultiFloorInfo();
    }

    public static boolean isChargePileExits() {
        Map<String, Pose> allLocations = getInstance().locations.get();
        return allLocations.containsKey(Definition.START_BACK_CHARGE_POSE);
    }

    public static String getMapName() {
        return getInstance().mapName;
    }

    public static boolean isPoseEstimate() {
        return getInstance().isPoseEstimate;
    }

    public static CruiseRouteBean getCruiseRoute() {
        return getInstance().cruiseRoute.get();
    }

    public static Pose getPose(String placeName) {
        if (TextUtils.isEmpty(placeName)) {
            return null;
        }

        Map<String, Pose> allLocations = getInstance().locations.get();
        if (allLocations != null) {
            return allLocations.get(placeName);
        }
        return null;
    }

    public static void setCurrentPose(Pose pose) {
        if (!isPoseEstimate()) {
            return;
        }
        getInstance().currentPose.set(pose);
    }

    public static boolean isLoadingPose(){
        return getInstance().isLoadingPose;
    }

    public static Pose getCurrentPose() {
        if (!isPoseEstimate()) {
            return null;
        }
        return getInstance().currentPose.get();
    }

    public static MultiFloorInfo getCurrentMultiFloorInfo() {
        List<MultiFloorInfo> multiFloorInfos = getInstance().multiFloorInfos.get();
        if (null == multiFloorInfos) {
            return null;
        }
        for (int i = 0; i < multiFloorInfos.size(); i++) {
            MultiFloorInfo multiFloorInfo = multiFloorInfos.get(i);
            if (TextUtils.equals(multiFloorInfo.getMapName(), getMapName())) {
                return multiFloorInfo;
            }
        }
        return null;
    }

    public static List<MultiFloorInfo> getMultiFloorInfos() {
        return getInstance().multiFloorInfos.get();
    }

    public static boolean isPlaceExists(String placeName) {
        Map<String, Pose> allLocations = getInstance().locations.get();
        return allLocations.containsKey(placeName);
    }

    public static String getFloorName(int floor) {
        List<MultiFloorInfo> mapInfo = getInstance().multiFloorInfos.get();
        for (int i = 0; i < mapInfo.size(); i++) {
            MultiFloorInfo info = mapInfo.get(i);
            if (info.getFloorIndex() == floor) {
                return info.getFloorAlias();
            }
        }
        return null;
    }

    /**
     * 获得充电楼层多层配置信息
     * 当前充电楼层就是主楼层
     */
    public static MultiFloorInfo getChargeFloorMultiInfo() {
        List<MultiFloorInfo> mapInfo = getInstance().multiFloorInfos.get();
        for (int i = 0; i < mapInfo.size(); i++) {
            MultiFloorInfo info = mapInfo.get(i);
            if (info.getFloorState() == MultiFloorInfo.MultiFloorType.MAIN_FLOOR) {
                return info;
            }
        }
        return null;
    }

    /**
     * 获取指定名称地点和机器当前位置间的距离
     *
     * @param placeName 地点名称
     * @return
     */
    public static double getPoseDistance(String placeName) {
        double distance = 0;
        Pose pose = getPose(placeName);
        if (pose != null) {
            distance = pose.getDistance(getCurrentPose());
        }
        Log.d(TAG, "getPoseDistance placeName: " + placeName + ", distance: " + distance);
        return distance;
    }

    /**
     * 根据名称列表获取坐标点列表
     *
     * <P><B>NOTE:</B>返回的数据为能查到的所有坐标点，无序</p>
     *
     * @param placeNames 位置名称列表
     * @return 坐标点列表
     */
    public static List<Pose> getPose(List<String> placeNames) {
        List<Pose> poseList = new ArrayList<>();
        for (String name : placeNames) {
            Pose pose = getPose(name);
            if (pose == null) {
                continue;
            }
            poseList.add(pose);
        }

        return poseList;
    }

    /**
     * 头部是否支持大角度旋转
     *
     * @return true 支持
     */
    public static boolean isSupportLargeRotate() {
        return H401.equals(sInstance.headVersion);
    }

    /**
     * 判断机器人是否在某一位置范围内
     *
     * @param name  位置名称
     * @param range 位置范围
     * @return 在范围内返回true
     */
    public static boolean isInLocation(String name, double range) {
        Pose pose = getPose(name);
        Pose currentPose = getCurrentPose();
        if (pose == null || currentPose == null) {
            Log.d(TAG, "Pose[" + name + "] : " + pose);
            return false;
        }
        Log.d(TAG, "isInLocation name: " + name
                + ", distance: " + pose.getDistance(currentPose) + ", pose: " + pose
                + ", currentPose: " + currentPose);

        return pose.getDistance(currentPose) <= range;
    }

    public static String getLanguage() {
        return getInstance().language;
    }

    public static boolean isRTL() {
        String language = getLanguage();
        Log.d(TAG, "isRTL robotLang : " + language);
        return !TextUtils.isEmpty(language) && language.contains("ar");
    }
    public static boolean isInReceptionLocation() {
        return isInLocation(RECEPTION_POINT, RECEPTION_RANGE);
    }

    private boolean getChargeStatus() {
        return isCharging || mApi.getChargeStatus();
    }

    public static boolean isCharging() {
        return sInstance.getChargeStatus();
    }

    public static void setCharging(boolean charging) {
        if (sInstance != null) {
            sInstance.isCharging = charging;
        }
    }

    public static void setBackPoint(String name, long delay, String hintText) {
        sInstance.backPoint = new BackPoint(name, delay, hintText);
    }

    public static void resetBackPoint() {
        sInstance.backPoint = null;
    }

    public static BackPoint getBackPoint() {
        return sInstance.backPoint;
    }

    private void retry(Runnable runnable) {
        DelayTask.submit(runnable, RETRY_INTERVAL);
    }

    public static class BackPoint {
        private String name;
        private long delay;
        private String hintText;

        private BackPoint(String name, long delay, String hintText) {
            this.name = name;
            this.delay = delay;
            this.hintText = hintText;
        }

        public String getName() {
            return name;
        }

        public long getDelay() {
            if (delay < 0) {
                delay = 0;
            }
            return delay;
        }

        public String getHintText() {
            return hintText;
        }
    }
}
