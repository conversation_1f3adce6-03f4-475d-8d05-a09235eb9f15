package com.ainirobot.platform.data;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.provider.Settings;
import android.util.Log;
import android.widget.Toast;

import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.component.LightGroupComponent;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.light.LightConstants;
import com.ainirobot.platform.light.LightManager;
import com.ainirobot.platform.light.impl.LightAnimation;
import com.ainirobot.platform.light.impl.LightColor;
import com.ainirobot.platform.light.impl.LightMultipleColor;
import com.ainirobot.platform.msg.MsgController;
import com.ainirobot.platform.utils.ToastUtil;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class TestManager {

    private static final String TAG = TestManager.class.getSimpleName();
    private static final String TEST_ACTION = "com.ainirobot.platform.testApi";
    private static final String TEST_GET_CORP_UUID = "getCorpUuid";
    private static final String TEST_GET_CORP_VOICE_ID = "getCorpVoiceId";
    private static final String TEST_GET_DOOR_STATUS = "getDoorStatus";
    private static final String TEST_SET_LOCK_ENABLE = "setLockEnable";
    private static final String TEST_LIGHT_COLOR = "setLightColor";
    private static final String TEST_LIGHT_ANIMATION = "setLightAnimation";
    private static final String TEST_LIGHT_MULTIPLE_COLOR = "setLightMultipleColor";
    private static final String TEST_LIGHT_MULTIPLE_COLOR_GROUP = "setLightMultipleColorGroup";
    private static final String TEST_MSG_PUSH = "msgPush";
    private static final String TEST_PLACE_SEARCH = "placeSearch";
    private static final String TEST_SET_NAVIGATION_PARAM = "setNavigationParam";
    private static final String TEST_CRASH = "testCrash";
    private static final String TEST_ANR = "testAnr";


    public static final String TEST_SETTING_NAVIGATION_ANGULAR_SPEED = "test_navigation_angular_speed";
    public static final String TEST_SETTING_NAVIGATION_ANGULAR_ACCELERATION = "test_navigation_angular_acceleration";
    public static final String TEST_SETTING_NAVIGATION_LINEAR_SPEED = "test_navigation_linear_speed";
    public static final String TEST_SETTING_NAVIGATION_LINEAR_ACCELERATION = "test_navigation_linear_acceleration";

    private static volatile TestManager sTestManager;

    private TestManager() {
        TestBroadcastReceiver receiver = new TestBroadcastReceiver();
        IntentFilter filter = new IntentFilter();
        filter.addAction(TEST_ACTION);
        BaseApplication.getContext().registerReceiver(receiver, filter);
    }

    public static TestManager getInstance() {
        if (sTestManager == null) {
            synchronized (TestManager.class) {
                if (sTestManager == null) {
                    sTestManager = new TestManager();
                }
            }
        }
        return sTestManager;
    }

    class TestBroadcastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null || intent.getAction() == null) {
                return;
            }
            if (TEST_ACTION.equals(intent.getAction())) {
                String[] names = intent.getStringArrayExtra("names");
                int[] values = intent.getIntArrayExtra("values");
                float[] fValues = intent.getFloatArrayExtra("fValues");
                boolean[] enables = intent.getBooleanArrayExtra("enables");
                if (names == null || names.length <= 0) {
                    return;
                }
                Log.d(TAG, "onReceive name: " + names[0] + ", names: " + names.length
                        + ", values: " + (values == null ? 0 : values.length)
                        + ", fValues: " + (fValues == null ? 0 : fValues.length)
                        + ", enables: " + (enables == null ? 0 : enables.length));
                switch (names[0]) {
                    case TEST_GET_CORP_UUID:
                        Log.d(TAG, "corpUuid: " + RobotSettings
                                .getCorpUUID(BaseApplication.getContext()));
                        break;
                    case TEST_GET_CORP_VOICE_ID:
                        Log.d(TAG, "corpVoiceId: " + RobotSettings
                                .getVoiceCorpId(BaseApplication.getContext()));
                        break;
                    case TEST_GET_DOOR_STATUS:
                        if (values == null || values.length <= 0) {
                            return;
                        }
                        RobotApi.getInstance().getDoorStatus(0, values[0],
                                null);
                        break;
                    case TEST_SET_LOCK_ENABLE:
                        if (values == null || values.length <= 1) {
                            return;
                        }
                        if (enables == null || enables.length <= 0) {
                            return;
                        }
                        RobotApi.getInstance().setLockEnable(0, values[0], values[1],
                                enables[0]);
                        break;
                    case TEST_MSG_PUSH:
                        if (names.length <= 1) {
                            return;
                        }
                        processMsgPush(names, values);
                        break;
                    case TEST_LIGHT_COLOR:
                        LightManager.getInstance().playColor(new LightColor(LightConstants.LIGHT_TARGET_ALL, Integer.parseInt("061A06", 16)));
                        break;
                    case TEST_LIGHT_ANIMATION:
                        LightManager.getInstance().playAnimation(new LightAnimation(Integer.parseInt("5493FF", 16),
                                Integer.parseInt("111d33", 16), 0, 0, -1,
                                1300, Integer.parseInt("5493FF", 16)));
                        break;
                    case TEST_LIGHT_MULTIPLE_COLOR:
                        int[] colorArray = new int[] {
                                Integer.parseInt("5493FF", 16),
                                Integer.parseInt("5493FF", 16),
                                Integer.parseInt("5493FF", 16),
                                Integer.parseInt("5493FF", 16),
                                Integer.parseInt("5493FF", 16),
                                Integer.parseInt("5493FF", 16),
                                Integer.parseInt("5493FF", 16),
                                0,0,0,0,0,0,0,0
                        };
                        LightManager.getInstance().playMultipleColor(new LightMultipleColor(colorArray));
                        break;
                    case TEST_LIGHT_MULTIPLE_COLOR_GROUP:
                        LightGroupComponent lightGroupComponent = new LightGroupComponent(TAG);
                        List<LightMultipleColor> colors = new ArrayList<>();
                        colors.add(new LightMultipleColor(new int[] {
                                Integer.parseInt("FFA500", 16),
                                Integer.parseInt("FFA500", 16),
                                Integer.parseInt("FFA500", 16),
                                Integer.parseInt("FFA500", 16),
                                Integer.parseInt("FFA500", 16),
                                Integer.parseInt("FFA500", 16),
                                Integer.parseInt("FFA500", 16),
                                Integer.parseInt("FFA500", 16),
                                0,0,0,0,0,0,0
                        }));
                        colors.add(new LightMultipleColor(new int[] {
                                0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
                        }));
                        JSONObject jsonOb = new JSONObject();
                        try {
                            jsonOb.put(ComponentParams.LightGroup.PARAM_INTERVAL_TIME, 300);
                            jsonOb.put(ComponentParams.LightGroup.PARAM_IS_LOOP, true);
                            Gson gson = new Gson();
                            jsonOb.put(ComponentParams.LightGroup.PARAM_LIGHT_GROUP_BEAN, gson.toJson(colors));
                            lightGroupComponent.start(jsonOb.toString());
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        break;
                    case TEST_SET_NAVIGATION_PARAM:
                        if (fValues == null || fValues.length <= 0) {
                            Toast.makeText(BaseApplication.getContext(),
                                    "set navigation param invalid", Toast.LENGTH_SHORT).show();
                            return;
                        }
                        Settings.Global.putFloat(BaseApplication.getContext()
                                .getContentResolver(), TEST_SETTING_NAVIGATION_ANGULAR_SPEED, fValues[0]);
                        if (fValues.length > 1) {
                            Settings.Global.putFloat(BaseApplication.getContext()
                                    .getContentResolver(), TEST_SETTING_NAVIGATION_ANGULAR_ACCELERATION, fValues[1]);
                        }
                        if (fValues.length > 2) {
                            Settings.Global.putFloat(BaseApplication.getContext()
                                    .getContentResolver(), TEST_SETTING_NAVIGATION_LINEAR_SPEED, fValues[2]);
                        }
                        if (fValues.length > 3) {
                            Settings.Global.putFloat(BaseApplication.getContext()
                                    .getContentResolver(), TEST_SETTING_NAVIGATION_LINEAR_ACCELERATION, fValues[3]);
                        }
                        float angularSpeed = Settings.Global.getFloat(BaseApplication.getContext()
                                .getContentResolver(), TEST_SETTING_NAVIGATION_ANGULAR_SPEED, -1);
                        float angularAcceleration = Settings.Global.getFloat(BaseApplication.getContext()
                                .getContentResolver(), TEST_SETTING_NAVIGATION_ANGULAR_ACCELERATION, -1);
                        float linearSpeed = Settings.Global.getFloat(BaseApplication.getContext()
                                .getContentResolver(), TEST_SETTING_NAVIGATION_LINEAR_SPEED, -1);
                        float linearAcceleration = Settings.Global.getFloat(BaseApplication.getContext()
                                .getContentResolver(), TEST_SETTING_NAVIGATION_LINEAR_ACCELERATION, -1);
                        Toast.makeText(BaseApplication.getContext(),
                                "set navigation param success"
                                        + "\nangular speed: " + angularSpeed
                                        + "\nangular acceleration: " + angularAcceleration
                                        + "\nlinear speed: " + linearSpeed
                                        + "\nlinear acceleration: " + linearAcceleration, Toast.LENGTH_LONG).show();
                        break;
                    case TEST_CRASH:
                        throw new RuntimeException("test crash");
                    case TEST_ANR:
                        try {
                            Thread.sleep(60 * 1000);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        break;
                    default:
                        break;
                }
            }
        }
    }

    private void processMsgPush(String[] names, int[] values) {
        String msgType = names[1];
        String detailId;
        switch (msgType) {
            case "dump":
                MsgController.getController().dump();
                break;
            case "deleteDetail":
                if (names.length <= 2) {
                    return;
                }
                detailId = names[2];
                MsgController.getController().deleteDetail(detailId);
                break;
            case "increasingDetail":
                if (names.length <= 2) {
                    return;
                }
                detailId = names[2];
                MsgController.getController().increaseDetailPlayTimes(detailId);
                break;
            default:
                break;
        }
    }
}
