package com.ainirobot.platform.data;

import android.content.ContentProviderOperation;
import android.content.ContentProviderResult;
import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.platform.bean.ConfigData;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;

public class ModuleDataMgr {
    public static final String TAG = ModuleDataMgr.class.getSimpleName();
    private static ModuleDataMgr mInstance = new ModuleDataMgr();
    private Context mContext = null;
    private static String URI_TAG = "content://com.ainirobot.ModuleDataProvider";

    public static ModuleDataMgr getInstance(){
        return mInstance;
    }

    private ModuleDataMgr(){

    }

    public enum ModuleType{

        MDT_UKNOWN("mdt_unknown"),
        MDT_GUIDE("module_guide"),
        MDT_RECEPTION("module_reception"),
        MDT_SKILL_HOME("module_skill_home"),
        MDT_QA("module_qa"),
        MDT_WELCOME("module_welcome"),
        MDT_REMOTE_GREET("module_remote_greet"),
        MDT_ADVERT("module_ads");

        private String name;
        public String getName(){
            return this.name;
        }

        ModuleType(String name){
            this.name = name;
        }
    }

    public void setContext(Context cx){
        mContext = cx;
    }

    public ArrayList<String> getConfigFiles(ModuleType mdt){
        Log.i(TAG, "getConfigFiles: mdt " + mdt + "  "+mdt.getName() );
        ArrayList<String> listFiles = null;
        do {

            if(mdt == ModuleType.MDT_UKNOWN){

                break;
            }
            String moduleCode = mdt.getName();
            listFiles = getConfigFiles(moduleCode);
        }while (false);

        return listFiles;
    }

    public ArrayList<String> getConfigFiles(String moduleCode){

        ArrayList<String> listFiles = null;
        do {

            if(TextUtils.isEmpty(moduleCode)){
                break;
            }
            Log.i(TAG, "getConfigFiles: mdt " + moduleCode);

            Log.i(TAG, "getConfigFiles: moduleCode " + moduleCode );
            if(TextUtils.isEmpty(moduleCode)){
                break;
            }
            Log.i(TAG, "getConfigFiles: mContext " + mContext );

            if (null == mContext){
                break;
            }

            Cursor cur = null;
            try{
                ContentResolver resolver = mContext.getContentResolver();
                Log.i(TAG, "getConfigFiles: resolver " + resolver );
                if (null == resolver){
                    break;
                }
                Uri uri = Uri.parse(String.format("%s/configfile?modulecode=%s", URI_TAG, moduleCode));
                Log.i(TAG, "getConfigFiles: resolver " + uri );
                cur = resolver.query(uri, null, null, null, null);
                Log.i(TAG, "getConfigFiles: resolver " + cur );
                 if(cur != null){
                    cur.moveToFirst();
                    String strConfigs = cur.getString(0);
                    if(!TextUtils.isEmpty(strConfigs)){
                        listFiles = new ArrayList<>();
                        for (String config: strConfigs.split(":")){
                            listFiles.add(config);
                        }
                    }

                }
            }catch (Exception ex){
                Log.i(TAG, "getConfigFiles: ex " + ex );
                ex.printStackTrace();
            }finally {
                if(null != cur){
                    cur.close();
                }
            }
        }while (false);
        return listFiles;
    }

    public ArrayList<ConfigData> getConfigData(String moduleCode){

        ArrayList<ConfigData> configDataList = new ArrayList<>();
        do {

            if(TextUtils.isEmpty(moduleCode)){
                break;
            }
            Log.i(TAG, "getConfigData: mdt " + moduleCode);

            Log.i(TAG, "getConfigData: moduleCode " + moduleCode );
            if(TextUtils.isEmpty(moduleCode)){
                break;
            }
            Log.i(TAG, "getConfigData: mContext " + mContext );

            if (null == mContext){
                break;
            }

            Cursor cur = null;
            try{
                ContentResolver resolver = mContext.getContentResolver();
                Log.i(TAG, "getConfigData: resolver " + resolver );
                if (null == resolver){
                    break;
                }
                Uri uri = Uri.parse(String.format("%s/configData?modulecode=%s", URI_TAG, moduleCode));
                Log.i(TAG, "getConfigData: resolver " + uri );
                cur = resolver.query(uri, null, null, null, null);
                Log.i(TAG, "getConfigData: resolver " + cur );
                if (cur != null) {
                    cur.moveToFirst();
                    String strConfigs = cur.getString(0);
                    if (!TextUtils.isEmpty(strConfigs)) {
                        JSONArray jsonArray = new JSONArray(strConfigs);
                        for (int i = 0; i < jsonArray.length(); i++) {
                            JSONObject jsonObject = jsonArray.optJSONObject(i);
                            if (jsonObject != null) {
                                ConfigData data = new ConfigData(jsonObject);
                                configDataList.add(data);
                            }
                        }
                    }
                }
            }catch (Exception ex){
                Log.i(TAG, "getConfigData: ex " + ex);
                ex.printStackTrace();
            }finally {
                if(null != cur){
                    cur.close();
                }
            }
        }while (false);
        return configDataList;
    }

    public String getResFile(ModuleType mdt, String strContext){
        String strRet = null;
        do {

            if(mdt == ModuleType.MDT_UKNOWN){
                break;
            }

            String moduleCode = mdt.getName();

            strRet = getResFile(moduleCode, strContext);

        }while (false);

        return strRet;
    }

    public String getResFile(String moduleCode, String strContext){

        String strRet = null;
        do {

            if(TextUtils.isEmpty(moduleCode)){
                break;
            }


            if (TextUtils.isEmpty(moduleCode) ||
                    TextUtils.isEmpty(strContext)){
                break;
            }

            Cursor cur = null;
            try{

                ContentResolver resolver = mContext.getContentResolver();
                if (null == resolver){
                    break;
                }
                String fileid = getStrMD5(strContext);
                Uri uri = Uri.parse(String.format("%s/resfile?fileid=%s&modulecode=%s", URI_TAG, fileid, moduleCode));
                cur = resolver.query(uri, null, null, null, null);
                if(cur != null){
                    cur.moveToFirst();
                    String sResFile = cur.getString(0);
                    if(!TextUtils.isEmpty(sResFile)){
                        strRet = sResFile;
                    }
                }

            }catch (Exception ex){
                ex.printStackTrace();
            }finally {
                if(null != cur){
                    cur.close();
                }
            }

        }while (false);

        return strRet;
    }

    public String[] getResFileBatch(String moduleCode, String paths){

        //String strRet = null;
        String[] arrPaths = null;
        do {

            if(TextUtils.isEmpty(moduleCode)){
                break;
            }


            if (TextUtils.isEmpty(moduleCode) ||
                    TextUtils.isEmpty(paths)){
                break;
            }

            ContentProviderResult[] results = null;
            try{
                ArrayList<ContentProviderOperation> operations = new ArrayList<ContentProviderOperation>();
                ContentResolver resolver = mContext.getContentResolver();
                if (null == resolver){
                    break;
                }
                String[] __paths = paths.split(",");
                if(null == __paths){
                    break;
                }
                for (String path : __paths) {
                    String fileid = getStrMD5(path);
                    Uri uri = Uri.parse(String.format("%s/resfileBatch?fileid=%s&modulecode=%s", URI_TAG, fileid, moduleCode));
                    ContentProviderOperation opt = ContentProviderOperation.newAssertQuery(uri)
                            .withValue("fileid", fileid)
                            .build();
                    operations.add(opt);
                }
                //results = resolver.applyBatch("com.ainirobot.remotecontrolservice", operations);
                results = resolver.applyBatch("com.ainirobot.ModuleDataProvider", operations);

                if(results != null){
                    arrPaths = new String[results.length];
                    for (int i = 0; i < results.length; i++) {
                        arrPaths[i] = results[i].uri.toString();
                    }
                }

            }catch (Exception ex){
                ex.printStackTrace();
            }finally {
                if(null != results){
                    //cur.close();
                    results = null;
                }
            }

        }while (false);

        return arrPaths;
    }

    public String getStrMD5(String string) {
        if (TextUtils.isEmpty(string)) {
            return "";
        }
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            byte[] bytes = md5.digest(string.getBytes("UTF-8"));
            StringBuilder result = new StringBuilder();
            for (byte b : bytes) {
                String temp = Integer.toHexString(b & 0xff);
                if (temp.length() == 1) {
                    temp = "0" + temp;
                }
                result.append(temp);
            }
            return result.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e){
            e.printStackTrace();
        }
        return "";
    }

    public String forceFetchModuledata(String moduleCode){

        String strRet = "";
        do {

            if(TextUtils.isEmpty(moduleCode)){
                Log.i(TAG, "forceFetchModuledata: moduleCode is null");
                break;
            }

            Log.i(TAG, "forceFetchModuledata: moduleCode " + moduleCode );
            if(TextUtils.isEmpty(moduleCode)){
                break;
            }
            Log.i(TAG, "forceFetchModuledata: mContext " + mContext );

            if (null == mContext){
                break;
            }

            Cursor cur = null;
            try{
                ContentResolver resolver = mContext.getContentResolver();
                Log.i(TAG, "forceFetchModuledata: resolver " + resolver );
                if (null == resolver){
                    break;
                }
                Uri uri = Uri.parse(String.format("%s/fetchdata?modulecode=%s", URI_TAG, moduleCode));
                Log.i(TAG, "forceFetchModuledata: resolver " + uri );
                cur = resolver.query(uri, null, null, null, null);
                Log.i(TAG, "forceFetchModuledata: resolver " + cur );
                if(cur != null){
                    //strRet = "error";
                    cur.moveToFirst();
                    String strConfigs = cur.getString(0);
                    if(TextUtils.isEmpty(strConfigs)){
                        strRet = "error";
                    }

                }
            }catch (Exception ex){
                Log.i(TAG, "getConfigFiles: ex " + ex );
                ex.printStackTrace();
            }finally {
                if(null != cur){
                    cur.close();
                }
            }
        }while (false);

        return strRet;
    }

}
