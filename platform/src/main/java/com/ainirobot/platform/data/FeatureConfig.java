/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.data;

import android.content.ComponentName;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.PlatformDef;
import com.ainirobot.platform.utils.LocalUtils;

/**
 * 功能配置
 */
public class FeatureConfig {

    private static final String TAG = "FeatureConfig";

    private static final String ENABLED = "1";
    private static final String SETTINGS_PRE_WAKEUP = "need_say_hello";
    private static final String SETTINGS_CM_DOOR = "switch_allow_open_cm_bluetooth_door";
    private static final String SETTINGS_CHARACTER_SP = "settings_character_sp";
    private static final String SETTINGS_CHARACTER_NAME = "settings_character_name";
    private static final String SETTINGS_CHARACTER = "robot_mode";

    private static boolean isNavigationEnable = true;

    /**
     * 是否启用广告功能
     *
     * @return 启用true，未启用false
     */
    public static boolean isAdsEnable() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SHOW_AD)
                == Definition.ROBOT_SETTING_ENABLE;
    }

    /**
     * 播放广告时是否允许做小动作
     *
     * @return true 允许
     */
    public static boolean isAdsTrickEnable() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SHOW_AD_AND_ROTATE)
                == Definition.ROBOT_SETTING_ENABLE;
    }

    /**
     * 是否启用预唤醒功能
     *
     * @return 启用返回true
     */
    public static boolean isPreWakeupEnable() {
        //启用广告功能后，禁止预唤醒
        if (isAdsEnable()) {
            return false;
        }

        return ENABLED.equals(SettingsUtil.getString(BaseApplication.getContext(), SETTINGS_PRE_WAKEUP));
    }

    /**
     * 是否启用小动作功能
     *
     * @return 启用true
     */
    public static boolean isTrickEnable() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SMALL_ACTION)
                == Definition.ROBOT_SETTING_ENABLE;
    }


    /**
     * 是否允许自主返回接待点
     *
     * @return 允许返回true
     */
    public static boolean isBackReceptionPointEnable() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_AUTO_BACK_RECEPTION)
                == Definition.ROBOT_SETTING_ENABLE;
    }

    /**
     * 获取导航线速度
     *
     * @return 速度值，unit: m/s
     */
    public static float getNaviLinearSpeed() {
        return RobotSettingApi.getInstance().getRobotFloat(Definition.ROBOT_SETTING_NAV_LINEAR_SPEED);
    }

    /**
     * 获取导航角速度
     *
     * @return 速度值，unit: m/s
     */
    public static float getNaviAngularSpeed() {
        return RobotSettingApi.getInstance().getRobotFloat(Definition.ROBOT_SETTING_NAV_ANGULAR_SPEED);
    }

    /**
     * 是否允许连通CM门禁
     *
     * @return true 允许
     */
    public static boolean isCMDoorEnable() {
        return SettingsUtil.getInt(BaseApplication.getContext(), SETTINGS_CM_DOOR, 1) == 1;
    }

    /**
     * 是否允许在引领过程中将头部旋转180度
     *
     * @return true 允许
     */
    public static boolean isAllowRotateHead() {
        SharedPreferences sharedPreferences = BaseApplication.getContext()
                .getSharedPreferences(PlatformDef.DEVELOPER_CONFIGURATION_ITEM, Context.MODE_PRIVATE);

        //硬件不支持
        if (!RobotInfo.isSupportLargeRotate()) {
            Log.d(TAG, "Hardware not support 270");
            return false;
        }
        return sharedPreferences.getBoolean(PlatformDef.OPEN_HEAD_ROTATE_ITEM, true);
    }

    /**
     * 是否允许讲解中进行对话
     *
     * @return
     */
    public static boolean isAllowGuideChat() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.SWITCH_ALLOW_CHAT_WHEN_INTERPRET)
                == Definition.ROBOT_SETTING_ENABLE;
    }

    /**
     * 获得原地服务是否开启
     *
     * @return true 开启
     */
    public static boolean isSituServiceOpened() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_SITU_SERVICE_STATUS)
                != Definition.ROBOT_SETTING_DISABLE;
    }

    /**
     * 是否支持充电闲聊
     *
     * @return
     */
    public static boolean isAllowChargingChat() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_USABLE_WHEN_CHARGING)
                == Definition.ROBOT_SETTING_ENABLE;
    }

    /**
     * 是否支持充电中导航（移动电源版本使用）
     *
     * @return
     */
    public static boolean isAllowChargingNavi() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_ENABLE_NAVIGATION_INCHARGING)
                == Definition.ROBOT_SETTING_ENABLE;
    }

    /**
     * 获取当前角色的Code值
     *
     * @return
     */
    public static int getCurrentCharacterCode() {
        String value = SettingsUtil.getString(BaseApplication.getContext(), SETTINGS_CHARACTER);
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            e.printStackTrace();
            return Integer.MIN_VALUE;
        }
    }

    /**
     * 获取返回接待点间隔时间
     */
    public static long getBackReceptionIntervalTime() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_WAITING_TIME);
    }

    /**
     * 是否底盘可用
     *
     * @return
     */
    public static boolean isNavigationEnable() {
        return isNavigationEnable;
    }

    public static void setNavigationEnable(boolean enable) {
        isNavigationEnable = enable;
    }

    public static boolean isMultiPersonNotTrack() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_FOCUS_FOLLOW)
                != Definition.ROBOT_SETTING_ENABLE;
    }

    public static void setCharacterName(String name) {
        Log.d(TAG, "Set character name : " + name);
        SharedPreferences sharedPreferences = BaseApplication.getContext()
                .getSharedPreferences(SETTINGS_CHARACTER_SP, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString(SETTINGS_CHARACTER_NAME, name);
        editor.apply();
    }

    public static String getCharacterName() {
        SharedPreferences sharedPreferences = BaseApplication.getContext()
                .getSharedPreferences(SETTINGS_CHARACTER_SP, Context.MODE_PRIVATE);
        String name = sharedPreferences.getString(SETTINGS_CHARACTER_NAME,
                "");
        Log.d(TAG, "Get character name : " + name);
        return name;
    }

    public static boolean isFirstBoot() {
        SharedPreferences sp = BaseApplication.getContext()
                .getSharedPreferences(PlatformDef.DEVELOPER_CONFIGURATION_ITEM,
                        Context.MODE_PRIVATE);
        String bootVideoState = sp.getString(PlatformDef.BOOT_VIDEO_CONFIG_STATE, "");
        Log.d(TAG, "isFirstBoot : " + bootVideoState);
        return isFirstActivation() && TextUtils.isEmpty(bootVideoState);
    }

    public static boolean isInitBoot() {
        return !isFirstConfigDisabled() || isFirstBoot();
    }

    public static boolean isFirstConfigDisabled() {
        try {
            PackageManager pm = BaseApplication.getContext().getPackageManager();
            ComponentName componentName = new ComponentName(Definition.FIRST_CONFIG_PACKAGE,
                    Definition.FIRST_CONFIG_ACTIVITY);

            int flag = pm.getComponentEnabledSetting(componentName);
            Log.d(TAG, "componentName:" + componentName + " enable status:" + flag);
            if (flag == PackageManager.COMPONENT_ENABLED_STATE_DEFAULT
                    || flag == PackageManager.COMPONENT_ENABLED_STATE_ENABLED) {
                return false;
            }
        } catch (IllegalArgumentException | NullPointerException e) {
            e.printStackTrace();
        }
        return true;
    }

    private static boolean isFirstActivation() {
        int firstActivation = LocalUtils.getGlobalSettings(BaseApplication.getContext(), PlatformDef.FIRST_ACTIVATION_KEY, 0);
        Log.d(TAG, "isFirstActivation : " + firstActivation);
        return firstActivation == 1;
    }

    public static void setBootVideoState() {
        SharedPreferences sp = BaseApplication.getContext()
                .getSharedPreferences(PlatformDef.DEVELOPER_CONFIGURATION_ITEM,
                        Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sp.edit();
        editor.putString(PlatformDef.BOOT_VIDEO_CONFIG_STATE,
                PlatformDef.BOOT_VIDEO_CONFIG_STATE);
        editor.apply();
        Log.d(TAG, "set setBootVideoState : ");
    }

    /**
     * @return false: 静默答复
     */
    public static boolean isReply() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_CHAT_REPLY)
                == Definition.ROBOT_SETTING_ENABLE;
    }

    public static boolean getIsAllowLiteHome() {
        int allow = LocalUtils.getGlobalSettings(BaseApplication.getContext(),
                PlatformDef.SWITCH_LITE_HOME, 0);
        return allow == 1;
    }
}
