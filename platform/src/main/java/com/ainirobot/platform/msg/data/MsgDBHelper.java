/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.msg.data;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.ainirobot.platform.msg.bean.MsgConstants;

public class MsgDBHelper extends SQLiteOpenHelper {

    private static final String TAG = "MsgDataHelper";

    public MsgDBHelper(Context context) {
        super(context, MsgConstants.DB_NAME, null, MsgConstants.DB_VERSION);
        SQLiteDatabase db = getWritableDatabase();
        db.close();
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        Log.i(TAG, "onCreate");
        createDetailTable(db);
        createLimitTable(db);
        createMsgTable(db);
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.i(TAG, "Upgrading database from version " + oldVersion
                + " to " + newVersion + ".");
        if (newVersion <= 2) {
            db.execSQL("DROP TABLE IF EXISTS " + MsgConstants.MsgColumns.TABLE_NAME + ";");
            db.execSQL("DROP TABLE IF EXISTS " + MsgConstants.LimitColumns.TABLE_NAME + ";");
            db.execSQL("DROP TABLE IF EXISTS " + MsgConstants.DetailColumns.TABLE_NAME + ";");
            onCreate(db);
            return;
        }
        if (oldVersion < 3) {
            db.execSQL("ALTER TABLE " + MsgConstants.DetailColumns.TABLE_NAME + " ADD COLUMN "
                    + MsgConstants.DetailColumns.DELETED + " INTEGER DEFAULT 0;");
        }

        if (oldVersion < 4) {
            db.execSQL("ALTER TABLE " + MsgConstants.DetailColumns.TABLE_NAME + " ADD COLUMN "
                    + MsgConstants.DetailColumns.RECOMMEND_PLAY_TIMES + " INTEGER DEFAULT 0;");
        }

        if (oldVersion < 5) {
            db.execSQL("ALTER TABLE " + MsgConstants.MsgColumns.TABLE_NAME + " ADD COLUMN "
                    + MsgConstants.MsgColumns.CREATE_TIME + " INTEGER DEFAULT 0;");
        }

        if (oldVersion < 6) {
            db.execSQL("ALTER TABLE " + MsgConstants.DetailColumns.TABLE_NAME + " ADD COLUMN "
                    + MsgConstants.DetailColumns.CLICK_ACTIONS + " TEXT;");
        }
    }

    @Override
    public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.i(TAG, "Downgrading database from version " + oldVersion
                + " to " + newVersion + ".");
        db.execSQL("DROP TABLE IF EXISTS " + MsgConstants.MsgColumns.TABLE_NAME + ";");
        db.execSQL("DROP TABLE IF EXISTS " + MsgConstants.LimitColumns.TABLE_NAME + ";");
        db.execSQL("DROP TABLE IF EXISTS " + MsgConstants.DetailColumns.TABLE_NAME + ";");
        onCreate(db);
    }

    private void createDetailTable(SQLiteDatabase db) {
        db.execSQL("CREATE TABLE " + MsgConstants.DetailColumns.TABLE_NAME + " (" +
                MsgConstants.DetailColumns.DETAIL_ID + " TEXT PRIMARY KEY," +
                MsgConstants.DetailColumns.CONFIG_VERSION + " TEXT," +
                MsgConstants.DetailColumns.PUSH_TASK_ID + " TEXT," +
                MsgConstants.DetailColumns.PLAY_TIMES + " INTEGER DEFAULT 0," +
                MsgConstants.DetailColumns.EXPIRES_IN + " TEXT," +
                MsgConstants.DetailColumns.RECOMMEND_TIMES + " INTEGER DEFAULT -1," +
                MsgConstants.DetailColumns.RES_TYPE + " INTEGER DEFAULT 0," +
                MsgConstants.DetailColumns.RES_IMAGE + " TEXT," +
                MsgConstants.DetailColumns.RES_VIDEO + " TEXT," +
                MsgConstants.DetailColumns.RES_TEXT + " TEXT," +
                MsgConstants.DetailColumns.RECOMMEND_PLAY_TIMES + " INTEGER DEFAULT 0," +
                MsgConstants.DetailColumns.CLICK_ACTIONS + " TEXT," +
                MsgConstants.DetailColumns.DELETED + " INTEGER DEFAULT 0" +
                ");");
    }

    private void createLimitTable(SQLiteDatabase db) {
        db.execSQL("CREATE TABLE " + MsgConstants.LimitColumns.TABLE_NAME + " (" +
                MsgConstants.LimitColumns.LIMIT_ID + " TEXT PRIMARY KEY," +
                MsgConstants.LimitColumns.LIMIT_TYPE + " INTEGER DEFAULT 0," +
                MsgConstants.LimitColumns.ROM_VERSION + " TEXT DEFAULT NULL," +
                MsgConstants.LimitColumns.OPK_ID + " TEXT DEFAULT NULL," +
                MsgConstants.LimitColumns.OPK_VERSION + " TEXT DEFAULT NULL," +
                MsgConstants.LimitColumns.OPK_PATH + " TEXT DEFAULT NULL" +
                ");");
    }

    private void createMsgTable(SQLiteDatabase db) {
        db.execSQL("CREATE TABLE " + MsgConstants.MsgColumns.TABLE_NAME + " (" +
                MsgConstants.MsgColumns.MSG_ID + " TEXT PRIMARY KEY," +
                MsgConstants.MsgColumns.DETAIL_ID + " TEXT REFERENCES " + MsgConstants.DetailColumns.TABLE_NAME +
                "(" + MsgConstants.DetailColumns.DETAIL_ID + ")," +
                MsgConstants.MsgColumns.LIMIT_ID + " TEXT REFERENCES " + MsgConstants.LimitColumns.TABLE_NAME +
                "(" + MsgConstants.LimitColumns.LIMIT_ID + ")," +
                MsgConstants.MsgColumns.TITLE + " TEXT," +
                MsgConstants.MsgColumns.PRIORITY + " INTEGER DEFAULT -1," +
                MsgConstants.MsgColumns.CREATE_TIME + " INTEGER DEFAULT 0" +
                ");");
    }
}
