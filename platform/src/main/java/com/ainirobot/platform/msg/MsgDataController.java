package com.ainirobot.platform.msg;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.actionbean.PushReportBean;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.bean.ConfigData;
import com.ainirobot.platform.bi.wrapper.ReportControl;
import com.ainirobot.platform.data.ModuleDataMgr;
import com.ainirobot.platform.msg.bean.MsgConstants;
import com.ainirobot.platform.msg.bi.PushReceivePoint;
import com.ainirobot.platform.msg.data.DetailDataHelper;
import com.ainirobot.platform.msg.data.LimitDataHelper;
import com.ainirobot.platform.msg.data.MsgDataHelper;
import com.ainirobot.platform.msg.data.bean.MsgBean;
import com.ainirobot.platform.msg.data.bean.MsgDetailBean;
import com.ainirobot.platform.msg.data.bean.MsgLimitBean;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class MsgDataController {

    private static final String TAG = MsgDataController.class.getSimpleName();

    private final Object sDataLock = new Object();
    private DetailDataHelper mDetailDataHelper;
    private LimitDataHelper mLimitDataHelper;
    private MsgDataHelper mMsgDataHelper;
    private Map<String, MsgBean> mMsgBeans;
    private Map<String, MsgLimitBean> mMsgLimitBeans;
    private Map<String, MsgDetailBean> mMsgDetailBeans;

    public MsgDataController() {
        mDetailDataHelper = new DetailDataHelper(BaseApplication.getContext());
        mLimitDataHelper = new LimitDataHelper(BaseApplication.getContext());
        mMsgDataHelper = new MsgDataHelper(BaseApplication.getContext());
        mMsgBeans = new HashMap<>();
        mMsgLimitBeans = new HashMap<>();
        mMsgDetailBeans = new HashMap<>();
    }

    public void updateData(List<ConfigData> configDataList) {
//        Log.d(TAG, "updateData configDataList: " + configDataList);
        synchronized (sDataLock) {
            mMsgDataHelper.deleteAll();
            mLimitDataHelper.deleteAll();
            if (configDataList != null) {
                long ctime = System.currentTimeMillis();
                for (ConfigData configData : configDataList) {
                    String pushTaskId = configData.getPushTaskId();
                    String configId = configData.getConfigId();
                    String configVersion = configData.getConfigVersion();
                    String config = MsgUtils.getConfigJson(configData.getConfigPath());
                    ReportControl.getInstance().reportMsg(new PushReceivePoint(configData
                            .getPushTaskId(), configData.getConfigId(),
                            configData.getConfigVersion(), config, ctime));
                    Log.d(TAG, "updateData pushTaskId: " + pushTaskId
                            + ", configId: " + configId + ", configVersion: " + configVersion
                            + ", config: " + config);
                    try {
                        JSONObject configJson = new JSONObject(config);
                        int priority = Integer.parseInt(configJson.optString("priority"));
                        long create_time = Long.parseLong(configJson.optString("create_time"));
                        String click_actions = configJson.optString("click_actions");
                        int count = 0;
                        String title;
                        List<MsgLimitBean> limitBeans = new ArrayList<>();
                        MsgDetailBean detailBean;
                        String time = configJson.optString("time");
                        if (TextUtils.isEmpty(time)) {
                            Log.e(TAG, "updateData time null");
                            continue;
                        }
                        JSONArray displays = configJson.optJSONArray("display");
                        if (displays == null || displays.length() <= 0) {
                            Log.e(TAG, "updateData display null");
                            continue;
                        }
                        for (int i = 0; i < displays.length(); i++) {
                            JSONObject display = displays.getJSONObject(i);
                            List<MsgLimitBean> currentLimitBeans = updateLimit(display);
                            if (currentLimitBeans != null) {
                                limitBeans.addAll(currentLimitBeans);
                            }
                            int localCount = Integer.parseInt(display.optString("count"));
                            if (localCount > 0) {
                                count = localCount;
                            }
                        }
                        JSONObject content = configJson.optJSONObject("content");
                        int style = content.optInt("style");
                        JSONObject body = content.optJSONObject("body");
                        title = body.optString("title");
                        String text = body.optString("text");
                        String image = body.optString("image");
                        String video = body.optString("video");
                        detailBean = new MsgDetailBean(configId, configVersion, pushTaskId, style, text,
                                !TextUtils.isEmpty(image)
                                        ? ModuleDataMgr.getInstance()
                                        .getResFile(MsgConstants.MSG_MODULE_CODE, image) : null, !TextUtils.isEmpty(video)
                                ? ModuleDataMgr.getInstance()
                                .getResFile(MsgConstants.MSG_MODULE_CODE, video) : null, count, time, click_actions);
                        mDetailDataHelper.insertDetail(detailBean);
                        if (limitBeans.size() > 0) {
                            for (MsgLimitBean limitBean : limitBeans) {
                                MsgBean bean = new MsgBean(limitBean.getLimitId(),
                                        detailBean.getDetailId(), title, priority, create_time);
                                mMsgDataHelper.insertMsg(bean);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            updateTempBeans();
        }
    }

    private void updateTempBeans() {
        mMsgBeans = mMsgDataHelper.getMessages();
        mMsgLimitBeans = mLimitDataHelper.getLimits();
        mMsgDetailBeans = mDetailDataHelper.getDetails();
        Iterator<Map.Entry<String, MsgDetailBean>> detailIterator = mMsgDetailBeans.entrySet().iterator();
        while (detailIterator.hasNext()) {
            String detailId = detailIterator.next().getKey();
            Iterator<Map.Entry<String, MsgBean>> msgIterator = mMsgBeans.entrySet().iterator();
            boolean isExist = false;
            while (msgIterator.hasNext()) {
                MsgBean msgBean = msgIterator.next().getValue();
                if (detailId.equals(msgBean.getDetailId())) {
                    isExist = true;
                    break;
                }
            }
            if (!isExist) {
                mDetailDataHelper.deleteDetail(detailId);
                detailIterator.remove();
            }
        }
    }

    private List<MsgLimitBean> updateLimit(JSONObject display) {
//        Log.e(TAG, "updateLimit display: " + display);
        String type = display.optString("type");
        if (TextUtils.isEmpty(type)) {
            return null;
        }
        List<MsgLimitBean> limitBeans = new ArrayList<>();
        if (type.equals("ota") || type.equals("boot")) {
            MsgLimitBean bean = new MsgLimitBean(getLimitType(type), type, null,
                    null, null);
            mLimitDataHelper.insertLimit(bean);
            limitBeans.add(bean);
            return limitBeans;
        }
        JSONArray opks = display.optJSONArray("opks");
        if (opks == null || opks.length() <= 0) {
            Log.e(TAG, "updateLimit opks null");
            return null;
        }
        for (int i = 0; i < opks.length(); i++) {
            JSONObject opk = opks.optJSONObject(i);
            String appId = opk.optString("opk_id");
            String appVersion = opk.optString("version");
            JSONArray appPaths = opk.optJSONArray("paths");
            if (!TextUtils.isEmpty(appId)) {
                String appPath;
                if (appPaths != null && appPaths.length() > 0) {
                    for (int k = 0; k < appPaths.length(); k++) {
                        appPath = appPaths.optString(k);
                        MsgLimitBean bean = new MsgLimitBean(getLimitType(type),
                                type, appId, appVersion, appPath);
                        mLimitDataHelper.insertLimit(bean);
                        limitBeans.add(bean);
                    }
                } else {
                    MsgLimitBean bean = new MsgLimitBean(getLimitType(type),
                            type, appId, appVersion, null);
                    mLimitDataHelper.insertLimit(bean);
                    limitBeans.add(bean);
                }
            }
        }
        return limitBeans;
    }

    public List<MsgBean> getMessages(int type, String appId, String appPath) {
        Log.d(TAG, "getMessages type: " + type + ", appId: " + appId
                + ", appPath: " + appPath);
        synchronized (sDataLock) {
            List<MsgBean> msgBeans = new ArrayList<>();
            List<String> detailIdList = new ArrayList<>();
            for (MsgLimitBean bean : mMsgLimitBeans.values()) {
                if (bean != null && bean.isPair(type, appId, appPath)) {
                    for (MsgBean msgBean : mMsgBeans.values()) {
                        if (msgBean != null && bean.getLimitId().equals(msgBean.getLimitId())) {
                            if (!detailIdList.contains(msgBean.getDetailId())) {
                                MsgDetailBean detailBean = mMsgDetailBeans.get(msgBean.getDetailId());
                                if (detailBean != null && !detailBean.isOverDue()) {
                                    if (detailBean.getRecommendTimes() <= detailBean.getRecommendPlayTimes()
                                            && isRecommendType(bean.getLimitId())) {
                                        continue;
                                    }
                                    msgBeans.add(msgBean);
                                    detailIdList.add(msgBean.getDetailId());
                                }
                            }
                        }
                    }
                }
            }
            if (msgBeans.size() > 0) {
                sortMessages(msgBeans);
            }
            return msgBeans;
        }
    }

    public MsgDetailBean getDetail(String detailId) {
        Log.d(TAG, "getDetail detailId: " + detailId);
        synchronized (sDataLock) {
            return mMsgDetailBeans.get(detailId);
        }
    }

    public int getLimitType(String type) {
        switch (type) {
            case "boot":
                return MsgLimitBean.MSG_LIMIT_TYPE_BOOT;
            case "ota":
                return MsgLimitBean.MSG_LIMIT_TYPE_OTA;
            case "opk":
                return MsgLimitBean.MSG_LIMIT_TYPE_APP_OBTAIN;
            case "notify":
                return MsgLimitBean.MSG_LIMIT_TYPE_NOTIFY;
            case "callopk":
                return MsgLimitBean.MSG_LIMIT_TYPE_CALL_OPK;
        }
        return -1;
    }

    public void increaseDetailPlayTimes(String detailId) {
        Log.d(TAG, "increaseDetailPlayTimes detailId: " + detailId);
        synchronized (sDataLock) {
            MsgDetailBean detailBean = mMsgDetailBeans.get(detailId);
            if (detailBean != null) {
                detailBean.setPlayTimes(detailBean.getPlayTimes() + 1);
                List<PushReportBean> reportBeans = new ArrayList<>();
                PushReportBean reportBean = new PushReportBean();
                reportBean.pushtask_rid = detailBean.getPushTaskId();
                reportBean.config_id = detailBean.getDetailId();
                reportBean.action = "show";
                reportBean.show_count = String.valueOf(detailBean.getPlayTimes());
                reportBeans.add(reportBean);
                RobotApi.getInstance().pushTaskReport(0, reportBeans, null);
                mDetailDataHelper.updatePlayTimes(detailBean.getDetailId(), detailBean.getPlayTimes());
            }
        }
    }

    public void deleteDetail(String detailId) {
        Log.d(TAG, "deleteDetail detailId: " + detailId);
        synchronized (sDataLock) {
            MsgDetailBean detailBean = mMsgDetailBeans.get(detailId);
            if (detailBean != null) {
                mDetailDataHelper.updateDeleted(detailBean.getDetailId(), true);
                mMsgDetailBeans.remove(detailId);
            }
        }
    }

    public MsgDetailBean getMessageDetail(String msgId) {
        Log.d(TAG, "getMsgDetail msgId: " + msgId);
        synchronized (sDataLock) {
            MsgBean bean = mMsgBeans.get(msgId);
            if (bean != null) {
                MsgDetailBean detailBean = mMsgDetailBeans.get(bean.getDetailId());
                if (detailBean != null) {
                    detailBean.setPlayTimes(detailBean.getPlayTimes() + 1);
                    List<PushReportBean> reportBeans = new ArrayList<>();
                    PushReportBean reportBean = new PushReportBean();
                    reportBean.pushtask_rid = detailBean.getPushTaskId();
                    reportBean.config_id = detailBean.getDetailId();
                    reportBean.action = "show";
                    reportBean.show_count = String.valueOf(detailBean.getPlayTimes());
                    reportBeans.add(reportBean);
                    RobotApi.getInstance().pushTaskReport(0, reportBeans, null);
                    mDetailDataHelper.updatePlayTimes(detailBean.getDetailId(), detailBean.getPlayTimes());
                    if (isRecommendType(bean.getLimitId())) {
                        detailBean.setRecommendPlayTimes(detailBean.getRecommendPlayTimes() + 1);
                        mDetailDataHelper.updateRecommendPlayTimes(detailBean.getDetailId(), detailBean.getRecommendPlayTimes());
                    }
                    return detailBean;
                }
            }
            return null;
        }
    }

    // 冒泡排序
    private void sortMessages(List<MsgBean> msgBeans) {
        for (int i = msgBeans.size() - 1; i > 0; i--) {
            for (int j = 0; j < i; j++) {
                if (msgBeans.get(j).isLowPriority(msgBeans.get(j + 1))) {
                    MsgBean tempMsg = msgBeans.get(j);
                    msgBeans.set(j, msgBeans.get(j + 1));
                    msgBeans.set(j + 1, tempMsg);
                }
            }
        }
    }

    private boolean isRecommendType(String limitId) {
        MsgLimitBean msgLimitBean = mMsgLimitBeans.get(limitId);
        if (msgLimitBean != null) {
            return msgLimitBean.getLimitType() == MsgLimitBean.MSG_LIMIT_TYPE_OTA
                    || msgLimitBean.getLimitType() == MsgLimitBean.MSG_LIMIT_TYPE_BOOT;
        }
        return false;
    }

    public void dump() {
        Log.d(TAG, "dump msg: " + mMsgDataHelper.getMessages().values());
        Log.d(TAG, "dump msg limit: " + mLimitDataHelper.getLimits().values());
        Log.d(TAG, "dump msg detail: " + mDetailDataHelper.getDetails().values());
    }
}
