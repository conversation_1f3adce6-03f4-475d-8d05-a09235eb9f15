/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.platform.msg.bi;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

public class PushDetailReceivePoint extends BiNativeReport {

    private static final String TABLE_NAME = "base_robot_push_detail_receive";
    private String config_id;
    private String content;
    private long ctime;

    public PushDetailReceivePoint(String configId, String content) {
        super(TABLE_NAME);
        this.config_id = configId;
        this.content = content;
        this.ctime = System.currentTimeMillis();
    }
}
