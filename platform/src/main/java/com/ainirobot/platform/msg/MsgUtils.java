package com.ainirobot.platform.msg;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.platform.react.AppManger;
import com.ainirobot.platform.react.OPKBeanV3;
import com.ainirobot.platform.utils.FileUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Calendar;
import java.util.Date;

public class MsgUtils {

    private static final String TAG = MsgUtils.class.getSimpleName();

    public static String getConfigJson(String path) {
        if (TextUtils.isEmpty(path) ||
                !FileUtils.checkFileExist(path)) {
            Log.i(TAG, "getConfigJson not exist: path --> " + path);
            return null;
        }
        Log.i(TAG, "getConfigJson: path exist --> " + path);
        StringBuilder strContent = new StringBuilder();
        try {
            File jsonFile = new File(path);
            InputStream is = new FileInputStream(jsonFile);
            InputStreamReader input = new InputStreamReader(is, "UTF-8");
            BufferedReader br = new BufferedReader(input);
            String line;
            while ((line = br.readLine()) != null) {
                strContent.append(line);
                strContent.append('\n');
            }
            br.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return strContent.toString();
    }

    public static boolean checkLowestRomVersion(String version) {
        Log.d(TAG, "checkLowestRomVersion version: " + version);
        if (TextUtils.isEmpty(version)) {
            return true;
        }

        String[] versionArray = version.split(".");
        if (versionArray.length <= 1) {
            return true;
        }

        String currentVersion = RobotSettings.getVersion();
        String[] currentVersionArray = currentVersion.replace('V', ' ').split(".");
        if (currentVersionArray.length <= 1) {
            return true;
        }

        if (Integer.parseInt(currentVersionArray[0]) > Integer.parseInt(versionArray[0])) {
            return false;
        } else if (Integer.parseInt(currentVersionArray[0]) < Integer.parseInt(versionArray[0])) {
            return true;
        } else return Integer.parseInt(currentVersionArray[1]) <= Integer.parseInt(versionArray[1]);

    }

    public static boolean checkOpkVersion(String appId, String version) {
        Log.d(TAG, "checkOpkVersion appId: " + appId + ", version: " + version);
        if (TextUtils.isEmpty(version) && TextUtils.isEmpty(appId)) {
            return true;
        }

        OPKBeanV3 rpkBean = AppManger.INSTANCE.getRPKByCharacter(appId);
        Log.d(TAG, "checkOpkVersion rpkBean: " + rpkBean);
        if (rpkBean == null || TextUtils.isEmpty(rpkBean.getVersionName())) {
            return false;
        }


        return version.equals(rpkBean.getVersionName());
    }

    public static boolean compareTime(Calendar date, Calendar dateCompare) {
        return date.after(dateCompare);
    }
}
