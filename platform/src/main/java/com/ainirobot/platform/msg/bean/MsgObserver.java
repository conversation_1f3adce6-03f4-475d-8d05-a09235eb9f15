package com.ainirobot.platform.msg.bean;

import android.util.Log;

import com.ainirobot.platform.msg.data.bean.MsgBean;
import com.ainirobot.platform.rn.listener.IRNMsgListener;

import java.util.List;

public class MsgObserver {

    private static final String TAG = MsgObserver.class.getSimpleName();

    private int type;
    private String appId;
    private String appPath;
    private List<MsgBean> lastMessages;
    private IRNMsgListener listener;

    public MsgObserver(int type, String appId, String appPath, IRNMsgListener listener) {
        this.type = type;
        this.appId = appId;
        this.appPath = appPath;
        this.listener = listener;
    }

    public int getType() {
        return type;
    }

    public String getAppId() {
        return appId;
    }

    public String getAppPath() {
        return appPath;
    }

    public IRNMsgListener getListener() {
        return listener;
    }

    public void setLastMessages(List<MsgBean> lastMessages) {
        this.lastMessages = lastMessages;
    }

    public boolean isMessagesChanged(List<MsgBean> messages) {
        Log.d(TAG, "isMessagesChanged last: " + this.lastMessages + ", current: " + messages);
        if (this.lastMessages == null || this.lastMessages.size() <= 0) {
            return messages != null && messages.size() > 0;
        } else if (messages == null || messages.size() <= 0) {
            return true;
        } else if (this.lastMessages.size() != messages.size()) {
            return true;
        } else {
            for (int i = 0; i < messages.size(); i++) {
                if (!messages.get(i).equals(this.lastMessages.get(i))) {
                    return true;
                }
            }
            return false;
        }
    }
}
