package com.ainirobot.platform.msg;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.platform.bean.ConfigData;
import com.ainirobot.platform.bi.wrapper.ReportControl;
import com.ainirobot.platform.data.ModuleDataMgr;
import com.ainirobot.platform.msg.bean.MsgConstants;
import com.ainirobot.platform.msg.bean.MsgObserver;
import com.ainirobot.platform.msg.bi.PushDetailReceivePoint;
import com.ainirobot.platform.msg.bi.PushPreShowPoint;
import com.ainirobot.platform.msg.data.DetailDataHelper;
import com.ainirobot.platform.msg.data.bean.MsgBean;
import com.ainirobot.platform.msg.data.bean.MsgDetailBean;
import com.ainirobot.platform.rn.IMsgRegistry;
import com.ainirobot.platform.rn.listener.IRNMsgListener;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class MsgController extends IMsgRegistry.Stub {

    private static final String TAG = MsgController.class.getSimpleName();

    private static final int MSG_MODULE_DATA_UPDATE = 0x1;
    private static final int MSG_MSG_TIME_CHECK = 0x2;

    private static volatile MsgController sController;

    private Handler mHandler;
    private MsgDataController mDataController;
    private ConcurrentHashMap<Integer, MsgObserver> mRegisterMsgMap;
    private Gson mGson;

    private MsgController() {
        mDataController = new MsgDataController();
        mRegisterMsgMap = new ConcurrentHashMap<>();
        mGson = new Gson();
        HandlerThread thread = new HandlerThread(DetailDataHelper.class.getSimpleName());
        thread.start();
        mHandler = new MsgHandler(thread.getLooper());
        mHandler.sendEmptyMessageDelayed(MSG_MSG_TIME_CHECK, 3 * 60 * 1000);
    }

    @Override
    public String getMessage(int type, String appId, String appPath) {
//        Log.d(TAG, "getMessage type: " + type + ", appId: " + appId + ", appPath: " + appPath);
        String messages = "";
        List<MsgBean> beans = mDataController.getMessages(type, appId, appPath);
        if (beans != null && beans.size() > 0) {
            messages = mGson.toJson(beans);
        }
        ReportControl.getInstance().reportMsg(new PushPreShowPoint(type, appId, appPath, messages));
        return messages;
    }

    @Override
    public String getMessageContent(String msgId) {
//        Log.d(TAG, "getMessageContent msgId: " + msgId);
        String messageContent = "";
        MsgDetailBean detailBean = mDataController.getMessageDetail(msgId);
        if (detailBean != null) {
            messageContent = mGson.toJson(detailBean);
        }
        ReportControl.getInstance().reportMsg(new PushDetailReceivePoint(detailBean != null
                ? detailBean.getDetailId() : "", messageContent));
        return messageContent;
    }

    @Override
    public String getDetail(String detailId) {
//        Log.d(TAG, "getDetail detailId: " + detailId);
        String messageContent = "";
        MsgDetailBean detailBean = mDataController.getDetail(detailId);
        if (detailBean != null) {
            messageContent = mGson.toJson(detailBean);
        }
        return messageContent;
    }

    @Override
    public void increaseDetailPlayTimes(String detailId) {
//        Log.d(TAG, "increaseDetailPlayTimes detailId: " + detailId);
        mDataController.increaseDetailPlayTimes(detailId);
    }

    @Override
    public void deleteDetail(String detailId) {
//        Log.d(TAG, "deleteDetail detailId: " + detailId);
        mDataController.deleteDetail(detailId);
        checkObserver();
    }

    @Override
    public void registerMsgChanged(int callbackId, int type, String appId, String appPath,
                                   IRNMsgListener listener) {
        Log.d(TAG, "registerMsgChanged callbackId: " + callbackId + ", type: " + type
                + ", appId: " + appId + ", appPath: " + appPath);
        MsgObserver observer = new MsgObserver(type, appId, appPath, listener);
        observer.setLastMessages(mDataController.getMessages(type, appId, appPath));
        mRegisterMsgMap.put(callbackId, observer);
    }

    @Override
    public void unregisterMsgChanged(int callbackId) {
        Log.d(TAG, "unregisterMsgChanged callbackId: " + callbackId);
        mRegisterMsgMap.remove(callbackId);
    }

    private class MsgHandler extends Handler {
        private MsgHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            Log.d(TAG, "handleMessage what: " + msg.what);
            switch (msg.what) {
                case MSG_MODULE_DATA_UPDATE:
                    obtainMsgData();
                    break;
                case MSG_MSG_TIME_CHECK:
                    checkObserver();
                    mHandler.sendEmptyMessageDelayed(MSG_MSG_TIME_CHECK, 3 * 60 * 1000);
                    break;
                default:
                    break;
            }
        }
    }

    public static MsgController getController() {
        if (sController == null) {
            synchronized (MsgController.class) {
                if (sController == null) {
                    sController = new MsgController();
                }
            }
        }
        return sController;
    }

    public void init() {
        obtainMsgData();
        RobotApi.getInstance().registerStatusListener(Definition.STATUS_MODULE_DATA_UPDATE,
                new StatusListener() {
                    @Override
                    public void onStatusUpdate(String type, String data) {
                        Log.d(TAG, "onStatusUpdate type: " + type + ", data: " + data);
                        if (Definition.STATUS_MODULE_DATA_UPDATE.equals(type)) {
                            try {
                                JSONObject jsonObject = new JSONObject(data);
                                String moduleCode = jsonObject.optString(MsgConstants.MSG_PARAM_MODULE_CODE);
                                String result = jsonObject.optString(MsgConstants.MSG_PARAM_RESULT);
                                if (MsgConstants.MSG_MODULE_CODE.equals(moduleCode)
                                        && MsgConstants.MSG_RESULT_SUCCESS.equals(result)) {
                                    mHandler.removeMessages(MSG_MODULE_DATA_UPDATE);
                                    mHandler.sendEmptyMessage(MSG_MODULE_DATA_UPDATE);
                                }
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                });
    }

    private void obtainMsgData() {
        Log.d(TAG, "obtainMsgData");
        ArrayList<ConfigData> configIdList = ModuleDataMgr.getInstance()
                .getConfigData(MsgConstants.MSG_MODULE_CODE);
        mDataController.updateData(configIdList);
        checkObserver();
    }

    private void checkObserver() {
        Log.d(TAG, "checkObserver");
        for (int callbackId: mRegisterMsgMap.keySet()) {
            MsgObserver observer = mRegisterMsgMap.get(callbackId);
            if (observer == null) {
                continue;
            }
            if (observer.isMessagesChanged(mDataController.getMessages(observer.getType(),
                    observer.getAppId(), observer.getAppPath()))) {
                try {
                    observer.getListener().onMsgChanged();
                } catch (RemoteException e) {
                    e.printStackTrace();
                    mRegisterMsgMap.remove(callbackId);
                }
            }
        }
    }

    public void clearObserver() {
        Log.d(TAG, "clearObserver");
        mRegisterMsgMap.clear();
    }

    public void dump() {
        mDataController.dump();
    }
}
