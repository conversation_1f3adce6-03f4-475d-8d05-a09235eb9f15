package com.ainirobot.platform.msg.data;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;

import com.ainirobot.coreservice.data.provider.BaseDataHelper;
import com.ainirobot.platform.msg.bean.MsgConstants;
import com.ainirobot.platform.msg.data.bean.MsgBean;

import java.util.HashMap;
import java.util.Map;

import static com.ainirobot.platform.msg.bean.MsgConstants.AUTHORITY_URI;

public class MsgDataHelper extends BaseDataHelper {

    private static final String TAG = MsgDataHelper.class.getSimpleName();
    private static final Uri CONTENT_URI = Uri.withAppendedPath(AUTHORITY_URI, MsgConstants.MsgColumns.TABLE_NAME);

    public MsgDataHelper(Context context) {
        super(context);
    }

    @Override
    public Uri getContentUri() {
        return CONTENT_URI;
    }

    public Map<String, MsgBean> getMessages() {
        Map<String, MsgBean> beans = new HashMap<>();
        Cursor cursor = query(null, null);
        while (cursor != null && cursor.moveToNext()) {
            MsgBean bean = cursorToMsgBean(cursor);
            beans.put(bean.getMsgId(), bean);
        }
        if (cursor != null) {
            cursor.close();
        }
        return beans;
    }

    public void insertMsg(MsgBean msgBean) {
        Uri uri = insert(msgBeanToContentValue(msgBean));
        if (uri == null) {
            updateMsg(msgBean);
        }
    }

    private int updateMsg(MsgBean msgBean) {
        return update(msgBeanToContentValue(msgBean),
                MsgConstants.MsgColumns.MSG_ID + "=?", new String[] {
                        msgBean.getMsgId()
                });
    }

    private MsgBean cursorToMsgBean(Cursor cursor) {
        MsgBean msgBean = new MsgBean();
        msgBean.setMsgId(cursor.getString(cursor.getColumnIndex(MsgConstants.MsgColumns.MSG_ID)));
        msgBean.setLimitId(cursor.getString(cursor.getColumnIndex(MsgConstants.MsgColumns.LIMIT_ID)));
        msgBean.setDetailId(cursor.getString(cursor.getColumnIndex(MsgConstants.MsgColumns.DETAIL_ID)));
        msgBean.setTitle(cursor.getString(cursor.getColumnIndex(MsgConstants.MsgColumns.TITLE)));
        msgBean.setPriority(cursor.getInt(cursor.getColumnIndex(MsgConstants.MsgColumns.PRIORITY)));
        msgBean.setCreateTime(cursor.getLong(cursor.getColumnIndex(MsgConstants.MsgColumns.CREATE_TIME)));
        return msgBean;
    }

    private ContentValues msgBeanToContentValue(MsgBean msgBean) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(MsgConstants.MsgColumns.MSG_ID, msgBean.getMsgId());
        contentValues.put(MsgConstants.MsgColumns.LIMIT_ID, msgBean.getLimitId());
        contentValues.put(MsgConstants.MsgColumns.DETAIL_ID, msgBean.getDetailId());
        contentValues.put(MsgConstants.MsgColumns.TITLE, msgBean.getTitle());
        contentValues.put(MsgConstants.MsgColumns.PRIORITY, msgBean.getPriority());
        contentValues.put(MsgConstants.MsgColumns.CREATE_TIME, msgBean.getCreateTime());
        return contentValues;
    }

    public int deleteMsg(String msgId) {
        return delete(MsgConstants.MsgColumns.MSG_ID + "=?",
                new String[]{
                        msgId
                });
    }

    public int deleteAll() {
        return delete(null,
                null);
    }
}
