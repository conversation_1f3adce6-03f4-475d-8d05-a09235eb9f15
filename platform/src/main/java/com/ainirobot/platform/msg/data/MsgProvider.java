package com.ainirobot.platform.msg.data;

import android.content.ContentValues;
import android.content.Context;
import android.database.sqlite.SQLiteOpenHelper;
import android.net.Uri;
import androidx.annotation.NonNull;
import android.util.Log;

import com.ainirobot.coreservice.data.provider.BaseProvider;
import com.ainirobot.coreservice.data.provider.SQLiteTable;

import java.util.List;

public class MsgProvider extends BaseProvider {

    private static final String TAG = "MsgProvider";

    @Override
    public boolean onCreate() {
        Log.i(TAG, "on create");
        mDBHelper = new MsgDBHelper(getContext());
        return true;
    }

    @Override
    public List<SQLiteTable> getTables() {
        return null;
    }

    @Override
    public SQLiteOpenHelper getDBHelper() {
        return new MsgDBHelper(getContext());
    }

    @Override
    public int update(@NonNull Uri uri, ContentValues values, String selection, String[] selectionArgs) {
        int count = super.update(uri, values, selection, selectionArgs);
        Context context = getContext();
        Log.d(TAG, "update count: " + count);
        if (context != null) {
            context.getContentResolver().notifyChange(uri, null);
        }
        return count;
    }

    @Override
    public String getType(@NonNull Uri uri) {
        return "vnd.android.cursor.item/" + uri.getLastPathSegment();
    }

    @Override
    public String matchTable(Uri uri) {
        return uri.getLastPathSegment();
    }
}
