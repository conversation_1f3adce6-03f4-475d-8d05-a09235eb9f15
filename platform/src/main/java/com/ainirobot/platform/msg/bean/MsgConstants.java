package com.ainirobot.platform.msg.bean;

import android.net.Uri;

public class MsgConstants {

    public static final String DB_NAME = "msg.db";
    public static final int DB_VERSION = 6;
    private static final String AUTHORITY = "com.ainirobot.platform.msgprovider";
    public static final Uri AUTHORITY_URI = Uri.parse("content://" + AUTHORITY);

    public static final String MSG_MODULE_CODE = "module_pushtask_message";
    public static final String MSG_PARAM_MODULE_CODE = "modulecode";
    public static final String MSG_PARAM_RESULT = "result";
    public static final String MSG_RESULT_SUCCESS = "success";

    public interface DetailColumns {
        String TABLE_NAME = "msg_detail";
        String DETAIL_ID = "detail_id";
        String CONFIG_VERSION = "config_version";
        String PUSH_TASK_ID = "push_task_id";
        String RES_TYPE = "res_type";
        String RES_IMAGE = "res_image";
        String RES_VIDEO = "res_video";
        String RES_TEXT = "res_text";
        String PLAY_TIMES = "play_times";
        String RECOMMEND_TIMES = "recommend_times";
        String RECOMMEND_PLAY_TIMES = "recommend_play_times";
        String EXPIRES_IN = "expires_in";
        String CLICK_ACTIONS = "click_actions";
        String DELETED = "deleted";
    }

    public interface MsgColumns {
        String TABLE_NAME = "msg";
        String MSG_ID = "msg_id";
        String DETAIL_ID = "detail_id";
        String LIMIT_ID = "limit_id";
        String TITLE = "title";
        String PRIORITY = "priority";
        String CREATE_TIME = "create_time";
    }

    public interface LimitColumns {
        String TABLE_NAME = "msg_limit";
        String LIMIT_ID = "limit_id";
        String LIMIT_TYPE = "limit_type";
        String ROM_VERSION = "rom_version";
        String OPK_ID = "opk_id";
        String OPK_VERSION = "opk_version";
        String OPK_PATH = "opk_path";
    }
}
