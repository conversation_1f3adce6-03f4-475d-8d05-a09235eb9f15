package com.ainirobot.platform.msg.data.bean;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.platform.msg.MsgUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class MsgDetailBean {

    private static final String TAG = MsgDetailBean.class.getSimpleName();

    private String detailId;
    private String configVersion;
    private String pushTaskId;
    private int resType;
    private String resText;
    private String resImage;
    private String resVideo;
    private int playTimes;
    private int recommendTimes;
    private int recommendPlayTimes;
    private transient String expireIn;
    private String clickActions;
    private boolean isDeleted;

    public MsgDetailBean() {}

    public MsgDetailBean(String configId, String configVersion, String pushTaskId, int resType,
                         String resText, String resImage, String resVideo, int recommendTimes,
                         String expireIn, String clickActions) {
        this.detailId = configId;
        this.configVersion = configVersion;
        this.pushTaskId = pushTaskId;
        this.resType = resType;
        this.resText = resText;
        this.resImage = resImage;
        this.resVideo = resVideo;
        this.recommendTimes = recommendTimes;
        this.expireIn = expireIn;
        this.clickActions = clickActions;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getConfigVersion() {
        return configVersion;
    }

    public void setConfigVersion(String configVersion) {
        this.configVersion = configVersion;
    }

    public String getPushTaskId() {
        return pushTaskId;
    }

    public void setPushTaskId(String pushTaskId) {
        this.pushTaskId = pushTaskId;
    }

    public int getResType() {
        return resType;
    }

    public void setResType(int resType) {
        this.resType = resType;
    }

    public String getResImage() {
        return resImage;
    }

    public void setResImage(String resImage) {
        this.resImage = resImage;
    }

    public String getResVideo() {
        return resVideo;
    }

    public void setResVideo(String resVideo) {
        this.resVideo = resVideo;
    }

    public String getResText() {
        return resText;
    }

    public void setResText(String resText) {
        this.resText = resText;
    }

    public int getPlayTimes() {
        return playTimes;
    }

    public void setPlayTimes(int playTimes) {
        this.playTimes = playTimes;
    }

    public int getRecommendTimes() {
        return recommendTimes;
    }

    public void setRecommendTimes(int recommendTimes) {
        this.recommendTimes = recommendTimes;
    }

    public int getRecommendPlayTimes() {
        return recommendPlayTimes;
    }

    public void setRecommendPlayTimes(int recommendPlayTimes) {
        this.recommendPlayTimes = recommendPlayTimes;
    }

    public String getExpireIn() {
        return expireIn;
    }

    public void setExpireIn(String expireIn) {
        this.expireIn = expireIn;
    }

    public String getClickActions() {
        return clickActions;
    }

    public void setClickActions(String clickActions) {
        this.clickActions = clickActions;
    }

    public boolean isDeleted() {
        return isDeleted;
    }

    public void setDeleted(boolean deleted) {
        isDeleted = deleted;
    }

    public boolean isOverDue() {
        if (isDeleted) {
            return true;
        }
        try {
            JSONArray times = new JSONArray(expireIn);
            if (times.length() > 0) {
                JSONObject time0 = times.optJSONObject(0);
                Log.d(TAG, "isOverDue time0: " + time0);
                if (time0 != null) {
                    JSONObject infoJson = time0.optJSONObject("info");
                    Log.d(TAG, "isOverDue infoJson: " + infoJson);
                    if (infoJson != null) {
                        JSONObject rangeJson = infoJson.optJSONObject("range");
                        long rangeStart = rangeJson.optLong("start") * 1000;
                        long rangeEnd = rangeJson.optLong("end") * 1000;
                        SimpleDateFormat sdf = new SimpleDateFormat();// 格式化时间
                        sdf.applyPattern("yyyy-MM-dd 00:00:00");
                        Date currentDate = Calendar.getInstance().getTime();
                        String currentData = sdf.format(currentDate);
                        long dataCurrent = sdf.parse(currentData).getTime();
                        Log.d(TAG, "isOverDue rangeStart: " + rangeStart
                                + ", rangeEnd: " + rangeEnd + ", dataCurrent: " + dataCurrent);
                        if (dataCurrent < rangeStart || dataCurrent > rangeEnd) {
                            return true;
                        }
                        JSONArray timeArray = infoJson.optJSONArray("parts");
                        Log.d(TAG, "isOverDue timeArray: " + timeArray);
                        if (timeArray == null || timeArray.length() <= 0) {
                            return false;
                        }
                        for (int i = 0; i < timeArray.length(); i++) {
                            JSONObject timeJson = timeArray.optJSONObject(i);
                            String timeStart = timeJson.optString("start");
                            String timeEnd = timeJson.optString("end");
                            Log.d(TAG, "isOverDue timeStart: " + timeStart
                                    + ", timeEnd: " + timeEnd);
                            if (TextUtils.isEmpty(timeStart) || TextUtils.isEmpty(timeEnd)) {
                                return true;
                            }
                            SimpleDateFormat dataFormat = new SimpleDateFormat("HH:mm:ss",
                                    Locale.CHINA);
                            Calendar current = Calendar.getInstance();
                            Calendar calendarStart = Calendar.getInstance();
                            calendarStart.setTime(dataFormat.parse(timeStart));
                            calendarStart.set(current.get(Calendar.YEAR), current.get(Calendar.MONTH), current.get(Calendar.DAY_OF_MONTH));
                            Calendar calendarEnd = Calendar.getInstance();
                            calendarEnd.setTime(dataFormat.parse(timeEnd));
                            calendarEnd.set(current.get(Calendar.YEAR), current.get(Calendar.MONTH), current.get(Calendar.DAY_OF_MONTH));
                            Log.d(TAG, "isOverDue calendarStart: " + calendarStart
                                    + ", calendarEnd: " + calendarEnd + ", current: " + current);
                            if (MsgUtils.compareTime(current, calendarStart)
                                    && MsgUtils.compareTime(calendarEnd, current)) {
                                return false;
                            }
                        }
                    }
                }
            }
        } catch (JSONException | ParseException e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public String toString() {
        return "MsgDetailBean{" +
                "detailId='" + detailId + '\'' +
                ", configVersion='" + configVersion + '\'' +
                ", pushTaskId='" + pushTaskId + '\'' +
                ", resType=" + resType +
                ", resText='" + resText + '\'' +
                ", resImage='" + resImage + '\'' +
                ", resVideo='" + resVideo + '\'' +
                ", playTimes=" + playTimes +
                ", recommendTimes=" + recommendTimes +
                ", recommendPlayTimes=" + recommendPlayTimes +
                ", expireIn='" + expireIn + '\'' +
                ", clickActions='" + clickActions + '\'' +
                ", isDeleted=" + isDeleted +
                '}';
    }
}
