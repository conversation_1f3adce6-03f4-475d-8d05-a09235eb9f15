package com.ainirobot.platform.msg.data;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;

import com.ainirobot.coreservice.data.provider.BaseDataHelper;
import com.ainirobot.platform.msg.bean.MsgConstants;
import com.ainirobot.platform.msg.data.bean.MsgLimitBean;

import java.util.HashMap;
import java.util.Map;

import static com.ainirobot.platform.msg.bean.MsgConstants.AUTHORITY_URI;

public class LimitDataHelper extends BaseDataHelper {

    private static final String TAG = LimitDataHelper.class.getSimpleName();
    private static final Uri CONTENT_URI = Uri.withAppendedPath(AUTHORITY_URI, MsgConstants.LimitColumns.TABLE_NAME);

    public LimitDataHelper(Context context) {
        super(context);
    }

    @Override
    public Uri getContentUri() {
        return CONTENT_URI;
    }

    public Uri insertLimit(MsgLimitBean limitBean) {
        return insert(limitBeanToContentValue(limitBean));
    }

    public Map<String, MsgLimitBean> getLimits() {
        Map<String, MsgLimitBean> beans = new HashMap<>();
        Cursor cursor = query(null, null);
        while (cursor != null && cursor.moveToNext()) {
            MsgLimitBean bean = cursorToMsgLimitBean(cursor);
            beans.put(bean.getLimitId(), bean);
        }
        if (cursor != null) {
            cursor.close();
        }
        return beans;
    }

    private ContentValues limitBeanToContentValue(MsgLimitBean limitBean) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(MsgConstants.LimitColumns.LIMIT_ID, limitBean.getLimitId());
        contentValues.put(MsgConstants.LimitColumns.LIMIT_TYPE, limitBean.getLimitType());
        contentValues.put(MsgConstants.LimitColumns.OPK_ID, limitBean.getAppId());
        contentValues.put(MsgConstants.LimitColumns.OPK_VERSION, limitBean.getAppVersion());
        contentValues.put(MsgConstants.LimitColumns.OPK_PATH, limitBean.getAppPath());
        return contentValues;
    }

    private MsgLimitBean cursorToMsgLimitBean(Cursor cursor) {
        MsgLimitBean limitBean = new MsgLimitBean();
        limitBean.setLimitId(cursor.getString(cursor.getColumnIndex(MsgConstants.LimitColumns.LIMIT_ID)));
        limitBean.setLimitType(cursor.getInt(cursor.getColumnIndex(MsgConstants.LimitColumns.LIMIT_TYPE)));
        limitBean.setAppId(cursor.getString(cursor.getColumnIndex(MsgConstants.LimitColumns.OPK_ID)));
        limitBean.setAppVersion(cursor.getString(cursor.getColumnIndex(MsgConstants.LimitColumns.OPK_VERSION)));
        limitBean.setAppPath(cursor.getString(cursor.getColumnIndex(MsgConstants.LimitColumns.OPK_PATH)));
        return limitBean;
    }

    int deleteLimit(String limitId) {
        return delete(MsgConstants.LimitColumns.LIMIT_ID + "=?",
                new String[] {
                        limitId
                });
    }

    public int deleteAll() {
        return delete(null,
                null);
    }
}
