package com.ainirobot.platform.msg.data.bean;

import android.text.TextUtils;

import com.ainirobot.platform.msg.MsgUtils;

public class MsgLimitBean {

    public static final int MSG_LIMIT_TYPE_APP_OBTAIN = 1;
    public static final int MSG_LIMIT_TYPE_BOOT = 2;
    public static final int MSG_LIMIT_TYPE_OTA = 3;
    public static final int MSG_LIMIT_TYPE_NOTIFY = 4;
    public static final int MSG_LIMIT_TYPE_CALL_OPK = 5;

    private String limitId;
    private String limitName;
    private int limitType;
    private String appId;
    private String appVersion;
    private String appPath;

    public MsgLimitBean() {}

    public MsgLimitBean(int limitType, String limitName, String appId, String appVersion,
                        String appPath) {
        this.limitType = limitType;
        this.limitName = limitName;
        this.appId = appId;
        this.appVersion = appVersion;
        this.appPath = appPath;
        this.limitId = generalLimitId();
    }

    public String getLimitId() {
        return limitId;
    }

    public void setLimitId(String limitId) {
        this.limitId = limitId;
    }

    public int getLimitType() {
        return limitType;
    }

    public void setLimitType(int limitType) {
        this.limitType = limitType;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getAppPath() {
        return appPath;
    }

    public void setAppPath(String appPath) {
        this.appPath = appPath;
    }

    public boolean isPair(int type, String appId, String appPath) {
        if (type != this.limitType) {
            return false;
        }

        // if both no appId, directly return true
        if (TextUtils.isEmpty(appId) && TextUtils.isEmpty(this.appId)) {
            return true;
        }

        if (TextUtils.isEmpty(appId) || TextUtils.isEmpty(this.appId)) {
            return false;
        }
        if (!appId.equals(this.appId)) {
            return false;
        }

        if (!TextUtils.isEmpty(this.appVersion)
                && !MsgUtils.checkOpkVersion(this.appId, this.appVersion)) {
            return false;
        }

        if (TextUtils.isEmpty(appPath) || TextUtils.isEmpty(this.appPath)) {
            return true;
        } else return appPath.equals(this.appPath);
    }

    private String generalLimitId() {
        return "limit_" + limitName + "_" + appId + "_" + appVersion + "_" + appPath;
    }

    @Override
    public String toString() {
        return "MsgLimitBean{" +
                "limitId='" + limitId + '\'' +
                ", limitType=" + limitType +
                ", limitName=" + limitName + '\'' +
                ", appId='" + appId + '\'' +
                ", appVersion='" + appVersion + '\'' +
                ", appPath='" + appPath + '\'' +
                '}';
    }
}
