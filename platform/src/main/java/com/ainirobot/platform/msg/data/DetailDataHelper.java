package com.ainirobot.platform.msg.data;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.util.Log;

import com.ainirobot.coreservice.data.provider.BaseDataHelper;
import com.ainirobot.platform.msg.bean.MsgConstants;
import com.ainirobot.platform.msg.data.bean.MsgDetailBean;

import java.util.HashMap;
import java.util.Map;

import static com.ainirobot.platform.msg.bean.MsgConstants.AUTHORITY_URI;

public class DetailDataHelper extends BaseDataHelper {

    private static final String TAG = DetailDataHelper.class.getSimpleName();
    private static final Uri CONTENT_URI = Uri.withAppendedPath(AUTHORITY_URI, MsgConstants.DetailColumns.TABLE_NAME);

    public DetailDataHelper(Context context) {
        super(context);
    }

    @Override
    public Uri getContentUri() {
        return CONTENT_URI;
    }

    private MsgDetailBean cursorToDetailBean(Cursor cursor) {
        MsgDetailBean msgDetailBean = new MsgDetailBean();
        msgDetailBean.setDetailId(cursor.getString(cursor.getColumnIndex(MsgConstants.DetailColumns.DETAIL_ID)));
        msgDetailBean.setConfigVersion(cursor.getString(cursor.getColumnIndex(MsgConstants.DetailColumns.CONFIG_VERSION)));
        msgDetailBean.setPushTaskId(cursor.getString(cursor.getColumnIndex(MsgConstants.DetailColumns.PUSH_TASK_ID)));
        msgDetailBean.setResType(cursor.getInt(cursor.getColumnIndex(MsgConstants.DetailColumns.RES_TYPE)));
        msgDetailBean.setResText(cursor.getString(cursor.getColumnIndex(MsgConstants.DetailColumns.RES_TEXT)));
        msgDetailBean.setResImage(cursor.getString(cursor.getColumnIndex(MsgConstants.DetailColumns.RES_IMAGE)));
        msgDetailBean.setResVideo(cursor.getString(cursor.getColumnIndex(MsgConstants.DetailColumns.RES_VIDEO)));
        msgDetailBean.setPlayTimes(cursor.getInt(cursor.getColumnIndex(MsgConstants.DetailColumns.PLAY_TIMES)));
        msgDetailBean.setRecommendTimes(cursor.getInt(cursor.getColumnIndex(MsgConstants.DetailColumns.RECOMMEND_TIMES)));
        msgDetailBean.setRecommendPlayTimes(cursor.getInt(cursor.getColumnIndex(MsgConstants.DetailColumns.RECOMMEND_PLAY_TIMES)));
        msgDetailBean.setExpireIn(cursor.getString(cursor.getColumnIndex(MsgConstants.DetailColumns.EXPIRES_IN)));
        msgDetailBean.setClickActions(cursor.getString(cursor.getColumnIndex(MsgConstants.DetailColumns.CLICK_ACTIONS)));
        msgDetailBean.setDeleted(cursor.getInt(cursor.getColumnIndex(MsgConstants.DetailColumns.DELETED)) == 1);
        return msgDetailBean;
    }

    private ContentValues msgBeanToContentValue(MsgDetailBean msgDetailBean) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(MsgConstants.DetailColumns.DETAIL_ID, msgDetailBean.getDetailId());
        contentValues.put(MsgConstants.DetailColumns.CONFIG_VERSION, msgDetailBean.getConfigVersion());
        contentValues.put(MsgConstants.DetailColumns.PUSH_TASK_ID, msgDetailBean.getPushTaskId());
        contentValues.put(MsgConstants.DetailColumns.RES_TYPE, msgDetailBean.getResType());
        contentValues.put(MsgConstants.DetailColumns.RES_TEXT, msgDetailBean.getResText());
        contentValues.put(MsgConstants.DetailColumns.RES_IMAGE, msgDetailBean.getResImage());
        contentValues.put(MsgConstants.DetailColumns.RES_VIDEO, msgDetailBean.getResVideo());
        contentValues.put(MsgConstants.DetailColumns.RECOMMEND_TIMES, msgDetailBean.getRecommendTimes());
        contentValues.put(MsgConstants.DetailColumns.EXPIRES_IN, msgDetailBean.getExpireIn());
        contentValues.put(MsgConstants.DetailColumns.CLICK_ACTIONS, msgDetailBean.getClickActions());
        return contentValues;
    }

    public MsgDetailBean getDetail(String detailId) {
        MsgDetailBean bean = null;
        Cursor cursor = query(MsgConstants.DetailColumns.DETAIL_ID + "=?", new String[] {
                detailId
        });
        if (cursor != null && cursor.moveToNext()) {
            bean = cursorToDetailBean(cursor);
        }
        if (cursor != null) {
            cursor.close();
        }
        return bean;
    }

    public Map<String, MsgDetailBean> getDetails() {
        Map<String, MsgDetailBean> beans = new HashMap<>();
        Cursor cursor = query(MsgConstants.DetailColumns.DELETED + "=?", new String[] {
                0 + ""
        });
        while (cursor != null && cursor.moveToNext()) {
            MsgDetailBean bean = cursorToDetailBean(cursor);
            beans.put(bean.getDetailId(), bean);
        }
        if (cursor != null) {
            cursor.close();
        }
        return beans;
    }

    public void insertDetail(MsgDetailBean msgDetailBean) {
        MsgDetailBean bean = getDetail(msgDetailBean.getDetailId());
        if (bean == null) {
            insert(msgBeanToContentValue(msgDetailBean));
        } else {
            updateDetail(msgDetailBean);
        }
    }

    public int updateDetail(MsgDetailBean msgDetailBean) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(MsgConstants.DetailColumns.DETAIL_ID, msgDetailBean.getDetailId());
        return update(msgBeanToContentValue(msgDetailBean),
                MsgConstants.DetailColumns.DETAIL_ID + "=?", new String[] {
                        msgDetailBean.getDetailId()
                });
    }

    public int updatePlayTimes(String detailId, int playTimes) {
        Log.d(TAG, "updatePlayTimes detailId: " + detailId + ", playTimes: " + playTimes);
        ContentValues contentValues = new ContentValues();
        contentValues.put(MsgConstants.DetailColumns.PLAY_TIMES, playTimes);
        return update(contentValues,
                MsgConstants.DetailColumns.DETAIL_ID + "=?", new String[] {
                        detailId
                });
    }

    public int updateRecommendPlayTimes(String detailId, int playTimes) {
        Log.d(TAG, "updateRecommendPlayTimes detailId: " + detailId + ", playTimes: " + playTimes);
        ContentValues contentValues = new ContentValues();
        contentValues.put(MsgConstants.DetailColumns.RECOMMEND_PLAY_TIMES, playTimes);
        return update(contentValues,
                MsgConstants.DetailColumns.DETAIL_ID + "=?", new String[] {
                        detailId
                });
    }

    public int updateDeleted(String detailId, boolean deleted) {
        Log.d(TAG, "updateDeleted detailId: " + detailId + ", deleted: " + deleted);
        ContentValues contentValues = new ContentValues();
        contentValues.put(MsgConstants.DetailColumns.DELETED, deleted ? 1 : 0);
        return update(contentValues,
                MsgConstants.DetailColumns.DETAIL_ID + "=?", new String[] {
                        detailId
                });
    }

    public int deleteDetail(String detailId) {
        return delete(MsgConstants.DetailColumns.DETAIL_ID + "=?",
                new String[]{
                        detailId
                });
    }
}
