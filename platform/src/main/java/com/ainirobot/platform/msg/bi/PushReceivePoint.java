/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.platform.msg.bi;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

public class PushReceivePoint extends BiNativeReport {

    private static final String TABLE_NAME = "base_robot_push_receive";
    private String push_id;
    private String config_id;
    private String config_version;
    private String content;
    private long ctime;

    public PushReceivePoint(String pushId, String configId, String configVersion, String content, long ctime) {
        super(TABLE_NAME);
        this.push_id = pushId;
        this.config_id = configId;
        this.config_version = configVersion;
        this.content = content;
        this.ctime = ctime;
    }
}
