package com.ainirobot.platform.msg.data.bean;

import com.ainirobot.platform.bean.net.video.db.IFileDataBase;

import java.util.Objects;

public class MsgBean {

    private String msgId;
    private transient String limitId;
    private String detailId;
    private String title;
    private int priority;
    private long createTime;

    public MsgBean() {}

    public MsgBean(String limitId, String detailId, String title, int priority, long create_time) {
        this.limitId = limitId;
        this.detailId = detailId;
        this.title = title;
        this.priority = priority;
        this.createTime = create_time;
        generalId();
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public String getLimitId() {
        return limitId;
    }

    public void setLimitId(String limitId) {
        this.limitId = limitId;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long create_time) {
        this.createTime = create_time;
    }

    public boolean isLowPriority(MsgBean bean) {
        if (bean.getPriority() == this.priority) {
            return bean.getCreateTime() > this.createTime;
        }
        return bean.getPriority() < this.priority;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MsgBean msgBean = (MsgBean) o;
        return detailId.equals(msgBean.detailId) &&
                priority == msgBean.priority &&
                title.equals(msgBean.title);
    }

    @Override
    public int hashCode() {
        return Objects.hash(msgId, title, priority);
    }

    public void generalId() {
        this.msgId = "msg_" + detailId + "_" + limitId;
    }

    @Override
    public String toString() {
        return "MsgBean{" +
                "msgId='" + msgId + '\'' +
                ", limitId='" + limitId + '\'' +
                ", detailId='" + detailId + '\'' +
                ", title='" + title + '\'' +
                ", priority=" + priority + '\'' +
                ", create_time=" + createTime +
                '}';
    }
}
