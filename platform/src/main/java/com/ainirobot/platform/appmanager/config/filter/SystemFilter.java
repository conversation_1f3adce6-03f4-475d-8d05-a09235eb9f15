package com.ainirobot.platform.appmanager.config.filter;

import com.ainirobot.platform.appmanager.config.storage.IStorage;

import java.util.HashMap;

public class SystemFilter implements IStorage {

    private static final String TAG = SystemFilter.class.getSimpleName();

    private IStorage mStorage;
    private IConvert mConvert;

    public SystemFilter(IStorage storage,IConvert convert) {
        this.mStorage = storage;
        this.mConvert = convert;
    }

    @Override
    public boolean isExistConfig(String appId) {
        return mStorage.isExistConfig(appId);
    }

    @Override
    public HashMap<String, Object> readConfig(String appId) {
        HashMap<String, Object> readConfig = mStorage.readConfig(appId);
        return mConvert.convertToLocal(readConfig);
    }

    @Override
    public boolean saveConfig(String appId, HashMap<String, Object> settings) {
        HashMap<String, Object> tmp = mConvert.convertToServer(settings);
        return mStorage.saveConfig(appId, tmp);
    }
}
