package com.ainirobot.platform.appmanager.config.impl;

import android.util.Log;

import com.ainirobot.platform.appmanager.config.UpdateListener;
import com.ainirobot.platform.appmanager.config.bean.ConfigItem;
import com.ainirobot.platform.appmanager.config.cloud.ICloud;
import com.ainirobot.platform.appmanager.config.storage.IStorage;
import com.google.gson.Gson;

import java.util.HashMap;

public class NormalConfig implements ISettingConfig {

    private static final String TAG = NormalConfig.class.getSimpleName();

    protected Gson mGson;

    private ICloud mCloud;
    private String mAppId;
    private IStorage mStorage;
    private UpdateListener mListener;

    public NormalConfig(String appId, IStorage storage, ICloud cloud) {
        this.mAppId = appId;
        this.mCloud = cloud;
        this.mStorage = storage;
        this.mGson = new Gson();
    }

    protected void setStorage(IStorage storage) {
        this.mStorage = storage;
    }

    HashMap<String, Object> getSettings() {
        return mStorage.readConfig(this.mAppId);
    }

    @Override
    public void registerUpdateListener(UpdateListener listener) {
        this.mListener = listener;
    }

    @Override
    public void setConfig(ConfigItem item) {
        if (!mStorage.isExistConfig(item.getAppId())) {
            Log.i(TAG, "setConfig config file not exist");
            return;
        }
        HashMap<String, Object> settings = prepareSettings();
        HashMap<String, Object> tmpSettings = item.getSettings();
        Log.i(TAG, "setConfig : " + mGson.toJson(settings) + " changeValue: " + mGson.toJson(tmpSettings));
        if (checkConfig(settings)) {
            if (tmpSettings != null && tmpSettings.size() > 0) {
                settings.putAll(tmpSettings);
            }
            mStorage.saveConfig(item.getAppId(), settings);
        }

        switch (item.getType()) {
            case COVER:
                if (mListener != null) {
                    mListener.onUpdate(mGson.toJson(mStorage.readConfig(mAppId)));
                }
                break;
            case FIRST_UPDATE:
            case UPDATE:
                mCloud.uploadSettings(item.getAppId());
                break;
            default:
                break;
        }
    }

    protected HashMap<String, Object> prepareSettings() {
        Log.i(TAG, "prepareSettings : " + mGson.toJson(getSettings()));
        return getSettings();
    }

    protected boolean checkConfig(HashMap<String, Object> settings) {
        return settings != null;
    }
}
