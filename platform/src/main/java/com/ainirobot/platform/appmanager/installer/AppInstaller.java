package com.ainirobot.platform.appmanager.installer;

import android.os.Environment;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import com.ainirobot.coreservice.client.ApiListener;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.platform.PlatformDef;
import com.ainirobot.platform.appmanager.AppInfo;
import com.ainirobot.platform.appmanager.AppResolver;
import com.ainirobot.platform.appmanager.task.AppInstall;
import com.ainirobot.platform.appmanager.task.AppOperTask;
import com.ainirobot.platform.appmanager.task.AppOperTask.Result;
import com.ainirobot.platform.appmanager.task.AppOperTask.StatusListener;
import com.ainirobot.platform.appmanager.task.AppUninstall;
import com.ainirobot.platform.appmanager.task.UpdateConfig;
import com.ainirobot.platform.bean.ConfigBean;
import com.ainirobot.platform.character.CharacterManager;
import com.ainirobot.platform.react.AppBeanV2;
import com.ainirobot.platform.react.AppManger;
import com.ainirobot.platform.react.OPKBeanV3;
import com.ainirobot.platform.utils.FileHelper;
import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;

public class AppInstaller {

    /**
     * 安装目录
     */
    private static final String INSTALL_DIR = "/robot/rndata";

    /**
     * 配置文件目录
     */
    private static final String CONFIG_DIR = "/robot/rnconfig";

    public String TAG;

    public Gson mGson;

    private ExecutorService mExecutor = Executors.newSingleThreadExecutor();

    private LinkedBlockingQueue<AppOperTask> mTasks = new LinkedBlockingQueue<>();
    private List<AppOperTask> mFailedTasks = new ArrayList<>();

    public AppListener mListener = new AppListener();

    private JSONArray mReportContent = new JSONArray();
    private final Map<String, String> mUploadSettingList = Collections.synchronizedMap(new HashMap<String, String>());
    private final List<String> mSyncedApp = new ArrayList<>();

    private boolean isPreparing = false;

    private boolean isUpdating = false;

    private boolean isRunning = false;
    private Future mFuture;

    private final Object LOCK = new Object();

    public AppInstaller() {
        this.TAG = this.getClass().getSimpleName();
        this.mGson = new Gson();
        setCoreConnectedListener();
    }

    public AppInstaller(String name) {
        this.TAG = this.getClass().getSimpleName() + "[" + name + "]";
        this.mGson = new Gson();
        setCoreConnectedListener();
    }

    public boolean isUpdating() {
        return (isPreparing || isUpdating) && !isAllCfgTask();
    }

    public boolean isRunning() {
        return isRunning;
    }

    public synchronized void start() {
        Log.d(TAG, "On start ******  " + isRunning);
        if (isRunning) {
            return;
        }
        isRunning = true;
        isPreparing = true;
        mFuture = mExecutor.submit(new Runnable() {
            @Override
            public void run() {
                handleAppUpdate();
            }
        });
    }

    public synchronized void stop() {
        Log.d(TAG, "On stop ******  " + isRunning);
        if (!isRunning) {
            return;
        }
        isRunning = false;
        isPreparing = false;
        mFuture.cancel(true);
        mFuture = null;
    }

    /**
     * 处理App更新
     */
    public void handleAppUpdate() {
        Log.d(TAG, "Start app update task");
        while (true) {
            try {
                if (mTasks.isEmpty()) {
                    Log.d(TAG, "App update finished");
                    isUpdating = false;
                    isPreparing = false;
                    mListener.onFinish();
                }

                AppOperTask task = mTasks.take();
                if (!isUpdating) {
                    Log.d(TAG, "Start app update : " + mTasks.size());
                    mFailedTasks.clear();
                    isUpdating = true;
                    isPreparing = false;
                    mListener.onStart(task, mTasks.size() + 1);
                }

                Result result = runTask(task);
                if (!mListener.onAppUpdate(this, task.getType(), task.mInfo.appId, result, mTasks.size())) {
                    installOpk(task.mInfo.appId, result.appInfo);
                    saveConfig(task.mInfo.appId, task.mInfo.configId, result.configPath);
                }

                AppResolver.deleteAppTask(task.mInfo);
                Log.d(TAG, "App install finished");
                if (result.result) {
                    Log.d(TAG, "App install succeed : " + task.mInfo.appName + "   " + task.getStatus() + "  " + task.getErrorCode());
                } else {
                    mFailedTasks.add(task);
                    Log.d(TAG, "App install failed : " + task.mInfo.appName + "   " + task.getStatus() + "  " + task.getErrorCode());
                    // TODO: 安装失败处理
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 失败重试
     */
    public void failedRetry() {
        if (getFailedTasks().isEmpty()) {
//            mListener.onStart(null, 0);
            mListener.onFinish();
            return;
        }
        List<AppOperTask> failedList = new ArrayList<>(getFailedTasks());
        for (AppOperTask task : failedList) {
            task.prepare();
            mTasks.offer(task);
        }
    }

    public AppOperTask createTask(AppInfo info) {
        AppOperTask task;
        switch (info.operate) {
            case AppOperTask.OPER_UNINSTALL:
                task = new AppUninstall(info);
                break;

            case AppOperTask.OPER_HOST_CONFIG_PORTAL:
                task = new UpdateConfig(info);
                break;

            default:
                task = new AppInstall(info);
                break;
        }

        task.setStatusListener(new StatusListener() {
            @Override
            public void onStatusUpdate(String status, String data) {
                reportStatus(data);
            }
        });
        return task;
    }

    public boolean addTask(AppInfo info) {
        AppOperTask task = createTask(info);

        if (!mTasks.contains(task) && task.prepare()) {
            Log.d(TAG, "Add app task : " + mGson.toJson(task.mInfo));
            mTasks.add(task);
            return true;
        } else {
            Log.d(TAG, "Add app task prepare failed : " + mGson.toJson(task.mInfo) + "  " + mTasks.contains(task));
            AppResolver.deleteAppTask(info);
            return false;
        }
    }

    public boolean addTask(AppOperTask task) {
        if (!mTasks.contains(task)) {
            mTasks.add(task);
            return true;
        } else {
            Log.d(TAG, "Add app task prepare failed : " + mGson.toJson(task.mInfo) + "  " + mTasks.contains(task));
            AppResolver.deleteAppTask(task.mInfo);
            return false;
        }
    }

    public boolean isAllCfgTask() {
        for (AppOperTask task : mTasks) {
            if (!AppOperTask.TYPE_CONFIG.equals(task.getType())) {
                Log.d(TAG, "isOpkTask:" + task.toString());
                return false;
            }
        }
        Log.d(TAG, "isAllCfgTask:" + ((mTasks != null) ? mTasks.size() : "null"));
        return true;
    }

    public List<AppOperTask> getFailedTasks() {
        return mFailedTasks;
    }

    private Result runTask(AppOperTask task) {
        Result result = task.run();
        if (task.mInfo.isSyncSetting) {
            syncConfigToServer(task.mInfo.appId, result.configPath);
        }
        return result;
    }

    /**
     * 安装OPk
     *
     * @param appId
     * @param appInfo
     */
    public void installOpk(String appId, AppBeanV2 appInfo) {
        Log.d(TAG, "Install opk : " + appId + "  " + appInfo);
        if (appInfo == null) {
            return;
        }

        File installDir = mkInstallDir(appId);
        try {
            Log.d(TAG, "Copy opk dir : " + appInfo.getRpkBean().getAppName() + "   " + installDir.getAbsolutePath());
            FileHelper.deleteDirectory(installDir);
            FileHelper.copyDirectory(new File(Objects.requireNonNull(appInfo.getRpkBean().getPath())), installDir);
            if (appInfo.getBundleIndexFilePath() == null) {
                appInfo.setBundleBizFilePath(installDir.getAbsolutePath() + "/biz.android.bundle");
                appInfo.setBundlePlatformFilePath(installDir.getAbsolutePath() + "/platform.android.bundle");
            } else {
                appInfo.setBundleIndexFilePath(installDir.getAbsolutePath() + "/index.android.bundle");
            }

            appInfo.getRpkBean().setPath(installDir.getAbsolutePath());

            AppManger.INSTANCE.addApp(appInfo);

            if (!AppInfo.OPK_PLUGIN.equals(appInfo.getRpkBean().getType())) {
                CharacterManager.getInstance().registerRNCharacter(appId);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 保存配置文件
     *
     * @param appId
     * @param configId
     * @param configPath
     */
    public void saveConfig(String appId, String configId, String configPath) {
        Log.d(TAG, "Save config : " + appId);
        if (TextUtils.isEmpty(configPath)) {
            Log.d(TAG, "Save config : config not exits " + appId);
            return;
        }

        OPKBeanV3 opkInfo = AppManger.INSTANCE.getRPKByCharacter(appId);
        if (opkInfo == null) {
            Log.d(TAG, "Save config failed : opk not exits " + appId);
            return;
        }

        try {
            File appConfig = mkConfigDir(appId);
            Log.d(TAG, "Save config  : " + configPath + "  " + new File(configPath).exists() + " " + appConfig.getAbsolutePath());
            FileHelper.copyFile(new File(configPath), appConfig);
            opkInfo.setConfigId(configId);
            opkInfo.setConfigPath(appConfig.getAbsolutePath());
            AppManger.INSTANCE.updateApp(opkInfo);
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, "Save config failed : file read error " + appId);
        }
    }

    /**
     * 创建存储目录
     *
     * @return
     */
    private File mkInstallDir(String appId) {
        return getPath(INSTALL_DIR + "/" + appId);
    }

    private File mkConfigDir(String appId) {
        File dir = getPath(CONFIG_DIR);
        File file = new File(dir, appId + ".cfg");
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return file;
    }

    private File getPath(String dirName) {
        File dir = new File(Environment.getExternalStorageDirectory(), dirName);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir;
    }

    /**
     * 上报任务状态到服务端
     * 如果当前还未连接，暂存起来，等到连接完成后重新发送
     *
     * @param data
     */
    private void reportStatus(String data) {
        try {
            synchronized(LOCK){
                if (data != null) {
                    mReportContent.put(new JSONObject(data));
                }
                if (RobotApi.getInstance().isApiConnectedService()) {
                    RobotApi.getInstance().sendStatusReport("appTask", mReportContent.toString());
                    mReportContent = new JSONArray();
                }
            }
        } catch (JSONException e) {
            Log.d(TAG, "Report status failed : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void syncSetting() {
        for (Map.Entry<String, String> entry : mUploadSettingList.entrySet()) {
            syncConfigToServer(entry.getKey(), entry.getValue());
        }
    }

    /**
     * 同步本地设置到服务端
     *
     * @param appId
     * @param path
     */
    private void syncConfigToServer(String appId, String path) {
        try {
            if (TextUtils.isEmpty(path)) {
                Log.d(TAG, "Sync setting to server failed : " + path);
                return;
            }

            if (RobotApi.getInstance().isApiConnectedService()) {
                //已同步过的App将不再进行配置同步
                if (mSyncedApp.contains(appId)) {
                    return;
                }

                String config = FileHelper.loadJsonFromFile(path);
                JSONObject json = new JSONObject(config);
                JSONObject appConfig = json.getJSONObject("app_config");
                String dataConfig = new String(Base64.decode(appConfig.getString("cfg_data"), Base64.DEFAULT));
                ConfigBean configBean = new ConfigBean(appId, dataConfig);
                configBean.updateData();
                appConfig.put("cfg_data", configBean.getConfigData());
                configBean.mergeConfigData();
                RobotApi.getInstance().sendStatusReport("appSettingUpload", mGson.toJson(configBean));
                Log.d(TAG, "Sync setting to server : " + mGson.toJson(configBean));

                mSyncedApp.add(appId);
            } else {
                mUploadSettingList.put(appId, path);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void onApiConnected() {
        syncSetting();
        reportStatus(null);
    }

    /**
     * 设置CoreService连接监听
     */
    private void setCoreConnectedListener() {
        RobotApi.getInstance().addApiEventListener(new ApiListener() {
            @Override
            public void handleApiDisabled() {

            }

            @Override
            public void handleApiConnected() {
                onApiConnected();
            }

            @Override
            public void handleApiDisconnected() {

            }
        });
    }

    public void setListener(AppListener listener) {
        this.mListener = listener;
    }

    public static class AppListener {

        /**
         * 更新开始
         *
         * @param task  首个任务
         * @param count 任务总数
         */
        public void onStart(AppOperTask task, int count) {

        }

        /**
         * App更新
         *
         * @param installer
         * @param operType       App操作类型
         * @param appId
         * @param result         App更新结果
         * @param notUpdateCount 剩余未更新的数量
         * @return 是否已处理 true 按照调用方策略处理  false 按默认策略处理
         */
        public boolean onAppUpdate(AppInstaller installer, String operType, String appId, Result result, int notUpdateCount) {
            return false;
        }

        public void onFinish() {

        }

    }
}
