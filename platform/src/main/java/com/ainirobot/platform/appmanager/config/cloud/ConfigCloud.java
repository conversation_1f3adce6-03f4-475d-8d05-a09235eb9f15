package com.ainirobot.platform.appmanager.config.cloud;

import android.os.Handler;
import android.os.HandlerThread;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import com.ainirobot.coreservice.client.ApiListener;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.platform.appmanager.config.impl.SystemConfig;
import com.ainirobot.platform.bean.ConfigBean;
import com.ainirobot.platform.react.AppManger;
import com.ainirobot.platform.react.OPKBeanV3;
import com.ainirobot.platform.utils.FileHelper;
import com.google.gson.Gson;

import org.json.JSONObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ConfigCloud implements ICloud {

    private static final String TAG = ConfigCloud.class.getSimpleName();

    private final ConcurrentHashMap<String, String> mUploadSettingList = new ConcurrentHashMap<String, String>();

    private Gson mGson;

    private Handler mHandler;

    public ConfigCloud(Gson gson) {
        this.mGson = gson;
        HandlerThread thread = new HandlerThread("upload config ");
        thread.start();
        mHandler = new Handler(thread.getLooper());
        setCoreConnectedListener();
    }

    @Override
    public void uploadSettings(final String appId) {
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                syncConfigToServer(appId, getConfigPath(appId));
            }
        });
    }

    private String getConfigPath(String appId) {
        if (TextUtils.equals(SystemConfig.APP_ID, appId)) {
            return SystemConfig.CONFIG_PATH;
        }

        OPKBeanV3 opkInfo = AppManger.INSTANCE.getRPKByCharacter(appId);
        if (opkInfo != null && !TextUtils.isEmpty(opkInfo.getConfigPath())) {
            return opkInfo.getConfigPath();
        }
        return null;
    }

    /**
     * 同步本地设置到服务端
     *
     * @param appId
     * @param path
     */
    private void syncConfigToServer(String appId, String path) {
        try {
            if (TextUtils.isEmpty(path)) {
                Log.d(TAG, "Sync setting to server failed : " + path);
                return;
            }

            if (RobotApi.getInstance().isApiConnectedService()) {
                //已同步过的App将不再进行配置同步
                String config = FileHelper.loadJsonFromFile(path);
                JSONObject json = new JSONObject(config);
                JSONObject appConfig = json.getJSONObject("app_config");
                String dataConfig = new String(Base64.decode(appConfig.getString("cfg_data"), Base64.DEFAULT));
                ConfigBean configBean = new ConfigBean(appId, dataConfig);
                configBean.updateData();
                appConfig.put("cfg_data", configBean.getConfigData());
                configBean.mergeRobotData();
                RobotApi.getInstance().sendStatusReport("appSettingUpload", mGson.toJson(configBean));
                Log.d(TAG, "Sync setting to server : " + mGson.toJson(configBean));
            } else {
                mUploadSettingList.put(appId, path);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 设置CoreService连接监听
     */
    private void setCoreConnectedListener() {
        RobotApi.getInstance().addApiEventListener(new ApiListener() {
            @Override
            public void handleApiDisabled() {

            }

            @Override
            public void handleApiConnected() {
                syncSetting();
            }

            @Override
            public void handleApiDisconnected() {

            }
        });
    }

    private void syncSetting() {
        for (Map.Entry<String, String> entry : mUploadSettingList.entrySet()) {
            syncConfigToServer(entry.getKey(), entry.getValue());
        }
    }
}
