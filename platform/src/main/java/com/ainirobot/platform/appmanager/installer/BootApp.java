package com.ainirobot.platform.appmanager.installer;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.platform.appmanager.AppInfo;
import com.ainirobot.platform.appmanager.AppResolver;
import com.ainirobot.platform.appmanager.AppResolver.AppObserver;
import com.ainirobot.platform.appmanager.installer.AppInstaller.AppListener;
import com.ainirobot.platform.appmanager.task.AppOperTask;
import com.ainirobot.platform.appmanager.task.AppOperTask.Result;
import com.ainirobot.platform.react.AppManger;
import com.ainirobot.platform.react.OPKBeanV3;
import com.google.gson.Gson;

import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 开机加载OPK列表
 */
public class BootApp {

    private static final String TAG = "BootApp";

    private static boolean isStarted = false;

    private static AppInstaller sAppInstaller = new AppInstaller("BootApp");

    private static AppInstaller sPluginInstaller = new AppInstaller("BootAppPlugin");

    private static AppListenerProxy sAppListener = new AppListenerProxy();
    private static AppListenerProxy sPluginListener = new AppListenerProxy();

    private static LinkedBlockingQueue<Boolean> sEvent = new LinkedBlockingQueue<>();

    private static final Object DATA_LOCK = new Object();

    /**
     * 30秒超时
     */
    private static final long DATA_TIMEOUT = 30 * 1000;

    /**
     * 是否已加载数据
     */
    private static volatile boolean isLoadData = false;

    private static volatile boolean isStopped = false;

    /**
     * 注册预置OPK更新事件
     */
    private static void registerObserver() {
        final AppInfo portal = AppResolver.getPortalOpk();

        AppResolver.registerBootAppObserver(new AppObserver() {
            @Override
            public void onAppUpdate(List<AppInfo> taskInfos) {
                Log.d(TAG, "On boot app : " + taskInfos.size() + "   " + isStopped);
                if (isStopped) {
                    return;
                }

                isLoadData = true;
                if (portal != null) {
                    Log.d(TAG, "On boot app portal : " + portal.appId);
                    taskInfos.add(0, portal);
                }

                for (AppInfo info : taskInfos) {
                    addTask(info);
                }

                sPluginInstaller.start();
                sAppInstaller.start();

                if (taskInfos.isEmpty()) {
                    sEvent.offer(true);
                }

                synchronized (DATA_LOCK) {
                    DATA_LOCK.notifyAll();
                }
                Log.d(TAG, "On boot app add finished ***** ");
            }
        });
    }

    public static boolean startInstall() {
        Log.d(TAG, "Start boot app install : " + isStarted);
        if (isStarted) {
            return false;
        }

        isStarted = true;
        isLoadData = false;
        sAppInstaller.setListener(sAppListener);
        sPluginInstaller.setListener(sPluginListener);

        registerObserver();

        if (!isLoadData) {
            synchronized (DATA_LOCK) {
                try {
                    Log.d(TAG, "Wait boot app data");
                    DATA_LOCK.wait(DATA_TIMEOUT);
                    Log.d(TAG, "Wait boot app data timeout : " + sAppInstaller.isUpdating());
                    if (!isLoadData) {
                        //超时后
                        return true;
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }

        return waitResult();
    }

    private static void addTask(AppInfo info) {
        //如果OPK类型为空，尝试从本地获取下类型
        if (TextUtils.isEmpty(info.opkType)) {
            OPKBeanV3 opkBean = AppManger.INSTANCE.getRPKByCharacter(info.appId);
            if (opkBean != null) {
                info.opkType = opkBean.getType();
            } else {
                Log.d(TAG, "Add task opk type error : " + new Gson().toJson(info));
                info.opkType = "plugin";
            }
        }

        if(!TextUtils.equals("opk",info.type)){
            info.opkType = "normal";
        }

        if (AppInfo.OPK_PLUGIN.equals(info.opkType)) {
            sPluginInstaller.addTask(info);
        } else {
            sAppInstaller.addTask(info);
        }
    }

    public static boolean retry() {
        Log.d(TAG, "Boot app failed retry start");
        sAppInstaller.failedRetry();
        sPluginInstaller.failedRetry();

        return waitResult();
    }

    private static boolean waitResult() {
        try {
            Log.d(TAG, "Wait result : ");
            while (sEvent.take()) {
                Log.d(TAG, "On boot app status update, app : " + sAppInstaller.isUpdating() + "  plugin : " + sPluginInstaller.isUpdating());
                if (sAppInstaller.isUpdating()
                        || sPluginInstaller.isUpdating()) {
                    continue;
                }

                isStarted = false;
                Log.d(TAG, "Boot app install finished : " + (sPluginInstaller.getFailedTasks().isEmpty()));
                return sPluginInstaller.getFailedTasks().isEmpty();
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 设置插件OPK的更新事件监听
     *
     * @param listener
     */
    public static void setPluginListener(AppListener listener) {
        if (listener != null) {
            sPluginListener.setListener(listener);
        }
    }

    /**
     * 设置普通OPK的更新事件监听
     *
     * @param listener
     */
    public static void setAppListener(AppListener listener) {
        if (listener != null) {
            sAppListener.setListener(listener);
        }
    }

    public static void stop() {
        isStopped = true;
    }

    public static boolean isInstalling() {
        return sAppInstaller.isUpdating() || sPluginInstaller.isUpdating();
    }

    public static boolean isPluginInstalling() {
        return sPluginInstaller.isUpdating();
    }

    public static boolean isFailed() {
        return !(sPluginInstaller.getFailedTasks().isEmpty());
    }

    private static class AppListenerProxy extends AppListener {
        private AppListener mListener;

        public void setListener(AppListener listener) {
            this.mListener = listener;
        }

        @Override
        public void onStart(AppOperTask task, int count) {
            if (this.mListener != null) {
                this.mListener.onStart(task, count);
            }
        }

        @Override
        public boolean onAppUpdate(AppInstaller installer, String operType, String appId, Result result, int notUpdateCount) {
            if (this.mListener != null) {
                return this.mListener.onAppUpdate(installer, operType, appId, result, notUpdateCount);
            }
            return false;
        }

        @Override
        public void onFinish() {
            if (this.mListener != null) {
                this.mListener.onFinish();
            }
            sEvent.offer(true);
        }
    }
}
