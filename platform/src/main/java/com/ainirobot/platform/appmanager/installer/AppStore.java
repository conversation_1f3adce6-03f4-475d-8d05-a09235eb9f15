package com.ainirobot.platform.appmanager.installer;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.platform.appmanager.AppResolver;
import com.ainirobot.platform.appmanager.AppResolver.AppObserver;
import com.ainirobot.platform.appmanager.AppInfo;
import com.ainirobot.platform.appmanager.installer.AppInstaller.AppListener;
import com.ainirobot.platform.appmanager.task.AppOperTask;
import com.ainirobot.platform.react.AppManger;
import com.ainirobot.platform.react.OPKBeanV3;

import java.util.ArrayList;
import java.util.List;

public class AppStore {

    private static final String TAG = "AppStore";

    private static boolean isStarted = false;

    /**
     * 是否允许安装插件OPK
     * OPK配置更新不受影响，只限制OPK的安装更新
     */
    private static boolean isAllowInstallPlugin = true;

    /**
     * 待执行任务列表
     */
    private static List<AppOperTask> sWaitingTasks = new ArrayList<>();

    private static AppInstaller sAppInstaller = new AppInstaller("AppStore");
    private static AppInstaller sPluginInstaller = new AppInstaller("AppStorePlugin");

    /**
     * 注册预置OPK更新事件
     */
    private static void registerObserver() {
        AppResolver.registerUpdateAppObserver(new AppObserver() {
            @Override
            public void onAppUpdate(List<AppInfo> taskInfos) {
                Log.d(TAG, "On app update : " + taskInfos.size());
                for (AppInfo info : taskInfos) {
                    addTask(info);
                }
            }
        });
    }

    private static void addTask(AppInfo info) {
        //如果OPK类型为空，尝试从本地获取下类型
        if (TextUtils.isEmpty(info.opkType)) {
            OPKBeanV3 opkBean = AppManger.INSTANCE.getRPKByCharacter(info.appId);
            if (opkBean != null) {
                info.opkType = opkBean.getType();
            } else {
                Log.d(TAG, "Add task opk type error : " + info.appId);
                info.opkType = "plugin";
            }
        }

        if(!TextUtils.equals("opk",info.type)){
            info.opkType = "normal";
        }

        if (AppInfo.OPK_PLUGIN.equals(info.opkType)) {
            AppOperTask task = sPluginInstaller.createTask(info);
            if (isAllowInstallPlugin || AppOperTask.TYPE_CONFIG.equals(task.getType())) {
                sPluginInstaller.addTask(info);
            } else {
                if (!sWaitingTasks.contains(task) && task.prepare()) {
                    Log.d(TAG, "Add wait app task : " + info.appId + "  " + info.appName);
                    sWaitingTasks.add(task);
                } else {
                    Log.d(TAG, "Add wait app task prepare failed : " + info.appId + "  " + info.appName);
                    AppResolver.deleteAppTask(info);
                }
            }
        } else {
            sAppInstaller.addTask(info);
        }
    }

    public static void start() {
        Log.d(TAG, "AppStore start : " + isStarted);
        if (isStarted) {
            return;
        }
        isStarted = true;
        registerObserver();
        sAppInstaller.start();
        sPluginInstaller.start();
    }

    /**
     * 设置插件OPK的更新事件监听
     *
     * @param listener
     */
    public static void setPluginListener(AppListener listener) {
        if (listener != null) {
            sPluginInstaller.setListener(listener);
        }
    }

    /**
     * 设置普通OPK的更新事件监听
     *
     * @param listener
     */
    public static void setAppListener(AppListener listener) {
        if (listener != null) {
            sAppInstaller.setListener(listener);
        }
    }

    public static void setAllowInstallPlugin(boolean isAllow) {
        Log.d(TAG, "Set allow install plugin : " + isAllow);
        isAllowInstallPlugin = isAllow;
        if (isAllowInstallPlugin) {
            for (AppOperTask task : sWaitingTasks) {
                sPluginInstaller.addTask(task);
            }
            sWaitingTasks.clear();
        }
    }

    public static boolean isRunning() {
        return isStarted;
    }

    public static boolean isPluginUpdating() {
        return sPluginInstaller.isUpdating();
    }

}
