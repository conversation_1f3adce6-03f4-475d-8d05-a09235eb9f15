package com.ainirobot.platform.appmanager;

public class AppInfo {
    /**
     * 插件类型OPK
     */
    public static final String OPK_PLUGIN = "plugin";

    /**
     * 宿主类型OPK
     */
    public static final String OPK_HOST = "host";

    /**
     * 普通类型OPK
     */
    public static final String OPK_NORMAl = "normal";

    public String appId;
    public String appName;
    public String taskId;
    public String version;
    public String type;
    public String url;
    public String configId;
    public String configUrl;
    public int operate;
    public boolean isBoot;

    /**
     * OPK类型
     */
    public String opkType;

    /**
     * 此优先级为预置OPK优先级，数值越小优先级越高，默认启动优先级最高的OPK
     */
    public int priority;

    /**
     * 该字段是在老架构升级新架构的时候同步机器人端本地设置需要
     */
    public boolean isSyncSetting;
}
