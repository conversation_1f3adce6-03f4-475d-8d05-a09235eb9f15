package com.ainirobot.platform.appmanager.config;

public class Constants {

    /**
     * 充电可招揽开关 0是关，1是开
     */
    public static final String SETTINGS_CHARGING_CHAT = "robot_usable_when_charging";

    /**
     * 休眠时间
     */
    public static final String SETTINGS_STANDBY_TIME = "robot_meal_setting_standby_time";


    /**
     * 定时关机
     */
    public static final String SETTINGS_SHUTDOWN_TIME = "robot_setting_shutdown_time";

    /**
     * 关机时间
     */
    public static final String SETTINGS_SHUTDOWN_SWITCH = "robot_setting_shutdown_switch";

    /**
     * 设置密码
     */
    public static final String SETTINGS_MEAL_PASSWORD = "robot_meal_setting_password";

    /**
     * 机器人密码开关
     */
    public static final String SETTINGS_SYSTEM_ROBOT_PASSWORD_SWITCH = "system_robot_password_switch";

    /**
     * 机器人密码
     */
    public static final String SETTINGS_SYSTEM_ROBOT_PASSWORD = "system_robot_password";

    /**
     * 机器人锁屏开关
     */
    public static final String SETTINGS_SYSTEM_AUTO_LOCK_SWITCH = "system_auto_lock_switch";

    /**
     * 锁屏时间
     */
    public static final String SETTINGS_SYSTEM_LOCK_TIME = "system_lock_time";

    /**
     * 防盗警报
     */
    public static final String ROBOT_SETTING_MAP_OUTSIDE_WARNING = "robot_setting_map_outside_warning";

    /**
     * 按距离远近导航
     */
    public static final String ROBOT_SETTINGS_NAVIGATION_BY_DISTANCE = "robot_navigation_by_distance";

    /**
     * 防撞条开关
     */
    public static final String ROBOT_SETTING_COLLISION_STRIP_SWITCH = "robot_setting_anticollision_strip_switch";

    /**
     * 托盘摄像头开关
     */
    public static final String SETTINGS_ROBOT_UVC_CAMERA_SWITCH = "robot_setting_uvc_camera_switch";

    /**
     * 托盘氛围灯开关
     */
    public static final String  SETTINGS_ROBOT_PRO_TRAY_LED_SWITCH = "robot_setting_pro_tray_led_switch";

    /**
     * 刹车模式
     */
    public static final String ROBOT_SETTINGS_NAVIGATION_BREAK_MODE = "robot_setting_navigation_break_mode_level";

    /**
     * 启动模式
     */
    public static final String ROBOT_SETTINGS_NAVIGATION_START_MODE = "robot_setting_navigation_start_mode_level";


    /**
     * opk休眠开关
     */
    public static final String ROBOT_SETTING_STANDBY_OPK_MODE_SWITCH =  "robot_setting_standby_opk_mode";

    /**
     * 雷达休眠可用
     */
    public static final String ROBOT_SETTING_RADAR_ENABLE_IN_STADNBY_SWITCH =  "robot_setting_radar_enable_in_standby";

    /**
     * 低电回充位置
     */
    public static final String ROBOT_SETTING_LOW_BATTERY_NAVI_LOCATION =  "robot_setting_low_battery_navi_location";

    /**
     * 低电回充位置
     */
    public static final String ROBOT_SETTING_LOW_BATTERY_NAVI_LOCATION_ENABLE =  "robot_setting_low_battery_navi_location_enable";

    /**
     * 低电量TTS开关
     */
    public static final String ROBOT_SETTING_LOW_BATTERY_TTS_SWITCH = "robot_setting_low_battery_tts_switch";
    /**
     * 充电方式
     */
    public static final String ROBOT_SETTINGS_CHARGING_TYPE = "robot_settings_charging_type";
    public static final String SETTINGS_CHARGING_TYPE = "charging_wire";
    //货物高度检测开关
    public static final String ROBOT_SETTING_HEIGHT_LIMIT = "robot_setting_height_limit";
    public static final String SETTINGS_CHARGING_CHAT_DEFAULT = "0";
    public static final String SETTINGS_STANDBY_TIME_DEFAULT = "15";
    public static final String SETTINGS_SHUTDOWN_ENABLE_DEFAULT = "1";
    public static final String SETTINGS_SHUTDOWN_DISABLE_DEFAULT = "0";
    public static final String SETTINGS_SHUTDOWN_TIME_DEFAULT = "00:30";
    public static final String SETTINGS_MEAL_PASSWORD_DEFAULT = "0000";
}
