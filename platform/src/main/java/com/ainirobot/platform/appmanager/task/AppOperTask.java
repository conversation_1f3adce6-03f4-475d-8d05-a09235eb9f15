package com.ainirobot.platform.appmanager.task;

import androidx.annotation.Nullable;
import android.util.Log;

import com.ainirobot.platform.appmanager.AppInfo;
import com.ainirobot.platform.react.AppBeanV2;
import com.ainirobot.platform.react.AppManger;
import com.ainirobot.platform.react.OPKBeanV3;

import org.json.JSONException;
import org.json.JSONObject;

public abstract class AppOperTask {

    public String TAG;

    /**
     * AppTask操作类型
     */
    public static final int OPER_INSTALL = 1;
    public static final int OPER_UNINSTALL = 2;
    public static final int OPER_HOST_CONFIG_PORTAL = 20;

    /**
     * 安装
     */
    public static final String TYPE_INSTALL = "ins";

    /**
     * 卸载
     */
    public static final String TYPE_UNINSTALL = "uni";

    /**
     * 升级
     */
    public static final String TYPE_UPDATE = "up";

    /**
     * 只更新配置
     */
    public static final String TYPE_CONFIG = "cfg";

    /**
     * 修改全局配置
     */
    public static final String TYPE_GLOBAL_CONFIG = "mp";

    /**
     * 异常
     */
    public static final String TYPE_NONE = "none";

    /**
     * 任务执行状态
     */
    public static final String STATUS_START = "task_start";
    public static final String STATUS_SUCCEED = "task_ok";
    public static final String STATUS_FAILED = "task_fail";

    /**
     * 当前任务状态
     */
    private String status;
    private int errorCode;
    public AppInfo mInfo;
    private StatusListener mListener;

    public AppOperTask(AppInfo info) {
        this.mInfo = info;
        this.TAG = this.getClass().getSimpleName() + "[" + mInfo.appName + "]";
    }

    public boolean prepare() {
        return true;
    }

    public Result run() {
        return null;
    }

    public void updateStatus(String status) {
        Log.d(TAG, "Update status : " + status + "  " + mInfo.appName);
        this.updateStatus(status, 1);
    }

    public void updateStatus(String status, int errorCode) {
        this.status = status;
        this.errorCode = errorCode;
        if (mListener != null) {
            mListener.onStatusUpdate(status, generateStatusInfo(status, errorCode));
        }
    }

    public String getType() {
        return "";
    }

    public int getErrorCode() {
        return errorCode;
    }

    public String getStatus() {
        return status;
    }

    public boolean isNeedReboot() {
        return false;
    }

    public boolean isFinished() {
        return STATUS_SUCCEED.equals(this.status) || STATUS_FAILED.equals(this.status);
    }

    public String generateStatusInfo(String status, int code) {
        try {
            JSONObject json = new JSONObject();
            json.put("task_id", mInfo.taskId);
            json.put("robotapp_id", mInfo.appId);
            json.put("robotapp_name", mInfo.appName);
            json.put("robotapp_version_new", mInfo.version);
            json.put("cfg_id_new", mInfo.configId);
            json.put("op_type", getType());
            json.put("process_status", status);
            json.put("process_code", code);
            OPKBeanV3 localAppInfo = AppManger.INSTANCE.getRPKByCharacter(mInfo.appId);
            if (localAppInfo != null) {
                json.put("robotapp_version_local", localAppInfo.getVersionName());
                json.put("cfg_id_local", localAppInfo.getConfigId());
            }
            return json.toString();
        } catch (JSONException e) {
            e.printStackTrace();
            return null;
        }
    }

    public void setStatusListener(StatusListener listener) {
        this.mListener = listener;
    }

    public interface StatusListener {
        void onStatusUpdate(String status, String data);
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (!(obj instanceof AppOperTask)) {
            return false;
        }

        AppOperTask task = (AppOperTask) obj;
        return this.mInfo.appId.equals(task.mInfo.appId) && this.mInfo.taskId.equals(task.mInfo.taskId);
    }

    @Override
    public int hashCode() {
        int result = 0;
        result = 31 * result + (this.mInfo.appId == null ? 0 : this.mInfo.appId.hashCode());
        result = 31 * result + (this.mInfo.taskId == null ? 0 : this.mInfo.taskId.hashCode());
        return result;
    }

    @Override
    public String toString() {
        return TAG + "{" +
                "appName=" + mInfo.appName +
                ", version=" + mInfo.version +
                ", type=" + mInfo.type +
                ", operate=" + mInfo.operate +
                ", opkType=" + mInfo.opkType +
                '}';
    }

    public class Result {
        public boolean result;
        public AppBeanV2 appInfo;
        public String configPath;
        public AppInfo taskInfo;
        public boolean ignore;
    }

}
