package com.ainirobot.platform.appmanager;

import android.content.ContentResolver;
import android.content.Context;
import android.database.ContentObserver;
import android.database.Cursor;
import android.net.Uri;
import android.os.Handler;
import android.os.HandlerThread;
import androidx.annotation.NonNull;
import android.util.Log;

import com.ainirobot.platform.utils.IOUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * OPK事件监听
 */
public class AppResolver {

    private static final String TAG = AppResolver.class.getSimpleName();

    /**
     * 预置OPK已从服务端加载完成标志
     */
    private static final int PRESET_LOADED = 101;

    /**
     * 开机获取预置App
     */
    private static final String PRESET = "preset";

    /**
     * 开机拉取已安装列表
     */
    private static final String BOOT = "boot";

    /**
     * App更新
     */
    private static final String UPDATE = "update";

    /**
     * App数据存储Uri
     */
    private static final Uri DATA_URI = Uri.parse("content://com.ainirobot.remotecontrol.dataprovider/AppTask");

    private static Handler sHandler;
    private static ContentResolver sResolver;

    public static void init(Context context) {
        HandlerThread thread = new HandlerThread("App-ContentObserver");
        thread.start();
        sHandler = new Handler(thread.getLooper());

        sResolver = context.getContentResolver();
    }

    public static void registerPresetAppObserver(final @NonNull AppObserver observer) {
        if (isPresetLoadFinished()) {
            observer.onAppUpdate(new ArrayList<AppInfo>());
            return;
        }
        registerAppObserver(PRESET, new AppObserver() {
            @Override
            public void onAppUpdate(List<AppInfo> taskInfos) {
                //预置OPK在加载完成后会插入一个加载完成的标志，需要从列表中清除
                Iterator<AppInfo> iterator = taskInfos.iterator();
                while (iterator.hasNext()) {
                    AppInfo info = iterator.next();
                    if (PRESET_LOADED == info.operate) {
                        iterator.remove();
                        deleteAppTask(info);
                    }
                }
//                isPresetLoadFinished();
                observer.onAppUpdate(taskInfos);
            }
        }, true, "event = '" + PRESET + "'");
    }

    public static AppInfo getPortalOpk() {
        List<AppInfo> data = readAppTask(true, "operate = '20'");
        Log.d(TAG, "Get portal opk : " + data.size());
        deleteAppTask(data);
        if (!data.isEmpty()) {
            return data.get(0);
        }
        return null;
    }

    public static boolean isPresetLoadFinished() {
        List<AppInfo> data = readAppTask(true, "operate = '101'");
        Log.d(TAG, "Preset app load finished : " + !data.isEmpty());
        deleteAppTask(data);
        return !data.isEmpty();
    }

    public static void registerBootAppObserver(final @NonNull AppObserver observer) {
        registerAppObserver(BOOT, observer, true, "event = '" + BOOT + "'");
    }

    public static void registerUpdateAppObserver(final @NonNull AppObserver observer) {
        registerAppObserver(BOOT, observer, false, "event != '" + PRESET + "'");
        registerAppObserver(UPDATE, observer, false, "event != '" + PRESET + "'");
    }

    private static void registerAppObserver(String event, final @NonNull AppObserver observer, final boolean isBoot) {
        registerAppObserver(event, observer, isBoot, null);
    }

    private static void registerAppObserver(String event, final @NonNull AppObserver observer, final boolean isBoot, final String slection) {
        if (sResolver == null) {
            throw new RuntimeException("AppResolver is not initialized");
        }

        List<AppInfo> appTasks = readAppTask(isBoot, slection);
        if (!appTasks.isEmpty()) {
            observer.onAppUpdate(appTasks);
        }

        sResolver.registerContentObserver(Uri.withAppendedPath(DATA_URI, event), false,
                new ContentObserver(sHandler) {
                    @Override
                    public void onChange(boolean selfChange) {
                        observer.onAppUpdate(readAppTask(true, slection));
                    }
                });
    }

    public static void reloadPreset() {
        sResolver.notifyChange(Uri.withAppendedPath(DATA_URI, "ReloadPreset"), null);
    }

    /**
     * 读取App操作任务
     */
    private static List<AppInfo> readAppTask(boolean isBoot, String selection) {
        Cursor cursor = null;
        List<AppInfo> tasks = new ArrayList<>();

        try {
            cursor = sResolver.query(DATA_URI, null, selection, null, null);
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    AppInfo taskInfo = new AppInfo();
                    taskInfo.appId = cursor.getString(cursor.getColumnIndex("appId"));
                    taskInfo.appName = cursor.getString(cursor.getColumnIndex("appName"));
                    taskInfo.type = cursor.getString(cursor.getColumnIndex("type"));
                    taskInfo.version = cursor.getString(cursor.getColumnIndex("version"));
                    taskInfo.url = cursor.getString(cursor.getColumnIndex("url"));
                    taskInfo.taskId = cursor.getString(cursor.getColumnIndex("taskId"));
                    taskInfo.configId = cursor.getString(cursor.getColumnIndex("configId"));
                    taskInfo.configUrl = cursor.getString(cursor.getColumnIndex("configUrl"));
                    taskInfo.operate = cursor.getInt(cursor.getColumnIndex("operate"));
                    taskInfo.opkType = cursor.getString(cursor.getColumnIndex("opkType"));
                    taskInfo.priority = cursor.getInt(cursor.getColumnIndex("priority"));
                    taskInfo.isSyncSetting = cursor.getInt(cursor.getColumnIndex("isSyncSetting")) == 1;
                    taskInfo.isBoot = isBoot;
                    tasks.add(taskInfo);
                }
            }
        } finally {
            IOUtils.close(cursor);
        }
        return tasks;
    }

    public static void deleteAppTask(List<AppInfo> tasks) {
        if (tasks == null) {
            return;
        }

        for (AppInfo info : tasks) {
            deleteAppTask(info);
        }
    }

    public static boolean deleteAppTask(AppInfo appTask) {
        if (sResolver == null) {
            throw new RuntimeException("AppResolver is not initialized");
        }

        Log.d(TAG, "Start delete app task : " + appTask);
        int row = 0;
        try {
            row = sResolver.delete(DATA_URI, "taskId=? and appId=?", new String[]{appTask.taskId, appTask.appId});
        } catch (Exception e) {
            e.printStackTrace();
        }

        Log.d(TAG, "Delete app task : " + (row > 0) + "   " + appTask.taskId + "   " + appTask.appId + "   " + appTask.appName);

        if (row <= 0) {
            //如果删除失败，打印数据库所有数据
            printAppTaskList();
        }

        return row > 0;
    }

    private static void printAppTaskList() {
        Cursor cursor = null;
        try {
            cursor = sResolver.query(DATA_URI, null, null, null, null);
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    AppInfo taskInfo = new AppInfo();
                    taskInfo.appId = cursor.getString(cursor.getColumnIndex("appId"));
                    taskInfo.appName = cursor.getString(cursor.getColumnIndex("appName"));
                    taskInfo.version = cursor.getString(cursor.getColumnIndex("version"));
                    taskInfo.taskId = cursor.getString(cursor.getColumnIndex("taskId"));
                    Log.d(TAG, "Print app task list : " + taskInfo.appId + "   " + taskInfo.taskId + "   " + taskInfo.appName + "  " + taskInfo.taskId);
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        } finally {
            IOUtils.close(cursor);
        }
    }

    public interface AppObserver {
        void onAppUpdate(List<AppInfo> taskInfos);
    }

}
