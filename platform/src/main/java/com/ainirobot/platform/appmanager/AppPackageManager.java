package com.ainirobot.platform.appmanager;


import android.content.Context;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import com.ainirobot.base.util.SystemUtils;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.appmanager.config.ConfigUtil;
import com.ainirobot.platform.appmanager.config.UpdateListener;
import com.ainirobot.platform.appmanager.config.bean.ConfigItem;
import com.ainirobot.platform.appmanager.config.filter.ConvertKey;
import com.ainirobot.platform.appmanager.config.filter.IConvert;
import com.ainirobot.platform.appmanager.config.impl.ConfigType;
import com.ainirobot.platform.appmanager.config.impl.SystemConfig;
import com.ainirobot.platform.appmanager.installer.AppInstaller;
import com.ainirobot.platform.appmanager.installer.AppInstaller.AppListener;
import com.ainirobot.platform.appmanager.installer.AppStore;
import com.ainirobot.platform.appmanager.installer.BootApp;
import com.ainirobot.platform.appmanager.installer.LocalApp;
import com.ainirobot.platform.appmanager.installer.PresetApp;
import com.ainirobot.platform.appmanager.task.AppOperTask;
import com.ainirobot.platform.appmanager.task.AppOperTask.Result;
import com.ainirobot.platform.react.AppManger;
import com.ainirobot.platform.react.OPKBeanV3;
import com.ainirobot.platform.react.OPKHelper;
import com.ainirobot.platform.react.reactnative.character.ReactCharacter;
import com.ainirobot.platform.utils.FileHelper;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class AppPackageManager {

    private static final String TAG = AppPackageManager.class.getSimpleName();
    private static AppPackageManager appManger;


    /**
     * 默认OPK id
     */
    private static final String DEFAULT_APP_ID = BaseApplication.getApplication().getDefaultReactNativeAppId();

    /**
     * 安装目录
     */
    private static final String INSTALL_DIR = "/robot/rndata";


    /**
     * 是否初始化安装（包含预置OPk及云端配置默认OPK的安装）
     */
    private volatile boolean isInitInstall = false;


    private ExecutorService mExecutor = Executors.newFixedThreadPool(3);

    /**
     * OPK配置更新时是否采用通知更新机制
     * <p>
     * 配置的通知更新机制依赖于上层OPK监听配置的变化，然后主动获取最新的配置
     * 当前豹小秘的OPK都没有适配，需要兼容存量OPK，在小秘上不使用通知机制
     */
    private boolean isConfigUpdateNotify = BaseApplication.getApplication().isConfigUpdateNotify();

    /**
     * 插件更新进度监听
     */
    private UpdateListener mUpdateListener;

    private UpdateListener mPresetAppListener;

    private static boolean isLoadBoot = false;

    private Context mContext;

    public static void init(Context context) {
        if (appManger == null) {
            appManger = new AppPackageManager(context);
        }
    }

    private AppPackageManager(Context context) {
        this.mContext = context;
        AppResolver.init(context);
        this.isInitInstall = isFirstInstall();
        boolean isReset = resetPresetApp();
        Log.d(TAG, "resetPresetApp:" + isReset);

        LocalApp.startInstallWorkflow();
        mExecutor.submit(new Runnable() {
            @Override
            public void run() {
                versionCompatible();
                startInstallTask();
            }
        });
    }

    private void startInstallTask() {
        if (isLoadPresetOpk()) {
            //安装本地预置
            LocalApp.startInstall();
            PresetApp.setListener(new PresetAppListener());
            PresetApp.startInstall();
            return;
        }
        startAppStore();
    }

    /**
     * 预置OPK安装成功
     */
    private void onPresetAppInstallSucceed() {
        mExecutor.submit(new Runnable() {
            @Override
            public void run() {
                setPresetVersion();
                startAppStore();
            }
        });
    }

    private void startAppStore() {
        Log.d(TAG, "Start app store : " + isConfigUpdateNotify);
        BootApp.setAppListener(new NormalAppListener());
        BootApp.setPluginListener(new PluginListener());
        //开机列表安装失败且是初次安装的情况下需要显示失败列表
        isLoadBoot = true;
        if (!BootApp.startInstall() && isInitInstall) {
            Log.d(TAG, "Boot app install failed");
            return;
        }

        isInitInstall = false;
        initAppStore();
    }

    /**
     * 设置是否允许更新OPK(目前特指插件类型的OPK)
     *
     * @param enable
     */
    public static void setUpdateEnable(boolean enable) {
        AppStore.setAllowInstallPlugin(enable);
    }

    public static boolean isNeedUpdatePresetApp() {
        return PresetApp.isNeedUpdatePresetApp();
    }

    public static boolean isInitInstall() {
        return appManger.isInitInstall;
    }

    public static boolean isUpdating() {
        Log.d(TAG, "Is updating Boot : " + BootApp.isPluginInstalling() + "  AppStore : " + AppStore.isPluginUpdating());
        return BootApp.isPluginInstalling() || AppStore.isPluginUpdating();
    }

    public static boolean checkUpdate() {
        return isUpdating();
    }

    public static void startUpdate() {
        appManger.mExecutor.submit(new Runnable() {
            @Override
            public void run() {
                if (PresetApp.isNeedUpdatePresetApp()) {
                    retryPresetApp();
                    return;
                }

                if (!isLoadBoot) {
                    appManger.startAppStore();
                    return;
                }

                if (appManger.isInitInstall) {
                    retryBootApp();
                }
            }
        });
    }

    public static void setUpdateListener(UpdateListener listener) {
        appManger.mUpdateListener = listener;
        registerSystemConfigListener(listener);
    }

    private static void registerSystemConfigListener(final UpdateListener listener) {
        ConfigManager.getInstance().registerUpdateListener(SystemConfig.APP_ID, new com.ainirobot.platform.appmanager.config.UpdateListener() {
            @Override
            public void onUpdate(String data) {
                if (listener != null) {
                    listener.onConfigUpdate(SystemConfig.APP_ID, data);
                }
            }
        });
    }

    public static void setPresetAppListener(UpdateListener listener) {
        appManger.mPresetAppListener = listener;
    }

    public static void cancelUpdate() {
        appManger.isInitInstall = false;
        appManger.initAppStore();
    }

    private void initAppStore() {
        Log.d(TAG, "Init app store");
        AppStore.setAppListener(new NormalAppListener());
        AppStore.setPluginListener(new PluginListener());
        BootApp.stop();
        AppStore.start();
    }

    /**
     * 失败重试
     */
    public static void retryUpdate() {
        appManger.mExecutor.submit(new Runnable() {
            @Override
            public void run() {
                if (PresetApp.isNeedUpdatePresetApp()) {
                    retryPresetApp();
                } else {
                    retryBootApp();
                }
            }
        });
    }

    /**
     * 重新安装预置OPK
     */
    private static void retryPresetApp() {
        if (PresetApp.isInstalling()) {
            Log.d(TAG, "Preset app retry : current is installing");
            return;
        }
        if (PresetApp.retry()) {
            appManger.setPresetVersion();
            appManger.startAppStore();
        } else {
            Log.d(TAG, "Preset app retry failed");
        }
    }

    private static void retryBootApp() {
        Log.d(TAG, "Retry boot app : " + BootApp.isPluginInstalling() + "   " + BootApp.isFailed());
        if (!BootApp.isPluginInstalling() && BootApp.isFailed()) {
            if (!BootApp.retry() && appManger.isInitInstall) {
                return;
            }
            appManger.isInitInstall = false;
            appManger.initAppStore();
        }
    }

    /**
     * 是否首次安装
     *
     * @return
     */
    private boolean isFirstInstall() {
        String presetVersion = getPresetVersion();
        if (!TextUtils.isEmpty(presetVersion)) {
            return false;
        }

        OPKBeanV3 bean = AppManger.INSTANCE.getRPKByCharacter(DEFAULT_APP_ID);
        return bean == null || !"v3".equals(bean.getOpkLoad());
    }

    /**
     * 重置App
     *
     * @return
     */
    private boolean resetPresetApp() {
        String presetVersion = getPresetVersion();
        if (TextUtils.isEmpty(presetVersion)) {
            return false;
        }
        String currentVersion = SystemUtils.getSystemVersion();
        if (ProductInfo.isMeissa2()) {
            int index = presetVersion.lastIndexOf(".") + 1;
            String presetType = presetVersion.substring(index, index + 4);
            index = currentVersion.lastIndexOf(".") + 1;
            String currentType = currentVersion.substring(index, index + 4);
            Log.d(TAG, "Preset type : " + presetType + "  Current type : " + currentType);
            if (!presetType.equals(currentType)) {
                FileHelper.deleteDirectory(new File(Environment.getExternalStorageDirectory(), INSTALL_DIR));
                return true;
            }
        }
        return false;
    }

    /**
     * 是否需要加载预置OPk
     *
     * @return
     */
    private boolean isLoadPresetOpk() {
        String presetVersion = getPresetVersion();
        Log.d(TAG, "Preset rom version : " + presetVersion);
        if (TextUtils.isEmpty(presetVersion)) {
            return true;
        }
        return !presetVersion.equals(SystemUtils.getSystemVersion());
    }

    /**
     * 保存预置OPK对应的Rom版本
     */
    private void setPresetVersion() {
        String systemVersion = SystemUtils.getSystemVersion();
        Log.d(TAG, "Set preset app version : " + systemVersion);
        if (ProductInfo.isMiniProduct()) {
            uninstallMiniJarvis();
        }

        RobotSettings.storage2SystemSettings(mContext, "PresetRomVersion", systemVersion);

    }

    /**
     * uninstall mini-jarvis
     */
    private void uninstallMiniJarvis() {
        Log.d(TAG, "uninstall mini-jarvis start");
        OPKBeanV3 appInfo_jarvis = AppManger.INSTANCE.getRPKByCharacter("system_d4b0af15568f6ed1baedf441066e6d5c");
        OPKBeanV3 appInfo_workflowUtils = AppManger.INSTANCE.getRPKByCharacter("system_f0937a82098b7eed5058da081fc5e1e3");
        if (appInfo_jarvis == null) {
            Log.d(TAG, "opk is empty, not need uninstall");
            return;
        }
        Log.d(TAG, "appInfo jarvis" + appInfo_jarvis.toString());
        if (appInfo_workflowUtils != null
                && OPKHelper.INSTANCE.compareVersion("2.14",
                Objects.requireNonNull(appInfo_jarvis.getCoreTarget())) < 0
                && OPKHelper.INSTANCE.compareVersion("2.14",
                Objects.requireNonNull(appInfo_workflowUtils.getCoreTarget())) >= 0) {
            Log.d(TAG, "appInfo workflowUtils" + appInfo_workflowUtils.toString());
            AppManger.INSTANCE.removeAppId("system_d4b0af15568f6ed1baedf441066e6d5c");
        }
    }

    /**
     * 获取预置OPK对应的Rom版本
     */
    private String getPresetVersion() {
        return RobotSettings.getGlobalSettings(mContext, "PresetRomVersion", "");
    }

    /**
     * 版本兼容处理
     * <p>
     * 修改了OPK配置的存储及解析方式，需要对老的配置进行处理
     */
    private void versionCompatible() {
        String presetVersion = getPresetVersion();
        if (TextUtils.isEmpty(presetVersion)) {
            //删除老配置，新配置会在安装OPK的时候重新加载
            File dir = new File(Environment.getExternalStorageDirectory(), "/robot/rnconfig");
            FileHelper.deleteDirectory(dir);
        }
    }

    private class NormalAppListener extends AppListener {

        @Override
        public boolean onAppUpdate(AppInstaller installer, String operType, final String appId, Result result, int notUpdateCount) {
            if (TextUtils.equals(appId, SystemConfig.APP_ID)) {
                boolean isExist = ConfigUtil.isExistConfig(SystemConfig.CONFIG_PATH);
                Log.i(TAG, "onAppUpdate : " + isExist + " configPath: " + result.configPath);
                ConfigUtil.saveConfig(appId, result.configPath);
                ConfigItem item = new ConfigItem(appId, isExist ? ConfigType.COVER : ConfigType.FIRST_UPDATE);
                ConfigManager.getInstance().setConfig(item);
                return true;
            }
            return false;
        }

    }

    private class PresetAppListener extends AppListener {

        private int count;
        private boolean result = false;

        @Override
        public void onStart(AppOperTask task, int count) {
            Log.d(TAG, "On preset app start : " + mPresetAppListener);
            this.result = true;
            if (mPresetAppListener != null) {
                this.count = count;
                mPresetAppListener.onStart();
                if (task != null) {
                    mPresetAppListener.onProgressChange(count, 1, task.mInfo.appName, task.getType());
                }
            }
        }

        @Override
        public boolean onAppUpdate(AppInstaller installer, String operType, String appId, Result result, int notUpdateCount) {
            Log.d(TAG, "On app update : " + appId + "  " + result.result);
            if (mPresetAppListener != null) {
                mPresetAppListener.onProgressChange(count, count - notUpdateCount, result.taskInfo.appName, operType);
            }

            if (!result.result) {
                this.result = false;
            }
            return false;
        }

        @Override
        public void onFinish() {
            Log.d(TAG, "On preset app finished : " + this.result);
            if (mPresetAppListener != null) {
                mPresetAppListener.onFinish(this.result, "");
            }

            if (this.result) {
                onPresetAppInstallSucceed();
            }
        }
    }

    /**
     * 插件更新事件处理
     */
    private class PluginListener extends AppListener {

        private List<AppInfo> mFailed = new ArrayList<>();
        private boolean isNeedReboot = false;

        /**
         * 已更新数量
         */
        private int mCount = 0;

        @Override
        public void onStart(AppOperTask task, int count) {
            Log.d(TAG, "On app update start : " + task.mInfo.appName + "   " + mUpdateListener);
            mFailed.clear();
            mCount = 0;
            isNeedReboot = false;
//            if (mUpdateListener != null) {
//                mUpdateListener.onStart();
//                mUpdateListener.onProgressChange(count, 1, task.mInfo.appName, task.getType());
//            }
        }

        @Override
        public boolean onAppUpdate(AppInstaller installer, String operType, String appId, Result result, int notUpdateCount) {
            Log.d(TAG, "On app update : " + result.taskInfo.appName + "   " + mUpdateListener + "   " + isNeedReboot);
            //如果当前更新已经需要重启，不再对配置更新进行通知，重启后会重新加载配置
            if (isConfigUpdateNotify && !isNeedReboot && isConfigUpdate(operType, result)) {
                handleConfigUpdate(installer, appId, result);
                return true;
            }

            mCount++;
            if (mUpdateListener != null) {
                if (!isNeedReboot) {
                    mUpdateListener.onStart();
                }
                mUpdateListener.onProgressChange(mCount + notUpdateCount, mCount, result.taskInfo.appName, operType);
            }
            isNeedReboot = true;
            if (!result.result) {
                mFailed.add(result.taskInfo);
            }
            return false;
        }

        /**
         * 是否配置更新
         *
         * @param operType
         * @param result
         * @return
         */
        private boolean isConfigUpdate(String operType, Result result) {
            if (AppOperTask.TYPE_CONFIG.equals(operType)) {
                return true;
            }

            if (AppOperTask.TYPE_INSTALL.equals(operType)
                    || AppOperTask.TYPE_UPDATE.equals(operType)) {
                return result.appInfo == null;
            }

            return false;
        }

        /**
         * OPK配置更新处理
         * OPK配置文件内部包含两部分配置：
         * 1. 系统级配置，用来配置OPK的优先级、语音指令, 需通知Workflow该部分配置发生了更改
         * 2. 业务级配置，接待后台配置的OPK业务需要的设置, 需通知OPK其配置发生了更改
         *
         * @param appId  OPK的id
         * @param result 更新结果
         */
        private void handleConfigUpdate(AppInstaller installer, String appId, Result result) {
            Log.d(TAG, "Handle config update : " + appId + "  " + result.taskInfo.appName + "   " + result.configPath);
            OPKBeanV3 appInfo = AppManger.INSTANCE.getRPKByCharacter(appId);
            if (appInfo != null && result.configPath != null) {
                try {
                    long oldUpdateTime = 0;
                    String oldAppConfigData = "";
                    if (appInfo.getConfigPath() != null) {
                        String oldConfig = FileHelper.loadJsonFromFile(appInfo.getConfigPath());
                        if (!TextUtils.isEmpty(oldConfig)) {
                            try {
                                JSONObject oldJson = new JSONObject(oldConfig);
                                if (oldJson.has("sys_config")) {
                                    oldUpdateTime = oldJson.getJSONObject("sys_config").getJSONObject("nlp").getLong("_update_time");
                                }

                                if (oldJson.has("app_config")) {
                                    oldAppConfigData = oldJson.getJSONObject("app_config").getString("cfg_data");
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }

                        Log.d(TAG, "Old config : " + oldConfig);
                    }

                    JSONObject newConfig = new JSONObject(FileHelper.loadJsonFromFile(result.configPath));
                    JSONObject newSysConfig = newConfig.getJSONObject("sys_config");
                    long newUpdateTime = newSysConfig.getJSONObject("nlp").getLong("_update_time");
                    JSONObject newAppConfig = newConfig.getJSONObject("app_config");

                    Log.d(TAG, "New config : " + newConfig.toString());

                    //从临时下载目录保存新配置文件到对应目录，并更新OPK信息
                    installer.saveConfig(appId, result.taskInfo.configId, result.configPath);
                    if (mUpdateListener == null) {
                        return;
                    }

                    //比对系统级配置的更新时间，时间不一致，表明系统级配置有更新，通知Workflow更新配置
                    if (newUpdateTime != oldUpdateTime) {
                        String current = ReactCharacter.Companion.getCurrentCharacter();
                        Log.d(TAG, "System config update : " + appId + "  " + current + "    " + AppManger.INSTANCE.getRPKByCharacter(current).getType());

                        //如果当前运行的是宿主类OPK，通知当前OPK有配置变更，该配置对非宿主类OPK无作用，不再通知
                        if (AppInfo.OPK_HOST.equals(AppManger.INSTANCE.getRPKByCharacter(current).getType())) {
                            newSysConfig.put("appId", appId);
                            mUpdateListener.onConfigUpdate(current, newSysConfig.toString());
                        }
                    }

                    //比对业务级配置内容，内容不一致，通知OPK其配置发生了变化
                    if (!oldAppConfigData.equals(newAppConfig.getString("cfg_data"))) {
                        String data = new String(Base64.decode(newAppConfig.getString("cfg_data"), Base64.DEFAULT));
                        newAppConfig.put("cfg_data", data);

                        Log.d(TAG, "App config update : " + appId + "  " + result.taskInfo.appName + "  " + newAppConfig.toString());
                        mUpdateListener.onConfigUpdate(appId, newAppConfig.toString());
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }


        @Override
        public void onFinish() {
            Log.d(TAG, "On app update finished : " + mUpdateListener);
            if (mUpdateListener != null) {
                try {
                    JSONObject message = new JSONObject();
                    message.put("isNeedReboot", isNeedReboot);

                    JSONArray array = new JSONArray();
                    for (AppInfo info : mFailed) {
                        JSONObject object = new JSONObject();
                        object.put("appName", info.appName);
                        object.put("icon", "");
                        array.put(object);
                    }
                    message.put("failed", array);

                    mUpdateListener.onFinish(mFailed.isEmpty(), message.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public interface UpdateListener {

        void onStart();

        /**
         * 进度更新
         *
         * @param count   更新的app总数量
         * @param index   当前更新到第几个
         * @param appName app名称
         * @param oper    操作类型
         */
        void onProgressChange(int count, int index, String appName, String oper);

        /**
         * OPK配置更新
         *
         * @param appId   OPK的id
         * @param content 新配置内容
         */
        void onConfigUpdate(String appId, String content);

        /**
         * 更新结束
         *
         * @param result  true 更新成功 false 更新失败
         * @param message 消息
         */
        void onFinish(boolean result, String message);

    }

}
