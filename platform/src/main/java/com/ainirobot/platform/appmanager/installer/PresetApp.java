package com.ainirobot.platform.appmanager.installer;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.base.util.SystemUtils;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.PlatformDef;
import com.ainirobot.platform.appmanager.AppInfo;
import com.ainirobot.platform.appmanager.AppResolver;
import com.ainirobot.platform.appmanager.AppResolver.AppObserver;
import com.ainirobot.platform.appmanager.installer.AppInstaller.AppListener;
import com.ainirobot.platform.appmanager.task.AppOperTask;
import com.ainirobot.platform.appmanager.task.AppOperTask.Result;
import com.ainirobot.platform.character.CharacterManager;
import com.ainirobot.platform.control.ControlManager;
import com.ainirobot.platform.react.server.control.RNServerManager;
import com.ainirobot.platform.utils.LocalUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 云端预置OPK
 */
public class PresetApp {

    private static final String TAG = "PresetApp";

    private static LinkedBlockingQueue<Boolean> mEvent = new LinkedBlockingQueue<>();

    private static AppInstaller sAppInstaller = new AppInstaller("Preset");

    private static boolean isNeedUpdatePresetApp = false;

    private static boolean isInstalling = false;

    private static boolean isRetry = false;

    private static AppListener sAppListener;

    /**
     * 60秒超时
     */
    private static final long DATA_TIMEOUT = 60 * 1000;

    /**
     * 是否已加载数据
     */
    private static volatile boolean isLoadData = false;

    private static final Object DATA_LOCK = new Object();

    /**
     * 默认启动的App
     */
    private static AppInfo defaultApp;

    /**
     * 注册预置OPK更新事件
     */
    private static void registerObserver() {
        AppResolver.registerPresetAppObserver(new AppObserver() {
            @Override
            public void onAppUpdate(List<AppInfo> taskInfos) {
                Log.d(TAG, "On preset app : " + taskInfos.size());
                isLoadData = true;
                synchronized (DATA_LOCK) {
                    DATA_LOCK.notifyAll();
                }

                if (!isNeedUpdatePresetApp) {
                    AppResolver.deleteAppTask(taskInfos);
                    return;
                }

                boolean hasTask = false;
                for (AppInfo info : taskInfos) {
                    if (sAppInstaller.addTask(info)) {
                        //只有添加成功，任务才有效
                        hasTask = true;
                    }

                    //插件OPK不能设置为默认自启动
                    if (AppInfo.OPK_PLUGIN.equals(info.opkType) || info.priority == 0) {
                        continue;
                    }

                    //服务端的配置中0是不默认启动
                    if (defaultApp == null
                            || defaultApp.priority > info.priority) {
                        defaultApp = info;
                    }
                }

                if (!hasTask) {
                    mEvent.offer(true);
                    isNeedUpdatePresetApp = false;
                    if (sAppListener != null) {
                        sAppListener.onStart(null, 0);
                        sAppListener.onFinish();
                    }
                    return;
                }

                sAppInstaller.start();
                if (defaultApp != null && !isRetry) {
                    Log.d(TAG, "Preset app default : " + defaultApp.appId + "   " + defaultApp.appName);
                    initDefault(defaultApp.appId);
                }
                Log.d(TAG, "On preset app add finished ***** ");
            }
        });
    }

    public static boolean startInstall() {
        Log.d(TAG, "Start preset app install : " + isNeedUpdatePresetApp);
        if (isNeedUpdatePresetApp) {
            throw new RuntimeException("Preset app installing");
        }

        sAppInstaller.getFailedTasks().clear();
        isNeedUpdatePresetApp = true;
        isInstalling = true;
        isLoadData = false;
        sAppInstaller.setListener(new AppListenerProxy());

        registerObserver();

        if (!waitData()) {
            return false;
        }

        return waitResult();
    }

    /**
     * 失败重试
     */
    public static boolean retry() {
        Log.d(TAG, "Preset app retry start : " + isNeedUpdatePresetApp);
        if (!isNeedUpdatePresetApp) {
            return true;
        }

        isInstalling = true;
        sAppInstaller.getFailedTasks().clear();
        isRetry = true;
        isLoadData = false;
        AppResolver.reloadPreset();

        if (!waitData()) {
            return false;
        }
        return waitResult();
    }

    private static boolean waitData() {
        if (!isLoadData) {
            synchronized (DATA_LOCK) {
                try {
                    Log.d(TAG, "Wait preset app data");
                    DATA_LOCK.wait(DATA_TIMEOUT);
                    Log.d(TAG, "Wait preset app data timeout : " + sAppInstaller.isUpdating());
                    if (!isLoadData) {
                        //超时后
                        isInstalling = false;
                        if (sAppListener != null) {
                            sAppListener.onFinish();
                        }
                        return false;
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        return true;
    }

    private static boolean waitResult() {
        try {
            if (mEvent.take()) {
                Log.d(TAG, "Preset app install finished : " + sAppInstaller.getFailedTasks().isEmpty());
                isInstalling = false;
                if (sAppInstaller.getFailedTasks().isEmpty()) {
                    isNeedUpdatePresetApp = false;
                    return true;
                }
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return false;
    }

    public static void setListener(AppListener listener) {
        sAppListener = listener;
    }

    public static boolean isNeedUpdatePresetApp() {
        return isNeedUpdatePresetApp;
    }

    public static boolean isInstalling() {
        return isInstalling;
    }

    public static AppInfo getDefault() {
        return defaultApp;
    }

    private static class AppListenerProxy extends AppListener {

        @Override
        public void onStart(AppOperTask task, int count) {
            if (sAppListener != null) {
                sAppListener.onStart(task, count);
            }
        }

        @Override
        public boolean onAppUpdate(AppInstaller installer, String operType, String appId, Result result, int notUpdateCount) {
            if (sAppListener != null) {
                return sAppListener.onAppUpdate(installer, operType, appId, result, notUpdateCount);
            }
            return false;
        }

        @Override
        public void onFinish() {
            mEvent.offer(true);
            if (sAppInstaller.getFailedTasks().isEmpty()) {
                isNeedUpdatePresetApp = false;
            }
            if (sAppListener != null) {
                sAppListener.onFinish();
            }
        }
    }

    /**
     * 注册默认预置OPK
     * appId为PlatformDef.PRESET_DEFAULT_OPK, 通用id, 后续根据从服务端加载的信息确定具体AppId
     */
    private static void initDefault(String appId) {

        CharacterManager.getInstance().registerRNCharacter(appId);

        //是否自动切换到RN
        boolean isAutoSwitch = BaseApplication.getApplication().getDefaultReactNativeAutoSwitch();

        if (isAutoSwitch) {
            //是否强制切换到RN
            boolean isForceSwitch = BaseApplication.getApplication().isForceSwitchRomOpk();

            String defaultCharacterName = LocalUtils.getGlobalSettings(BaseApplication.getApplication(), PlatformDef.OB_OPK_SWITCH, "");
            if (TextUtils.isEmpty(defaultCharacterName) || isForceSwitch) {
                Log.d(TAG, "Switch default :  " + appId);
                switchCharacter(appId);
            }
        }
    }

    /**
     * 启动OPK
     *
     * @param appId
     */
    private static void switchCharacter(String appId) {
        try {
            RNServerManager.getInstance().setUpdate(true);
            JSONObject json = new JSONObject();
            json.put("name", appId);
            json.put("isPreset", true);
            ControlManager.handleRequest(0, PlatformDef.SWITCH_CHARACTER, "", json.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

}
