package com.ainirobot.platform.appmanager;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Handler;
import android.os.HandlerThread;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.appmanager.config.Constants;
import com.ainirobot.platform.appmanager.config.IConfig;
import com.ainirobot.platform.appmanager.config.UpdateListener;
import com.ainirobot.platform.appmanager.config.bean.ConfigItem;
import com.ainirobot.platform.appmanager.config.cloud.ConfigCloud;
import com.ainirobot.platform.appmanager.config.cloud.ICloud;
import com.ainirobot.platform.appmanager.config.impl.ConfigType;
import com.ainirobot.platform.appmanager.config.impl.ISettingConfig;
import com.ainirobot.platform.appmanager.config.impl.NormalConfig;
import com.ainirobot.platform.appmanager.config.impl.SystemConfig;
import com.ainirobot.platform.appmanager.config.storage.ConfigStorage;
import com.ainirobot.platform.appmanager.config.storage.IStorage;
import com.google.gson.Gson;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;


public class ConfigManager implements IConfig {

    private static final String TAG = ConfigManager.class.getSimpleName();
    private static final String ACTION_SETTING = "com.ainirobot.moduleapp.global.settings";

    private Gson mGson;
    private ICloud mCloud;
    private IStorage mStorage;
    private RobotSettingApi mRobotSetting;
    private ConcurrentHashMap<String, ISettingConfig> mConfigs = new ConcurrentHashMap<>();

    private Future mFuture;
    private Handler mHandler;
    private boolean isRunning = false;
    private ExecutorService mExecutor = Executors.newSingleThreadExecutor();
    private LinkedBlockingQueue<ConfigItem> mTasks = new LinkedBlockingQueue<>();

    private static class ConfigManagerInner {
        static ConfigManager INSTANCE = new ConfigManager();
    }

    public static ConfigManager getInstance() {
        return ConfigManagerInner.INSTANCE;
    }

    private ConfigManager() {
        mGson = new Gson();
        mCloud = new ConfigCloud(mGson);
        mStorage = new ConfigStorage(mGson);
        mRobotSetting = RobotSettingApi.getInstance();
        mConfigs.put(SystemConfig.APP_ID, new SystemConfig(mStorage, mCloud, mRobotSetting));

        initSettingPool();
        registerOpkSettingListener();

        setDefaultHeadAngle();
    }

    private void initSettingPool() {
        HandlerThread thread = new HandlerThread("receiver config");
        thread.start();
        mHandler = new Handler(thread.getLooper());
        start();
    }

    private void registerOpkSettingListener() {
        BroadcastReceiver receiver = new OpkBroadcastReceiver();
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ACTION_SETTING);
        BaseApplication.getContext().registerReceiver(receiver, intentFilter);
    }

    public void start() {
        Log.d(TAG, "On start ******  " + isRunning);
        if (isRunning) {
            return;
        }
        isRunning = true;
        mFuture = mExecutor.submit(new Runnable() {
            @Override
            public void run() {
                handleUpdate();
            }
        });
    }

    private void handleUpdate() {
        while (true) {
            try {
                ConfigItem item = mTasks.take();
                Log.i(TAG, "handleUpdate : " + item.getAppId() + " type: " + item.getType() + " value: " + item.getSettings());
                ISettingConfig iSettingConfig = buildConfig(item.getAppId());
                Log.i(TAG, "handleUpdate : " + iSettingConfig.getClass().getSimpleName());
                iSettingConfig.setConfig(item);
            } catch (Exception e) {
                e.printStackTrace();
                break;
            }
        }
    }

    private ISettingConfig buildConfig(String appId) {
        if (mConfigs.containsKey(appId)) {
            return mConfigs.get(appId);
        }
        ISettingConfig config = new NormalConfig(appId, mStorage, mCloud);
        mConfigs.put(appId, config);
        return config;
    }

    @Override
    public void registerUpdateListener(String appId, UpdateListener listener) {
        ISettingConfig iSettingConfig = buildConfig(appId);
        if (iSettingConfig != null) {
            iSettingConfig.registerUpdateListener(listener);
        }
    }

    @Override
    public void setConfig(final ConfigItem item) {
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                addTask(item);
            }
        });
    }

    private void addTask(ConfigItem item) {
        if (!mTasks.contains(item)) {
            Log.d(TAG, "Add app task : " + mGson.toJson(item));
            mTasks.add(item);
        }
    }

    private void setDefaultHeadAngle() {
        boolean enableWakeUpFree = mRobotSetting.getRobotInt(Definition.ROBOT_SETTING_VOICE_MODE) == 0;
        Log.d(TAG, "setDefaultHeadAngle: " + enableWakeUpFree);
        if (enableWakeUpFree) {
           int angle = mRobotSetting.getRobotInt(Definition.ROBOT_SETTING_WAKEUP_FREE_HEAD_ANGLE);
           Log.d(TAG, "setDefaultHeadAngle: " + angle);
           RobotApi.getInstance().setDefaultHeadAngle(0, angle);
        }
    }

    class OpkBroadcastReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null) {
                String key = intent.getStringExtra("key");
                String value = intent.getStringExtra("value");
                Log.i(TAG, "onReceive: key :" + key + " value :" + value);
                if (!TextUtils.isEmpty(key)) {
                    syncGlobalToRobotSettings(key, value);
                }
            }
        }
    }

    private void syncGlobalToRobotSettings(String key, String value) {
        switch (key) {
            case Definition.ROBOT_USABLE_WHEN_CHARGING:
            case Definition.ROBOT_SETTING_SPEAKER_ROLE:
            case Constants.SETTINGS_STANDBY_TIME:
            case Constants.SETTINGS_MEAL_PASSWORD:
            case Constants.SETTINGS_SHUTDOWN_TIME:
            case Constants.SETTINGS_SHUTDOWN_SWITCH:
            case Constants.SETTINGS_SYSTEM_ROBOT_PASSWORD_SWITCH:
            case Constants.SETTINGS_SYSTEM_AUTO_LOCK_SWITCH:
            case Constants.SETTINGS_SYSTEM_LOCK_TIME:
            case Constants.SETTINGS_SYSTEM_ROBOT_PASSWORD:
            case Constants.ROBOT_SETTING_MAP_OUTSIDE_WARNING:
            case Constants.ROBOT_SETTINGS_NAVIGATION_BY_DISTANCE:
            case Constants.ROBOT_SETTING_COLLISION_STRIP_SWITCH:
            case Constants.SETTINGS_ROBOT_UVC_CAMERA_SWITCH:
            case Constants.SETTINGS_ROBOT_PRO_TRAY_LED_SWITCH:
            case Constants.ROBOT_SETTINGS_NAVIGATION_BREAK_MODE:
            case Constants.ROBOT_SETTINGS_NAVIGATION_START_MODE:
            case Constants.ROBOT_SETTING_STANDBY_OPK_MODE_SWITCH:
            case Constants.ROBOT_SETTING_RADAR_ENABLE_IN_STADNBY_SWITCH:
            case Constants.ROBOT_SETTING_LOW_BATTERY_NAVI_LOCATION:
            case Constants.ROBOT_SETTING_LOW_BATTERY_NAVI_LOCATION_ENABLE:
            case Constants.ROBOT_SETTING_LOW_BATTERY_TTS_SWITCH:
            case  Definition.ROBOT_SETTINGS_CHARGING_TYPE:
//            case Constants.ROBOT_SETTING_HEIGHT_LIMIT:
                Log.i(TAG, "setting change value: " + value);
                ConfigItem item = new ConfigItem(SystemConfig.APP_ID, ConfigType.UPDATE);
                item.setConfig(key, value);
                ConfigManager.getInstance().setConfig(item);
                break;
            default:
                break;
        }
    }
}
