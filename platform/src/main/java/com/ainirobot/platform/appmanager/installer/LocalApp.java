package com.ainirobot.platform.appmanager.installer;

import android.content.Context;
import android.content.res.AssetManager;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.PlatformDef;
import com.ainirobot.platform.bean.CharacterInfo;
import com.ainirobot.platform.character.Character;
import com.ainirobot.platform.character.CharacterManager;
import com.ainirobot.platform.control.ControlManager;
import com.ainirobot.platform.react.AppBeanV2;
import com.ainirobot.platform.react.AppManger;
import com.ainirobot.platform.react.OPKBeanV3;
import com.ainirobot.platform.react.OPKHelper;
import com.ainirobot.platform.react.reactnative.character.ReactCharacter;
import com.ainirobot.platform.react.server.control.RNServerManager;
import com.ainirobot.platform.utils.AgentSystemCleanupUtil;
import com.ainirobot.platform.utils.FileHelper;
import com.ainirobot.platform.utils.LocalUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * ROM内预置的OPK
 */
public class LocalApp {
    private static final String TAG = LocalApp.class.getSimpleName();

    /**
     * OPK目录
     */
    private static final String OPK_DIR = "opk/robotos";
    private static final String AGENT_OPK_DIR = "opk/agentos";

    /**
     * 预置OPK命名规则：4段 {名称}-{版本}-{AppId}-{编译日期}
     */
    private static final int NAME_PART_LENGTH = 4;

    /**
     * 插件预置目录（在OPK目录下）
     */
    private static final String PLUGIN_DIR = "plugin";

    /**
     * OPK存储目录
     */
    private static String mOPKDir;

    /**
     * 安装目录
     */
    private static final String INSTALL_DIR = "/robot/rndata";

    /**
     * 默认OPK id
     */
    private static final String DEFAULT_APP_ID = BaseApplication.getApplication().getDefaultReactNativeAppId();

    public static boolean startInstall() {
        mOPKDir = getPath(INSTALL_DIR);

        String systemOSType = RobotSettings.getGlobalSettings(BaseApplication.getContext(), Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE, "");
        Log.d(TAG, "startInstall systemOSType=" + systemOSType);

        Log.d(TAG, "Cleaning all existing OPK data before installation");
        AgentSystemCleanupUtil.cleanAllOpkData(BaseApplication.getContext());

        if (TextUtils.equals(systemOSType, Definition.OSType.AGENTOS.getValue())) {
            Log.d(TAG, "Installing agent OPKs...");
            startInstallPresetOpk(AGENT_OPK_DIR);
            startInstallPresetOpk(AGENT_OPK_DIR + "/" + PLUGIN_DIR);
        } else {
            Log.d(TAG, "Installing robot OPKs...");
            startInstallPresetOpk(OPK_DIR);
            startInstallPresetOpk(OPK_DIR + "/" + PLUGIN_DIR);
        }

        startDefault();
        return true;
    }

    public static void startInstallWorkflow() {
        mOPKDir = getPath(INSTALL_DIR);

        String systemOSType = RobotSettings.getGlobalSettings(BaseApplication.getContext(), Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE, "");
        Log.d(TAG, "startInstallWorkflow systemOSType=" + systemOSType);

        if (TextUtils.equals(systemOSType, Definition.OSType.AGENTOS.getValue())) {
            startInstallPresetOpk(AGENT_OPK_DIR);
        } else {
            startInstallPresetOpk(OPK_DIR);
        }
    }

    private static void startInstallPresetOpk(String dir) {
        Context context = BaseApplication.getContext();
        Log.d(TAG, "Install preset opk dir=" + dir);
        try {
            if(context == null){
                Log.d(TAG, "Install preset opk failed: context is null");
                return;
            }
            AssetManager assetManager = context.getAssets();
            if(assetManager == null){
                Log.d(TAG, "Install preset opk failed: assetManager is null");
                return;
            }
            String[] opkList = context.getAssets().list(dir);
            if (opkList == null) {
                Log.d(TAG, "Install preset opk : " + dir + "is empty");
                return;
            }

            for (String name : opkList) {
                // 跳过非OPK文件（如目录或其他文件）
                if (!name.endsWith(".opk")) {
                    Log.d(TAG, "Skip non-opk file : " + name);
                    continue;
                }
                
                Log.d(TAG, "Start install preset opk : " + name);
                if (!isNewVersion(name)) {
                    Log.d(TAG, "Install preset opk failed : version too old " + name);
                    continue;
                }

                String path = copyAssets(context, dir, name);
                installOpk(path);
            }

        } catch (IOException e) {
            e.printStackTrace();
            Log.d(TAG, "Install preset opk : assets no dir  " + dir);
        }
    }

    /**
     * 预置OPK版本比对
     */
    private static boolean isNewVersion(String fileName) {
        String[] namePart = fileName.split("-");
        if (namePart.length == NAME_PART_LENGTH) {
            String version = namePart[1];
            String appId = namePart[2];

            try {
                Date date = new SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault()).parse(namePart[3]);
                String buildVersion = new SimpleDateFormat("yyMMddHH", Locale.getDefault()).format(date);
                version = version.substring(0, version.lastIndexOf(".") + 1) + buildVersion;
            } catch (ParseException e) {
                e.printStackTrace();
            }

            //App不存在
            if (!AppManger.INSTANCE.isAppValid(appId)) {
                Log.d(TAG, "Install preset opk : current is not exits " + appId);
                return true;
            }

            OPKBeanV3 appInfo = AppManger.INSTANCE.getRPKByCharacter(appId);
            return appInfo == null
                    || OPKHelper.INSTANCE.compareVersion(appInfo.getVersionName(), version) == 1;
        }
        return false;
    }

    /**
     * 拷贝Assets文件到sdcard目录
     *
     * @param context
     * @param dir
     * @param fileName
     * @return
     */
    private static String copyAssets(Context context, String dir, String fileName) {
        File file = new File(mOPKDir, fileName);
        try {
            InputStream input = context.getAssets().open(dir + "/" + fileName);
            FileHelper.copyFile(input, file);
            return file.getAbsolutePath();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static String getPath(String dirName) {
        File dir = new File(Environment.getExternalStorageDirectory(), dirName);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir.getAbsolutePath();
    }

    /**
     * 安装OPK
     *
     * @param path
     */
    private static boolean installOpk(String path) {
        AppBeanV2 appBean = OPKHelper.INSTANCE.getAppBeanForOPKFile(path, "");
        if (appBean != null) {
            AppManger.INSTANCE.addApp(appBean);

            //注册OPK信息
            CharacterManager.getInstance().registerRNCharacter(appBean.getRpkBean().getAppid());
            return true;
        } else {
            Log.d(TAG, "Install preset opk failed : " + path);
            return false;
        }
    }


    /**
     * 注册默认预置OPK
     */
    private static void startDefault() {

        //是否自动切换到RN
        boolean isAutoSwitch = BaseApplication.getApplication().getDefaultReactNativeAutoSwitch();

        if (isAutoSwitch) {
            //是否强制切换到RN
            boolean isForceSwitch = BaseApplication.getApplication().isForceSwitchRomOpk();

            String defaultCharacterName = LocalUtils.getGlobalSettings(BaseApplication.getApplication(), PlatformDef.OB_OPK_SWITCH, "");

            Character character = CharacterManager.getInstance().getDefault();
            if (character instanceof ReactCharacter) {
                Log.d(TAG, "Current is rn, not switch");
                return;
            }

            // 动态获取当前系统类型对应的默认应用 ID
            String currentDefaultAppId = BaseApplication.getApplication().getDefaultReactNativeAppId();
            Log.d(TAG, "Current system default app id: " + currentDefaultAppId);

            if (!CharacterManager.getInstance().isRegistered(currentDefaultAppId, CharacterInfo.PLATFORM_RN)) {
                Log.d(TAG, "Opk not exits : " + currentDefaultAppId);
                return;
            }

            if (TextUtils.isEmpty(defaultCharacterName) || isForceSwitch) {
                Log.d(TAG, "Switch local default app :  " + currentDefaultAppId);
                switchCharacter(currentDefaultAppId);
            }
        }
    }

    /**
     * 启动OPK
     *
     * @param appId
     */
    private static void switchCharacter(String appId) {
        try {
            // 先设置为默认角色
            Character character = CharacterManager.getInstance().getCharacter(appId);
            if (character != null) {
                CharacterManager.getInstance().setDefault(character);
                Log.d(TAG, "Set default character: " + appId);
            }
            
            RNServerManager.getInstance().setUpdate(true);
            JSONObject json = new JSONObject();
            json.put("name", appId);
            ControlManager.handleRequest(0, PlatformDef.SWITCH_CHARACTER, "", json.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
