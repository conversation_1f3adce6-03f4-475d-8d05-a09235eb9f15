package com.ainirobot.platform.appmanager.config.filter;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.platform.appmanager.config.Constants;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class ConvertKey implements IConvert {

    private static final String TAG = ConvertKey.class.getSimpleName();

    private HashMap<String, String> mSystemServers = new HashMap<>();

    public ConvertKey() {
        mSystemServers.put(Constants.SETTINGS_CHARGING_CHAT, "system_enable_touter_on_recharge");
        //休眠,robot,Setting
        mSystemServers.put(Constants.SETTINGS_STANDBY_TIME, "system_sleep_interval");
        //定时开关机时间， 有值为开
        mSystemServers.put(Constants.SETTINGS_SHUTDOWN_TIME, "system_auto_shutdown_time");
        //定时开关机开关
        mSystemServers.put(Constants.SETTINGS_SHUTDOWN_SWITCH, "system_enable_auto_shutdown");
        //设置密码
        mSystemServers.put(Constants.SETTINGS_MEAL_PASSWORD, "system_admin_password");
        mSystemServers.put(Constants.SETTINGS_SYSTEM_ROBOT_PASSWORD_SWITCH, "system_robot_password_switch");
        //锁屏开关
        mSystemServers.put(Constants.SETTINGS_SYSTEM_AUTO_LOCK_SWITCH, "system_auto_lock_switch");
        //锁屏时间
        mSystemServers.put(Constants.SETTINGS_SYSTEM_LOCK_TIME, "system_lock_time");

        mSystemServers.put(Constants.SETTINGS_SYSTEM_ROBOT_PASSWORD, "system_robot_password");
        //防盗警报
        mSystemServers.put(Constants.ROBOT_SETTING_MAP_OUTSIDE_WARNING, "robot_setting_map_outside_warning");
        //按距离远近导航
        mSystemServers.put(Constants.ROBOT_SETTINGS_NAVIGATION_BY_DISTANCE, "robot_navigation_by_distance");
        //防撞条开关
        mSystemServers.put(Constants.ROBOT_SETTING_COLLISION_STRIP_SWITCH, "robot_setting_anticollision_strip_switch");
        //托盘摄像头开关
        mSystemServers.put(Constants.SETTINGS_ROBOT_UVC_CAMERA_SWITCH, "robot_setting_uvc_camera_switch");
        //托盘氛围灯开关
        mSystemServers.put(Constants.SETTINGS_ROBOT_PRO_TRAY_LED_SWITCH, "robot_setting_pro_tray_led_switch");
        //第三方target
        mSystemServers.put(Definition.ROBOT_SETTING_ENABLE_TARGET_CUSTOM, "system_enable_target_custom");
        //单码定位，默认为开，1
        mSystemServers.put(Definition.ROBOT_SETTING_ENABLE_SINGLE_MARKER, "system_enable_single_marker");
        //动态物体过滤功能，默认为开，1
        mSystemServers.put(Definition.ROBOT_SETTING_ENABLE_DYNAMIC_MAP_FILTER, "system_enable_dynamic_map_filter");
        //黑屏检测, 默认为开, 1
        mSystemServers.put(Definition.ROBOT_SETTING_ENABLE_BLACKCHECK, "system_enable_blackcheck");
        //禁用激光数据过滤采信禁行线, 默认为, 0
        mSystemServers.put(Definition.ROBOT_SETTING_ENABLE_GRID_FILTER_USE_COSTMAP_OBS, "system_disable_grid_filter_use_costmap_obs");
        //刹车模式
        mSystemServers.put(Constants.ROBOT_SETTINGS_NAVIGATION_BREAK_MODE, "robot_setting_navigation_break_mode_level");
        //启动模式
        mSystemServers.put(Constants.ROBOT_SETTINGS_NAVIGATION_START_MODE, "robot_setting_navigation_start_mode_level");
        //低电回充位置
        mSystemServers.put(Constants.ROBOT_SETTING_LOW_BATTERY_NAVI_LOCATION, "robot_setting_low_battery_navi_location");
        //低电回充位置开关
        mSystemServers.put(Constants.ROBOT_SETTING_LOW_BATTERY_NAVI_LOCATION_ENABLE, "robot_setting_low_battery_navi_location_enable");
        //低电量TTS开关
        mSystemServers.put(Constants.ROBOT_SETTING_LOW_BATTERY_TTS_SWITCH, "robot_setting_low_battery_tts_switch");
        //充电方式
        mSystemServers.put(Constants.ROBOT_SETTINGS_CHARGING_TYPE, "robot_settings_charging_type");
        //货物高度检测开关
//        mSystemServers.put(Constants.ROBOT_SETTING_HEIGHT_LIMIT, "robot_setting_height_limit");

    }

    @Override
    public HashMap<String, Object> convertToServer(HashMap<String, Object> settings) {
        Set<Map.Entry<String, Object>> entries = settings.entrySet();
        HashMap<String, Object> tmp = new HashMap<>(settings.size());
        for (Map.Entry<String, Object> entry : entries) {
            if (mSystemServers.containsKey(entry.getKey())) {
//                if (TextUtils.equals(Constants.SETTINGS_SHUTDOWN_SWITCH, entry.getKey())) {
//                    tmp.put(mSystemServers.get(Constants.SETTINGS_SHUTDOWN_SWITCH), TextUtils.isEmpty((CharSequence) entry.getValue()) ? "0" : "1");
//                }
                if (TextUtils.equals(Constants.SETTINGS_CHARGING_CHAT, entry.getKey())) {
                    tmp.put(mSystemServers.get(entry.getKey()), transStringToInt(entry.getValue() == null ? "" : String.valueOf(entry.getValue())));
                    continue;
                }
                if (TextUtils.equals(Constants.SETTINGS_SHUTDOWN_SWITCH, entry.getKey())) {
                    tmp.put(mSystemServers.get(entry.getKey()), transStringToInt(entry.getValue() == null ? "" : String.valueOf(entry.getValue())));
                    continue;
                }
                if (TextUtils.equals(Definition.ROBOT_SETTING_ENABLE_GRID_FILTER_USE_COSTMAP_OBS, entry.getKey())) {
                    // convertToLocal 做了反写转换, convertToServer没有做, 导致下次云端下发的值变化
                    String v = String.valueOf(entry.getValue());
                    Log.i(TAG, "convertToServer USE_COSTMAP_OBS :" + v);
                    if (TextUtils.isEmpty(v)) {
                        v = "0";
                    }
                    if (v.equals("1")) {
                        tmp.put(entry.getKey(), "0");
                    } else {
                        tmp.put(entry.getKey(), "1");
                    }
                    continue;
                }
                tmp.put(mSystemServers.get(entry.getKey()), entry.getValue());
            }
        }
        return tmp;
    }

    @Override
    public HashMap<String, Object> convertToLocal(HashMap<String, Object> readConfig) {
        Log.i(TAG, "readConfig :" + readConfig);
        Set<Map.Entry<String, Object>> entries = readConfig.entrySet();
        HashMap<String, Object> tmp = new HashMap<>(readConfig.size());
        for (Map.Entry<String, Object> entry : entries) {
            String key = getKey(entry.getKey());
            if (key == null) {
                continue;
            }
            //TODO:这种强转逻辑后面开发者极易丢失添加，应该替换其它方案处理
            if (TextUtils.equals(Constants.SETTINGS_CHARGING_CHAT, key) ||
                    TextUtils.equals(Constants.ROBOT_SETTING_MAP_OUTSIDE_WARNING, key)) {
                tmp.put(key, transIntToString(entry.getValue() == null ? "" : String.valueOf(entry.getValue())));
                continue;
            } else if (TextUtils.equals(Definition.ROBOT_SETTING_ENABLE_GRID_FILTER_USE_COSTMAP_OBS, key)) {
                ///> 语义翻转, 云控只能添加给0, 不添加给1, 所以这个云控的语义是禁用,
                ///> 而此设置端上语义是开启需进行语义翻转
                ///> 至于端上为何不能直接才用禁用语义, 原因如下
                ///> 导航优先于业务启动, 需要获取配置, 如配置无法得到则, 配置接口会固定返回 0, 如果采取禁用语义, 那么这个0则是开启不应该开启的选项
                String v = String.valueOf(entry.getValue());
                Log.i(TAG, "convertToLocal USE_COSTMAP_OBS :" + v);
                if (TextUtils.isEmpty(v)) {
                    v = "1";
                }
                if (v.equals("1")) {
                    tmp.put(key, "0");
                } else {
                    tmp.put(key, "1");
                }
                continue;
            }
            tmp.put(key, entry.getValue());
        }
//        resetShutdownSwitch(readConfig, tmp);
        Log.i(TAG, "convertToLocal save " + tmp);
        return tmp;
    }

    private String transIntToString(String value) {
        return String.valueOf(transStringToInt(value));
    }

    private int transStringToInt(String value) {
        if (TextUtils.isEmpty(value)) {
            return 0;
        }
        if (value.indexOf(".") > 0) {
            return Double.valueOf(value).intValue();
        }
        return Integer.parseInt(value);
    }

//    private void resetShutdownSwitch(HashMap<String, Object> readConfig, HashMap<String, Object> tmp) {
//        Object obj = readConfig.get(mSystemServers.get(Constants.SETTINGS_SHUTDOWN_SWITCH));
//        if (obj == null) {
//            return;
//        }
//        String switchSettings = String.valueOf(obj);
//        Log.i(TAG, "readConfig  robot_setting_shutdown_switch " + switchSettings);
//        if (transStringToInt(switchSettings) == 0) {
//            tmp.put(Constants.SETTINGS_SHUTDOWN_TIME, "");
//        }
//    }

    private String getKey(String value) {
        Set<Map.Entry<String, String>> entries = mSystemServers.entrySet();
        for (Map.Entry<String, String> entry : entries) {
            if (TextUtils.equals(entry.getValue(), value)) {
                return entry.getKey();
            }
        }
        return null;
    }
}
