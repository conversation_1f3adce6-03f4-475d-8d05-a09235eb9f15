package com.ainirobot.platform.appmanager.task;

import com.ainirobot.platform.appmanager.AppInfo;
import com.ainirobot.platform.react.AppManger;
import com.ainirobot.platform.react.OPKBeanV3;

/**
 * 卸载OPK
 */
public class AppUninstall extends AppOperTask {

    public static final String STATUS_UNINSTALL_SKIP = "uninstall_skip";
    public static final String STATUS_UNINSTALL_START = "uninstall_start";
    public static final String STATUS_UNINSTALL_OK = "uninstall_ok";
    public static final String STATUS_UNINSTALL_FAILED = "uninstall_fail";

    public AppUninstall(AppInfo info) {
        super(info);
    }

    @Override
    public boolean prepare() {
        if (!AppManger.INSTANCE.isAppValid(mInfo.appId)) {
            updateStatus(STATUS_UNINSTALL_SKIP);
            updateStatus(STATUS_SUCCEED);
            return false;
        }
        return true;
    }

    @Override
    public Result run() {
        updateStatus(STATUS_START);
        if (!AppManger.INSTANCE.isAppValid(mInfo.appId)) {
            updateStatus(STATUS_UNINSTALL_SKIP);
        } else {
            updateStatus(STATUS_UNINSTALL_START);
            uninstallOpk(mInfo.appId);
            updateStatus(STATUS_UNINSTALL_OK);
        }
        updateStatus(STATUS_SUCCEED);

        Result result = new Result();
        result.result = true;
        result.taskInfo = mInfo;
        return result;
    }

    /**
     * 卸载OPK
     *
     * @param appId
     */
    private void uninstallOpk(String appId) {
        OPKBeanV3 appInfo = AppManger.INSTANCE.getRPKByCharacter(appId);
        if (appInfo == null) {
            return;
        }

        AppManger.INSTANCE.removeAppId(appId);
    }

    @Override
    public String getType() {
        return TYPE_UNINSTALL;
    }

    @Override
    public boolean isNeedReboot() {
        return true;
    }
}
