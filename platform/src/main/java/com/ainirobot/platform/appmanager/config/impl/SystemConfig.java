package com.ainirobot.platform.appmanager.config.impl;

import android.content.Intent;
import android.os.Build;
import android.os.Environment;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingListener;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.appmanager.config.Constants;
import com.ainirobot.platform.appmanager.config.bean.ConfigItem;
import com.ainirobot.platform.appmanager.config.bi.SystemConfigReport;
import com.ainirobot.platform.appmanager.config.cloud.ICloud;
import com.ainirobot.platform.appmanager.config.filter.ConvertKey;
import com.ainirobot.platform.appmanager.config.filter.IConvert;
import com.ainirobot.platform.appmanager.config.filter.SystemFilter;
import com.ainirobot.platform.appmanager.config.storage.IStorage;
import com.ainirobot.platform.bi.wrapper.ReportControl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Created by wuyong on 2018/6/28.
 */

public class SystemConfig extends NormalConfig {

    private static final String TAG = SystemConfig.class.getSimpleName();

    public static final String APP_ID = "system_robot_settings";
    public static final String CONFIG_PATH = Environment.getExternalStorageDirectory() + "/robot/rnconfig/system_robot_settings.cfg";

    private IConvert mConvert;

    private ICloud mCloud;

    private SystemFilter mSystemFilter;
    private RobotSettingApi mRobotSetting;

    private HashMap<String, String> mSystemKeys = new HashMap<>();
    private HashMap<String, Object> mPrepareSettings = new HashMap<>();
    private ArrayList<String> mSetChangeOnly = new ArrayList<>();

    public SystemConfig(IStorage storage, ICloud cloud, RobotSettingApi robotSetting) {
        super(APP_ID, storage, cloud);
        this.mConvert = new ConvertKey();
        this.mSystemFilter = new SystemFilter(storage, this.mConvert);
        this.setStorage(this.mSystemFilter);

        this.mRobotSetting = robotSetting;
        initKeys();
        initDefaultValues();
        initSetChangeOnly();
        onChargingListener();
    }




    private void initKeys() {
        mSystemKeys.put(Constants.SETTINGS_CHARGING_CHAT, Constants.SETTINGS_CHARGING_CHAT_DEFAULT);
        mSystemKeys.put(Constants.SETTINGS_STANDBY_TIME, Constants.SETTINGS_STANDBY_TIME_DEFAULT);
        mSystemKeys.put(Constants.SETTINGS_SHUTDOWN_TIME, Constants.SETTINGS_SHUTDOWN_TIME_DEFAULT);
        mSystemKeys.put(Constants.SETTINGS_MEAL_PASSWORD, Constants.SETTINGS_MEAL_PASSWORD_DEFAULT);
        mSystemKeys.put(Constants.SETTINGS_SHUTDOWN_SWITCH, Constants.SETTINGS_SHUTDOWN_DISABLE_DEFAULT);
        mSystemKeys.put(Definition.ROBOT_SETTING_ENABLE_BLACKCHECK, "1");
        mSystemKeys.put(Definition.ROBOT_SETTING_ENABLE_GRID_FILTER_USE_COSTMAP_OBS, "0");
        mSystemKeys.put(Definition.ROBOT_SETTINGS_CHARGING_TYPE, Constants.SETTINGS_CHARGING_TYPE);
    }



    private void onChargingListener() {
        mRobotSetting.registerRobotSettingListener(new RobotSettingListener() {
            @Override
            public void onRobotSettingChanged(String key) {
                Log.i(TAG, "onChargingListener : " + key);
                if (Definition.ROBOT_SETTINGS_CHARGING_TYPE.equals(key)) {
                    String oldValue = mPrepareSettings.get(key) != null ? mPrepareSettings.get(key).toString() : null;
                    String newValue = getRobotSettingValue(Definition.ROBOT_SETTINGS_CHARGING_TYPE);
                    // 只有当充电类型真正发生变化时才进行初始化和广播
                    if (!TextUtils.equals(oldValue, newValue)) {
                        // 通过发送广播触发同步
                        Intent intent = new Intent("com.ainirobot.moduleapp.global.settings");
                        intent.putExtra("key", Definition.ROBOT_SETTINGS_CHARGING_TYPE);
                        intent.putExtra("value", newValue);
                        BaseApplication.getContext().sendBroadcast(intent);
                        mPrepareSettings.put(Definition.ROBOT_SETTINGS_CHARGING_TYPE, newValue);
                    } else {
                        Log.d(TAG, "Charging type unchanged: " + newValue + ", skip initialization and broadcast");
                    }
                }
            }
        }, Definition.ROBOT_SETTINGS_CHARGING_TYPE);

    }


    private void initDefaultValues() {
        Set<Map.Entry<String, String>> entries = mSystemKeys.entrySet();
        for (Map.Entry<String, String> entry : entries) {
            String robotStringValue = getRobotSettingValue(entry.getKey());
            mPrepareSettings.put(entry.getKey(), robotStringValue != null ? robotStringValue : entry.getValue());
        }
        Log.i(TAG, "initDefaultValues : " + mGson.toJson(mPrepareSettings));
    }

    private void initSetChangeOnly() {
        mSetChangeOnly.add(Definition.ROBOT_SETTING_ENABLE_TARGET_CUSTOM);
    }

    private String getRobotSettingValue(String key) {
        String robotStringValue = mRobotSetting.getRobotString(key);
        if (TextUtils.isEmpty(robotStringValue)) {
            robotStringValue = Settings.Global.getString(BaseApplication.getContext().getContentResolver(), key);
        }
        return robotStringValue;
    }

    @Override
    protected HashMap<String, Object> prepareSettings() {
        HashMap<String, Object> prepareSettings = super.prepareSettings();
        if (prepareSettings.size() > 0) {
            mPrepareSettings.putAll(prepareSettings);
        }
        Log.i(TAG, "prepareSettings : size: " + prepareSettings.size());
        Log.i(TAG, "prepareSettings : mPrepareSettings: " + mPrepareSettings);
        return mPrepareSettings;
    }

    @Override
    public void setConfig(ConfigItem item) {
        super.setConfig(item);
        updateSystemSettings(item);
        reportConfig(item);
    }

    private void updateSystemSettings(ConfigItem item) {
        Log.i(TAG, "updateSystemSettings :" + item.getType());
        switch (item.getType()) {
            case COVER:
            case FIRST_UPDATE:
                setRobotSettings(prepareSettings());
                break;
            case UPDATE:
                HashMap<String, Object> settings = item.getSettings();
                if (settings != null && settings.size() > 0) {
                    setRobotSettings(settings);
                }
                break;
            default:
                break;
        }
    }

    private void setRobotSettings(HashMap<String, Object> tmp) {
        if (tmp != null && tmp.size() > 0) {
            Set<Map.Entry<String, Object>> entries = tmp.entrySet();
            for (Map.Entry<String, Object> entry : entries) {
                Log.i(TAG, "setRobotSettings : " + entry.getKey() + " value: " + entry.getValue());
                try {
                    String value = "";
                    if (entry.getValue() instanceof String) {
                        value = entry.getValue() == null ? "" : (String) entry.getValue();
                    } else if (entry.getValue() instanceof Integer) {
                        value = String.valueOf(entry.getValue());
                    } else if (entry.getValue() instanceof Double) {
                        value = String.valueOf(((Double) entry.getValue()).intValue());
                    } else if (entry.getValue() instanceof Boolean) {
                        value = ((Boolean) entry.getValue()) ? "1" : "0";
                    }
                    if (mSetChangeOnly.contains(entry.getKey()) &&
                            mRobotSetting.getRobotString(entry.getKey()).equals(value)) {
                        Log.d(TAG, entry.getKey() + " not change.skip!");
                        continue;
                    }
                    if(ProductInfo.isSaiphXdOrBigScreen() && Definition.ROBOT_USABLE_WHEN_CHARGING.equals(entry.getKey())){
                        //消毒豹opk默认的配置项Definition.ROBOT_USABLE_WHEN_CHARGING,不同步到CoreService db，避免opk默认配置修改系统配置.
                        continue;
                    }
                    if(Constants.ROBOT_SETTINGS_CHARGING_TYPE.equals(entry.getKey())){
                        Log.d(TAG, entry.getKey() + " ，充电方式禁止云端写入机器端！");
                        continue;
                    }
                    mRobotSetting.setRobotString(entry.getKey(), value);
                    Settings.Global.putString(BaseApplication.getContext().getContentResolver(), entry.getKey(), value);
                } catch (Exception e) {
                    Log.e(TAG, "error: " + e.getMessage());
                }
            }
        }
    }

    private void reportConfig(ConfigItem item) {
        try{
            Log.i(TAG, "reportConfig: " + item.getSettings());
            //刹车模式、启动模式不上报
            if(item.getSettings() != null && (item.getSettings().containsKey(Constants.ROBOT_SETTINGS_NAVIGATION_BREAK_MODE)
                    || item.getSettings().containsKey(Constants.ROBOT_SETTINGS_NAVIGATION_START_MODE))){
                return;
            }
        }catch (Exception e){
            Log.e(TAG, "error: " + e.getMessage());
        }


        switch (item.getType()) {
            case COVER:
            case FIRST_UPDATE:
                reportSystemConfig(mConvert.convertToServer(prepareSettings()));
                break;
            case UPDATE:
                HashMap<String, Object> settings = item.getSettings();
                if (settings != null && settings.size() > 0) {
                    reportSystemConfig(mConvert.convertToServer(settings));
                }
                break;
            default:
                break;
        }
    }

    private void reportSystemConfig(HashMap<String, Object> settings) {
        if (settings != null && settings.size() > 0) {
            Set<Map.Entry<String, Object>> entries = settings.entrySet();
            for (Map.Entry<String, Object> entry : entries) {
                ReportControl.getInstance().reportMsg(new SystemConfigReport(entry.getKey(), entry.getValue() == null ? "" : String.valueOf(entry.getValue())));
            }
        }
    }
}
