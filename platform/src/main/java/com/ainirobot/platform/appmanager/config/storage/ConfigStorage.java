package com.ainirobot.platform.appmanager.config.storage;

import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import com.ainirobot.platform.appmanager.config.ConfigUtil;
import com.ainirobot.platform.appmanager.config.impl.SystemConfig;
import com.ainirobot.platform.react.AppManger;
import com.ainirobot.platform.react.OPKBeanV3;
import com.ainirobot.platform.utils.FileHelper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.util.HashMap;

public class ConfigStorage implements IStorage {

    private static final String TAG = ConfigStorage.class.getSimpleName();

    private Gson mGson;

    public ConfigStorage(Gson gson) {
        this.mGson = gson;
    }

    private String getConfigPath(String appId) {
        if (TextUtils.equals(SystemConfig.APP_ID, appId)) {
            return SystemConfig.CONFIG_PATH;
        }

        OPKBeanV3 opkInfo = AppManger.INSTANCE.getRPKByCharacter(appId);
        if (opkInfo != null && !TextUtils.isEmpty(opkInfo.getConfigPath())) {
            return opkInfo.getConfigPath();
        }
        return null;
    }

    @Override
    public boolean isExistConfig(String appId){
        String configPath = getConfigPath(appId);
        return configPath != null && FileHelper.isExist(configPath);
    }

    @Override
    public HashMap<String, Object> readConfig(String appId) {
        JSONObject appConfig = getAppConfig(appId);
        if (appConfig == null) {
            Log.e(TAG, "appConfig is null");
            return new HashMap<>(16);
        }
        try {
            String configData = new String(Base64.decode(appConfig.getString("cfg_data"), Base64.DEFAULT));
            JSONObject configJson = new JSONObject(configData);
            String dataConfig = configJson.getString("robot");
            Log.i(TAG,"readConfig : " + dataConfig);
            if (!TextUtils.isEmpty(dataConfig)) {
                return mGson.fromJson(dataConfig, new TypeToken<HashMap<String, Object>>() {
                }.getType());
            }
        } catch (Exception e) {
            Log.e(TAG, "readConfig error" + e.getMessage());
        }
        return new HashMap<>(16);
    }

    @Override
    public boolean saveConfig(String appId, HashMap<String, Object> configs) {
        Log.i(TAG, "saveConfig : " + appId + " data: " + mGson.toJson(configs));
        JSONObject appConfig = getAppConfig(appId);
        String realData = getConfigData(configs, appConfig);
        Log.i(TAG, "coverConfig : " + realData);
        if (!TextUtils.isEmpty(realData)) {
            String configPath = getConfigPath(appId);
            if (configPath != null && FileHelper.isExist(configPath)) {
                FileHelper.deleteTargetFile(configPath);
            }
            FileHelper.saveToFile(configPath, realData);
            return true;
        }
        return false;
    }

    @NotNull
    private String getConfigData(HashMap<String, Object> configs, JSONObject appConfig) {
        String realData = "";
        try {
            String configData = new String(Base64.decode(appConfig.getString("cfg_data"), Base64.DEFAULT));
            JSONObject configJson = new JSONObject(configData);
            configJson.put("robot", new JSONObject(configs));
            String dataConfig = ConfigUtil.filterValue(configJson.toString());;
            Log.i(TAG,"dataConfig : " + dataConfig);
            appConfig.put("cfg_data", new String(Base64.encode(dataConfig.getBytes(), Base64.DEFAULT)));
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("app_config", appConfig);
            realData = jsonObject.toString();
        } catch (Exception e) {
            Log.e(TAG, "readConfig error" + e.getMessage());
        }
        return realData;
    }

    private JSONObject getAppConfig(String appId) {
        String configPath = getConfigPath(appId);
        if (configPath == null || TextUtils.isEmpty(configPath)) {
            Log.e(TAG, "readConfig configPath is null");
            return null;
        }

        try {
            String config = FileHelper.loadJsonFromFile(configPath);
            JSONObject json = new JSONObject(config);
            return json.getJSONObject("app_config");
        } catch (Exception e) {
            Log.e(TAG, "getAppConfig error :" + e.getMessage());
        }
        return null;
    }

}
