package com.ainirobot.platform.appmanager.task;

import android.content.Context;
import android.util.Log;

import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.appmanager.AppInfo;
import com.ainirobot.platform.react.AppManger;
import com.ainirobot.platform.react.OPKBeanV3;

import com.ainirobot.platform.utils.LocalUtils;


/**
 * 更新宿主配置
 */
public class UpdateConfig extends AppOperTask {

    private static final String TAG = "UpdateConfig";

    private final String PORTAL = "portal";

    public UpdateConfig(AppInfo info) {
        super(info);
    }

    @Override
    public boolean prepare() {
        if (getPortalOpk().equals(mInfo.appId)) {
            return false;
        }

        OPKBeanV3 info = AppManger.INSTANCE.getRPKByCharacter(mInfo.appId);
        if (info != null) {
            mInfo.appName = info.getAppName();
        } else {
            mInfo.appName = "默认OPK";
        }
        return true;
    }

    @Override
    public Result run() {
        setPortalOpk(mInfo.appId);
        Result result = new Result();
        result.taskInfo = mInfo;
        result.result = true;
        return result;
    }

    private void setPortalOpk(String appId) {
        Log.d(TAG, "Set portal opk : " + appId + "   " + mInfo.appName);
        Context context = BaseApplication.getContext();
        LocalUtils.set(context, PORTAL, appId);
    }

    private String getPortalOpk() {
        Context context = BaseApplication.getContext();
        return LocalUtils.get(context, PORTAL);
    }

    @Override
    public boolean isNeedReboot() {
        return true;
    }

    @Override
    public String getType() {
        return AppOperTask.TYPE_GLOBAL_CONFIG;
    }
}
