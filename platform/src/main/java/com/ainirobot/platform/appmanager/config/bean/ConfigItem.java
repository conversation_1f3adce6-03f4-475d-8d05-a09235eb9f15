package com.ainirobot.platform.appmanager.config.bean;

import com.ainirobot.platform.appmanager.config.ConfigUtil;
import com.ainirobot.platform.appmanager.config.impl.ConfigType;

import java.util.HashMap;


public class ConfigItem {

    private String mAppId;
    private ConfigType mType;
    private HashMap<String,Object> mSettings;

    public ConfigItem(String mAppId, ConfigType mType) {
        super();
        this.mAppId = mAppId;
        this.mType = mType;
    }

    public String getAppId() {
        return mAppId;
    }

    public void setAppId(String appId) {
        this.mAppId = appId;
    }

    public ConfigType getType() {
        return mType;
    }

    public void setType(ConfigType type) {
        this.mType = type;
    }

    public HashMap<String, Object> getSettings() {
        return mSettings;
    }

    public void setSettings(HashMap<String, Object> sSettings) {
        this.mSettings = sSettings;
    }

    public void setConfig(String key,String value){
        HashMap<String,Object> tmpMap = new HashMap<>(10);
        tmpMap.put(key, ConfigUtil.filterValue(value));
        this.mSettings = tmpMap;
    }
}
