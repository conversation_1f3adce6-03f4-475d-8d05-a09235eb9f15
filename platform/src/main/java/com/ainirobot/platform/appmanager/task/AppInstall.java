package com.ainirobot.platform.appmanager.task;

import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.platform.appmanager.AppInfo;
import com.ainirobot.platform.appmanager.config.ConfigUtil;
import com.ainirobot.platform.react.AppManger;
import com.ainirobot.platform.react.OPKBeanV3;
import com.ainirobot.platform.react.OPKHelper;
import com.ainirobot.platform.utils.FileDownloadManager;
import com.ainirobot.platform.utils.FileDownloadManager.DownloadListener;
import com.ainirobot.platform.utils.FileHelper;
import com.ainirobot.platform.utils.OpkFile;

import java.io.File;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 插件安装
 */
public class AppInstall extends AppOperTask {

    /**
     * 下载目录
     */
    private static final String DOWNLOAD_DIR = "/robot/rndownload";


    /**
     * 最小剩余空间, 机器剩余空间小于200M, 不再进行OPK安装和更新
     */
    private static final long MIN_SPACE = 200 * 1024 * 1024;

    /*------------ OPK安装失败原因 --------*/
    /**
     * 参数错误
     */
    private static final int INSTALL_ERROR_PARAMS_INVALID = -1001;

    /**
     * 机器剩余空间不足
     */
    private static final int INSTALL_ERROR_NOT_ENOUGH_SPACE = -1008;

    public static final int INSTALL_ERROR_OPK_FAILED = -1010;
    public static final int INSTALL_ERROR_OPK_ERROR = -1011;
    public static final int INSTALL_SAVE_PLUGIN_INFO_FAILED = -1009;
    public static final int INSTALL_ERROR_CONFIG_FAILED = -1008;
    public static final int INSTALL_ERROR_FILE_NOT_EXISTS = -1004;
    private static final int INSTALL_ERROR_CFG_CONTENT = -1005;
    private static final int INSTALL_ERROR_OPK_DOWNLOAD_FAILED = -1006;
    private static final int INSTALL_ERROR_CFG_DOWNLOAD_FAILED = -1007;

    public static final String STATUS_OPK_SKIP = "opk_skip";
    public static final String STATUS_OPK_DOWNLOAD_START = "opk_download_start";
    public static final String STATUS_OPK_DOWNLOAD_OK = "opk_download_ok";
    public static final String STATUS_OPK_DOWNLOAD_FAILED = "opk_download_fail";
    public static final String STATUS_CFG_DOWNLOAD_START = "cfg_download_start";
    public static final String STATUS_CFG_DOWNLOAD_OK = "cfg_download_ok";
    public static final String STATUS_CFG_DOWNLOAD_FAILED = "cfg_download_fail";
    public static final String STATUS_OPK_INSTALL_START = "opk_install_start";
    public static final String STATUS_OPK_INSTALL_OK = "opk_install_ok";
    public static final String STATUS_OPK_INSTALL_FAILED = "opk_install_fail";
    public static final String STATUS_CFG_SKIP = "cfg_skip";
    public static final String STATUS_CFG_INSTALL_START = "cfg_install_start";
    public static final String STATUS_CFG_INSTALL_OK = "cfg_install_ok";
    public static final String STATUS_CFG_INSTALL_FAILED = "cfg_install_fail";

    /**
     * 操作类型
     */
    private String mOperType;

    /**
     * 下载存储目录
     */
    private File mDownloadDir;

    /**
     * OPk存储路径
     */
    private String mOpkPath;

    /**
     * 配置文件存储路径
     */
    private String mConfigPath;

    private LinkedBlockingQueue<Boolean> mOpkEvent = new LinkedBlockingQueue<>(1);
    private LinkedBlockingQueue<Boolean> mConfigEvent = new LinkedBlockingQueue<>(1);

    public AppInstall(AppInfo info) {
        super(info);
        mOperType = initOperType();
        mDownloadDir = mkDownloadDir();
        Log.d(TAG, "DEBUG======dir:" + mDownloadDir);
    }

    private String initOperType() {
        if (!TextUtils.isEmpty(mInfo.url)) {
            if (isNewOPKVersion()) {
                if (AppManger.INSTANCE.isAppValid(mInfo.appId)) {
                    Log.d(TAG, "DEBUG======update");
                    return TYPE_UPDATE;
                } else {
                    Log.d(TAG, "DEBUG======install");
                    return TYPE_INSTALL;
                }
            } else {
                Log.e(TAG, "has opk url, but new opk version low");
            }
        }

        if (!TextUtils.isEmpty(mInfo.configUrl)) {
            return TYPE_CONFIG;
        }

        Log.e(TAG, "has opk url, but new opk version low");
        return TYPE_NONE;
    }

    @Override
    public boolean prepare() {
        //参数错误
        if (TYPE_NONE.equals(getType())) {
            updateStatus(STATUS_FAILED, INSTALL_ERROR_PARAMS_INVALID);
            return false;
        }

        long freeSize = Environment.getExternalStorageDirectory().getFreeSpace();
        //本地存储空间不足
        if (freeSize < MIN_SPACE) {
            updateStatus(STATUS_FAILED, INSTALL_ERROR_NOT_ENOUGH_SPACE);
            return false;
        }

        if (!isNewOPKVersion()
                && !isNewConfigVersion()) {
            updateStatus(STATUS_SUCCEED);
            return false;
        }

        if (isNewOPKVersion()) {
            startDownloadOpk();
        }
        if (isNewConfigVersion()) {
            startDownloadConfig();
        }
        return true;
    }

    @Override
    public Result run() {
        updateStatus(STATUS_START);
        Result result = install();

        if (result.result) {
            updateStatus(STATUS_SUCCEED);
        }

        deleteDownloadFile();
        return result;
    }

    /**
     * 安装
     *
     * @return
     */
    public Result install() {
        Result result = new Result();
        result.taskInfo = mInfo;
        if (!handleOpk(result)) {
            //再次检查下版本，有可能在下载OPK期间，该OPK已被安装
            if (!isNewOPKVersion()) {
                Log.d(TAG, "Opk skip : " + mInfo.appName);
                updateStatus(STATUS_OPK_SKIP);
            } else {
                result.result = false;
                if (!result.ignore) {
                    updateStatus(STATUS_FAILED, INSTALL_ERROR_OPK_FAILED);
                }
                return result;
            }
        }

        if (!handleConfig(result)) {
            if (TYPE_CONFIG.equals(getType())) {
                result.result = false;
                if (!result.ignore) {
                    updateStatus(STATUS_FAILED, INSTALL_ERROR_CONFIG_FAILED);
                }
                return result;
            }
        }
        result.result = true;
        return result;
    }

    /**
     * 处理配置文件
     *
     * @param result
     * @return
     */
    private boolean handleConfig(Result result) {
        Log.d(TAG, "Handle config : " + mInfo.appName + "  " + mInfo.configUrl);
        if (TextUtils.isEmpty(mInfo.configUrl)) {
            return true;
        }

        if (!isNewConfigVersion()) {
            Log.d(TAG, "Config skip : " + mInfo.appName);
            updateStatus(STATUS_CFG_SKIP);
            return true;
        }

        try {
            Log.d(TAG, "Task config event : " + mOpkEvent.size());
            Boolean cfgDownloadResult = mConfigEvent.poll(50, TimeUnit.SECONDS);
            Log.d(TAG, "Task opk result : " + cfgDownloadResult);

            //下载失败
            if (cfgDownloadResult == null || !cfgDownloadResult) {
                updateStatus(STATUS_CFG_DOWNLOAD_FAILED);
                return false;
            }

            //该安装任务中不包含OPK
            if (mConfigPath == null) {
                return true;
            }

            result.configPath = mConfigPath;
            return true;
        } catch (InterruptedException e) {
            e.printStackTrace();
            result.ignore = true;
            return false;
        }
    }

    private boolean isNewOPKVersion() {
        if (TextUtils.isEmpty(mInfo.version)) {
            return false;
        }

        OPKBeanV3 opkInfo = AppManger.INSTANCE.getRPKByCharacter(mInfo.appId);
        //与当前版本一致
        return opkInfo == null
                || OPKHelper.INSTANCE.compareVersion(opkInfo.getVersionName(), mInfo.version) > 0;
    }

    private boolean isNewConfigVersion() {
        OPKBeanV3 opkInfo = AppManger.INSTANCE.getRPKByCharacter(mInfo.appId);
        Log.d(TAG, "ConfigId : " + mInfo.configId + "   local : " + (opkInfo == null ? "null" : opkInfo.getConfigId()));

        if (opkInfo == null
                || opkInfo.getConfigId() == null
                || !opkInfo.getConfigId().equals(mInfo.configId)
                || opkInfo.getConfigPath() == null) {
            return true;
        }

        return !new File(opkInfo.getConfigPath()).exists();
    }

    /**
     * 处理OPK文件
     *
     * @param result
     * @return
     */
    private boolean handleOpk(Result result) {
        Log.d(TAG, "Handle opk : " + mInfo.appName + "  " + mInfo.url);
        if (TextUtils.isEmpty(mInfo.url)) {
            return true;
        }

        //非新版本
        if (!isNewOPKVersion()) {
            Log.d(TAG, "Opk skip : " + mInfo.appName);
            updateStatus(STATUS_OPK_SKIP);
            return true;
        }

        try {
            Log.d(TAG, "Task opk event : " + mOpkEvent.size() + " " + mInfo.appName);
            Boolean opkDownloadResult = mOpkEvent.poll(2 * 60, TimeUnit.SECONDS);
            Log.d(TAG, "Task opk result : " + opkDownloadResult);

            //下载失败
            if (opkDownloadResult == null || !opkDownloadResult) {
                updateStatus(STATUS_OPK_DOWNLOAD_FAILED);
                return false;
            }

            //该安装任务中不包含OPK
            if (mOpkPath == null) {
                return true;
            }

            result.appInfo = OPKHelper.INSTANCE.getAppBeanForOPKFile(mOpkPath, "");
            if (result.appInfo == null) {
                updateStatus(STATUS_OPK_INSTALL_FAILED, INSTALL_ERROR_OPK_ERROR);
                return false;
            }

            if (!mInfo.appId.equals(result.appInfo.getRpkBean().getAppid())) {
                Log.d(TAG, "AppId not match : " + mInfo.appId + "    " + result.appInfo.getRpkBean().getAppid());
                updateStatus(STATUS_OPK_INSTALL_FAILED, INSTALL_ERROR_PARAMS_INVALID);
                result.appInfo = null;
                return false;
            }

            if (!TextUtils.isEmpty(mInfo.appName)) {
                result.appInfo.getRpkBean().setAppName(mInfo.appName);
            }

            Log.d(TAG, "Install app info : " + result.appInfo.getRpkBean().getAppName());
            return true;
        } catch (InterruptedException e) {
            e.printStackTrace();
            result.ignore = true;
            return false;
        }
    }

    /**
     * 创建存储目录
     *
     * @return
     */
    private File mkDownloadDir() {
        return getPath(DOWNLOAD_DIR + "/" + mInfo.appId + "/" + mInfo.version);
    }

    private File getPath(String dirName) {
        File dir = new File(Environment.getExternalStorageDirectory(), dirName);
        boolean res = false;
        if (!dir.exists()) {
            res = dir.mkdirs();
        }
        Log.e(TAG, "getPath:" + dirName + ", res:" + res + ", exists:" + dir.exists());
        return dir;
    }

    /**
     * 开始下载OPK文件
     */
    private void startDownloadOpk() {
        //单独更新配置文件时，OPK下载URL可能为空
        if (TextUtils.isEmpty(mInfo.url)) {
            mOpkEvent.offer(true);
            return;
        }

        Log.d(TAG, "Start download apk : " + mInfo.appName + "   " + mInfo.url);
        File file = new File(mDownloadDir, mInfo.appId + ".opk");
        //当前OPK文件已存在，且验证通过
        if (file.exists() && OpkFile.verify(file) == 0) {
            mOpkPath = file.getAbsolutePath();
            mOpkEvent.offer(true);
            return;
        }

        FileDownloadManager.download(mInfo.url, file, new DownloadListener() {
            @Override
            public void onSucceed(String uri) {
                mOpkPath = uri;
                Log.d(TAG, "OPK download succeed : " + mInfo.appName + "    " + mOpkPath);
                mOpkEvent.offer(true);
            }

            @Override
            public void onFailed(int errorCode) {
                mOpkEvent.offer(false);
                Log.d(TAG, "OPK download failed : " + mInfo.appName);
            }
        });
    }

    /**
     * 开始下载配置文件
     */
    private void startDownloadConfig() {
        if (TextUtils.isEmpty(mInfo.configUrl)) {
            mConfigEvent.offer(true);
            return;
        }

        Log.d(TAG, "Start download config : " + mInfo.appName + "   " + mInfo.configUrl + "   " + mDownloadDir);
        File file = new File(mDownloadDir, mInfo.configId + ".cfg");
        if (file.exists() && ConfigUtil.verifyConfig(file.getAbsolutePath())) {
            mConfigPath = file.getAbsolutePath();
            mConfigEvent.offer(true);
            return;
        }

        FileDownloadManager.download(mInfo.configUrl, file, new DownloadListener() {
            @Override
            public void onSucceed(String uri) {
                mConfigPath = uri;
                Log.d(TAG, "Config download succeed : " + mInfo.appName + "    " + mConfigPath);
                mConfigEvent.offer(true);
            }

            @Override
            public void onFailed(int errorCode) {
                mConfigEvent.offer(false);
                Log.d(TAG, "Config download failed : " + mInfo.appName);
            }
        });
    }

    public void deleteDownloadFile() {
        if (TextUtils.isEmpty(mInfo.version)) {
            return;
        }

        Log.d(TAG, "Delete download file : " + mInfo.version + "   " + mInfo.appId);
        File[] files = mDownloadDir.getParentFile().listFiles();
        for (File file : files) {
            if (file.isDirectory() && OPKHelper.INSTANCE.compareVersion(file.getName(), mInfo.version) > 0) {
                Log.d(TAG, "Delete download file : " + file.getAbsolutePath() + "   " + mInfo.version);
                FileHelper.deleteDirectory(file);
            }
        }
    }

    @Override
    public String getType() {
        return mOperType;
    }
}
