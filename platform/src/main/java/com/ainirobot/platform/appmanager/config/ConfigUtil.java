package com.ainirobot.platform.appmanager.config;

import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.platform.utils.FileHelper;

import java.io.File;
import java.io.IOException;

import org.json.JSONException;
import org.json.JSONObject;

public class ConfigUtil {

    private static final String TAG = ConfigUtil.class.getSimpleName();

    private static final String CONFIG_DIR = "/robot/rnconfig";

    /**
     * 配置文件是否存在
     * */
    public static boolean isExistConfig(String configPath){
        return configPath != null && FileHelper.isExist(configPath);
    }

    /**
     * 保存配置文件
     *
     * @param appId
     * @param configPath
     */
    public static String saveConfig(String appId, String configPath) {
        Log.d(TAG, "Save config : " + appId);
        if (TextUtils.isEmpty(configPath)) {
            Log.d(TAG, "Save config : config not exits " + appId);
            return null;
        }

        try {
            File appConfig = mkConfigDir(appId);
            Log.d(TAG, "Save config  : " + configPath + "  " + new File(configPath).exists() + " " + appConfig.getAbsolutePath());
            FileHelper.copyFile(new File(configPath), appConfig);
            return appConfig.getAbsolutePath();
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, "Save config failed : file read error " + appId);
        }
        return null;
    }

    private static File getPath(String dirName) {
        File dir = new File(Environment.getExternalStorageDirectory(), dirName);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir;
    }

    private static File mkConfigDir(String appId) {
        File dir = getPath(CONFIG_DIR);
        File file = new File(dir, appId + ".cfg");
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return file;
    }

    public static String filterValue(String value){
        value = value.replace("\\", "");
        value = value.replace("\"[", "[");
        value = value.replace("]\"", "]");
        value = value.replace("\"{", "{");
        value = value.replace("}\"", "}");
        return value;
    }

    public static boolean verifyConfig(String file) {
        try {
            Log.d(TAG, "check config file " + file + " start");
            String config = FileHelper.loadJsonFromFile(file);
            /**
             * 因为现阶段没有相关的校验文件完整性的方案
             * 所有暂定判断文件内容是否是json数据来校验文件
             */
            new JSONObject(config);
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
            Log.d(TAG, "verify config fail");
        }
        return false;
    }
}
