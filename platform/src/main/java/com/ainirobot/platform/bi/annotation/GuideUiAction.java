package com.ainirobot.platform.bi.annotation;

import androidx.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static com.ainirobot.platform.bi.annotation.GuideUiAction.*;

/**
 * guide module report action
 *
 * @version V1.0.0
 * @date 2019/2/20 15:06
 */

@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@IntDef({NEXT_STATION
        , FINISH
        , REQUEST
        , PAUSE
        , RESUME
        , PLAYLIST
        , PLAY_VIDEO
})
public @interface GuideUiAction {
     int NEXT_STATION = 1;
     int FINISH = 2;
     int REQUEST = 3;
     int PAUSE = 4;
     int RESUME = 5;
     int PLAYLIST = 6;
     int PLAY_VIDEO = 7;

}
