/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;
import com.ainirobot.platform.bi.wrapper.utils.ShareDataUtils;

public class LeadingFinishPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_leading_finish";

    private String destination;
    private String link_id;
    private long start_time;
    private String person_id;
    private String name;
    private long ctime;

    public LeadingFinishPoint(String destination, String link_id, long start_time) {
        super(TABLE_NAME);
        this.destination = destination;
        this.link_id = link_id;
        this.start_time = start_time;
        this.person_id = ShareDataUtils.getPersonId();
        this.name = ShareDataUtils.getPersonName();
        this.ctime = System.currentTimeMillis();
    }
}
