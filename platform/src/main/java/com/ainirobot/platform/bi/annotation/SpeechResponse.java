package com.ainirobot.platform.bi.annotation;

import androidx.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * speech response annotation
 *
 * @version V1.0.0
 * @date 2019/4/10 15:50
 */
@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@IntDef({SpeechResponse.SPEECH_NOT_SUPPORT
        , SpeechResponse.SPEECH_NOT_RESPONSE
        , SpeechResponse.SPEECH_RESPONSE
        , SpeechResponse.SPEECH_TRANSMISSION
        , SpeechResponse.SPEECH_BACKGROUND
        , SpeechResponse.SPEECH_ADD_QUEUE})
public @interface SpeechResponse {
     int SPEECH_NOT_SUPPORT = 0;
     int SPEECH_NOT_RESPONSE = 1;
     int SPEECH_RESPONSE = 2;
     int SPEECH_TRANSMISSION = 3;
     int SPEECH_BACKGROUND = 4;
     int SPEECH_ADD_QUEUE = 5;
}
