/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

/**
 * reception finish bi report, table-gb_reception
 *
 * @date 2019/4/25 17:32
 */
public class ReceptionFinishPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_reception";
    private String register_name;
    private String register_tel;
    private String register_type;
    private long enter_time;
    private long confirm_time;
    private long take_picture_time;
    private long get_guide_time;
    private long complete_time;
    private String guide_or_not;
    private String intent;

    public ReceptionFinishPoint(String registerName, String registerTel,
                                String registerType, long enterTime,
                                long confirmTime, long takePictureTime,
                                long getGuideTime, long completeTime,
                                String guideOrNot, String intent) {
        super(TABLE_NAME);
        this.register_name = registerName;
        this.register_tel = registerTel;
        this.register_type = registerType;
        this.enter_time = enterTime;
        this.confirm_time = confirmTime;
        this.take_picture_time = takePictureTime;
        this.get_guide_time = getGuideTime;
        this.complete_time = completeTime;
        this.guide_or_not = guideOrNot;
        this.intent = intent;
    }
}
