package com.ainirobot.platform.bi.annotation;

import androidx.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * opk operation status
 *
 * @version V1.0.0
 * @date 2019/8/13 21:15
 */
@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.SOURCE)
@IntDef
public @interface OpkOpStatus {

    int OPK_OP_SUCCESS = 1;
    /**
     * receive operation cmd
     */
    int RECEIVE_CMD = 2;

    /**
     * launch rn cmd execute result
     */
    int LAUNCHER_SUCCESS = 3;
    int LAUNCHER_FAIL = 4;


    /**
     * close rn cmd execute result
     */
    int CLOSE_RN_SUCCESS = 3;
    int CLOSE_RN_FAIL = 4;

    /**
     * rn run execute precess
     */
    int RN_START_DOWNLOAD_RESOURCE =  5;
    int RN_DOWNLOAD_SUCCESS = 6;
    int RN_DOWNLOAD_FAILED = 7;
    int RN_OPK_START_SUCCESS = 8;
    int RN_OPK_START_FAILED = 9;
}
