package com.ainirobot.platform.bi.annotation;

import androidx.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * opk operation type
 *
 * @version V1.0.0
 * @date 2019/8/13 21:10
 */
@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.SOURCE)
@IntDef
public @interface OpkOpType {

    int LAUNCH_RN = 1;
    int CLOSE_RN = 2;

    int OPK_SIGNED = 3;
    int OPK_UNZIP = 4;
    int OPK_REGISTER = 5;
}
