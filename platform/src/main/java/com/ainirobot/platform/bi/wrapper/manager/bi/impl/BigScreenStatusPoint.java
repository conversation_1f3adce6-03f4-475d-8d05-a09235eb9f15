package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

public class BigScreenStatusPoint extends BiNativeReport {
    private final static String TAG = BigScreenStatusPoint.class.getSimpleName();

    private int scene;
    private int error_type;
    private long ctime;

    public BigScreenStatusPoint(int scene, int errorType) {
        super("gb_dp_signal");
        this.scene = scene;
        this.error_type = errorType;
        this.ctime = System.currentTimeMillis();
    }
}
