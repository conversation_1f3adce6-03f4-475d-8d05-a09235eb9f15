package com.ainirobot.platform.bi.annotation;

import androidx.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * back charging annotation
 *
 * @version V1.0.0
 * @date 2019/4/10 15:50
 */
@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@IntDef({BackCharging.BACK_CHARGING_SUCCESS
        , BackCharging.BACK_CHARGING_FAIL
        , BackCharging.BACK_CHARGING_FAIL_NO_SIGNAL})
public @interface BackCharging {
     int BACK_CHARGING_SUCCESS = 30000;
     int BACK_CHARGING_FAIL = -30200;
     int BACK_CHARGING_FAIL_NO_SIGNAL = -30201;
}
