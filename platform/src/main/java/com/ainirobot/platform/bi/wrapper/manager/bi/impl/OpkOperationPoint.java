package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

/**
 * @version V1.0.0
 * @date 2019/8/13 15:57
 */
public class OpkOperationPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_opk_switch_flow";

    private String msg_id;
    private String app_id;
    private int operation_type;
    private int operation_status;
    private String error_message;
    private long ctime;

    public OpkOperationPoint(String msgId, String appId, int operationType,
                             int operationStatus, String errorMessage) {
        super(TABLE_NAME);
        this.msg_id = msgId;
        this.app_id = appId;
        this.operation_type = operationType;
        this.operation_status = operationStatus;
        this.error_message = errorMessage;
        this.ctime = System.currentTimeMillis();
    }
}
