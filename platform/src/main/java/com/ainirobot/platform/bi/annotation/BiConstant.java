package com.ainirobot.platform.bi.annotation;

/**
 * bi mode range
 *
 * @version V1.0.0
 * @date 2019/1/25 19:59
 */
public class BiConstant {
    /**
     * leading intent trigger way
     */
    public static final int MODE_LEADING_START_BY_VOICE = 1;
    public static final int MODE_LEADING_START_BY_QUERY_BY_TEXT = 2;
    public static final int MODE_LEADING_START_BY_UI_CLICK = 3;
    public static final int MODE_LEADING_START_BY_OTHER = 4;
    /**
     * leading intent trigger from
     */
    public static final int LEADING_START_FROM_QUERY_LOCATION = 1;
    public static final int LEADING_START_FROM_CONFIRM_QUERY_LOCATION = 2;
    public static final int LEADING_START_FROM_RECEPTION = 3;

    /**
     * leading quit trigger way
     */
    public static final int LEADING_QUIT_MODE_BY_UI_CLICK = 1;
    public static final int LEADING_QUIT_MODE_BY_VOICE = 2;
    public static final int LEADING_QUIT_MODE_BY_NOT_LOCATED = 3;
    public static final int LEADING_QUIT_MODE_BY_RELOCATED_FAILED = 4;
    public static final int LEADING_QUIT_MODE_BY_DESTINATION_UNREACHABLE = 5;
    public static final int LEADING_QUIT_MODE_BY_INTERRUPTED = 6;
    public static final int LEADING_QUIT_MODE_BY_OTHER = 7;


    /**
     * query location trigger way
     */
    public static final int MODE_QUERY_LOCATION_BY_VOICE = 1;
    public static final int MODE_QUERY_LOCATION_BY_QUERY_BY_TEXT = 2;
    public static final int MODE_QUERY_LOCATION_BY_UI_CLICK = 3;

    /**
     * query location result
     */
    public static final int RESULT_QUERY_LOCATION_DIRECT_LEADING = 1;
    public static final int RESULT_QUERY_LOCATION_CONFIRM_LEADING = 2;
    public static final int RESULT_QUERY_LOCATION_ONLY_IMAGE = 3;
    public static final int RESULT_QUERY_LOCATION_SHOW_ANSWER = 4;
    public static final int RESULT_QUERY_LOCATION_SHOW_LOCAL_LOCATIONS = 5;
    public static final int RESULT_QUERY_LOCATION_SHOW_LOCATIONS = 6;
    public static final int RESULT_QUERY_LOCATION_LOCAL_NO_LOCATION = 7;
    public static final int RESULT_QUERY_LOCATION_BY_MAIN_CLICK = 8;


    /**
     * guide action trigger way
     */
    public static final int MODE_GUIDE_ACTION_BY_VOICE = 1;
    public static final int MODE_GUIDE_ACTION_BY_QUERY_BY_TEXT = 2;
    public static final int MODE_GUIDE_ACTION_BY_UI_CLICK = 3;
    public static final int MODE_GUIDE_ACTION_BY_OTHER = 4;


    /**
     * guide action interaction way
     */
    public static final int ACTION_GUIDE_ACTION_NEXT_STATION = 1;
    public static final int ACTION_GUIDE_ACTION_FINISH = 2;
    public static final int ACTION_GUIDE_ACTION_REQUEST = 3;
    public static final int ACTION_GUIDE_ACTION_PAUSE = 4;
    public static final int ACTION_GUIDE_ACTION_RECOVER = 5;
    public static final int ACTION_GUIDE_ACTION_PLAYLIST = 6;
    public static final int ACTION_GUIDE_ACTION_PLAY_VIDEO = 7;

    /**
     * guide action scene
     */
    public static final int SCENE_GUIDE_ACTION_AT_STATION = 1;
    public static final int SCENE_GUIDE_ACTION_ON_THE_WAY = 2;

    /**
     * reception start mode
     */
    public static final int RECEPTION_START_MODE_VOICE = 1;
    public static final int RECEPTION_START_MODE_QUERY_BY_TEXT = 2;
    public static final int RECEPTION_START_MODE_UI_CLICK = 3;

    /**
     * dance start mode
     */
    public static final int DANCE_START_MODE_VOICE = 1;
    public static final int DANCE_START_MODE_UI_CLICK = 2;
    public static final int DANCE_START_MODE_OTHER = 3;

    /**
     * charging occur method
     */
    public static final byte CHARGING_OCCUR_SPEECH = 0x01;
    public static final byte CHARGING_OCCUR_WEIXIN_GO = 0x05;

    /**
     * auto back charging status
     */
    public static final String CHARGE_FAIL_STATUS = "charge_fail_status";
    public static final String CHARGE_FAIL_REASON = "charge_fail_reason";
    public static final int CHARGE_FAIL_WHEN_NOT_ESTIMATE = 1001;
    public static final int CHARGE_FAIL_WHEN_NAVIGATION = 1002;
    public static final int CHARGE_FAIL_WHEN_LARGE_MAP_NAV_TIMEOUT = 1003;
    public static final int CHARGE_FAIL_WHEN_PARSE_IN_LOCATION = 1004;
    public static final int CHARGE_FAIL_WHEN_NOT_MOVE_FOR_20S = 1005;
    public static final int CHARGE_FAIL_WHEN_PSB_CHARGE = 1006;
    public static final int CHARGE_FAIL_WHEN_PSB_NO_SIGNAL = 1007;
    public static final int COUNT_OFFSET = 1;
}
