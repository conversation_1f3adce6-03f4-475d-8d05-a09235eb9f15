package com.ainirobot.platform.bi;

import android.content.Intent;
import android.util.Log;

import com.ainirobot.platform.BaseApplication;

import java.util.UUID;

/**
 * @version V1.0.0
 * @date 2019/1/7 15:17
 */
public class WakeUpIdWrapper {

    private static final String TAG = "WakeUpIdWrapper";
    /**
     * wakeupId广播action
     */
    private static final String ACTION_WAKEUP_ID = "action_wakeup_id";
    /**
     * wakeupId的key
     */
    private static final String DATA_WAKEUP_ID = "data_wakeup_id";
    /**
     * wakeupId广播接收者权限
     */
    private static final String WAKEUP_ID_RECEIVER_PERMISSION = "com.ainirobot.permission.WAKEUP_ID_RECEIVER";

    private static volatile WakeUpIdWrapper sWakeUpIdWrapper;

    /**
     * 当前wakeUpId
     */
    private volatile String mCurrentWakeUpId;

    private WakeUpIdWrapper() {}

    public static WakeUpIdWrapper getInstance() {
        if (sWakeUpIdWrapper == null) {
            synchronized (WakeUpIdWrapper.class) {
                if (sWakeUpIdWrapper == null) {
                    sWakeUpIdWrapper = new WakeUpIdWrapper();
                }
            }
        }
        return sWakeUpIdWrapper;
    }

    /**
     * 生成wakeUpId并广播
     */
    public void generateWakeUpId() {
        mCurrentWakeUpId = UUID.randomUUID().toString();
        Intent intent = new Intent(ACTION_WAKEUP_ID);
        intent.putExtra(DATA_WAKEUP_ID, mCurrentWakeUpId);
        BaseApplication.getContext().sendBroadcast(intent, WAKEUP_ID_RECEIVER_PERMISSION);
        Log.i(TAG, "generateWakeUpId: currentWakeUpId=" + mCurrentWakeUpId);
    }

    /**
     * 生成wakeUpId并广播
     */
    public String getWakeUpId() {
        Log.i(TAG, "generateWakeUpId: currentWakeUpId=" + mCurrentWakeUpId);
        return mCurrentWakeUpId;
    }
}
