/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;
import com.ainirobot.platform.bi.wrapper.utils.ShareDataUtils;

/**
 * advertising view bi report, table-gb_advertising_view
 *
 * @date 2019/5/21 17:32
 */
public class AdvertisingViewPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_advertising_view";

    private int ad_num;
    private int p_num;
    private long duration;
    private String person_id;
    private long ctime;

    public AdvertisingViewPoint(int adNum, int pNum, long duration) {
        super(TABLE_NAME);
        this.ad_num = adNum;
        this.p_num = pNum;
        this.duration = duration;
        this.person_id = ShareDataUtils.getPersonId();
        this.ctime = System.currentTimeMillis();
    }
}
