package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;


/**
 * speech nlp response
 *
 * @version V1.0.0
 * @date 2019/4/19 10:21
 */
public class SpeechResponsePoint extends BiNativeReport {
    private static final String TABLE_NAME = "gb_y_sid";

    private String sid;
    private int response;
    private String appname;
    private long ctime;

    public SpeechResponsePoint(String sid, int response, String appName) {
        super(TABLE_NAME);
        this.sid = sid;
        this.response = response;
        this.appname = appName;
        this.ctime = System.currentTimeMillis();
    }
}
