package com.ainirobot.platform.bi.annotation;

import androidx.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static com.ainirobot.platform.bi.annotation.GuideScene.*;


/**
 * guide module report action
 *
 * @version V1.0.0
 * @date 2019/2/20 15:06
 */

@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@IntDef({STATION, ON_THE_WAY})
public @interface GuideScene {
     int STATION = 1;
     int ON_THE_WAY = 2;
}
