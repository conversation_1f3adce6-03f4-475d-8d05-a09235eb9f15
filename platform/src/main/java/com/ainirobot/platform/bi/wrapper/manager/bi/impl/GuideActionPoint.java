package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

/**
 * guide mode interaction bi report table-gb_guide_action
 *
 * @version V1.0.0
 * @date 2019/2/20 14:44
 */
public class GuideActionPoint extends BiNativeReport {
    private static final String TAG = "GuideActionReport";
    private static final String TABLE_NAME = "gb_guide_action";

    private String line_name;
    private String name;
    private int num;
    private int which;
    private int mode;
    private int action;
    private int scene;
    private String link_id;
    private long ctime;

    public GuideActionPoint(String lineName, String stationName, int stationNum,
                            int stationSeq, int mode, int scene, String linkId, int uiAction) {
        super(TABLE_NAME);
        this.line_name = lineName;
        this.name = stationName;
        this.num = stationNum;
        this.which = stationSeq;
        this.mode = mode;
        this.scene = scene;
        this.link_id = linkId;
        this.action = uiAction;
        this.ctime = System.currentTimeMillis();
    }
}
