package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

/**
 *  description, gb_rn_download_opk
 *
 * @date 2019/4/25 17:32
 */
public class RnDownloadOpkPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_rn_download_opk";

    private int result;
    private String appid;
    private String versionName;
    private String coreTarget;

    public RnDownloadOpkPoint(int result, String appid, String versionName, String coreTarget) {
        super(TABLE_NAME);
        this.result = result;
        this.appid = appid;
        this.versionName = versionName;
        this.coreTarget = coreTarget;
    }
}
