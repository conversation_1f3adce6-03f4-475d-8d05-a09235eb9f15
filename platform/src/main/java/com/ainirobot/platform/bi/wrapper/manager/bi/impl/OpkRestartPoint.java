package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

/**
 * @version V1.0.0
 * @date 2019/8/13 15:57
 */
public class OpkRestartPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_opk_restart";

    private String opkversion;
    private String appid;
    private long ctime;

    public OpkRestartPoint(String opkversion, String appid) {
        super(TABLE_NAME);
        this.opkversion = opkversion;
        this.appid = appid;
        this.ctime = System.currentTimeMillis();
    }
}
