package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

/**
 * @version V1.0.0
 * @date 2019/8/13 15:57
 */
public class OpkPerformanceMonitorPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_performance_monitor";

    private String opkversion;
    private String appid;
    private int type;
    private int threshold;
    private int memory;
    private double cpu;

    public OpkPerformanceMonitorPoint(String opkversion, String appid, int type,
                                      int threshold, int memory, double cpu) {
        super(TABLE_NAME);
        this.opkversion = opkversion;
        this.appid = appid;
        this.threshold = threshold;
        this.memory = memory;
        this.cpu = cpu;
        this.type = type;
    }
}
