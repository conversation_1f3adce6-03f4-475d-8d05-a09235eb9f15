package com.ainirobot.platform.bi.annotation;

import androidx.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static com.ainirobot.platform.bi.annotation.GuideQuitMode.*;

/**
 * guide quit trigger way
 *
 * @version V1.0.0
 * @date 2019/2/21 11:01
 */

@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.PARAMETER, ElementType.FIELD})
@IntDef({UI_CLICK, VOICE, NOT_LOCATED
        , RELOCATED_FAILED, DESTINATION_UNREACHABLE
        , INTERRUPTED, OTHER})
public @interface GuideQuitMode {
    int OTHER = 0;
    int UI_CLICK = 1;
    int VOICE = 2;
    int NOT_LOCATED = 3;
    int RELOCATED_FAILED = 4;
    int DESTINATION_UNREACHABLE = 5;
    int INTERRUPTED = 6;
}
