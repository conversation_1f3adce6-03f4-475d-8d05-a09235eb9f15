package com.ainirobot.platform.bi.wrapper;

import androidx.annotation.NonNull;
import android.util.Log;

import com.ainirobot.platform.bi.wrapper.manager.Report;
import com.ainirobot.platform.bi.wrapper.manager.at.AtReportManager;
import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReportManager;
import com.ainirobot.platform.bi.wrapper.manager.bijs.BiJsReportManager;
import com.ainirobot.platform.bi.wrapper.manager.ew.EwReportManager;
import com.ainirobot.platform.bi.wrapper.utils.ParamLegalCheckUtils;

/**
 * control report manager deliver
 *
 * @version V1.0.0
 * @date 2019/3/13 20:22
 */
@SuppressWarnings("unchecked")
public class ReportControl {
    private static final String TAG = "ReportControl";
    private volatile static ReportControl mInstance;
    private AtReportManager atReportManager;
    private BiNativeReportManager biNativeReportManager;
    private EwReportManager ewReportManager;
    private BiJsReportManager biJsReportManager;

    private ReportControl() {
        atReportManager = AtReportManager.getInstance();
        biNativeReportManager = BiNativeReportManager.getInstance();
        ewReportManager = EwReportManager.getInstance();
        biJsReportManager = BiJsReportManager.getInstance();
    }


    public static ReportControl getInstance() {
        if (mInstance == null) {
            synchronized (ReportControl.class) {
                if (mInstance == null) {
                    mInstance = new ReportControl();
                }
            }
        }
        return mInstance;
    }

    /**
     * report report that Defined {@link Report}
     * <p>
     * <p>
     * for eq:
     * ReportControl.getInstance().reportMsg(Report.DESTINATION.setValue("destination"))
     * </p>
     *
     * @param report bi、auto test or early warning abstract report point
     * @see Report
     */
    public void reportMsg(@NonNull Report report) {
        ParamLegalCheckUtils.isNull(report);
        Log.i(TAG, "report thread = " + Thread.currentThread().getName());
        boolean biHandleState = biNativeReportManager.handleReport(report);
        boolean atHandleState = atReportManager.handleReport(report);
        boolean ewHandleState = ewReportManager.handleReport(report);
        boolean biJsHandleState = biJsReportManager.handleReport(report);

        if (biHandleState) {
            Log.i(TAG, "reportMsg: bi");
            biNativeReportManager.report(report);
        }
        if (biJsHandleState) {
            Log.i(TAG, "reportMsg: bi js");
            biJsReportManager.report(report);
        }
        if (atHandleState) {
            Log.i(TAG, "reportMsg: at");
            atReportManager.report(report);
        }
        if (ewHandleState) {
            Log.i(TAG, "reportMsg: ew");
            ewReportManager.report(report);
        }
    }
}
