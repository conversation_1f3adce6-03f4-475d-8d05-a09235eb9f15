/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

/**
 * guide score bi report  table name "gb_guide_rating"
 *
 * @version V1.0.0
 * @date 2019/4/19 10:21
 */
public class GuideScorePoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_guide_rating";

    private String exit_type;
    private int rating;
    private long ctime;
    private String from;
    private String content;

    public GuideScorePoint(int rating, String exitType) {
        super(TABLE_NAME);
        this.ctime = System.currentTimeMillis();
        this.from = "";
        this.content = "";
        this.exit_type = exitType;
        this.rating = rating;
    }
}
