package com.ainirobot.platform.bi.wrapper.manager.bi;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;
import com.ainirobot.platform.bi.wrapper.manager.BaseReportManager;
import com.ainirobot.platform.bi.wrapper.manager.Report;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Map;

/**
 * Buried Point report management
 *
 * @version V1.0.0
 * @date 2019/3/13 20:23
 */
public class BiNativeReportManager extends BaseReportManager {
    private static final String TAG = "BiNativeReportManager";
    private volatile static BiNativeReportManager mInstance;
    private static final String SPECIAL = "__";

    private BiNativeReportManager() {
    }

    public static BiNativeReportManager getInstance() {
        if (mInstance == null) {
            synchronized (BiNativeReportManager.class) {
                if (mInstance == null) {
                    mInstance = new BiNativeReportManager();
                }
            }
        }
        return mInstance;
    }

    @Override
    public boolean handleReport(Report report) {
        return report instanceof BiNativeReport;
    }

    @Override
    public void report(Report report) {
        BiNativeReport nativeBi = (BiNativeReport) report;
        Field[] fields = nativeBi.getClass().getDeclaredFields();
        String tableName = nativeBi.getTableName();
        if (TextUtils.isEmpty(tableName)) {
            Log.e(TAG, "report fail for tableName null");
            return;
        }
        BaseBiReport re = new BaseBiReport(tableName);
        for (Field field : fields) {
            // 对于每个属性，获取属性名
            String varName = field.getName();
            try {
                boolean access = field.isAccessible();
                if (!access)
                    field.setAccessible(true);
                int ModifierFlags = field.getModifiers();
                if ((ModifierFlags & Modifier.STATIC) != 0) {
                    continue;
                }
                Object value = field.get(nativeBi);
                // for special field
                if (varName.contains(SPECIAL)) {
                    varName = varName.substring(0, varName.lastIndexOf(SPECIAL));
                }
                Log.d(TAG, "field: " + varName + ", value: " + value);
                re.addData(varName, String.valueOf(value));
                if (!access)
                    field.setAccessible(false);
            } catch (Exception ex) {
                Log.e(TAG, "report fail for exception ex: " + ex.getMessage());
            }
        }
        re.report();
    }
}
