package com.ainirobot.platform.bi.wrapper.manager.at;

import android.util.Log;

import com.ainirobot.platform.bi.wrapper.manager.BaseReportManager;
import com.ainirobot.platform.bi.wrapper.manager.Report;
import com.ainirobot.remotecontrolservice.testtools.RobotTestTools;

/**
 * auto test report management
 *
 * @version V2.0.0
 * @date 2019/9/23
 */
public class AtReportManager extends BaseReportManager<String> {
    private static final String TAG = "AtReportManager";
    private volatile static AtReportManager mInstance;

    private AtReportManager() {
    }

    public static AtReportManager getInstance() {
        if (mInstance == null) {
            synchronized (AtReportManager.class) {
                if (mInstance == null) {
                    mInstance = new AtReportManager();
                }
            }
        }
        return mInstance;
    }

    @Override
    public boolean handleReport(Report report) {
        return report instanceof AtReport;
    }

    @Override
    public void report(Report report) {
        AtReport atReport = (AtReport) report;
        switch (atReport.getAtType()) {
            case error:
                RobotTestTools.LogError(atReport.getValue());
                break;
            case start:
                RobotTestTools.LogStart(atReport.getValue());
                break;
            case failed:
                RobotTestTools.LogFailed(atReport.getValue());
                break;
            case success:
                RobotTestTools.LogSuccess(atReport.getValue());
                break;
            case exception:
                break;
            default:
                Log.e(TAG, "error type");
                break;
        }
    }
}
