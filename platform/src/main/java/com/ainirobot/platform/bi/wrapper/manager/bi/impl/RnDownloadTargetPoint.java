package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

/**
 * description, table-gb_rn_download_coretarget
 *
 * @date 2019/4/25 17:32
 */
public class RnDownloadTargetPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_rn_download_coretarget";

    private int result;
    private int new__;
    private String version;

    public RnDownloadTargetPoint(int newValue, int result, String version) {
        super(TABLE_NAME);
        this.result = result;
        this.version = version;
        this.new__ = newValue;
    }
}
