package com.ainirobot.platform.bi.wrapper.utils;


import java.util.Collection;

/**
 * function params legal check utils
 *
 * @version V1.0.0
 * @date 2019/5/18 18:26
 */
public class ParamLegalCheckUtils {

    public static void isNull(Object object) {
        if (object == null) {
            throw new IllegalArgumentException("argument exception param can't be null");
        }
    }

    public static void isNull(Object object, String exceptionMsg) {
        if (object == null) {
            throw new IllegalArgumentException(exceptionMsg);
        }
    }

    public static void isCollectionEmpty(Collection collection) {
        isNull(collection);
        if (collection.isEmpty()) {
            throw new IllegalArgumentException("argument exception param can't be empty");
        }
    }

    public static void isCollectionEmpty(Collection collection, String exceptionMsg) {
        isNull(collection);
        if (collection.isEmpty()) {
            throw new IllegalArgumentException(exceptionMsg);
        }
    }

}
