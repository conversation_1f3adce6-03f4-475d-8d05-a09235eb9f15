/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;
import com.ainirobot.platform.bi.wrapper.utils.ShareDataUtils;

/**
 * person duration when wake up bi report, table-gb_person_duration
 *
 * @date 2019/5/21 17:32
 */
public class PersonDurationPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_person_duration";

    private long start_time;
    private String person_id;
    private String name;
    private String gender;
    private String age;
    private long ctime;

    public PersonDurationPoint(long start_time) {
        super(TABLE_NAME);
        this.start_time = start_time;
        this.person_id = ShareDataUtils.getPersonId();
        this.name = ShareDataUtils.getPersonName();
        this.gender = ShareDataUtils.getPersonGender();
        this.age = ShareDataUtils.getPersonAge();
        this.ctime = System.currentTimeMillis() - 5000;
    }
}
