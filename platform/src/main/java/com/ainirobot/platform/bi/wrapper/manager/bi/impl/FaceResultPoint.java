/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

/**
 * face result bi report, table-gb_face_result
 *
 * @date 2019/5/21 17:32
 */
public class FaceResultPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_face_result";

    private int num;
    private String person_id;
    private String name;
    private String gender;
    private String age;
    private String role;
    private String face_id;
    private String face_register_time;
    private String req_id;
    private long ctime;

    public FaceResultPoint(int num, String personId, String name, String gender,
                           String age, String role, String faceId, String faceRegisterTime,
                           String reqId) {
        super(TABLE_NAME);
        this.num = num;
        this.person_id = personId;
        this.name = name;
        this.gender = gender;
        this.age = age;
        this.role = role;
        this.face_id = faceId;
        this.face_register_time = faceRegisterTime;
        this.req_id = reqId;
        this.ctime = System.currentTimeMillis();
    }
}
