package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

/**
 * @version V1.0.0
 * @date 2019/8/13 15:57
 */
public class OpkPublishPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_opk_publish_flow";

    private String msg_id;
    private String app_id;
    private String robot_status;
    private String operation_type;
    private String operation_status;
    private String error_message;
    private long ctime;

    public OpkPublishPoint(String msgId, String appId, String robotStatus,
                           String operationType, String operationStatus,
                           String errorMessage) {
        super(TABLE_NAME);
        this.msg_id = msgId;
        this.app_id = appId;
        this.robot_status = robotStatus;
        this.operation_status = operationStatus;
        this.operation_type = operationType;
        this.error_message = errorMessage;
        this.ctime = System.currentTimeMillis();
    }
}
