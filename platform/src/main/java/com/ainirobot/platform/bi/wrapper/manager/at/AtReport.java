package com.ainirobot.platform.bi.wrapper.manager.at;

import com.ainirobot.platform.bi.wrapper.manager.Report;

public class AtReport implements Report {

    public enum AtType {
        error, exception, failed, start, success
    }

    private AtType atType;
    private String value;

    public AtReport(AtType type, String value) {
        this.atType = type;
        this.value = value;
    }

    public AtType getAtType() {
        return atType;
    }

    public String getValue() {
        return value;
    }
}
