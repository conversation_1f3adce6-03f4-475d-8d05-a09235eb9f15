package com.ainirobot.platform.bi.annotation;

import androidx.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import static com.ainirobot.platform.bi.annotation.BiConstant.*;

/**
 * leading start from bi annotation
 *
 * @version V1.0.0
 * @date 2019/1/25 19:57
 */
@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.PARAMETER, ElementType.FIELD})
@IntDef({LEADING_START_FROM_QUERY_LOCATION
    , LEADING_START_FROM_CONFIRM_QUERY_LOCATION
    , LEADING_START_FROM_RECEPTION})
public @interface LeadingStartFrom {

}

