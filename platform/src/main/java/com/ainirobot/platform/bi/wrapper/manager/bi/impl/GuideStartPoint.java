/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;
import com.ainirobot.platform.bi.wrapper.utils.ShareDataUtils;

/**
 * guide start bi report  table name "gb_guide"
 *
 * @version V1.0.0
 * @date 2019/4/19 10:21
 */
public class GuideStartPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_guide";

    private String line_name;
    private int num;
    private int mode;
    private String from;
    private String station_name;
    private String which;
    private String link_id;
    private long ctime;
    private String name;
    private String person_id;

    public GuideStartPoint(String lineName, int stationNum, int mode,
                           String from, String stationName, String startStationSeq,
                           String linkId, long startTime) {
        super(TABLE_NAME);
        this.line_name = lineName;
        this.num = stationNum;
        this.mode = mode;
        this.from = from;
        this.station_name = stationName;
        this.which = startStationSeq;
        this.link_id = linkId;
        this.name = ShareDataUtils.getPersonName();
        this.person_id = ShareDataUtils.getPersonId();
        this.ctime = startTime;
    }
}
