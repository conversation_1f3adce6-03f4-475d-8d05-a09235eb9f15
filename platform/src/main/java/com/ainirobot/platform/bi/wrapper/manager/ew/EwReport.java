package com.ainirobot.platform.bi.wrapper.manager.ew;

import com.ainirobot.platform.bi.wrapper.manager.Report;

public class EwReport implements Report {
    public enum EwType {
        warning, moduleChange
    }

    private EwType ewType;
    private String value;

    public EwReport(EwType type, String value) {
        this.ewType = type;
        this.value = value;
    }

    public EwType getEwType() {
        return ewType;
    }

    public String getValue() {
        return value;
    }
}
