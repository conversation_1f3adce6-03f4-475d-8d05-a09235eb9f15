package com.ainirobot.platform.bi.wrapper.manager.ew;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.platform.bi.wrapper.manager.BaseReportManager;
import com.ainirobot.platform.bi.wrapper.manager.Report;

/**
 * early warning report management
 *
 * @version V1.0.0
 * @date 2019/3/13 20:34
 */
public class EwReportManager extends BaseReportManager<String> {
    private static final String TAG = "EwJsReportManager";
    private volatile static EwReportManager mInstance;

    private EwReportManager() {
    }

    public static EwReportManager getInstance() {
        if (mInstance == null) {
            synchronized (EwReportManager.class) {
                if (mInstance == null) {
                    mInstance = new EwReportManager();
                }
            }
        }
        return mInstance;
    }

    @Override
    public boolean handleReport(Report report) {
        return report instanceof EwReport;
    }

    @Override
    public void report(Report report) {
        if (!RobotApi.getInstance().isApiConnectedService()) {
            Log.w(TAG, "report: service not connect");
            return;
        }
        EwReport ewReport = (EwReport) report;
        switch (ewReport.getEwType()) {
            case warning:

                RobotApi.getInstance().sendStatusReport(Definition.STATUS_WARNING_REPORT,
                        ewReport.getValue());
                break;
            case moduleChange:
                RobotApi.getInstance().sendStatusReport(Definition.STATUS_MODULE_CHANGE,
                        ewReport.getValue());
                break;
            default:
                Log.e(TAG, "error type");
                break;
        }
    }

}
