/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;
import com.ainirobot.platform.bi.wrapper.utils.ShareDataUtils;

/**
 * reception bi report, table-gb_reception_start
 *
 * @date 2019/4/25 17:32
 */
public class ReceptionStartPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_reception_start";
    private int mode;
    private int group_size;
    private String intent;
    private String register_name;
    private String person_id;
    private String name;
    private long ctime;

    public ReceptionStartPoint(int mode, int groupSize,
                               String intent, String registerName) {
        super(TABLE_NAME);
        this.mode = mode;
        this.group_size = groupSize;
        this.intent = intent;
        this.register_name = registerName;
        this.person_id = ShareDataUtils.getPersonId();
        this.name = ShareDataUtils.getPersonName();
        this.ctime = System.currentTimeMillis();
    }
}
