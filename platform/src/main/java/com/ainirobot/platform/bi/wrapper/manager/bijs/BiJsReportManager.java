package com.ainirobot.platform.bi.wrapper.manager.bijs;


import android.util.Log;

import com.ainirobot.coreservice.client.upload.bi.BiReport;
import com.ainirobot.platform.bi.wrapper.Constant;
import com.ainirobot.platform.bi.wrapper.manager.BaseReportManager;
import com.ainirobot.platform.bi.wrapper.manager.Report;

import org.json.JSONException;
import org.json.JSONObject;


/**
 * Buried Point js report management
 *
 * @version V1.0.0
 * @date 2019/3/13 20:23
 */
public class BiJsReportManager extends BaseReportManager<String> {
    private static final String TAG = "BiNativeReportManager";
    private volatile static BiJsReportManager mInstance;


    private BiJsReportManager() {
    }

    public static BiJsReportManager getInstance() {
        if (mInstance == null) {
            synchronized (BiJsReportManager.class) {
                if (mInstance == null) {
                    mInstance = new BiJsReportManager();
                }
            }
        }
        return mInstance;
    }

    @Override
    public boolean handleReport(Report report) {
        return report instanceof BiJsReport;
    }

    @Override
    public void report(Report report) {
        try {
            JSONObject jsonObject = new JSONObject(((BiJsReport)report).getValue());
            String tableName = jsonObject.getString(Constant.BI_TABLE_NAME);
            String data = jsonObject.getString(Constant.BI_DATA);
            BiReport.report(tableName, data);
        } catch (JSONException e) {
            Log.w(TAG, "biJsReport: js report data format error ");
            e.printStackTrace();
        }

    }

}
