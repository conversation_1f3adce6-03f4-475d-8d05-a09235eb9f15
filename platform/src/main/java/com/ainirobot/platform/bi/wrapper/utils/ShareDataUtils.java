package com.ainirobot.platform.bi.wrapper.utils;


import androidx.annotation.NonNull;
import android.util.Log;

import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.person.PersonApi;
import com.ainirobot.platform.BaseApplication;

import java.util.List;

/**
 * share data area tools
 *
 * @version V1.0.0
 * @date 2019/3/27 18:10
 */
public class ShareDataUtils {

    private static final String TAG = "ShareDataUtils";

    private static final String EXCEPTION_MSG = "person can't be null";
    private static Person wakeUpPerson = new Person();
    private static int mMaxPersonNum;

    /**
     * get detect person name lately
     *
     * @return person name
     * @see Person
     */
    public static String getPersonName() {
        String personName = "";
        Person person = PersonApi.getInstance().getFocusPerson();
        person = person == null ? wakeUpPerson : person;
        if (person == null) {
            return personName;
        }
        personName = person.getName();
        return personName == null ? "" : personName;
    }

    /**
     * {@link #getPersonName()}
     *
     * @return person name
     * @see Person
     */
    public static String getPersonName(@NonNull Person person) {
        ParamLegalCheckUtils.isNull(person, EXCEPTION_MSG);
        String personName = person.getName();
        return personName == null ? "" : personName;
    }

    /**
     * get detect person id lately
     *
     * @return person id
     * @see Person
     */
    public static String getPersonId() {
        String personId = "";
        Person person = PersonApi.getInstance().getFocusPerson();
        person = person == null ? wakeUpPerson : person;
        if (person == null) {
            return personId;
        }
        personId = person.getUserId();
        return personId == null ? "" : personId;
    }

    /**
     * {@link #getPersonId()}
     *
     * @return person id
     * @see Person
     */
    public static String getPersonId(@NonNull Person person) {
        ParamLegalCheckUtils.isNull(person, EXCEPTION_MSG);
        String personId = person.getUserId();
        return personId == null ? "" : personId;
    }

    /**
     * get detect person gender lately
     *
     * @return person gender
     * @see Person
     */
    public static String getPersonGender() {
        String gender = "";
        Person person = PersonApi.getInstance().getFocusPerson();
        person = person == null ? wakeUpPerson : person;
        if (person == null) {
            return gender;
        }
        gender = person.getGender() == null ? "" : person.getGender();
        return gender;
    }

    /**
     * {@link #getPersonGender()}
     *
     * @return person gender
     * @see Person
     */
    public static String getPersonGender(@NonNull Person person) {
        ParamLegalCheckUtils.isNull(person, EXCEPTION_MSG);
        String gender = person.getGender();
        return gender == null ? "" : gender;
    }


    /**
     * get detect person age lately
     *
     * @return person age
     * @see Person
     */
    public static String getPersonAge() {
        String age = "";
        Person person = PersonApi.getInstance().getFocusPerson();
        person = person == null ? wakeUpPerson : person;
        if (person == null) {
            return age;
        }
        age = person.getAge() <= 0 ? "" : String.valueOf(person.getAge());
        return age;
    }


    /**
     * {@link #getPersonAge()}
     *
     * @return person age
     * @see Person
     */
    public static String getPersonAge(@NonNull Person person) {
        ParamLegalCheckUtils.isNull(person, EXCEPTION_MSG);
        int age = person.getAge();
        return age <= 0 ? "" : String.valueOf(person.getAge());
    }

    /**
     * @return person role
     * @see Person
     */
    public static String getPersonRole(@NonNull Person person) {
        ParamLegalCheckUtils.isNull(person, EXCEPTION_MSG);
        String role = person.getRole();
        return role == null ? "" : role;
    }

    /**
     * @return person remote detect face id
     * @see Person
     */
    public static String getPersonRemoteFaceId(@NonNull Person person) {
        ParamLegalCheckUtils.isNull(person, EXCEPTION_MSG);
        String remoteFaceId = person.getRemoteFaceId();
        return remoteFaceId == null ? "" : remoteFaceId;
    }

    public static String getPersonRegisterTime(@NonNull Person person) {
        ParamLegalCheckUtils.isNull(person, EXCEPTION_MSG);
        String registerTime = person.getUserRegisterTime();
        return registerTime == null ? "" : registerTime;
    }

    /**
     * @return person remote detect req id
     * @see Person
     */
    public static String getPersonRemoteReqId(@NonNull Person person) {
        ParamLegalCheckUtils.isNull(person, EXCEPTION_MSG);
        String remoteReqId = person.getRemoteReqId();
        return remoteReqId == null ? "" : remoteReqId;
    }

    /**
     * get application package name
     *
     * @return package
     */
    public static String getPackageName() {
        return BaseApplication.getContext().getPackageName();
    }

    public static Person getWakeUpPerson() {
        return wakeUpPerson;
    }

    /**
     * vision wakeup person info
     *
     * @param wakeUpPerson wake up person info
     */
    public static void setWakeUpPerson(Person wakeUpPerson) {
        if (wakeUpPerson == null) {
            return;
        }
        Log.i(TAG, "setWakeUpPerson: update wakeUpPerson" + wakeUpPerson.getName());
        ShareDataUtils.wakeUpPerson = wakeUpPerson;
    }

    public static int getPersonNum() {
        List<Person> list = PersonApi.getInstance().getAllPersons();
        return list == null ? 0 : list.size();
    }

    public static int getMaxPersonNum() {
        return mMaxPersonNum;
    }

    /**
     * update watch advert num
     *
     * @param maxPersonNum watch advert max person num
     */
    public static void setMaxPersonNum(int maxPersonNum) {
        mMaxPersonNum = maxPersonNum > mMaxPersonNum
                ? maxPersonNum : mMaxPersonNum;
    }

    /**
     * clear watch advert max person num
     */
    public static void clearMaxPersonNum() {
        mMaxPersonNum = 0;
    }
}
