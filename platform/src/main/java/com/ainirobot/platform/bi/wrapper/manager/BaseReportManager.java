package com.ainirobot.platform.bi.wrapper.manager;

import android.util.Log;

/**
 * base report manager class
 *
 * @version V1.0.0
 * @date 2019/3/25 15:07
 */
public class BaseReportManager<T> {

    private static final String TAG = BaseReportManager.class.getSimpleName();

    /**
     * check report
     *
     * @param report report report
     * @return true-current report manager handle, false-don't handle
     */
    public boolean handleReport(Report report) {
        Log.e(TAG, "handleReport not handle");
        return false;
    }

    public void report(Report report) {
        Log.e(TAG, "report not handle");
    }
}
