package com.ainirobot.platform.bi.wrapper.manager.bi.impl;


import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;
import com.ainirobot.platform.bi.wrapper.utils.ShareDataUtils;


/**
 * query location module bi table-gb_askway_result
 *
 * @version V1.0.0
 * @date 2019/1/29 17:32
 */
public class QueryLocationPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_askway_result";
    private String destination;
    private int mode;
    private int result;
    private long ctime;
    private String person_id;
    private String name;

    public QueryLocationPoint(String destination, int mode, int result) {
        super(TABLE_NAME);
        this.ctime = System.currentTimeMillis();
        this.person_id = ShareDataUtils.getPersonId();
        this.name = ShareDataUtils.getPersonName();
        this.destination = destination;
        this.mode = mode;
        this.result = result;
    }
}
