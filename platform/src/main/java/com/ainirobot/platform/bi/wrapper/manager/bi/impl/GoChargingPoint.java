/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

/**
 * go charging bi report, table-gb_recharge_point
 *
 * @date 2019/5/23 17:32
 */
public class GoChargingPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_recharge_point";

    private int result;
    private long ctime;

    public GoChargingPoint(int result) {
        super(TABLE_NAME);
        this.ctime = System.currentTimeMillis();
        this.result = result;
    }
}
