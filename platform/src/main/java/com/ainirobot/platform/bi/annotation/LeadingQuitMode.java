package com.ainirobot.platform.bi.annotation;

import androidx.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * leading quit trigger way
 *
 * @version V1.0.0
 * @date 2019/2/21 11:01
 */

@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.PARAMETER, ElementType.FIELD})
@IntDef({BiConstant.LEADING_QUIT_MODE_BY_UI_CLICK
        , BiConstant.LEADING_QUIT_MODE_BY_VOICE
        , BiConstant.LEADING_QUIT_MODE_BY_NOT_LOCATED
        , BiConstant.LEADING_QUIT_MODE_BY_RELOCATED_FAILED
        , BiConstant.LEADING_QUIT_MODE_BY_DESTINATION_UNREACHABLE
        , BiConstant.LEADING_QUIT_MODE_BY_INTERRUPTED
        , BiConstant.LEADING_QUIT_MODE_BY_OTHER})
public @interface LeadingQuitMode {
}
