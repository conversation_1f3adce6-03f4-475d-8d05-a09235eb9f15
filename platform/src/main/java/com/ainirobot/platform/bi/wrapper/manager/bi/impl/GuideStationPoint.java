package com.ainirobot.platform.bi.wrapper.manager.bi.impl;


import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;


/**
 * guide station state report bi table-gb_guide_station
 *
 * @version V1.0.0
 * @date 2019/2/18 16:25
 */
public class GuideStationPoint extends BiNativeReport {
    private static final String TABLE_NAME = "gb_guide_station";

    private String line_name;
    private String name;
    private int num;
    private int which;
    private String link_id;
    private long prev_time;
    private long ask_time;
    private long arrive_time;
    private long intro_time;
    private long ctime;

    public GuideStationPoint(String lineName, int which, int num, String name,
                             long prevTime, long arriveTime, long introTime,
                             long askTime, String linkId) {
        super(TABLE_NAME);
        this.line_name = lineName;
        this.which = which;
        this.num = num;
        this.name = name;
        this.prev_time = prevTime;
        this.arrive_time = arriveTime;
        this.intro_time = introTime;
        this.ask_time = askTime;
        this.link_id = linkId;
        this.ctime = System.currentTimeMillis();
    }
}
