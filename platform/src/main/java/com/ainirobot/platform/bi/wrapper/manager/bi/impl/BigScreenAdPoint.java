package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

public class BigScreenAdPoint extends BiNativeReport {
    private final static String TAG = BigScreenAdPoint.class.getSimpleName();

    private String mall_id;
    private String ad_id;
    private String ad_name;
    private long ad_begin;
    private long ad_end;
    private int ad_type;
    private int ad_category;
    private int ad_sound;
    private long ctime;

    public BigScreenAdPoint(String mallId, String adId, String adName, long adBegin,
                            long adEnd, int adType, int addCategory, int adSound) {
        super("gb_dp_ad");
        this.mall_id = mallId;
        this.ad_id = adId;
        this.ad_name = adName;
        this.ad_begin = adBegin;
        this.ad_end = adEnd;
        this.ad_type = adType;
        this.ad_category = addCategory;
        this.ad_sound = adSound;
        this.ctime = System.currentTimeMillis();
    }
}
