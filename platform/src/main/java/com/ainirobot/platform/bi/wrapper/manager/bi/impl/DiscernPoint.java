package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

public class DiscernPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_discern";

    private int result;
    private int reason;
    private long ctime;

    public DiscernPoint(int result, int reason) {
        super(TABLE_NAME);
        this.ctime = System.currentTimeMillis();
        this.result = result;
        this.reason = reason;
    }
}
