package com.ainirobot.platform.bi.annotation;

import androidx.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static com.ainirobot.platform.bi.annotation.BiConstant.*;

/**
 * leading start from bi annotation
 *
 * @version V1.0.0
 * @date 2019/1/25 19:57
 */
@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.PARAMETER, ElementType.FIELD})
@IntDef({RESULT_QUERY_LOCATION_DIRECT_LEADING
        , RESULT_QUERY_LOCATION_CONFIRM_LEADING
        , RESULT_QUERY_LOCATION_ONLY_IMAGE
        , RESULT_QUERY_LOCATION_SHOW_ANSWER
        , RESULT_QUERY_LOCATION_SHOW_LOCAL_LOCATIONS
        , RESULT_QUERY_LOCATION_SHOW_LOCATIONS
        , RESULT_QUERY_LOCATION_LOCAL_NO_LOCATION
        , RESULT_QUERY_LOCATION_BY_MAIN_CLICK})
public @interface QueryLocationResult {

}

