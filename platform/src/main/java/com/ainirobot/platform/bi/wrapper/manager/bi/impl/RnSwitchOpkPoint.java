package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

/**
 * description, table-gb_rn_switch_opk
 *
 * @date 2019/4/25 17:32
 */
public class RnSwitchOpkPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_rn_switch_opk";

    private int result;
    private String versionName;
    private String coreTarget;
    private String appid;

    public RnSwitchOpkPoint(int result, String versionName, String coreTarget, String appid) {
        super(TABLE_NAME);
        this.result = result;
        this.versionName = versionName;
        this.coreTarget = coreTarget;
        this.appid = appid;
    }
}
