package com.ainirobot.platform.bi.wrapper.annotation;

import androidx.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @version V1.0.0
 * @date 2019/8/13 16:32
 */
@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.SOURCE)
@IntDef
public @interface CloseOpkOpStatus {
    int RECEIVE_CLOSE_RN_CMD = 1;
    int CLOSE_RN_SUCCESS = 2;
    int SCLOSE_RN_FAIL = 3;
}
