package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;

public class RnHeartBeatTimeoutPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_rn_heartbeat_timeout";

    private String opkversion;
    private String appid;
    private String overtime;

    public RnHeartBeatTimeoutPoint(String opkversion, String appid, String overtime) {
        super(TABLE_NAME);
        this.opkversion = opkversion;
        this.appid = appid;
        this.overtime = overtime;
    }

}
