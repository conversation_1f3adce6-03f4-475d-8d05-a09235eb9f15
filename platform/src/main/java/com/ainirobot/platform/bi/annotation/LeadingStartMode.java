package com.ainirobot.platform.bi.annotation;

import androidx.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static com.ainirobot.platform.bi.annotation.BiConstant.*;

/**
 * leading start mode bi annotation
 *
 * @version V1.0.0
 * @date 2019/1/25 19:57
 */
@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.PARAMETER, ElementType.FIELD})
@IntDef({MODE_LEADING_START_BY_VOICE
    , MODE_LEADING_START_BY_UI_CLICK
    , MODE_LEADING_START_BY_QUERY_BY_TEXT
    , MODE_LEADING_START_BY_OTHER})
public @interface LeadingStartMode {

}

