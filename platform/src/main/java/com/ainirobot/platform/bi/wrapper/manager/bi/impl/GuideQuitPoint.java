/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.bi.wrapper.manager.bi.impl;

import com.ainirobot.platform.bi.wrapper.manager.bi.BiNativeReport;
import com.ainirobot.platform.bi.wrapper.utils.ShareDataUtils;

/**
 * guide quit bi report  table name "gb_guide_quit"
 *
 * @version V1.0.0
 * @date 2019/4/19 10:21
 */
public class GuideQuitPoint extends BiNativeReport {

    private static final String TABLE_NAME = "gb_guide_quit";

    private String line_name;
    private int mode;
    private String link_id;
    private long start_time;
    private long ctime;
    private String person_id;
    private String name;

    public GuideQuitPoint(long startTime, String lineName, int mode, String linkId) {
        super(TABLE_NAME);
        this.start_time = startTime;
        this.line_name = lineName;
        this.mode = mode;
        this.link_id = linkId;
        this.name = ShareDataUtils.getPersonName();
        this.person_id = ShareDataUtils.getPersonId();
        this.ctime = System.currentTimeMillis();
    }
}
