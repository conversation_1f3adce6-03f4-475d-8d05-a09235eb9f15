package com.ainirobot.platform;

public class PlatformDef {
    public static final String DEVELOPER_CONFIGURATION_ITEM = "developer_configuration_item";
    public static final String BEAMFOMING_ITEM = "beamfoming_item";
    public static final String ANGLE_CENTER_ITEM = "angle_center_item";
    public static final String ANGLE_RANGE_ITEM = "angle_range_item";

    public static final String START_CHARGING = "start_charging";
    public static final String STOP_CHARGING = "stop_charging";
    public static final String SET_WORK_MODE = "set_work_mode";

    public static final String SWITCH_APP_PLATFORM = "switch_app_platform";
    public static final String LAUNCH_RN = "launch_rn";
    public static final String CLOSE_RN = "close_rn";
    public static final String LIST_RN_OPK = "list_rn";
    public static final String UNINSTALL_RN = "uninstall_rn";
    public static final String SWITCH_CHARACTER = "switch_character";

    public static final String REQ_MODE_KEY = "mode";
    public static final String STATUS_SYNC_SERVER_CMD = "status_sync_server_cmd";

    /* --- tx1与 head service连接状态 --- */
    public static final String STATUS_HW_CONNECTED = "connected";
    public static final String STATUS_HW_DISCONNECTED = "disconnected";
    public static final String HW_HEAD_DISCONNECTED = "hw_head_disconnected";
    public static final String HW_HEAD_CONNECT = "hw_head_connected";

    public static final String WHEEL_MOTOR_BLOCKED = "wheel_motor_blocked";

    public static final String SWITCH_LITE_HOME = "switch_lite_home";
    public static final String BOOT_VIDEO_CONFIG_STATE = "boot_video_config_state";
    public static final String FIRST_ACTIVATION_KEY = "first_activation";
    public static final String OPEN_HEAD_ROTATE_ITEM = "open_head_rotate_item";

    public static final String MSG_BUNDLE_ID = "bundle_id";
    public static final String MSG_BUNDLE_TEXT = "bundle_text";
    public static final String MSG_BUNDLE_INTENT = "bundle_intent";
    public static final String MSG_BUNDLE_PARAM = "bundle_param";

    public static final String SKILL_CHAT_NAME = "chat";
    public static final String ASSETS_PRE = "file:///android_asset/";

    public static final int BAN_OPEN_CM_DOOR = 0;
    public static final int ALLOW_OPEN_CM_DOOR = 1;
    public static final int FOLLOW_WHO_SPEAK = 1;
    public static final String SWITCH_ALLOW_OPEN_CM_BLUETOOTH_DOOR = "switch_allow_open_cm_bluetooth_door";
    public static final String GLOBAL_REGULAR_PATROL = "global_regular_patrol";

    public static final String CRUISE_START = "cruise_start";
    public static final String CRUISE_AUTO_START = "cruise_auto_start";
    public static final String CRUISE_START_PATTERN = "robot_navigation&cruise";
    public static final String CRUISE_AUTO_START_PATTERN = "robot_navigation&auto_cruise";
    public static final String CRUISE_END = "cruise_end";
    public static final String CRUISE_EXIT = "cruise_exit";
    public static final String CRUISE_AUTO_EXIT = "cruise_auto_exit";
    public static final String CRUISE_AUTO_EXIT_PATTERN = "robot_navigation&auto_stop_cruise";
    public static final String OB_OPK_SWITCH = "ob_opk_switch";

    public static final String JSON_KEY_DESTINATION = "destination";
    public static final String JSON_KEY_IS_MAIN_DESTINATION = "mainDestination";

    public static final String SWITCH_OPK = "switch_opk";

}
