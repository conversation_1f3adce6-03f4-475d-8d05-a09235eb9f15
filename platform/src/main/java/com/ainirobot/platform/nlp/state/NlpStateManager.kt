package com.ainirobot.platform.nlp.state

import android.text.TextUtils
import android.util.Log
import com.ainirobot.platform.nlp.data.NlpOpt
import com.ainirobot.platform.nlp.data.Scene
import com.ainirobot.platform.nlp.data.UserSession
import com.ainirobot.platform.speech.SpeechApi
import com.ainirobot.platform.utils.GsonUtil
import java.util.*
import java.util.concurrent.ConcurrentHashMap

class NlpStateManager {
    private var sceneMap: ConcurrentHashMap<String, Scene>? = null
    private var appLifeCycle: ConcurrentHashMap<String, AppLifeCycle>? = null
    private var userSession: ConcurrentHashMap<String, UserSession>? = null
    private var syncMap: ConcurrentHashMap<String, Any>? = null
    private var serverList: MutableList<String>? = null
    private var debug: Boolean? = null;
    private val TAG = "NlpStateManager"

    companion object {

        private var instance: NlpStateManager? = null
            get() {
                if (field == null) {
                    field = NlpStateManager()
                }
                return field
            }

        @Synchronized
        fun get(): NlpStateManager {
            return instance!!
        }
    }

    fun setServerApp(appList: List<String>) {
        try {
            if (appList != null && appList.isNotEmpty()) {
                if (serverList == null) {
                    serverList = Collections.synchronizedList(ArrayList<String>())
                }
                for (item in appList) {
                    if (!(serverList!!.contains(item))) {
                        serverList!!.add(item)
                    }
                }
            }
        } catch (e: java.lang.Exception) {
            Log.d(TAG, "setServerApp error")
        }
    }

    fun setAppLifeCycle(appId: String?, life: AppLifeCycle) {
        try {
            if (appId != null) {
                if (appLifeCycle == null) {
                    appLifeCycle = ConcurrentHashMap<String, AppLifeCycle>()
                }
                appLifeCycle!![appId] = life
            } else {
                Log.d(TAG, "setAppLifeCycle appid is null")
            }
        } catch (e: java.lang.Exception) {
            Log.d(TAG, "setAppLifeCycle error")
        }
    }

    fun setAppVersion(appId: String?, version: String?) {
        try {
            if (appId != null && version != null) {
                if (sceneMap == null) {
                    sceneMap = ConcurrentHashMap<String, Scene>()
                }
                if (sceneMap!!.containsKey(appId)) {
                    val result = (sceneMap!![appId] as Scene)
                    result.version = version
                    sceneMap!![appId] = result
                } else {
                    val scene = Scene(appId)
                    scene.version = version
                    sceneMap!![appId] = scene
                }
            } else {
                Log.d(TAG, "setAppVersion appid|version is null")
            }
        } catch (e: java.lang.Exception) {
            Log.d(TAG, "setAppVersion error")
        }
    }

    fun setNLPDebug(value: Boolean) {
        this.debug = value
    }

    fun setSyncCustomNlpData(map: Map<String, Any>) {
        try {
            if (syncMap == null) {
                syncMap = ConcurrentHashMap<String, Any>()
            }
            if (map.isNotEmpty()) {
                for ((key, value) in map!!) {
                    syncMap!![key] = value
                }
            }
        } catch (e: java.lang.Exception) {
            Log.d(TAG, "setSyncCustomNlpData error")
        }
    }

    fun setAsyncCustomNlpData(opt: String, data: String) {
        try {
            //场景切换，维护场景状态
            if (opt != null && data != null) {

                if (opt == NlpOpt.OPT_SCENE_SWITCH || opt == NlpOpt.OPT_SET_SCENE_PRIORITY) {
                    if (sceneMap == null) {
                        sceneMap = ConcurrentHashMap<String, Scene>()
                    }
                    val scene = GsonUtil.fromJson(data, Scene::class.java)
                    if (!TextUtils.isEmpty(scene.app_id)) {
                        if (sceneMap!!.containsKey(scene.app_id)) {
                            val result = sceneMap!![scene.app_id] as Scene
                            result.toCopy(scene)
                            sceneMap!![scene.app_id] = result
                        } else {
                            val result = Scene(scene.app_id)
                            result.toCopy(scene)
                            sceneMap!![scene.app_id] = result
                        }
                    }
                }

                if (opt == NlpOpt.OPT_USER_SESSION) {
                    if (userSession == null) {
                        userSession = ConcurrentHashMap<String, UserSession>()
                    }
                    val us = GsonUtil.fromJson(data, UserSession::class.java)
                    if (!TextUtils.isEmpty(us.appid) && !TextUtils.isEmpty(us.userSession)) {
                        if (userSession!!.containsKey(us.appid)) {
                            userSession!![us.appid] = us
                        } else {
                            val temp = UserSession(us.appid, us.userSession)
                            userSession!![us.appid] = temp
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.d(TAG, "setAsyncCustomNlpData error")
        }
    }

    fun setAppPath(appId: String?, path: String?) {
        try {
            if (appId != null && path != null) {
                if (sceneMap == null) {
                    sceneMap = ConcurrentHashMap<String, Scene>()
                }
                if (sceneMap!!.containsKey(appId)) {
                    val result = (sceneMap!![appId] as Scene)
                    result.path = path
                    sceneMap!![appId] = result
                } else {
                    val scene = Scene(appId)
                    scene.path = path
                    sceneMap!![appId] = scene
                }
            } else {
                Log.d(TAG, "setAppVersion appid|version is null");
            }
        } catch (e: Exception) {
            Log.d(TAG, "setAppPath error")
        }

    }

    fun resetNlpLauncher() {
        try {
            Log.d(TAG, "初始化中")
            if (this.debug != null) {
                SpeechApi.getInstance().setNLPDebug(this.debug!!)
            }

            //初始化生命周期
            SpeechApi.getInstance().resetNlpState()
            if (appLifeCycle != null && appLifeCycle!!.size > 0) {
                Log.d(TAG, "appLifeCycle:" + appLifeCycle!!.size.toString())
                for ((appId, lifecycleStatus) in appLifeCycle!!) {
                    Log.d(TAG, "appLifeCycle-appid:$appId | life:$lifecycleStatus")
                    if (lifecycleStatus == AppLifeCycle.CREATE) {
                        SpeechApi.getInstance().startApp(appId)
                    }
                    if (lifecycleStatus == AppLifeCycle.FOREGROUND) {
                        SpeechApi.getInstance().moveToForeground(appId)
                    }
                    if (lifecycleStatus == AppLifeCycle.BACKGROUND) {
                        SpeechApi.getInstance().moveToBack(appId)
                    }
                    if (lifecycleStatus == AppLifeCycle.DESTROY) {
                        SpeechApi.getInstance().destroyApp(appId)
                    }
                }
            }

            if (syncMap != null && syncMap!!.size > 0) {
                Log.d(TAG, "syncMap:" + syncMap!!.size.toString())
                SpeechApi.getInstance().setSyncCustomNlpData(syncMap)
            }

            //scene
            try {
                if (sceneMap != null && sceneMap!!.size > 0) {
                    Log.d(TAG, "sceneMap:" + sceneMap!!.size.toString())
                    for ((appId, scene) in sceneMap!!) {
                        Log.d(TAG, "sceneMap-appid:$appId | scene:" + GsonUtil.toJson(scene))
                        SpeechApi.getInstance().setAsyncCustomNlpData(NlpOpt.OPT_SCENE_SWITCH, GsonUtil.toJson(scene))
                        if (!TextUtils.isEmpty(scene.priority)) {
                            SpeechApi.getInstance().setAsyncCustomNlpData(NlpOpt.OPT_SET_SCENE_PRIORITY, GsonUtil.toJson(scene))
                        }
                    }
                }
            } catch (e: java.lang.Exception) {
                Log.d(TAG, "scene初始化 失败")
            }

            try {
                //UserSession
                if (userSession != null && userSession!!.size > 0) {
                    Log.d(TAG, "userSession:" + userSession!!.size.toString())
                    for ((key, value) in userSession!!) {
                        Log.d(TAG, "userSession-appid:$key | userSession:" + GsonUtil.toJson(value))
                        SpeechApi.getInstance().setAsyncCustomNlpData(NlpOpt.OPT_USER_SESSION, GsonUtil.toJson(value))
                    }
                }
            } catch (e: java.lang.Exception) {
                Log.d(TAG, "UserSession初始化 失败")
            }

            //serverList
            if (serverList != null && serverList!!.size > 0) {
                Log.d(TAG, "serverList:" + serverList!!.size.toString())
                SpeechApi.getInstance().setServerApp(serverList)
            }
        } catch (e: java.lang.Exception) {
            Log.d(TAG, "初始化 失败")
        }
    }
}

enum class AppLifeCycle {
    CREATE, FOREGROUND, BACKGROUND, DESTROY
}