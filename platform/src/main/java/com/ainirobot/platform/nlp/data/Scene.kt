package com.ainirobot.platform.nlp.data

import android.text.TextUtils

class Scene {
    var app_id: String = ""
        private set
    var path: String = "Main"
        set(value) = if (value == null) {
            field = "Main"
        } else {
            field = value
        }
    var version: String = ""
        set(value) = if (value == null) {
            field = ""
        } else {
            field = value
        }
    var subPage: String = ""
        set(value) = if (value == null) {
            field = ""
        } else {
            field = value
        }
    var priority: String = "NORMAL"
        private set

    constructor(app_id: String) {
        this.app_id = app_id
    }

    fun toCopy(scene: Scene): Scene {
        if (!TextUtils.isEmpty(scene.app_id) && scene.app_id == this.app_id) {
            if (!TextUtils.isEmpty(scene.path))
                this.path = scene.path
            if (!TextUtils.isEmpty(scene.version))
                this.version = scene.version
            if (!TextUtils.isEmpty(scene.subPage))
                this.subPage = scene.subPage
            if (!TextUtils.isEmpty(scene.priority))
                this.priority = scene.priority
        }
        return this
    }

    override fun equals(obj: Any?): Boolean {
        if (obj == null) {
            return false
        }

        if (this === obj) {
            return true
        }

        if (this.javaClass != obj.javaClass) {
            return false
        }
        var scene: Scene = obj as Scene
        if (scene.app_id == this.app_id
                && scene.path == this.path
                && scene.version == this.version
                && scene.priority == this.priority && scene.subPage == this.subPage) {
            return true
        }
        return false
    }

    override fun hashCode(): Int {
        var code: String = this.app_id + this.path + this.version + this.priority.toString() + this.subPage
        return code.hashCode()
    }

}
