package com.ainirobot.platform.bean.net.video.action;


import android.os.Looper;
import android.util.Log;

import com.ainirobot.platform.bean.net.video.IVideoView;
import com.ainirobot.platform.bean.net.video.bean.Request;
import com.ainirobot.platform.bean.net.video.bean.Response;

public class VideoAction implements Action {

    private static final String TAG = "VideoAction";
    private Request mRequest;
    private IVideoView mVideoView;

    public VideoAction(Request request, IVideoView videoView) {
        this.mRequest = request;
        this.mVideoView = videoView;
    }

    @Override
    public IVideoView getVideoCallBack(){
        return mVideoView;
    }

    @Override
    public Request request() {
        return mRequest;
    }

    @Override
    public String getTag() {
        return mRequest.getTag();
    }

    @Override
    public void complete(Response response) {
        Log.i(TAG, "result = " + response + "， Thread = " + (Looper.myLooper() == Looper.getMainLooper()));
        mVideoView.onSuccess(response);
    }

    @Override
    public void error(Response response) {
        Log.i(TAG, "error");
        mVideoView.onFailure(response);
    }
}
