/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.bean;

import androidx.annotation.StringDef;
import android.text.TextUtils;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class CharacterInfo {

    public static final String PLATFORM_RN = "ReactNative";
    public static final String PLATFORM_NATIVE = "Native";

    @StringDef({PLATFORM_RN, PLATFORM_NATIVE})
    @Retention(RetentionPolicy.SOURCE)
    public  @interface Platform {

    }

    /**
     * 角色实现平台（ReactNative/Native）
     */
    @Platform
    private final String platform;

    /**
     * 角色名称
     */
    private final String name;

    /**
     * 是否默认角色，开机后自动切换为默认角色
     */
    private boolean isDefault = false;

    public CharacterInfo(String name, @Platform String platform) {
        this.name = name;
        this.platform = platform;
    }

    public String getName() {
        return name;
    }

    public String getPlatform() {
        return platform;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean isDefault) {
        this.isDefault = isDefault;
    }

    @Override
    public int hashCode() {
        int result = 0;
        result = 31 * result + (name == null ? 0 : name.hashCode());
        result = 31 * result + (platform == null ? 0 : platform.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof CharacterInfo) {
            CharacterInfo info = (CharacterInfo) obj;
            return TextUtils.equals(info.getName(), this.getName())
                    && TextUtils.equals(info.getPlatform(), this.getPlatform());
        }
        return false;
    }
}
