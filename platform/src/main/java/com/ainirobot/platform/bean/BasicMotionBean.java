package com.ainirobot.platform.bean;

/**
 * Created by Orion on 2019/8/10.
 */
public class BasicMotionBean {

    private Mode mode;

    /**
     * 线速度：0.1 - 1.2 m/s
     */
    private float linearSpeed;

    /**
     * 距离，最大5m
     */
    private float distance;

    /**
     * 角速度：10 - 100 角度/s （约 0.2 - 1.8 弧度/s）
     */
    private float angularSpeed;

    /**
     * 角度，最大360
     */
    private float angle;

    /**
     * 向前避停，（豹小秘目前无效）
     */
    private boolean avoidStop;

    public BasicMotionBean() {
        this.mode = Mode.goForward;
        this.linearSpeed = 0.3f;
        this.distance = 0.1f;
        this.angularSpeed = 30;
        this.angle = 10;
        this.avoidStop = false;
    }

    public Mode getMode() {
        return mode;
    }

    public void setMode(Mode mode) {
        this.mode = mode;
    }

    public float getLinearSpeed() {
        return linearSpeed;
    }

    public void setLinearSpeed(float linearSpeed) {
        this.linearSpeed = linearSpeed;
    }

    public float getDistance() {
        return distance;
    }

    public void setDistance(float distance) {
        this.distance = distance;
    }

    public float getAngularSpeed() {
        return angularSpeed;
    }

    public void setAngularSpeed(float angularSpeed) {
        this.angularSpeed = angularSpeed;
    }

    public float getAngle() {
        return angle;
    }

    public void setAngle(float angle) {
        this.angle = angle;
    }

    public boolean isAvoidStop() {
        return avoidStop;
    }

    public void setAvoidStop(boolean avoidStop) {
        this.avoidStop = avoidStop;
    }

    public enum Mode {
        goForward,
        goBackward,
        turnLeft,
        turnRight
    }

    @Override
    public String toString() {
        return "BasicMotionBean{" +
                "mode=" + mode +
                ", linearSpeed=" + linearSpeed +
                ", distance=" + distance +
                ", angularSpeed=" + angularSpeed +
                ", angle=" + angle +
                ", avoidStop=" + avoidStop +
                '}';
    }
}
