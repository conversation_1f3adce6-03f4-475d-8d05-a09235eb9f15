package com.ainirobot.platform.bean.net.video.db;

import java.util.List;

public interface IFileDataBase {

    String queryPathByUrl(String url);

    String queryStateByUrl(String url);

    int queryBreakContinueByUrl(String url);

    long queryTotalLengthByUrl(String url);

    List<String> scanDelayTime(int day);

    void insert(String url, String path);

    void updateState(String downloadUrl, DownloadState state);

    void updateDownloadTotalAndBreakContinue(String downloadUrl, long totalLength, int breakContinue);

    void delete(String path);
}
