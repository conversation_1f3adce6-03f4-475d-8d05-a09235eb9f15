package com.ainirobot.platform.bean.net.video.downloader;

import android.content.Context;

import com.ainirobot.platform.bean.net.video.FileUtil;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.RandomAccessFile;

public class FileCache {


    private File file;
    private String url;
    private RandomAccessFile randomFile;

    public FileCache(Context context, String url){
        this.file = FileUtil.createFile(context, FileUtil.getFileName(url));
        this.url = url;
        try {
            this.randomFile = new RandomAccessFile(file, "rwd");
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
    }

    public String getFilePath() {
        return file.getAbsolutePath();
    }

    public File getFile() {
        return file;
    }

    public boolean exists() {
        return file.exists();
    }

    public String getUrl() {
        return url;
    }

    public long length() {
        return file.length();
    }

    public void seek(long startPos) throws IOException {
        randomFile.seek(startPos);
    }

    public void write(byte[] buffer,int offset) throws IOException {
        randomFile.write(buffer, 0, offset);
    }

    public void close(){
        try {
            randomFile.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
