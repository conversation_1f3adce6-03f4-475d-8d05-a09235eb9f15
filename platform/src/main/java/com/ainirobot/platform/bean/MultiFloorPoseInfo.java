package com.ainirobot.platform.bean;

public class MultiFloorPoseInfo {
    public String poseName;
    public int floorIndex;
    public boolean isAdjustAngle;
    public boolean isReversePoseTheta;

    public MultiFloorPoseInfo(String poseName, int floorIndex) {
        this.poseName = poseName;
        this.floorIndex = floorIndex;
    }

    public MultiFloorPoseInfo(String poseName, int floorIndex, boolean isAdjustAngle) {
        this.poseName = poseName;
        this.floorIndex = floorIndex;
        this.isAdjustAngle = isAdjustAngle;
    }
    public MultiFloorPoseInfo(String poseName, int floorIndex, boolean isAdjustAngle, boolean isReversePoseTheta) {
        this.poseName = poseName;
        this.floorIndex = floorIndex;
        this.isAdjustAngle = isAdjustAngle;
        this.isReversePoseTheta = isReversePoseTheta;
    }

    @Override
    public String toString() {
        return "MultiFloorPoseInfo{" +
                "poseName='" + poseName + '\'' +
                ", floorIndex=" + floorIndex +
                '}';
    }
}
