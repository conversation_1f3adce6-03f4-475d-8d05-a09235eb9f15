package com.ainirobot.platform.bean.net.video.downloader;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.platform.bean.net.video.db.DownloadState;
import com.ainirobot.platform.bean.net.video.db.IFileDataBase;

import java.util.concurrent.atomic.AtomicInteger;

public class FileDownloader implements DownloadThread.OnDownloadThreadListener {

    private static final String TAG = "FileDownloader";
    private DownloadProgressListener listener;
    private IFileDataBase mDataBase;
    private DownloadThread thread;
    private HttpProxy proxy;
    private String downloadUrl;
    private FileCache fileCache;
    private AtomicInteger retryCount = new AtomicInteger();

    public void setDownloadProgressListener(DownloadProgressListener listener) {
        this.listener = listener;
    }

    public FileDownloader(HttpProxy proxy, FileCache fileCache, IFileDataBase dataBase) {
        this.proxy = proxy;
        this.downloadUrl = fileCache.getUrl();
        this.mDataBase = dataBase;
        this.fileCache = fileCache;
        initCache();
    }

    private void initCache() {
        long totalLength = mDataBase.queryTotalLengthByUrl(downloadUrl);
        if (totalLength == -1L) {
            mDataBase.insert(downloadUrl, fileCache.getFilePath());
        } else {
            needUpdateState(totalLength);
        }
    }

    private void needUpdateState(long totalLength) {
        if (fileCache.length() != 0 && fileCache.exists()) {
            if (fileCache.length() == totalLength) {
                mDataBase.updateState(downloadUrl, DownloadState.FINISH);
            }
        }
    }

    @Override
    public void update(long size) {
        if (listener != null) {
            listener.onDownloadSize(size);
        }
    }

    public void download() {
        if (fileCache.exists() && fileCache.length() > 0) {
            checkState(downloadUrl);
        } else {
            realDownload();
        }
    }

    private void checkState(String downloadUrl) {
        String state = mDataBase.queryStateByUrl(downloadUrl);
        if (!TextUtils.isEmpty(state)) {
            switch (DownloadState.valueOf(state)) {
                case FINISH:
                    if (listener != null) {
                        listener.onDownloadFinish();
                    }
                    break;
                case IDEL:
                case CANCEL:
                case FAILURE:
                    realDownload();
                    break;
                case DOWNLOAD:
                    if (thread != null) {
                        print("this task is downloading...");
                    } else {
                        realDownload();
                    }
                    break;
                default:
                    break;
            }
        }
    }

    private void realDownload() {
        print(String.format("downloadSize = %d", fileCache.length()));
        mDataBase.updateState(downloadUrl, DownloadState.DOWNLOAD);
        thread = new DownloadThread(proxy, fileCache,mDataBase);
        thread.setOnDownloadThreadListener(this);
        thread.start();
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void cancel() {
        if (thread != null && mDataBase != null) {
            String state = mDataBase.queryStateByUrl(downloadUrl);
            if (!TextUtils.isEmpty(state)) {
                switch (DownloadState.valueOf(state)) {
                    case FINISH:
                        break;
                    case IDEL:
                    case FAILURE:
                    case DOWNLOAD:
                        thread.cancel();
                        break;
                    default:
                        break;
                }
            }
        }
    }

    @Override
    public void changeState(DownloadState state) {
        Log.i(TAG, String.format("changeState :%s", state.name()));
        if (mDataBase != null) {
            mDataBase.updateState(downloadUrl, state);
        }
        if (state == DownloadState.FAILURE) {
            if (retryCount.get() < 3) {
                Log.i(TAG, String.format("download failure retry count %d", retryCount.get()));
                retryCount.incrementAndGet();
                checkState(downloadUrl);
            } else {
                if (listener != null) {
                    listener.onDownloadFailed(state.name());
                }
            }
        } else if (state == DownloadState.FINISH) {
            if (listener != null) {
                listener.onDownloadFinish();
            }
        } else if (state == DownloadState.CANCEL) {
            if (listener != null) {
                listener.onDownloadFailed(state.name());
            }
        }
    }

    private static void print(String msg) {
        Log.i(TAG, msg);
    }
}
