package com.ainirobot.platform.bean;

import android.util.Log;

import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.google.gson.annotations.SerializedName;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class ConfigBean {

    private static class Data {
        String serverKey;
        String localKey;
        //0-int, 1-float, 2-string
        int type;

        Data(String serverKey, String localKey, int type) {
            this.serverKey = serverKey;
            this.localKey = localKey;
            this.type = type;
        }
    }

    private static Map<String, List<Data>> mTranslateMap = new HashMap<>();

    static {
        //基础opk
        List<Data> opkData = new ArrayList<>();
        opkData.add(new Data("enable_auto_back_reception", "robot_auto_back_reception", 0));
        opkData.add(new Data("back_reception_waiting_time", "robot_setting_waiting_time", 0));
        opkData.add(new Data("enable_focus_follow", "robot_focus_follow", 0));
        opkData.add(new Data("focus_follow_style", "settings_global_switch_follow_style", 0));
        opkData.add(new Data("enable_small_action", "robot_samll_action", 0));
        opkData.add(new Data("enable_chat_replay", "robot_chat_reply", 0));
        opkData.add(new Data("enable_mask_detection", "robot_mask_detection", 0));
        opkData.add(new Data("greet_speed", "robot_setting_greet_linear_speed", 2));
        opkData.add(new Data("nav_speed", "robot_setting_nav_linear_speed", 2));
        mTranslateMap.put("system_f8e65f2e96b695a53c571f3a69c9bdba", opkData);
        List<Data> leadingData = new ArrayList<>();
        leadingData.add(new Data("lead_speed", "robot_setting_lead_linear_speed", 2));
        mTranslateMap.put("system_576f1b540139d34db37b515800e9506c", leadingData);
        List<Data> guideData = new ArrayList<>();
        guideData.add(new Data("enable_explain_interactive", "switch_allow_chat_when_interpret", 0));
        guideData.add(new Data("walking_speed", "robot_setting_guide_linear_speed", 2));
        mTranslateMap.put("system_535a8059fc87f870346a79eaed463b8b", guideData);
        List<Data> cruiseData = new ArrayList<>();
        cruiseData.add(new Data("cruise_speed", "robot_setting_cruise_linear_speed", 2));
        mTranslateMap.put("system_464a8e79eff3dce0442956244d698809", cruiseData);
        List<Data> advertData = new ArrayList<>();
        advertData.add(new Data("enable_ad_rotate", "robot_show_ad_and_rotate", 0));
        mTranslateMap.put("system_24cfaa233ef1febe35579d1d28003890", advertData);
        List<Data> navigationData = new ArrayList<>();
        navigationData.add(new Data("nav_speed", "robot_setting_nav_linear_speed", 2));
        mTranslateMap.put("system_cb2d5843362e445fead4a9e4d2e4545b", navigationData);
    }

    @SerializedName("robotapp_id")
    private String appId;

    @SerializedName("cfg_type")
    private String configType = "user_robot";

    @SerializedName("cfg_data")
    private String configData;

    private transient String filePath;

    public ConfigBean(String appId, String configData) {
        this.appId = appId;
        this.configData = configData;
    }

    public void updateData() {
        List<Data> translateList = mTranslateMap.get(appId);
        if (translateList == null) {
            return;
        }
        try {
            JSONObject config = new JSONObject(configData);
            JSONObject robot = config.optJSONObject("robot");
            if (robot == null) {
                robot = new JSONObject();
                config.put("robot", robot);
            }
            JSONObject corp = config.optJSONObject("corp");
            JSONObject global = config.optJSONObject("global");
            for (Data translate : translateList) {
                Object value;
                switch (translate.type) {
                    case 0:
                        value = RobotSettingApi.getInstance().getRobotInt(translate.localKey);
                        break;
                    case 1:
                        value = RobotSettingApi.getInstance().getRobotFloat(translate.localKey);
                        break;
                    default:
                        value = RobotSettingApi.getInstance().getRobotString(translate.localKey);
                        break;
                }
                robot.put(translate.serverKey, value);
                if (corp != null && corp.has(translate.serverKey)) {
                    corp.put(translate.serverKey, value);
                }
                if (global != null && global.has(translate.serverKey)) {
                    global.put(translate.serverKey, value);
                }
            }
            configData = config.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void mergeConfigData() {
        try {
            JSONObject config = new JSONObject(configData);
            JSONObject newConfig = new JSONObject();
            Iterator<String> it = config.keys();
            while (it.hasNext()) {
                String key = it.next();
                JSONObject data = config.optJSONObject(key);
                if (data != null) {
                    Iterator<String> subIt = data.keys();
                    while (subIt.hasNext()) {
                        String subKey = subIt.next();
                        newConfig.put(subKey, data.get(subKey));
                    }
                } else {
                    newConfig.put(key, config.opt(key));
                }
            }
            configData = newConfig.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }


    public void mergeRobotData() {
        try {
            JSONObject configJson = new JSONObject(configData);
            JSONObject config = configJson.optJSONObject("robot");
            if (config == null) {
                Log.i("ConfigBean", "robot is null: ");
                return;
            }
            Log.i("ConfigBean", "mergeRobotData start: " + config);
            JSONObject newConfig = new JSONObject();
            Iterator<String> it = config.keys();
            while (it.hasNext()) {
                String key = it.next();
                JSONObject data = config.optJSONObject(key);
                if (data != null) {
                    Iterator<String> subIt = data.keys();
                    while (subIt.hasNext()) {
                        String subKey = subIt.next();
                        newConfig.put(subKey, data.get(subKey));
                    }
                } else {
                    newConfig.put(key, config.opt(key));
                }
            }
            configData = newConfig.toString();
            Log.i("ConfigBean", "mergeRobotData end: " + configData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public String getAppId() {
        return appId;
    }

    public String getConfigData() {
        return configData;
    }

    public void setFilePath(String path) {
        filePath = path;
    }

    public String getFilePath() {
        return filePath;
    }

}
