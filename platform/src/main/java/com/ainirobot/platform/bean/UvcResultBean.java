package com.ainirobot.platform.bean;

import com.google.gson.annotations.SerializedName;

/**
 * 托盘摄像头识别结果
 */
public class UvcResultBean {

    /**
     * 识别状态码：
     * 底层返回的标识：0-有物品，1-无物品
     * 上报给业务层的标识统一修改为：0-无物品，1-有物品
     */
    @SerializedName("label_id")
    private int label_id;
    /**
     * 物品描述
     */
    @SerializedName("label")
    private String label;
    /**
     * 识别置信度
     */
    @SerializedName("score")
    private float score = 0f;

    /**
     * 托盘层数标识：1-第一层，2-第二层，3-第三层
     */
    private int trayLayer;

    public UvcResultBean(int trayLayer, int label_id) {
        this(trayLayer, label_id, "", 0f);
    }

    public UvcResultBean(int trayLayer, int label_id, String label, float score) {
        this.trayLayer = trayLayer;
        this.label_id = label_id;
        this.label = label;
        this.score = score;
    }

    public int getTrayLayer() {
        return trayLayer;
    }

    public void setTrayLayer(int trayLayer) {
        this.trayLayer = trayLayer;
    }

    public int getLabel_id() {
        return label_id;
    }

    public void setLabel_id(int label_id) {
        this.label_id = label_id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public float getScore() {
        return score;
    }

    public void setScore(float score) {
        this.score = score;
    }

    @Override
    public String toString() {
        return "ClassifyBean{" +
                "trayLayer=" + trayLayer +
                "label_id=" + label_id +
                ", label='" + label + '\'' +
                ", score=" + score +
                '}';
    }

}
