/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.bean.net.body;

import com.ainirobot.platform.utils.IOUtils;

import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class MultipartBody implements RequestBody {


    private static final byte[] BOUNDARY = UUID.randomUUID().toString().getBytes(); //Use UTF_8 unconditionally
    private static final byte[] CRLF = {'\r', '\n'};
    private static final byte[] DASHDASH = {'-', '-'};
    private static final byte[] COLONSPACE = {':', ' '};

    private List<Part> mParts = new ArrayList<>();

    @Override
    public long getContentLength() {
        return -1;
    }

    public String getContentType() {
        return "multipart/form-data; boundary=" + IOUtils.getUtf8String(BOUNDARY);
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        for (Part part : mParts) {
            HashMap<String, String> headers = part.headers;
            RequestBody body = part.body;

            out.write(DASHDASH);
            out.write(BOUNDARY);
            out.write(CRLF);

            if (!headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    writeHeader(out, entry.getKey(), entry.getValue());
                }
            }

            String contentType = body.getContentType();
            if (contentType != null) {
                writeHeader(out, "Content-Type", contentType);
            }

            long contentLength = body.getContentLength();
            if (contentLength != -1) {
                writeHeader(out, "Content-Length", String.valueOf(contentLength));
            }
            out.write(CRLF);
            body.writeTo(out);
            out.write(CRLF);
        }

        out.write(DASHDASH);
        out.write(BOUNDARY);
        out.write(DASHDASH);
        out.write(CRLF);
    }

    private void writeHeader(OutputStream out, String key, String value) throws IOException {
        out.write(key.getBytes("UTF-8"));
        out.write(COLONSPACE);
        out.write(value.getBytes("UTF-8"));
        out.write(CRLF);
    }

    public void addPart(String name, RequestBody body) {
        addPart(name, null, body);
    }

    public void addPart(String name, HashMap<String, String> headers, RequestBody body) {
        StringBuilder disposition = new StringBuilder("form-data; name=");
        appendQuotedString(disposition, name);

        if (body instanceof FileBody) {
            String filename = ((FileBody) body).getFileName();
            disposition.append("; filename=");
            appendQuotedString(disposition, filename);
        }

        if (headers == null) {
            headers = new HashMap<>();
        }
        headers.put("Content-Disposition", disposition.toString());
        mParts.add(new Part(headers, body));
    }

    private static final class Part {

        private final HashMap<String, String> headers;
        private final RequestBody body;

        private Part(HashMap<String, String> headers, RequestBody body) {
            this.body = body;
            this.headers = headers;
        }

    }

    private StringBuilder appendQuotedString(StringBuilder target, String key) {
        target.append('"');
        for (int i = 0, len = key.length(); i < len; i++) {
            char ch = key.charAt(i);
            switch (ch) {
                case '\n':
                    target.append("%0A");
                    break;
                case '\r':
                    target.append("%0D");
                    break;
                case '"':
                    target.append("%22");
                    break;
                default:
                    target.append(ch);
                    break;
            }
        }
        target.append('"');
        return target;
    }
}
