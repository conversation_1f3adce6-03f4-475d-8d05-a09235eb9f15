package com.ainirobot.platform.bean;

import java.util.ArrayList;

public class AutoCruiseTaskInfo {

    private ArrayList<String> startTimeList = new ArrayList<>();
    private ArrayList<String> endTimeList = new ArrayList<>();

    public ArrayList<String> getStartTimeList() {
        return startTimeList;
    }

    public void setStartTimeList(final ArrayList<String> startTimeList) {
        this.startTimeList = startTimeList;
    }

    public ArrayList<String> getEndTimeList() {
        return endTimeList;
    }

    public void setEndTimeList(final ArrayList<String> endTimeList) {
        this.endTimeList = endTimeList;
    }

    @Override public String toString() {
        return "AutoCruiseTaskInfo{"
            + "startTimeList="
            + startTimeList
            + ", endTimeList="
            + endTimeList
            + '}';
    }
}
