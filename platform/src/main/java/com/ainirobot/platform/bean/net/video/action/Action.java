package com.ainirobot.platform.bean.net.video.action;

import com.ainirobot.platform.bean.net.video.IVideoView;
import com.ainirobot.platform.bean.net.video.bean.Request;
import com.ainirobot.platform.bean.net.video.bean.Response;

public interface Action {

    Request request();

    String getTag();

    IVideoView getVideoCallBack();

    void complete(Response response);

    void error(Response response);
}
