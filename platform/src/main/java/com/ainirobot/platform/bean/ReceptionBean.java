/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.bean;

import java.io.Serializable;

public class ReceptionBean implements Serializable {
    private String visitor_name;
    private String user_id;
    private String tel;
    private String purpose;
    private String respondent_name;
    private String location;
    private String task_id;
    private String dept;
    private String form_id;
    private String company;
    private String code;
    private int group_size;
    private long visit_time;
    private String group_name;
    private Extension extension;

    public ReceptionBean() {
    }

    public Extension getExtension() {
        return extension;
    }

    public void setExtension(Extension extension) {
        this.extension = extension;
    }

    public String getName() {
        return visitor_name;
    }

    public String getPhone() {
        return tel;
    }

    public String getPurpose() {
        return purpose;
    }

    public String getStaff() {
        return respondent_name;
    }

    public String getLocation() {
        return location;
    }

    public String getRemoteId() { return user_id; }

    public String getTaskId() { return task_id; }

    public void setVisitor_name(String visitor_name) {
        this.visitor_name = visitor_name;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getUser_id(){
        return user_id;
    }

    public String getCode(){
        return code;
    }

    public void setCode(String code){
        this.code = code;
    }

    public void setGroup_size(int group_size){
        this.group_size = group_size;
    }

    public int getGroup_size(){
        return group_size;
    }

    public void setVisit_time(long visit_time){
        this.visit_time = visit_time;
    }
    public long getVisit_time(){
        return visit_time;
    }
    public void setGroup_name(String group_name){
        this.group_name = group_name;
    }
    public String getGroup_name(){
        return group_name;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public void setRespondent_name(String respondent_name) {
        this.respondent_name = respondent_name;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public void setTask_id(String task_id) {
        this.task_id = task_id;
    }

    public void setDept(String dept){
        this.dept = dept;
    }
    public void setForm_id(String form_id){
        this.form_id = form_id;
    }
    public void setCompany(String company){
        this.company = company;
    }
    public String getDept(){
        return dept;
    }
    public String getForm_id(){
        return form_id;
    }
    public String getCompany(){
        return company;
    }

    public static class Extension implements Serializable {
        private String title_number;

        public String getTitle_number() {
            return title_number;
        }

        public void setTitle_number(String title_number) {
            this.title_number = title_number;
        }

        @Override
        public String toString() {
            return "Extension{" +
                    "title_number='" + title_number + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "ReceptionBean{" +
                "visitor_name='" + visitor_name + '\'' +
                ", user_id='" + user_id + '\'' +
                ", tel='" + tel + '\'' +
                ", purpose='" + purpose + '\'' +
                ", respondent_name='" + respondent_name + '\'' +
                ", location='" + location + '\'' +
                ", task_id='" + task_id + '\'' +
                ", dept='" + dept + '\'' +
                ", form_id='" + form_id + '\'' +
                ", company='" + company + '\'' +
                ", code='" + code + '\'' +
                ", group_size=" + group_size +
                ", visit_time=" + visit_time +
                ", group_name='" + group_name + '\'' +
                ", extension=" + extension +
                '}';
    }
}
