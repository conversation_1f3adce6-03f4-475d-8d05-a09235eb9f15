/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.bean.net;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.platform.bean.net.body.RequestBody;
import com.ainirobot.platform.utils.IOUtils;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.Map;

public class NetworkRunnable implements Runnable {

    private final HttpClient mClient;
    private final Request mRequest;
    private final Response.Listener mListener;
    private boolean isCanceled = false;

    public NetworkRunnable(HttpClient client,
                           Request request, Response.Listener listener) {
        this.mClient = client;
        this.mRequest = request;
        this.mListener = listener;
    }

    @Override
    public void run() {
        if (!isCanceled) {
            performRequest();
            return;
        }

        if (mListener != null) {
            mClient.getDispatcher().finished(this);
            mListener.onFailure(new IOException("Canceled"));
        }
    }

    public void cancel() {
        isCanceled = true;
    }

    public Object getTag() {
        return mRequest.getTag();
    }

    public void performRequest() {
        String url = mRequest.getUrl();
        String method = mRequest.getMethod();
        String sessionId = mRequest.getSessionId();
        RequestBody body = mRequest.getBody();
        Map<String, String> headers = mRequest.getHeaders();

        OutputStream out = null;
        InputStream in = null;
        try {
            HttpURLConnection urlConnection = (HttpURLConnection) new URL(url).openConnection();
            if ("POST".equals(method)) {
                urlConnection.setDoOutput(true);
            }
            urlConnection.setDoInput(true);
            urlConnection.setUseCaches(false);
            Log.d("rpk",  "method" + method);
            urlConnection.setRequestMethod(method);
            if (!TextUtils.isEmpty(sessionId)) {
                urlConnection.setRequestProperty("Cookie", sessionId);
            }
            for (Map.Entry<String, String> header : headers.entrySet()) {
                urlConnection.setRequestProperty(header.getKey(), header.getValue());
            }

            if (body != null) {
                urlConnection.setRequestProperty("Content-Type", body.getContentType());

                out = urlConnection.getOutputStream();
                body.writeTo(out);
                out.flush();
            }

            if (isCanceled) {
                if (mListener != null) {
                    mListener.onFailure(new IOException("Canceled"));
                }
                return;
            }

            urlConnection.connect();
            int responseCode = urlConnection.getResponseCode();
            if (mListener != null) {
                Map<String, List<String>> responseHeaders = urlConnection.getHeaderFields();
                long contentLength = urlConnection.getContentLength();
                String contentType = urlConnection.getContentType();
                String contentEncoding = urlConnection.getContentEncoding();
                String responseMessage = urlConnection.getResponseMessage();
                Log.d("rpk", responseMessage + responseCode);
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    in = urlConnection.getInputStream();
                }

                Response response = new Response(responseCode, getBytesByInputStream(in), contentLength);
                response.setContentType(contentType);
                response.setHeaders(responseHeaders);
                response.setContentEncoding(contentEncoding);
                response.setResponseMessage(responseMessage);
                mListener.onResponse(response);
            }

        } catch (IOException e) {
            e.printStackTrace();
            if (mListener != null) {
                mListener.onFailure(e);
            }
        } finally {
            IOUtils.close(out);
            IOUtils.close(in);
            mClient.getDispatcher().finished(this);
        }
    }

    private byte[] getBytesByInputStream(InputStream is) {
        byte[] bytes = null;
        BufferedInputStream bis = new BufferedInputStream(is);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        BufferedOutputStream bos = new BufferedOutputStream(baos);
        byte[] buffer = new byte[1024 * 8];
        int length = 0;
        try {
            while ((length = bis.read(buffer)) > 0) {
                bos.write(buffer, 0, length);
            }
            bos.flush();
            bytes = baos.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                bos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                bis.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return bytes;
    }

}
