package com.ainirobot.platform.bean;

public class PoseItem {
        public float x;
        public float y;
        public float theta;
        public String name;
        public int postype;

        /*public PoseItem(String value) {
            String[] split = value.split(" ");
            Float[] poseF = new Float[3];
            for (int j = 0; j < split.length; j++) {
                String[] temp = split[j].split("=");
                poseF[j] = Float.valueOf(temp[1]);
            }

            this.x = poseF[0];
            this.y = poseF[1];
            this.theta = poseF[2];
        }*/

        @Override
        public String toString() {
            return "x=" + x + "  y=" + y + " theta=" + theta + " name=" + name + ",postype:" + postype;
        }

        public double getDistance(PoseItem pose) {
            if (pose == null) {
                return 1.7976931348623157E308D;
            } else {
                double destX = (double) this.x;
                double destY = (double) this.y;
                double x = (double) pose.x;
                double y = (double) pose.y;
                return Math.sqrt(Math.pow(x - destX, 2.0D) + Math.pow(y - destY, 2.0D));
            }
        }

        public String getName() {
            return name;
        }
}
