/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.bean.net;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.platform.bean.net.body.RequestBody;

import java.util.HashMap;

public final class Request {
    private static final String TAG = Request.class.getSimpleName();
    private final String mUrl;
    private final String mMethod;
    private final String mSessionId;
    private HashMap<String,String> mHeaders;
    private final RequestBody mBody;
    private final Object mTag;

    private Request(Builder builder) {
        this.mUrl = builder.url;
        this.mMethod = builder.method;
        this.mSessionId = builder.session;
        this.mHeaders = builder.headers;
        this.mBody = builder.body;
        this.mTag = (builder.tag != null) ? builder.tag : this;
        Log.d(TAG, "tag:" + this.mTag);
    }

    public String getUrl(){
        return mUrl;
    }

    public String getMethod(){
        return mMethod;
    }

    public String getSessionId(){
        return mSessionId;
    }

    public HashMap<String,String> getHeaders(){
        return mHeaders;
    }

    public RequestBody getBody(){
        return mBody;
    }

    public Object getTag(){
        return mTag;
    }

    public static class Builder {
        private static final String GET = "GET";
        private static final String POST = "POST";

        private String url;
        private String method;
        private String session;
        private HashMap<String, String> headers;
        private RequestBody body;
        private Object tag;

        public Builder() {
            this.method = GET;
            headers = new HashMap<>();
        }

        public Builder url(String url) {
            this.url = url;
            return this;
        }


        public Builder session(String session) {
            this.session = session;
            return this;
        }

        public Builder header(String key, String value) {
            headers.put(key, value);
            return this;
        }

        public Builder get() {
            return method(GET, null);
        }

        public Builder post(RequestBody body) {
            return method(POST, body);
        }

        private Builder method(String method, RequestBody body) {
            if (TextUtils.isEmpty(method)) {
                throw new IllegalArgumentException("method is empty");
            }

            this.method = method;
            this.body = body;
            return this;
        }

        /**
         * It can be used later to cancel the request
         */
        public Builder tag(Object tag) {
            this.tag = tag;
            return this;
        }

        public Request build(){
            return new Request(this);
        }

    }
}
