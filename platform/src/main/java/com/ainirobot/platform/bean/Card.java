package com.ainirobot.platform.bean;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class Card implements Serializable {

    private String text;
    private String linkAccount;
    private String order;
    private List<CardUI> ui = new ArrayList<>();
    private Recommend recommend;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getLinkAccount() {
        return linkAccount;
    }

    public void setLinkAccount(String linkAccount) {
        this.linkAccount = linkAccount;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public List<CardUI> getUi() {
        return ui;
    }

    public void setUi(List<CardUI> ui) {
        this.ui = ui;
    }

    public Recommend getRecommend() {
        return recommend;
    }

    public void setRecommend(Recommend recommend) {
        this.recommend = recommend;
    }

    @Override
    public String toString() {
        return "Card{" +
                "text='" + text + '\'' +
                ", linkAccount='" + linkAccount + '\'' +
                ", order='" + order + '\'' +
                ", ui=" + ui +
                '}';
    }

    /**
     * 闲聊类型，图片，视频
     */
    public enum CardUiType {
        IMAGE("Image"), VIDEO("StandardVideo"), TEXT("text");

        private String type;

        public String getType() {
            return type;
        }

        CardUiType(String type) {
            this.type = type;
        }
    }

    public static class CardUI implements Serializable {
        private String type;
        private CardImage image;
        private CardVideo video;

        private int width;
        private int height;

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public CardImage getImage() {
            return image;
        }

        public void setImage(CardImage image) {
            this.image = image;
        }

        public CardVideo getVideo() {
            return video;
        }

        public void setVideo(CardVideo video) {
            this.video = video;
        }

        public int getWidth() {
            return width;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public int getHeight() {
            return height;
        }

        public void setHeight(int height) {
            this.height = height;
        }

        @Override
        public String toString() {
            return "CardUI{" +
                "type='" + type + '\'' +
                ", image=" + image +
                ", video=" + video +
                ", width=" + width +
                ", height=" + height +
                '}';
        }
    }

    public static class CardImage implements Serializable {
        private String src;
        private String link;
        private String backgroundColor;

        private int width;
        private int height;

        /**
         * 获取按比例缩放的高度，宽度不变，高度自适应
         * @param targetWidth
         * @return
         */
        public int getCalHeight(int targetWidth) {
            if (width == 0 || height == 0) {
                return targetWidth;
            }
            return targetWidth * height / width;
        }

        public int getWidth() {
            return width;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public int getHeight() {
            return height;
        }

        public void setHeight(int height) {
            this.height = height;
        }

        public String getSrc() {
            return src;
        }

        public void setSrc(String src) {
            this.src = src;
        }

        public String getLink() {
            return link;
        }

        public void setLink(String link) {
            this.link = link;
        }

        public String getBackgroundColor() {
            return backgroundColor;
        }

        public void setBackgroundColor(String backgroundColor) {
            this.backgroundColor = backgroundColor;
        }

        @Override
        public String toString() {
            return "CardImage{" +
                "src='" + src + '\'' +
                ", link='" + link + '\'' +
                ", backgroundColor='" + backgroundColor + '\'' +
                ", width=" + width +
                ", height=" + height +
                '}';
        }
    }

    //video":{"autoPlay":"false","autoPlayType":"afterTts",
    // "backgroundImage":"http://test-jiedai.ainirobot.com/media/bo/upload/images/20181113/85706a3880a473411a8e347d34aae30d_720x540.jpg",
    // "imageHeight":0,"imageWidth":0,
    // "src":"http://test-jiedai.ainirobot.com/media/bo/upload/video/20181113/431fb963ba033d25716c3ddd0b491fef.mp4",
    // "title":"菊花台MV","videoHeight":0,"videoWidth":0,"volume":"0"},"width":0}

    public static class CardVideo implements Serializable {

        private String src;
        private String title;
        private String backgroundImage;
        private boolean autoPlay;
        private String autoPlayType;
        private String volume;

        private int imageWidth;
        private int imageHeight;
        private int videoWidth;
        private int videoHeight;

        public int getImageWidth() {
            return imageWidth;
        }

        public void setImageWidth(int imageWidth) {
            this.imageWidth = imageWidth;
        }

        public int getImageHeight() {
            return imageHeight;
        }

        public void setImageHeight(int imageHeight) {
            this.imageHeight = imageHeight;
        }

        public int getVideoWidth() {
            return videoWidth;
        }

        public void setVideoWidth(int videoWidth) {
            this.videoWidth = videoWidth;
        }

        public int getVideoHeight() {
            return videoHeight;
        }

        public void setVideoHeight(int videoHeight) {
            this.videoHeight = videoHeight;
        }

        public String getSrc() {
            return src;
        }

        public void setSrc(String src) {
            this.src = src;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getBackgroundImage() {
            return backgroundImage;
        }

        public void setBackgroundImage(String backgroundImage) {
            this.backgroundImage = backgroundImage;
        }

        public boolean isAutoPlay() {
            return autoPlay;
        }

        public void setAutoPlay(boolean autoPlay) {
            this.autoPlay = autoPlay;
        }

        public String getAutoPlayType() {
            return autoPlayType;
        }

        public void setAutoPlayType(String autoPlayType) {
            this.autoPlayType = autoPlayType;
        }

        public String getVolume() {
            return volume;
        }

        public void setVolume(String volume) {
            this.volume = volume;
        }

        @Override
        public String toString() {
            return "CardVideo{" +
                    "src='" + src + '\'' +
                    ", title='" + title + '\'' +
                    ", backgroundImage='" + backgroundImage + '\'' +
                    ", autoPlay='" + autoPlay + '\'' +
                    ", autoPlayType='" + autoPlayType + '\'' +
                    ", volume='" + volume + '\'' +
                    '}';
        }
    }


    public static class Recommend implements Serializable {
        private List<Cards> cards;
        private List<Guide> guides;
        private String title; //other显示弹框的标题

        public List<Cards> getCards() {
            return cards;
        }

        public void setCards(List<Cards> cards) {
            this.cards = cards;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public List<Guide> getGuides() {
            return guides;
        }

        public void setGuides(List<Guide> guides) {
            this.guides = guides;
        }

        @Override
        public String toString() {
            return "Recommend{" +
                "cards=" + cards +
                ", guides=" + guides +
                ", title='" + title + '\'' +
                '}';
        }
    }

    public static class Cards implements Serializable {
        private String text;
        private String type; //video|image|text|sound|html
        private String uuid;
        private String layout_width; //fill_parent or wrap_content

        public String getUuid() {
            return uuid;
        }

        public void setUuid(String uuid) {
            this.uuid = uuid;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getLayout_width() {
            return layout_width;
        }

        public void setLayout_width(String layout_width) {
            this.layout_width = layout_width;
        }

        public boolean isFillParent() {
            return getLayout_width() == null ? false : getLayout_width().equals("fill_parent");
        }

        @Override
        public String toString() {
            return "Cards{" +
                "text='" + text + '\'' +
                ", type='" + type + '\'' +
                ", uuid='" + uuid + '\'' +
                ", layout_width='" + layout_width + '\'' +
                '}';
        }
    }

    public static class Guide implements Serializable {
        private String uuid;
        private OutSpeech outSpeech;
        private CardImage image;
        private CardVideo video;
        private String type;

        public String getUuid() {
            return uuid;
        }

        public void setUuid(String uuid) {
            this.uuid = uuid;
        }

        public OutSpeech getOutSpeech() {
            return outSpeech;
        }

        public void setOutSpeech(OutSpeech outSpeech) {
            this.outSpeech = outSpeech;
        }

        public CardImage getImage() {
            return image;
        }

        public void setImage(CardImage image) {
            this.image = image;
        }

        public CardVideo getVideo() {
            return video;
        }

        public void setVideo(CardVideo video) {
            this.video = video;
        }


        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        @Override
        public String toString() {
            return "Guide{" +
                "uuid='" + uuid + '\'' +
                ", outSpeech=" + outSpeech +
                ", image=" + image +
                ", video=" + video +
                ", type='" + type + '\'' +
                '}';
        }
    }

    public static class OutSpeech {

        private String type;
        private String text;
        private String textBGM;
        private boolean textPlay;


        public void setType(String type) {
            this.type = type;
        }
        public String getType() {
            return type;
        }

        public void setText(String text) {
            this.text = text;
        }
        public String getText() {
            return text;
        }

        public void setTextBGM(String textBGM) {
            this.textBGM = textBGM;
        }
        public String getTextBGM() {
            return textBGM;
        }

        public void setTextPlay(boolean textPlay) {
            this.textPlay = textPlay;
        }
        public boolean getTextPlay() {
            return textPlay;
        }


        @Override
        public String toString() {
            return "OutSpeech{" +
                "type='" + type + '\'' +
                ", text='" + text + '\'' +
                ", textBGM='" + textBGM + '\'' +
                ", textPlay=" + textPlay +
                '}';
        }
    }
}