package com.ainirobot.platform.bean.net.video;

import java.net.HttpURLConnection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class RequestProperty {

    private static Map<String, String> mRequestProperty = new HashMap<>();
    static {
        mRequestProperty.put("Accept", "image/gif, image/jpeg, image/pjpeg, image/pjpeg, application/x-shockwave-flash, application/xaml+xml, application/vnd.ms-xpsdocument, application/x-ms-xbap, application/x-ms-application, application/vnd.ms-excel, application/vnd.ms-powerpoint, application/msword, */*");
        mRequestProperty.put("Accept-Language", "zh-CN");
        mRequestProperty.put("Charset", "UTF-8");
        mRequestProperty.put("User-Agent", "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.2; Trident/4.0; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.04506.30; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)");
        mRequestProperty.put("Connection", "Keep-Alive");
    }

    private static Map<String, String> getBaseRequestProperty() {
        return mRequestProperty;
    }

    public static void addBaseRequestProperty(HttpURLConnection conn) {
        Map<String, String> header =  getBaseRequestProperty();
        if(header != null && header.size() > 0){
            Set<Map.Entry<String, String>> entries = header.entrySet();
            for (Map.Entry<String, String> entry:entries){
                conn.setRequestProperty(entry.getKey(),entry.getValue());
            }
        }
    }
}
