package com.ainirobot.platform.bean.net.video.bean;


import com.ainirobot.platform.bean.net.video.action.Action;

public class Response {

    private Action mAction;
    private String path;
    private String msg;
    private int code;

    public Response(Action action,String path, int code, String msg) {
        this.mAction = action;
        this.path = path;
        this.code = code;
        this.msg = msg;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Request request(){
        return mAction.request();
    }

    public Action getAction() {
        return mAction;
    }
}
