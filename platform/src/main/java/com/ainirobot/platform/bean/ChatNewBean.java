/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.bean;

import android.util.Log;

public class ChatNewBean {

    public enum ChatNewType {
        USER_TEXT, ANSWER_TEXT_PURE, ANSWER_TEXT_IMAGE_SINGLE,
        ANSWER_TEXT_IMAGE_MULTI, ANSWER_TEXT_VIDEO, EMPTY_ROW,
        ANSWER_TEXT_IMAGE_GLOBAL_SINGLE, ANSWER_TEXT_GLOBAL_VIDEO,
        USER_TEXT_GLOBAL, ANSWER_TEXT_PURE_GLOBAL,
    }

    private String userText;
    private String answerText;
    private ChatNewType type;
    private Card.CardUI cardUI;
    private String intent;
    private int emptyHeight;
    private float mAlpha;

    public static ChatNewType getType(int type) {
        ChatNewType[] types = ChatNewType.values();
        for (ChatNewType newType : types) {
            if (type == newType.ordinal()) {
                return newType;
            }
        }
        return null;
    }

    public String getUserText() {
        return userText;
    }

    public void setUserText(String userText) {
        this.userText = userText;
    }

    public String getAnswerText() {
        return answerText;
    }

    public void setAnswerText(String answerText) {
        this.answerText = answerText;
    }

    public ChatNewType getType() {
        return type;
    }

    public void setType(ChatNewType type) {
        this.type = type;
    }

    public Card.CardUI getCardUI() {
        return cardUI;
    }

    public void setCardUI(Card.CardUI cardUI) {
        this.cardUI = cardUI;
    }

    public int getEmptyHeight() {
        return emptyHeight;
    }

    public void setEmptyHeight(int emptyHeight) {
        this.emptyHeight = emptyHeight;
    }

    public String getIntent() {
        Log.d(this.getClass().getSimpleName(), "intent: " + intent);
        return intent;
    }

    public void setIntent(String intent) {
        this.intent = intent;
    }

    public float getAlpha() {
        return mAlpha;
    }

    public void setAlpha(float mAlpha) {
        this.mAlpha = mAlpha;
    }

}
