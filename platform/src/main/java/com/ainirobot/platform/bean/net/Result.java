/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.bean.net;

public class Result<T> {

    public static final int OK = 200;

    int code;
    T data;
    String msg;

    public int getCode() {
        return code;
    }

    public T getData() {
        return data;
    }

    public String getErrorMessage() {
        return msg;
    }

    public void setCode(int code) { this.code = code; }

    public void setData(T data) {
        this.data = data;
    }

    public void setErrorMessage(String msg) {
        this.msg = msg;
    }

    public boolean isSucceed(){
        return code == OK;
    }

    public static abstract class Listener<T> {

        public abstract void onResult(T result);

        public void onFailure(int statusCode, String message){

        }
    }
}
