package com.ainirobot.platform.bean.net.video.bean;

public final class Request {

    private final String mUrl;
    private final String mTag;

    private Request(Builder builder) {
        this.mUrl = builder.url;
        this.mTag = builder.tag;
    }

    public String getUrl() {
        return mUrl;
    }

    public String getTag() {
        return mTag;
    }

    public static class Builder {

        private String url;
        private String tag;

        public Builder() {
        }

        public Builder url(String url) {
            this.url = url;
            return this;
        }

        public Builder tag(String tag) {
            this.tag = tag;
            return this;
        }

        public Request build() {
            return new Request(this);
        }

    }
}
