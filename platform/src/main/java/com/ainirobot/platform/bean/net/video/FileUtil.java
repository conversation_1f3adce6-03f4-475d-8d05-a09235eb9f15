package com.ainirobot.platform.bean.net.video;

import android.content.Context;
import android.text.TextUtils;

import com.ainirobot.platform.bean.Directory;

import java.io.File;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class FileUtil {

    private static String PATTERN_RULE = "^.*?\\.(mp4|3gp|mkv|avi)$";

    public static boolean checkSuffix(String urlName) {
        Matcher matcher = Pattern.compile(PATTERN_RULE, Pattern.CASE_INSENSITIVE).matcher(urlName);
        if (matcher.find()) {
            return true;
        }
        return false;
    }

    public static String getFileName(String url) {
        if (TextUtils.isEmpty(url)) {
            return null;
        }
        String fileName = null;
        int lastIndex = url.lastIndexOf("/");
        if (lastIndex == -1) {
            return null;
        }
        fileName = url.substring(lastIndex + 1);
        return getStrMD5(fileName).concat(fileName.substring(fileName.lastIndexOf("."),fileName.length()));
    }

    public static String getStrMD5(String string) {

        if (TextUtils.isEmpty(string)) {
            return "";
        }
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            byte[] bytes = md5.digest(string.getBytes("UTF-8"));
            StringBuilder result = new StringBuilder();
            for (byte b : bytes) {
                String temp = Integer.toHexString(b & 0xff);
                if (temp.length() == 1) {
                    temp = "0" + temp;
                }
                result.append(temp);
            }
            return result.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static File createFile(Context context, String fileName) {
        final File file = Directory.EXTERNAL_STORAGE.getCacheDir(context);
        if (!file.exists()) {
            file.mkdirs();
        }
        final File dirFile = new File(file, "video");
        if (!dirFile.exists()) {
            dirFile.mkdirs();
        }

        final File videoFile = new File(dirFile, fileName);
        if (!videoFile.exists()) {
            try {
                videoFile.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return videoFile;
    }
}
