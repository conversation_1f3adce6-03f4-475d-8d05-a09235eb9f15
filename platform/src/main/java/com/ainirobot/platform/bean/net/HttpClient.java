/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.bean.net;

import android.util.Log;

public class HttpClient {
    private static final String TAG = "HttpClient";
    private long connectTimeout = 30000;
    private long readTimeout = 30000;
    private final Dispatcher mDispatcher;

    public HttpClient() {
        this.mDispatcher = new Dispatcher();
    }

    public void setConnectTimeout(long connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(long readTimeout) {
        this.readTimeout = readTimeout;
    }

    public long getConnectTimeout() {
        return connectTimeout;
    }

    public long getReadTimeout() {
        return readTimeout;
    }

    public Dispatcher getDispatcher(){
        return mDispatcher;
    }

    public void setMaxRequests(int maxRequests){
        mDispatcher.setMaxRequests(maxRequests);
    }

    public void execute(Request request){
        execute(request,null);
    }

    public void execute(Request request, Response.Listener listener){
        NetworkRunnable runnable = new NetworkRunnable(this,request,listener);
        mDispatcher.submit(runnable);
    }

    public void cancel(Object tag){
        Log.d(TAG, "tag:" + tag);
        mDispatcher.cancel(tag);
    }
}
