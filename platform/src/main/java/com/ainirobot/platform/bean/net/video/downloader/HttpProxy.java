package com.ainirobot.platform.bean.net.video.downloader;

import com.ainirobot.platform.bean.net.video.RequestProperty;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;

public class HttpProxy {

    private String downloadUrl;
    private int timeout;
    private HttpURLConnection mConnection;

    public HttpProxy(String downloadUrl, int timeout) {
        this.downloadUrl = downloadUrl;
        this.timeout = timeout;
    }

    public HttpProxy newHttpConnection(String method) throws IOException {
        mConnection = (HttpURLConnection) new URL(downloadUrl).openConnection();
        if (timeout > 0) {
            mConnection.setConnectTimeout(timeout);
            mConnection.setReadTimeout(timeout);
        }
        mConnection.setRequestMethod(method);
        RequestProperty.addBaseRequestProperty(mConnection);
        return this;
    }

    public HttpProxy setRange(long startPos) {
        if (mConnection != null) {
            mConnection.setRequestProperty("Range", "bytes=" + startPos + "-");
        }
        return this;
    }

    public HttpURLConnection connect() throws IOException {
        if (mConnection != null) {
            mConnection.connect();
        }
        return mConnection;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }
}
