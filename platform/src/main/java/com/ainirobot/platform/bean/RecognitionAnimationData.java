/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.bean;

public class RecognitionAnimationData {

    String mQuery_Type;

    String mHeader_Text;

    public RecognitionAnimationData(String queryType, String headerText){
        mQuery_Type = queryType;
        mHeader_Text = headerText;
    }

    public String getmQuery_Type() {
        return mQuery_Type;
    }

    public void setmQuery_Type(String mQuery_Type) {
        this.mQuery_Type = mQuery_Type;
    }

    public String getmHeader_Text() {
        return mHeader_Text;
    }

    public void setmHeader_Text(String mHeader_Text) {
        this.mHeader_Text = mHeader_Text;
    }

}
