package com.ainirobot.platform.bean.net.video.bean;


import android.content.Context;

import com.ainirobot.platform.bean.net.video.db.IFileDataBase;
import com.ainirobot.platform.bean.net.video.downloader.FileCache;

public class Hunter {

    private Context mContext;
    private FileCache mFile;
    private IFileDataBase mDataBase;
    private OnHunterListener mListener;

    public Hunter(Context context, FileCache file,IFileDataBase dataBase) {
        this.mContext = context;
        this.mFile = file;
        this.mDataBase = dataBase;
    }

    public Context getContext() {
        return mContext;
    }

    public FileCache getFileCache() {
        return mFile;
    }

    public IFileDataBase getDataBase() {
        return mDataBase;
    }

    public void setListener(OnHunterListener listener) {
        mListener = listener;
    }

    public OnHunterListener getListener() {
        return mListener;
    }

    public interface OnHunterListener {
        void onResponse(String filePath, int code, String msg);
    }
}
