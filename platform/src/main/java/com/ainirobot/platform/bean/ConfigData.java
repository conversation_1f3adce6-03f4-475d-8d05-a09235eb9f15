package com.ainirobot.platform.bean;

import org.json.JSONObject;

public class ConfigData {

    private String configId;
    private String configVersion;
    private String configPath;
    private String pushTaskId;

    public ConfigData(JSONObject json) {
        configId = json.optString("configId");
        configVersion = json.optString("configVersion");
        pushTaskId = json.optString("pushTaskId");
        configPath = json.optString("configPath");
    }

    public String getConfigId() {
        return configId;
    }

    public String getConfigVersion() {
        return configVersion;
    }

    public String getPushTaskId() {
        return pushTaskId;
    }

    public String getConfigPath() {
        return configPath;
    }

    @Override
    public String toString() {
        return "ConfigData{" +
                "configId='" + configId + '\'' +
                ", configPath='" + configPath + '\'' +
                ", configVersion='" + configVersion + '\'' +
                ", pushTaskId='" + pushTaskId + '\'' +
                '}';
    }
}
