/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.bean.net;

import android.util.Log;

import java.util.ArrayDeque;
import java.util.Deque;
import java.util.Iterator;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public final class Dispatcher {
    private static final String TAG = Dispatcher.class.getSimpleName();

    private int mMaxRequests = 64;
    private final ExecutorService mExecutor;
    private final Deque<NetworkRunnable> mRunningRunnables = new ArrayDeque<>();
    private final Deque<NetworkRunnable> mWaitingRunnables = new ArrayDeque<>();

    public Dispatcher() {
        mExecutor = new ThreadPoolExecutor(0, Integer.MAX_VALUE,
                60, TimeUnit.SECONDS, new SynchronousQueue<Runnable>());
    }

    public void setMaxRequests(int maxRequests) {
        if (maxRequests < 1) {
            throw new IllegalArgumentException("maxRequests < 1 : " + maxRequests);
        }
        this.mMaxRequests = maxRequests;
        performRunnable();
    }

    public int getMaxRequests() {
        return mMaxRequests;
    }

    public synchronized void cancelAll() {
        for (NetworkRunnable runnable : mRunningRunnables) {
            runnable.cancel();
        }

        mWaitingRunnables.clear();
    }

    public synchronized void cancel(Object tag) {
        for (NetworkRunnable runnable : mRunningRunnables) {
            Log.d(TAG, "runable.getTag():" + runnable.getTag() + ",tag:" + tag);
            if (runnable.getTag() == tag) {
                runnable.cancel();
                return;
            }
        }

        Iterator<NetworkRunnable> iterator = mWaitingRunnables.iterator();
        while (iterator.hasNext()) {
            NetworkRunnable runnable = iterator.next();
            if (runnable.getTag() == tag) {
                iterator.remove();
            }
        }
    }

    private void performRunnable() {
        if (mRunningRunnables.size() >= mMaxRequests
                || mWaitingRunnables.isEmpty()) {
            return;
        }

        Iterator<NetworkRunnable> iterator = mWaitingRunnables.iterator();
        while (iterator.hasNext()) {
            NetworkRunnable runnable = iterator.next();
            mRunningRunnables.add(runnable);
            mExecutor.execute(runnable);
            iterator.remove();

            if (mRunningRunnables.size() > mMaxRequests) {
                return;
            }
        }
    }

    public synchronized void submit(NetworkRunnable runnable) {
        if (mRunningRunnables.size() < mMaxRequests) {
            mRunningRunnables.add(runnable);
            mExecutor.execute(runnable);
        } else {
            mWaitingRunnables.add(runnable);
        }
    }

    public synchronized boolean finished(NetworkRunnable runnable) {
        if (mRunningRunnables.remove(runnable)) {
            performRunnable();
            return true;
        }
        return false;
    }

}
