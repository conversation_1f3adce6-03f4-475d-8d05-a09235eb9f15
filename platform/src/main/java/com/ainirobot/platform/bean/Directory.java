
/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.platform.bean;

import android.content.Context;
import android.os.Environment;
import androidx.annotation.NonNull;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Locale;

public enum Directory {

    /**
     * Directory returned by {@link Context#getFilesDir()}
     */
    FILES {
        @Override
        protected File getRoot(Context ctx) {
            return ctx.getFilesDir();
        }
    },

    /**
     * Directory returned by {@link Context#getExternalFilesDir(String)}
     */
    EXTERNAL_FILES {
        @Override
        protected File getRoot(Context ctx) {
            return ctx.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS);
        }
    },

    /**
     * Directory returned by {@link Context#getCacheDir()}
     */
    CACHE {
        @Override
        protected File getRoot(Context ctx) {
            return ctx.getCacheDir();
        }
    },

    /**
     * Directory returned by {@link Context#getExternalCacheDir()}
     */
    EXTERNAL_CACHE {
        @Override
        protected File getRoot(Context ctx) {
            return ctx.getExternalCacheDir();
        }
    },


    /**
     * Directory returned by {@link Environment#getExternalStorageDirectory()}
     */
    EXTERNAL_STORAGE {
        @Override
        protected File getRoot(Context ctx) {
            return Environment.getExternalStorageDirectory();
        }
    };

    private final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd", Locale.CHINA);

    protected final String CACHE_DIR = "robot/cache";
    public static final String DIR_DEFAULT = "820";
    public static final String DIR_AIUI = "aiui";
    public static final String DIR_NAVIGATION = "navigation";
    public static final String DIR_TX1 = "tx1";
    public static final String DIR_PICTURE = "picture";
    public static final String DIR_SH_PICTURE = "smartshootpicture";

    protected abstract File getRoot(Context ctx);

    public File getCacheDir(Context ctx) {
        File root = getRoot(ctx);
        File dir = new File(root, CACHE_DIR);
        if (dir.exists()) {
            dir.mkdirs();
        }
        return dir;
    }

    public File getFile(Context ctx) {
        return getFile(ctx, null);
    }

    public File getFile(Context ctx, String fileName) {
        return getFile(ctx, DIR_DEFAULT, fileName);
    }

    public File getFile(Context ctx, @NonNull String dirName, String fileName) {
        File cacheDir = getCacheDir(ctx);
        File dir = new File(cacheDir, dirName);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        if (fileName == null) {
            fileName = getCurrentDate() + ".jpg";
        }
        return new File(dir, fileName);
    }

    public File getFileByDefaultName(Context ctx, @NonNull String dirName, String fileName) {
        File cacheDir = getCacheDir(ctx);
        File dir = new File(cacheDir, dirName);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        if (fileName == null) {
            fileName = System.currentTimeMillis() + ".jpg";
        }
        return new File(dir, fileName);
    }

    private String getCurrentDate() {
        return DATE_FORMAT.format(System.currentTimeMillis());
    }


}
