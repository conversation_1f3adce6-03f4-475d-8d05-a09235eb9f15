package com.ainirobot.platform.bean.net.video.downloader;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.platform.bean.net.video.db.DownloadState;
import com.ainirobot.platform.bean.net.video.db.IFileDataBase;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.util.Map;

public class DownloadThread extends Thread {

    private static final String TAG = "DownloadThread";
    private static final int BUFFER_SIZE = 4 * 1024;
    public static final String ACCEPT_RANGES = "Accept-Ranges";
    private HttpProxy proxy;
    private volatile boolean isCancel = false;
    private OnDownloadThreadListener listener;
    private HttpURLConnection mConn;
    private FileCache fileCache;
    private IFileDataBase mDataBase;

    public void setOnDownloadThreadListener(OnDownloadThreadListener listener) {
        this.listener = listener;
    }

    public DownloadThread(HttpProxy proxy, FileCache fileCache, IFileDataBase dataBase) {
        super(String.format("DownloadThread"));
        this.proxy = proxy;
        this.fileCache = fileCache;
        this.mDataBase = dataBase;
    }

    public void cancel() {
        isCancel = true;
    }

    @Override
    public void run() {
        try {
            long startPos = getStartPos();
            long downLength = startPos;

            proxy = proxy.newHttpConnection("GET");
            if (startPos != 0) {
                proxy.setRange(startPos);
            }
            mConn = proxy.connect();

            long len = mConn.getContentLength();

            if (startPos == 0) {
                mDataBase.updateDownloadTotalAndBreakContinue(proxy.getDownloadUrl(), len, canBreakContinueDownload() ? 1 : 0);
            }

            int responseCode = mConn.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_PARTIAL) {

                InputStream inStream = mConn.getInputStream();

                print("Thread start download from position " + startPos);
                fileCache.seek(startPos);
                int offset = 0;
                byte[] buffer = new byte[BUFFER_SIZE];
                while ((offset = inStream.read(buffer, 0, BUFFER_SIZE)) != -1) {
                    fileCache.write(buffer, offset);
                    downLength += offset;
                    if (listener != null) {
                        listener.update(downLength);
                    }
                    if (isCancel) {
                        print("Thread download pause " + downLength);
                        if (listener != null) {
                            listener.changeState(DownloadState.CANCEL);
                        }
                        break;
                    }
                }

                print("proxy current len:" + len + ", file:" + downLength);
                if (mDataBase.queryTotalLengthByUrl(proxy.getDownloadUrl()) == downLength) {
                    if (listener != null) {
                        listener.changeState(DownloadState.FINISH);
                    }
                }
                fileCache.close();
                inStream.close();
            } else {
                if (listener != null) {
                    listener.changeState(DownloadState.FAILURE);
                }
            }
        } catch (IOException e) {
            print("Thread:" + e);
            if (listener != null) {
                listener.changeState(DownloadState.FAILURE);
            }
        } finally {
            if (mConn != null) {
                mConn.disconnect();
            }
        }
    }

    private long getStartPos() {
        long startPos = fileCache.length();
        if (startPos != 0) {
            if (mDataBase.queryBreakContinueByUrl(proxy.getDownloadUrl()) == 0) {
                startPos = 0;
            }
        }
        return startPos;
    }

    private boolean canBreakContinueDownload() {
        if (mConn != null) {
            Map headers = mConn.getHeaderFields();
            if (headers.containsKey(ACCEPT_RANGES)) {
                String val = headers.get(ACCEPT_RANGES).toString();
                return TextUtils.equals("[bytes]", val);
            }
        }
        return false;
    }

    public interface OnDownloadThreadListener {
        void update(long length);

        void changeState(DownloadState downloadState);
    }

    private static void print(String msg) {
        Log.i(TAG, msg);
    }
}
