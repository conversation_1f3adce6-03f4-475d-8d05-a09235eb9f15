package com.ainirobot.platform.bean;

import java.util.ArrayList;

public class OpenAppBean {
    private String pkg_name;
    private String class_name;
    private ArrayList<KeyVal> params;

    public OpenAppBean(String pkg_name, String class_name, ArrayList<KeyVal> params) {
        this.pkg_name = pkg_name;
        this.class_name = class_name;
        this.params = params;
    }

    public String getPkg_name() {
        return pkg_name;
    }

    public void setPkg_name(String pkg_name) {
        this.pkg_name = pkg_name;
    }

    public String getClass_name() {
        return class_name;
    }

    public void setClass_name(String class_name) {
        this.class_name = class_name;
    }

    public ArrayList<KeyVal> getParams() {
        return params;
    }

    public void setParams(ArrayList<KeyVal> params) {
        this.params = params;
    }

    public static class KeyVal {
        private String key;
        private String value;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

}
