/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.bean;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class IntentInfo {

    /**
     * 切换角色指令
     */
    public static final int TYPE_PLATFORM = 1;

    public static final int TYPE_SWITCH_CHARACTER = 2;

    @IntDef({TYPE_SWITCH_CHARACTER, TYPE_PLATFORM})
    @Retention(RetentionPolicy.SOURCE)
    public @interface Type {
    }

    /**
     * 指令标识
     */
    private final String intent;

    /**
     * 处理该指令的角色
     */
    private String character;

    @Type
    private final int type;

    public IntentInfo(String intent, @Type int type) {
        this.intent = intent;
        this.type = type;
    }

    public void setCharacter(String character) {
        this.character = character;
    }

    public String getIntent() {
        return intent;
    }

    public String getCharacter() {
        return character;
    }

    @Type
    public int getType() {
        return type;
    }
}
