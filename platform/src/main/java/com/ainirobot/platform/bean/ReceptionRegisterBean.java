/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.bean;

import java.io.Serializable;

public class ReceptionRegisterBean implements Serializable {
    private String status;
    private String visitor_id;
    private int group_size;         //团队邀约总人数
    private int exist_count;        //签到中已存在总人数
    private int check_in_count;     //签到总人数
    private int new_count;          //签到中新注册人数
    private boolean resubmit;       //是否重复签到
    private boolean is_staff;       //是否是员工

    public ReceptionRegisterBean() {
    }

    public String getStatus() {
        return status;
    }

    public String getVisitor_id() {
        return visitor_id;
    }

    public int getGroup_size(){
        return group_size;
    }

    public int getExist_count(){
        return exist_count;
    }

    public int getCheck_in_count(){
        return check_in_count;
    }

    public int getNew_count(){
        return new_count;
    }

    public boolean getResubmit(){
        return this.resubmit;
    }

    public boolean getIsStaff(){
        return this.is_staff;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setVisitor_id(String visitor_id) {
        this.visitor_id = visitor_id;
    }

    public void setGroup_size(int group_size){
        this.group_size = group_size;
    }

    public void setExist_count(int exist_count){
        this.exist_count = exist_count;
    }

    public void setCheck_in_count(int check_in_count){
        this.check_in_count = check_in_count;
    }

    public void setNew_count(int new_count){
        this.new_count = new_count;
    }

    public void setResubmit(boolean resubmit){
        this.resubmit = resubmit;
    }

    public void setIs_staff(boolean isStaff){
        this.is_staff = isStaff;
    }

    @Override
    public String toString() {
        return "status:" + status + " visitor_id:" + visitor_id
                + " group_size:" + group_size + " exist_count:" + exist_count
                + " check_in_count:" + check_in_count + " new_count:"
                +  new_count + " resubmit:"+ resubmit + " isStaff:"+is_staff;
    }

}
