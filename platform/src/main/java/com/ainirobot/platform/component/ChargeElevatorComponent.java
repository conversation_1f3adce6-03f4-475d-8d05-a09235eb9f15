package com.ainirobot.platform.component;

import static com.ainirobot.platform.component.definition.ComponentResult.RESULT_NAVIGATION_ARRIVED;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.ainirobot.platform.data.RobotInfo;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Data: 2022/6/23 2:20 下午
 * <p>
 * 多楼层跨层回充
 * <p>
 * 若需要跨楼层，先调用NavigationElevatorComponent乘梯组件到"回充点",再调ChargeComponent执行回充任务；
 * 若不需要跨楼层，否则直接调ChargeComponent执行回充任务。
 */
public class ChargeElevatorComponent extends Component {

    private static final long DEFAULT_CHARGE_TIMEOUT = 3 * Definition.MINUTE;
    private static final double DEFAULT_AVOID_DISTANCE = 0.1;
    private static final long DEFAULT_AVOID_TIMEOUT = 20 * Definition.SECOND;
    private static final long DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT = 300 * 1000;

    private MultiFloorInfo mCurrentFloorInfo;   //当前楼层信息
    private MultiFloorInfo mChargeFloorInfo;      //充电楼层信息
    private String mMultiDestination;   //多层导航时，需要先导航的目的地
    private int mTargetFloor;   //充电楼层的floorIndex

    private ChargeMultiState mStatus = ChargeMultiState.IDLE;
    private NavigationElevatorComponent mNavigationElevatorComponent;
    private ChargeStartComponent mChargeStartComponent;
    private long mChargeTimeout;
    private double mAvoidDistance;
    private long mAvoidTimeout;
    private long mMultiWaitTimeout;

    public enum ChargeMultiState {
        IDLE,
        PREPARE,
        ELEVATOR,   //执行乘梯组件
        CHARGE,     //执行回充组件
        FINISH
    }

    public ChargeElevatorComponent(String name) {
        this(name, Looper.myLooper());
    }

    public ChargeElevatorComponent(String name, Looper looper) {
        super(name, looper);
        mNavigationElevatorComponent = new NavigationElevatorComponent(mName, mLooper);
    }

    @Override
    protected void onStart(String params) {
        Log.i(mName, "onStart: " + params);
        try {
            JSONObject json = new JSONObject(params);
            mChargeTimeout = json.optLong(ComponentParams.ChargeStart.PARAM_CHARGE_TIMEOUT,
                    DEFAULT_CHARGE_TIMEOUT);
            mAvoidDistance = json.optDouble(ComponentParams.ChargeStart.PARAM_AVOID_DISTANCE,
                    DEFAULT_AVOID_DISTANCE);
            mAvoidTimeout = json.optLong(ComponentParams.ChargeStart.PARAM_AVOID_TIMEOUT,
                    DEFAULT_AVOID_TIMEOUT);
            mMultiWaitTimeout = json.optLong(ComponentParams.ChargeStart.PARAM_MULTI_WAIT_TIMEOUT,
                    DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT);
        } catch (JSONException e) {
            mChargeTimeout = DEFAULT_CHARGE_TIMEOUT;
            mAvoidDistance = DEFAULT_AVOID_DISTANCE;
            mAvoidTimeout = DEFAULT_AVOID_TIMEOUT;
            mMultiWaitTimeout = DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT;
        }
        startComponent();
    }

    private void startComponent() {
        //没有开启乘梯功能，尝试直接回充
        if (!isEnableMultiNavigation()) {
            startChargeComponent();
            return;
        }
        //配置信息是否有问题
        if (!getMultiFloorInfo()) {
            return;
        }

        getMultiFloorDestination(); //更新目的地楼层参数
        startNavigationElevatorComponent(); //乘梯导航
    }

    private boolean getMultiFloorInfo() {
        mChargeFloorInfo = RobotInfo.getChargeFloorMultiInfo();
        mCurrentFloorInfo = RobotInfo.getCurrentMultiFloorInfo();
        if (null == mChargeFloorInfo || TextUtils.isEmpty(mChargeFloorInfo.getMapName())) {
            onFinish(ComponentResult.RESULT_ELEVATOR_CONFIG_INVALID,
                    "charge floor multi config invalid");
            return false;
        }
        if (null == mCurrentFloorInfo || TextUtils.isEmpty(mChargeFloorInfo.getMapName())) {
            onFinish(ComponentResult.RESULT_ELEVATOR_CONFIG_INVALID,
                    "current floor multi config invalid");
            return false;
        }
        return true;
    }

    /**
     * 获得跨层导航时，需要的目的地
     */
    private void getMultiFloorDestination() {
        mTargetFloor = mChargeFloorInfo.getFloorIndex();
        //先跨层导航到充电楼层的"回充点"，再用chargeComponent回充
        mMultiDestination = Definition.START_BACK_CHARGE_POSE;
    }

    /**
     * 是否开启了电梯功能
     */
    private boolean isEnableMultiNavigation() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED)
                == Definition.ROBOT_SETTING_ENABLE;
    }

    /**
     * 当前是否为充电楼层
     */
    private boolean currentIsChargeFloor() {
        return TextUtils.equals(mCurrentFloorInfo.getMapName(), mChargeFloorInfo.getMapName());
    }

    private void startNavigationElevatorComponent() {
        //乘梯导航组件
        mNavigationElevatorComponent.setComponentListener(mNavigationFinishListener);
        mNavigationElevatorComponent.setStatusListener(mNavigationStatusListener);
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(ComponentParams.Navigation.PARAM_DESTINATION, mMultiDestination);
            jsonObject.put(ComponentParams.Navigation.PARAM_TARGET_FLOOR, mTargetFloor);

            mStatus = ChargeMultiState.ELEVATOR;
            updateStatus(ComponentStatus.STATUS_PREPARE_TAKE_ELEVATOR, jsonObject.toString());
            mNavigationElevatorComponent.start(jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
            stop(STOP_TIMEOUT, ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR,
                    "json parse exception");
        }
    }

    private Component.ComponentListener mNavigationFinishListener =
            new ComponentListener() {
                @Override
                public void onFinish(int result, String message, String extraData) {
                    if (mStatus == ChargeMultiState.ELEVATOR) {
                        handleFinishResult(result, message);
                    }
                }
            };

    private void handleFinishResult(int result, String message) {
        if (result == RESULT_NAVIGATION_ARRIVED && isAlive()) {
            //arrived target floor
            startChargeComponent();
            return;
        }
        stop(STOP_TIMEOUT, result, message);
    }

    private Component.ComponentStatusListener mNavigationStatusListener =
            new ComponentStatusListener() {
                @Override
                public void onStatusUpdate(int status, String data, String extraData) {
                    updateStatus(status, data);
                }
            };


    private void startChargeComponent() {
        //回充组件
        mChargeStartComponent = new ChargeStartComponent(mName);
        mChargeStartComponent.setComponentListener(mChargeFinishListener);
        mChargeStartComponent.setStatusListener(mChargeStatusListener);
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(ComponentParams.ChargeStart.PARAM_AVOID_TIMEOUT, mAvoidTimeout);
            jsonObject.put(ComponentParams.ChargeStart.PARAM_CHARGE_TIMEOUT, mChargeTimeout);
            jsonObject.put(ComponentParams.ChargeStart.PARAM_AVOID_DISTANCE, mAvoidDistance);
            jsonObject.put(ComponentParams.ChargeStart.PARAM_MULTI_WAIT_TIMEOUT, mMultiWaitTimeout);

            mStatus = ChargeMultiState.CHARGE;
            updateStatus(ComponentStatus.STATUS_START_GO_CHARGE, "go charge");
            mChargeStartComponent.start(jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }


    private final Component.ComponentListener mChargeFinishListener =
            new ComponentListener() {
                @Override
                public void onFinish(int result, String message, String extraData) {
                    //非计划回充有可能被系统接管，
                    //当action停止时需要判断 是乘梯项目、开启乘梯功能、当前为充电状态
                    if (result == Definition.ACTION_RESPONSE_STOP_SUCCESS
                            && ProductInfo.isElevatorCtrlProduct()
                            && isEnableMultiNavigation()
                            && mApi.getRobotChargingStatus()) {
                        //都符合，认为回充成功
                        stop(STOP_TIMEOUT, ComponentResult.RESULT_SUCCESS, "");
                        return;
                    }
                    stop(STOP_TIMEOUT, result, message);
                }
            };


    private final Component.ComponentStatusListener mChargeStatusListener =
            new ComponentStatusListener() {
                @Override
                public void onStatusUpdate(int status, String data, String extraData) {
                    updateStatus(status, data);
                }
            };


    @Override
    protected void onUpdate(String intent, String params) {

    }

    @Override
    protected void onStop(int reason) {
        mStatus = ChargeMultiState.IDLE;
        stopNavigationElevatorComponent();
        stopChargeSComponent();
    }

    private void stopNavigationElevatorComponent() {
        if (null != mNavigationElevatorComponent) {
            mNavigationElevatorComponent.setComponentListener(null);
            mNavigationElevatorComponent.setStatusListener(null);
            mNavigationElevatorComponent.stop(500);
        }
    }

    private void stopChargeSComponent() {
        if (null != mChargeStartComponent) {
            mChargeStartComponent.setComponentListener(null);
            mChargeStartComponent.setStatusListener(null);
            mChargeStartComponent.stop(500);
        }
    }

    private void onFinish(int result, String message) {
        mStatus = ChargeMultiState.FINISH;
        stop(STOP_TIMEOUT, result, message, "");
    }
}
