/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.platform.bi.wrapper.ReportControl;
import com.ainirobot.platform.bi.wrapper.manager.Report;
import com.ainirobot.platform.bi.wrapper.manager.at.AtReport;
import com.ainirobot.platform.bi.wrapper.manager.bi.impl.FaceResultPoint;
import com.ainirobot.platform.bi.wrapper.utils.ShareDataUtils;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.ainirobot.platform.utils.VisionUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

/**
 * 获取视觉图片并远程识别，业务层并没有直接使用该组件。
 * 业务层使用{@link PersonAppearComponent}做人脸识别。
 */
public class RecognizeComponent extends Component {

    private static final int MAX_PICTURE_NUM = 1;
    private static final long DEFAULT_RECOGNIZE_TIMEOUT = 2000;

    private long mRecognizeTimeout;
    private Timer mNetSearchTimer;
    private boolean mIsNeedDeletePic;
    private Person mCurrentPerson;
    private List<String> mPictureList;

    public RecognizeComponent(String name) {
        this(name, Looper.myLooper());
    }

    public RecognizeComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    public void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            int personId = json.optInt(ComponentParams.Recognize.PARAM_PERSON_ID, -1);
            mRecognizeTimeout = json.optLong(ComponentParams.Recognize.PARAM_TIMEOUT,
                    DEFAULT_RECOGNIZE_TIMEOUT);
            mIsNeedDeletePic = json.optBoolean(ComponentParams.Recognize.PARAM_IS_NEED_DELETE_PIC,
                    true);
            if (personId >= 0) {
                mCurrentPerson = new Person();
                mCurrentPerson.setId(personId);
                startNetSearchTimer();
                getPictureById();
            } else {
                processRecognizeResult(ComponentError.ERROR_PARAMS_PERSON_ID_INVALID);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.d(mName, "onUpdate params: " + params);
    }

    private void getPictureById() {
        Log.d(mName, "getPictureById");
        ReportControl.getInstance().reportMsg(new AtReport(AtReport.AtType.success,
                "开始拿照片"));
        mApi.getPictureById(mCurrentPerson.getId(),
                MAX_PICTURE_NUM, new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.v(mName, "getPictureById result: " + result +
                                ", message: " + message);
                        mPictureList = new ArrayList<>();
                        try {
                            JSONObject json = new JSONObject(message == null ? "" :
                                    message);
                            String status = json.optString("status");
                            int personId = json.optInt("id", -1);
                            Log.d(mName, "getPictureById personId: " + personId +
                                    ", currentId: " + mCurrentPerson.getId() +
                                    ", status: " + status);
                            if (!TextUtils.isEmpty(status) && "failed".equals(status)) {
                                ReportControl.getInstance().reportMsg(new AtReport(AtReport.AtType.success,
                                        "拿照片失败"));
                                cancelNetSearchTimer();
                                processRecognizeResult(ComponentError.ERROR_GET_PICTURE_FAILED);
                                return;
                            }
                            JSONArray pictures = json.optJSONArray("pictures");
                            if (TextUtils.isEmpty(pictures.optString(0))) {
                                cancelNetSearchTimer();
                                processRecognizeResult(ComponentError.ERROR_GET_PICTURE_FAILED);
                                return;
                            }
                            ReportControl.getInstance().reportMsg(new AtReport(AtReport.AtType.success,
                                    "拿照片成功"));
                            String picture = pictures.optString(0);
                            if (!mIsNeedDeletePic) {
                                File file = new File(picture);
                                if (file.exists()) {
                                    String name = "keep_" + file.getName();
                                    picture = file.getParent() + File.separator + name;
                                    file.renameTo(new File(picture));
                                }
                            }
                            mPictureList.add(picture);
                            processRecognizeStatus(ComponentStatus.STATUS_PICTURE_PATH, picture);
                            Log.d(mName, "getPictureById picture: " + picture);
                        } catch (JSONException e) {
                            e.printStackTrace();
                            cancelNetSearchTimer();
                            processRecognizeResult(ComponentError.ERROR_GET_PICTURE_FAILED);
                        }

                        if (mPictureList.size() < MAX_PICTURE_NUM) {
                            Log.e(mName, "getPictureById pictureList size < " +
                                    MAX_PICTURE_NUM);
                            return;
                        }
                        remoteDetect();
                    }
                });
    }

    private void remoteDetect() {
        Log.d(mName, "remoteDetect");
        ReportControl.getInstance().reportMsg(new AtReport(AtReport.AtType.success,
                "开始网络识别照片"));
        mApi.getPersonInfoFromNet(String.valueOf(mCurrentPerson.getId()),
                mPictureList, new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(mName, "remoteDetect result: " + result +
                                ", message: " + message);
                        if (mCurrentPerson == null) {
                            Log.e(mName, "remoteDetect maybe has finished");
                            return;
                        }
                        ReportControl.getInstance().reportMsg(new AtReport(AtReport.AtType.success,
                                "网络识别照片返回"));
                        cancelNetSearchTimer();
                        if (VisionUtils.isRemoteDetectSuccess(message)) {
                            mCurrentPerson = VisionUtils.updatePersonData(mCurrentPerson,
                                    message, true);
                            processRecognizeResult(ComponentResult.RESULT_SUCCESS);
                        } else {
                            processRecognizeResult(ComponentError.ERROR_REMOTE_DETECT_FAILED);
                        }
                    }
                });
    }

    private void processRecognizeStatus(int status, String data) {
        Log.d(mName, "processRecognizeStatus status: " + status + ", data: " + data);
        updateStatus(status, data);
    }


    private void processRecognizeResult(int reason) {
        Log.d(mName, "processRecognizeResult reason: " + reason);
        if (mIsNeedDeletePic) {
            VisionUtils.deletePictures(mPictureList);
        }
        reportFaceDetect(reason);
        stop(500, reason, mCurrentPerson == null ? null :
                mCurrentPerson.toGson());
    }

    private void reportFaceDetect(int result) {
        if (mCurrentPerson == null || result != ComponentResult.RESULT_SUCCESS) {
            return;
        }
        String personId = ShareDataUtils.getPersonId(mCurrentPerson);
        String personName = ShareDataUtils.getPersonName(mCurrentPerson);
        String age = ShareDataUtils.getPersonAge(mCurrentPerson);
        String gender = ShareDataUtils.getPersonGender(mCurrentPerson);
        String role = ShareDataUtils.getPersonRole(mCurrentPerson);
        String remoteFaceId = ShareDataUtils.getPersonRemoteFaceId(mCurrentPerson);
        String remoteReqId = ShareDataUtils.getPersonRemoteReqId(mCurrentPerson);
        String registerTime = ShareDataUtils.getPersonRegisterTime(mCurrentPerson);
        int num = ShareDataUtils.getPersonNum();
        ReportControl.getInstance().reportMsg(new FaceResultPoint(num, personId, personName,
                gender, age, role, remoteFaceId, registerTime, remoteReqId));
    }

    @Override
    public void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        cancelNetSearchTimer();
    }

    private synchronized void startNetSearchTimer() {
        cancelNetSearchTimer();
        mNetSearchTimer = new Timer();
        mNetSearchTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.i(mName, "startNetSearchTimer netSearchTimer timeout");
                cancelNetSearchTimer();
                processRecognizeResult(ComponentError.ERROR_RECOGNIZE_TIMEOUT);
            }
        }, mRecognizeTimeout);
    }

    private synchronized void cancelNetSearchTimer() {
        if (mNetSearchTimer != null) {
            mNetSearchTimer.cancel();
            mNetSearchTimer = null;
        }
    }
}
