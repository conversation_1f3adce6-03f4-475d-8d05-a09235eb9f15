/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;

import org.json.JSONException;
import org.json.JSONObject;

public class RecoverPositiveComponent extends Component {

    private static final double DEFAULT_COORDINATE_DEVIATION = 0.5;

    private String mSpecialPlace;
    private double mCoordinateDeviation;

    public RecoverPositiveComponent(String name) {
        this(name, Looper.myLooper());
    }

    public RecoverPositiveComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    public void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            String specialPlace = json.optString(ComponentParams.RecoverPositive.PARAM_PLACE_NAME, "");
            if (TextUtils.isEmpty(specialPlace)) {
                processRecoverResult(ComponentError.ERROR_PARAMS_PLACE_NAME_INVALID, null);
                return;
            }
            mSpecialPlace = specialPlace;
            mCoordinateDeviation = json.optDouble(ComponentParams.RecoverPositive.PARAM_COORDINATE_DEVIATION,
                    DEFAULT_COORDINATE_DEVIATION);
        } catch (JSONException e) {
            e.printStackTrace();
            return;
        }
        isEstimate();
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.d(mName, "onUpdate params: " + params);
    }

    private void isEstimate() {
        mApi.isRobotEstimate(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(mName, "isEstimate result:" + result + " message:" + message);

                if (result == Definition.RESULT_OK && "true".equals(message)) {
                    isPlaceExist();
                } else {
                    processRecoverResult(ComponentError.ERROR_NOT_ESTIMATE, null);
                }
            }
        });
    }

    private void isPlaceExist() {
        mApi.getPlace(mSpecialPlace, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(mName, "isPlaceExist result:" + result + " message:" + message);
                if (result < 0 || TextUtils.isEmpty(message)) {
                    processRecoverResult(ComponentError.ERROR_PLACE_NOT_EXIST, null);
                    return;
                }
                checkRobotInSpecialLocation();
            }
        });
    }

    private void checkRobotInSpecialLocation() {
        mApi.isRobotInLocation(mSpecialPlace, mCoordinateDeviation, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(mName, "checkRobotInSpecialLocation onResult result: " + result +
                        ", message: " + message);
                boolean isInLocation;
                try {
                    JSONObject json = new JSONObject(message);
                    isInLocation = json.optBoolean(Definition
                            .JSON_NAVI_IS_IN_LOCATION, false);
                } catch (JSONException e) {
                    isInLocation = false;
                }

                if (isInLocation) {
                    resumeSpecialPlaceTheta();
                } else {
                    processRecoverResult(ComponentError.ERROR_TOO_FAR_WITH_PLACE, null);
                }
            }
        });
    }

    private void resumeSpecialPlaceTheta() {
        mApi.resumeSpecialPlaceTheta(mSpecialPlace, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(mName, "resumeSpecialPlaceTheta result: " + result + ", message: " + message);
                if (result == Definition.RESULT_OK && "true".equals(message)) {
                    processRecoverResult(ComponentResult.RESULT_SUCCESS, null);
                } else {
                    processRecoverResult(ComponentError.ERROR_RECOVER_POSITIVE_FAILED, message);
                }
            }
        });
    }

    private void processRecoverResult(int reason, String message) {
        stop(100, reason, message);
    }

    @Override
    public void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        mApi.stopResumeSpecialPlaceTheta();
    }
}
