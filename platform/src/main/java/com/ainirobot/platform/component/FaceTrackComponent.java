/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.component;

import android.os.Looper;
import android.util.Log;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentParams.FaceTrack;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import org.json.JSONException;
import org.json.JSONObject;

public class FaceTrackComponent extends Component {

    private static final float DEFAULT_LOST_DISTANCE = 2;
    private static final long DEFAULT_LOST_TIMEOUT = 2000;
    private static final long STOP_TIMEOUT = 500;

    private int mPersonId;
    private boolean mIsNeedMoveBody;
    private double mLostDistance;
    private long mLostTimeout;

    public FaceTrackComponent(String name) {
        this(name, Looper.myLooper());
    }

    public FaceTrackComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    public void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            int personId = json.optInt(ComponentParams.FaceTrack.PARAM_PERSON_ID, -1);
            if (personId < 0) {
                processTrackResult(ComponentError.ERROR_PARAMS_PERSON_COULD_NOT_BE_TRACKED);
                return;
            }
            mPersonId = personId;
            mIsNeedMoveBody = json.optBoolean(FaceTrack.PARAM_IS_NEED_MOVE_BODY, true);
            mLostDistance = json.optDouble(FaceTrack.PARAM_LOST_DISTANCE,
                    DEFAULT_LOST_DISTANCE);
            mLostTimeout = json.optLong(FaceTrack.PARAM_LOST_TIMEOUT,
                    DEFAULT_LOST_TIMEOUT);
            obstacleDetect();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.d(mName, "onUpdate params: " + params);
    }

    private void obstacleDetect() {
        mApi.hasObstacleInArea(-30, 30,
                0.1, 1, new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        if (!isAlive()) {
                            return;
                        }
                        Log.i(mName, "FaceTrack hasObstacleInArea result: " + result + " message= " + message);
                        if ("true".equals(message)) {
                            startTrackPerson();
                        } else {
                            processTrackResult(ComponentError.ERROR_HAS_FACE_NO_OBSTACLE);
                        }
                    }
                });
    }
    private void startTrackPerson() {
        mApi.startFocusFollow(mPersonId,
                mLostTimeout, (float) mLostDistance, new ActionListener() {

                    @Override
                    public void onStatusUpdate(int status, String data) {
                        Log.d(mName, "startTrackPerson onStatusUpdate " +
                                "status: " + status + ", data: " + data);
                        switch (status) {
                            case Definition.STATUS_TRACK_TARGET_SUCCEED:
                                processStatus(ComponentStatus.STATUS_TRACK_SUCCESS, null);
                                break;
                            case Definition.STATUS_GUEST_LOST:
                                processTrackResult(ComponentResult.RESULT_GUEST_LOST_TIMEOUT);
                                break;
                            case Definition.STATUS_GUEST_FARAWAY:
                                processTrackResult(ComponentResult.RESULT_GUEST_FAR_AWAY);
                                break;
                            default:
                                processStatus(status, data);
                                break;
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorString) {
                        Log.d(mName, "startTrackPerson onError" +
                                "errorCode: " + errorCode +
                                ", errorString: " + errorString);
                        switch (errorCode) {
                            case Definition.ACTION_RESPONSE_ALREADY_RUN:
                                processTrackResult(ComponentError.ERROR_REQUEST_RES_BUSY);
                                break;
                            case Definition.ACTION_RESPONSE_REQUEST_RES_ERROR:
                                processTrackResult(ComponentError.ERROR_REQUEST_RES_FAILED);
                                break;
                            default:
                                break;
                        }
                    }

                    @Override
                    public void onResult(int status, String responseString) {
                        Log.d(mName, "startTrackPerson onResult status: " + status +
                                ", responseString: " + responseString);
                        switch (status) {
                            case Definition.ACTION_RESPONSE_STOP_SUCCESS:
                                break;
                            default:
                                break;
                        }
                    }
                });
    }

    private void processStatus(int status, String result) {
        Log.d(mName, "processStatus status: " + status + ", result: " + result);
        updateStatus(status, result);
    }

    private void processTrackResult(int result) {
        Log.d(mName, "processTrackResult result: " + result);
        stop(STOP_TIMEOUT, result, null);
    }

    @Override
    public void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        mApi.stopFocusFollow();
    }
}
