/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.platform.component;

import android.os.Looper;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class NavigationCheckComponent extends Component {

    private final String DELAY_TAG = "naviCheckTimeout";
    private final String START_TAG = "delayStartTimer";
    private static final int DEFAULT_ESTIMATE_RETRY_COUNT = 5;
    /**
     * 多机情况下机器人目标点0.5m范围内进行聚合
     */
    private static final double DEFAULT_DESTINATION_AVAILABLE_RANGE = 0.5;
    private static final double DEFAULT_MIN_DESTINATION_RANGE = 0.01;
    private volatile CheckState mState = CheckState.IDLE;
    private static final long STOP_TIMEOUT = 500;
    private long mTimeOut = -1L;

    private static final int BASE_NAVIGATION_ERROR = -20010000;
    /**
     * Lora配置异常，此状态在NavigationService内定义
     */
    private static final int NAVI_ERROR_MULTIPLE_LORA_CONFIG_FAIL = BASE_NAVIGATION_ERROR - 1;
    /**
     * 多机地图不匹配，此状态在NavigationService内定义
     */
    private static final int NAVI_ERROR_MULTIPLE_MAP_NOT_MATCH = BASE_NAVIGATION_ERROR - 2;
    /**
     * 多机Lora断连，此状态在NavigationService内定义
     */
    private static final int NAVI_ERROR_MULTIPLE_LORA_DISCONNECT = BASE_NAVIGATION_ERROR - 3;

    private static final int NAVI_ERROR_MULTIPLE_VERSION_NOT_MATCH = BASE_NAVIGATION_ERROR - 4;

    private static final int NAVI_CHANGE_GOAL_TIME_OFFSET = 2000;

    private Gson mGson;
    /**
     * 目的地
     */
    private String mDestination;
    /**
     * 备用目的地列表
     */
    private List<String> mStandbyDesList;
    /**
     * 补位需要透传的参数信息
     */
    private JSONObject mNavigationParams;
    /**
     * 机器人目标点半径
     */
    private double mDesRange = DEFAULT_DESTINATION_AVAILABLE_RANGE;
    /**
     * 动态调度策略:0 Lora优先级星形补位, 1 点位优先级星形补位, 2 点位优先级火车补位
     */
    private int mQueuingStrategy = 0;
    /**
     * 仅在导航时补位: false 机器推动离开位置也补位（默认）, true 机器导航离开才补位
     */
    private boolean mFillingStrategy = false;

    /**
     * 重定位
     */
    private final ResetEstimateComponent mResetEstimateComponent;
    /**
     * 导航策略
     */
    private Definition.AdvNaviStrategy mAdvNaviStrategy;
    /**
     * 是否自动重定位
     */
    private boolean mIsAutoEstimate = true;
    /**
     * 自动重定位次数
     */
    private int mRetryCount = DEFAULT_ESTIMATE_RETRY_COUNT;
    private String mCurrentPlace;

    public enum CheckState {
        IDLE,
        NAVIGATION,
        END
    }

    public NavigationCheckComponent(String name) {
        this(name, Looper.myLooper());
    }

    public NavigationCheckComponent(String name, Looper looper) {
        super(name, looper);
        mGson = new Gson();
        mResetEstimateComponent = new ResetEstimateComponent(name, looper);
    }

    @Override
    public void onStart(String params) {
        Log.i(mName, "onStart : " + params);
        try {
            mNavigationParams = new JSONObject();
            JSONObject jsonParams = new JSONObject(params);
            mDestination = jsonParams.optString(ComponentParams.NavigationCheck.PARAM_DESTINATION);
            mTimeOut = jsonParams.optLong(ComponentParams.NavigationCheck.PARAM_TIMEOUT, -1);
            mDesRange = jsonParams.optDouble(ComponentParams.NavigationCheck.PARAM_DESTINATION_RANGE, DEFAULT_DESTINATION_AVAILABLE_RANGE);
            if (mDesRange < DEFAULT_MIN_DESTINATION_RANGE) {
                mDesRange = DEFAULT_MIN_DESTINATION_RANGE;
            }
            mQueuingStrategy = jsonParams.optInt(ComponentParams.NavigationCheck.PARAM_DYNAMIC, 0);
            mCurrentPlace = jsonParams.optString(ComponentParams.NavigationCheck.PARAM_CURRENT_PLACE, "");
            mNavigationParams = jsonParams.optJSONObject(ComponentParams.NavigationCheck.PARAM_NAVGATION_PARAMS);
            mRetryCount = mNavigationParams.optInt(ComponentParams.ResetEstimate.PARAM_RESET_ESTIMATE_COUNT
                    , DEFAULT_ESTIMATE_RETRY_COUNT);
            mIsAutoEstimate = mNavigationParams.optBoolean(ComponentParams.Navigation.PARAM_AUTO_RESET_ESTIMATE,
                    true);
            JSONArray standbyList = jsonParams.optJSONArray(ComponentParams.NavigationCheck.PARAM_STANDBY_DESTINATION_LIST);
            mStandbyDesList = new ArrayList<String>();
            if (standbyList != null) {
                for (int i = 0; i < standbyList.length(); i++) {
                    String standbyData = standbyList.getString(i);
                    mStandbyDesList.add(standbyData);
                }
            }
            Log.e(mName, "mStandbyDesList size:" + mStandbyDesList.size());
            mFillingStrategy = jsonParams.optBoolean(ComponentParams.NavigationCheck.PARAM_FILLING_WHEN_DELIVERY, false);
            if (TextUtils.isEmpty(mDestination)) {
                processResult(ComponentError.ERROR_PARAMS_PLACE_NAME_INVALID);
            } else {
                prepareMultiRobotNavi();
            }
        } catch (JSONException e) {
            e.printStackTrace();
            Log.e(mName, "onStart Error: " + e.getMessage());
            processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR);
        }
    }

    private void prepareMultiRobotNavi(){
        if (mTimeOut > 0) {
            sendTaskTimeoutMsg();
        }
        mAdvNaviStrategy = getNaviStrategy();
        Log.i(mName, "getNaviStrategy: " + mAdvNaviStrategy.name());
        startMultiRobotNavi(mAdvNaviStrategy);
    }

    private void initNaviParams() {
        try {
            mNavigationParams.put(NavigationAdvancedBean.Navigation.PARAM_IN_DESTINATION_RANGE, mDesRange);
            mNavigationParams.put(NavigationAdvancedBean.Navigation.PARAM_FILLING_WHEN_DELIVERY, mFillingStrategy);//补位中使用
            mNavigationParams.put(NavigationAdvancedBean.Navigation.PARAM_CURRENT_PLACE, mCurrentPlace);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }


    private void startMultiRobotNavi(Definition.AdvNaviStrategy naviStrategy) {
        mState = CheckState.NAVIGATION;
        initNaviParams();
        mApi.startMultiRobotNavigation(
                mDestination,
                naviStrategy,
                mStandbyDesList,
                mNavigationParams.toString(),
                new ActionListener() {
                    @Override
                    public void onResult(int status, String message, String extraData) throws RemoteException {
                        Log.e(mName, "onResult: " + status + " responseString：" + message + " extraData：" + extraData);
                        handlerResult(status, message, extraData);
                    }

                    @Override
                    public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                        Log.e(mName, "onError: " + errorCode + " errorString：" + errorString + " extraData：" + extraData);
                        handlerError(errorCode, errorString, extraData);
                    }

                    @Override
                    public void onStatusUpdate(int status, String data, String extraData) throws RemoteException {
                        Log.e(mName, "onStatusUpdate: " + status + " data：" + data + " extraData：" + extraData);
                        handlerStatusUpdate(status, data, extraData);
                    }
                });
    }


    /**
     * 根据业务区分导航策略
     */
    private Definition.AdvNaviStrategy getNaviStrategy() {
        switch (mQueuingStrategy) {
            //lora优先级星形补位
            case 0:
                return Definition.AdvNaviStrategy.STAR_BY_LORA;
            //点位优先级星形补位
            case 1:
                return Definition.AdvNaviStrategy.STAR_BY_POINT;
            //点位优先级火车补位
            case 2:
                return Definition.AdvNaviStrategy.TRAIN_BY_POINT;
        }
        return Definition.AdvNaviStrategy.DEFAULT;
    }

    private void handlerStatusUpdate(int status, String data, String extraData) {
        switch (status) {
            //补位开始
            case Definition.STATUS_NAVI_REPLACE_DESTINATION:
            //开始导航
            case Definition.STATUS_START_NAVIGATION:
                updateStatus(ComponentStatus.STATUS_START_NAVIGATION, data, extraData);
                break;
            case Definition.STATUS_DISTANCE_WITH_DESTINATION:
                updateStatus(ComponentStatus.STATUS_DISTANCE_WITH_DESTINATION, data, extraData);
                break;
            case Definition.STATUS_NAVI_ARRIVED_REPLACE_DESTINATION:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_TRANSFER_REPLACE_DESTINATION, data, extraData);
                break;
            case Definition.STATUS_NAVI_AVOID_IMMEDIATELY:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_IMMEDIATELY, data, extraData);
                break;
            default:
                navigationStatusUpdate(status, data, extraData);
                break;
        }

    }

    private void handlerError(int errorCode, String errorString, String extraData) {
        switch (errorCode) {
            case Definition.ERROR_LISTENER_INVALID:
                processResult(ComponentError.ERROR_QUERY_POSE_LIST_FAILED);
                break;
            case Definition.ERROR_TARGET_NOT_FOUND:
                processResult(ComponentError.ERROR_DESTINATION_LIST_NOT_EXIST);
                break;
            //导航事件已结束。业务自己处理定位丢失问题，决定是否重定位、重新导航
            case Definition.ERROR_ESTIMATE_ERROR:
                startResetEstimate(errorString, extraData);
                break;
            case Definition.ERROR_PARAMS_JSON_PARSER_ERROR:
                processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR, errorString, extraData);
                break;
            case Definition.ERROR_NO_AVAILABLE_DESTINATION:
                processResult(ComponentError.ERROR_NO_AVAILABLE_DESTINATION, errorString, extraData);
                break;
            default:
                navigationResult(errorCode, errorString, extraData);
                break;

        }

    }

    private void handlerResult(int status, String message, String extraData) {
        switch (status) {
            //导航到达
            case Definition.RESULT_OK:
            case Definition.RESULT_NAVIGATION_ARRIVED:
                //结束任务
                processResult(ComponentResult.RESULT_NAVIGATION_ARRIVED, message, extraData);
                break;
            //地点可用
            case Definition.RESULT_DESTINATION_AVAILABLE:
                processResult(ComponentResult.RESULT_NAVIGATION_DESTINATION_AVAILABLE, message, extraData);
                break;
            //到达点位附近
            case Definition.RESULT_DESTINATION_IN_RANGE:
                processResult(ComponentResult.RESULT_NAVIGATION_IN_DESTINATION_RANGE, message, extraData);
                break;
            default:
                navigationResult(status, message, extraData);
                break;
        }
    }

    private void startResetEstimate(String data, String extraData) {
        Log.d(mName, "startResetEstimate " + mIsAutoEstimate);
        if (!mIsAutoEstimate) {
            updateStatus(ComponentStatus.STATUS_ESTIMATE_LOST, data, extraData);
            return;
        }

        try {
            mResetEstimateComponent.setComponentListener(mResetEstimateComponentListener);
            mResetEstimateComponent.setStatusListener(mResetEstimateComponentStatus);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(ComponentParams.ResetEstimate.PARAM_RESET_ESTIMATE_COUNT, mRetryCount);
            mResetEstimateComponent.start(jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void stopResetEstimate() {
        Log.d(mName, "stopResetEstimate");
        mResetEstimateComponent.setComponentListener(null);
        mResetEstimateComponent.setStatusListener(null);
        mResetEstimateComponent.stop(100);
    }

    private final ComponentListener mResetEstimateComponentListener = new ComponentListener() {
        @Override
        public void onFinish(int result, String message, String extraData) {
            Log.d(mName, "onFinish result: " + result + ", message: " + message);
            switch (result) {
                case ComponentResult.RESULT_SUCCESS:
                    if (isAlive()) {
                        startMultiRobotNavi(mAdvNaviStrategy);
                        updateStatus(ComponentStatus.STATUS_NAVIGATION_RESET_ESTIMATE_SUCCESS
                                , "navigation reset estimate success", extraData);
                    }
                    break;
                case ComponentResult.RESULT_RESET_ESTIMATE_FAIL:
                    processResult(ComponentError.ERROR_NAVIGATION_RESET_ESTIMATE_FAIL, "", extraData);
                    break;
                default:
                    updateStatus(result, message, extraData);
                    break;
            }
        }
    };

    private final ComponentStatusListener mResetEstimateComponentStatus = new ComponentStatusListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            Log.d(mName, "onStatusUpdate status: " + status + ", data: " + data);
            updateStatus(status, data, extraData);
        }
    };

    private void simulateCreateNavigationErrorEvent(int errorCode, String desMsg) {
        Log.d(mName, "simulateCreateNavigationErrorEvent:" + errorCode);
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("code", errorCode);
            jsonObject.put("message", desMsg);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        processResult(ComponentError.ERROR_MULTIPLE_MODE_ERROR, "", jsonObject.toString());
    }

    private void sendTaskTimeoutMsg() {
        DelayTask.cancel(DELAY_TAG);
        DelayTask.submit(DELAY_TAG, new Runnable() {
            @Override
            public void run() {
                if (mState != CheckState.IDLE || mState != CheckState.END) {
                    processResult(ComponentError.ERROR_NAVIGATION_CHECK_TIMEOUT);
                }
            }
        }, mTimeOut);
    }

    private void processResult(int result) {
        processResult(result, "", "");
    }

    private void processResult(int result, String message, String extraData) {
        if (!isAlive()) {
            return;
        }
        stop(STOP_TIMEOUT, result, message, extraData);
    }

    @Override
    protected void onUpdate(String intent, String params) {

    }

    @Override
    protected void onStop(int reason) {
        mState = CheckState.IDLE;
        DelayTask.cancel(DELAY_TAG);
        Log.e(mName, "onStop reset mDestinationPose");
        mCurrentPlace = null;
        mApi.stopMultiRobotNavigation();
        stopResetEstimate();
    }

    private void navigationResult(int status, String message, String extraData) {
        switch (status) {
            //----------  RESULT  ----------//
            case Definition.ACTION_RESPONSE_STOP_SUCCESS:
                processResult(status, message + " 停止成功", extraData);
                break;
            case Definition.RESULT_FAILURE:
                processResult(status, message + " 导航失败", extraData);
                break;
            case Definition.STATUS_NAVI_OUT_MAP:
                processResult(ComponentError.ERROR_NAVIGATION_OUT_MAP, message, extraData);
                break;
            case Definition.STATUS_NAVI_GLOBAL_PATH_FAILED:
                processResult(ComponentError.ERROR_NAVIGATION_GLOBAL_PATH_FAILED, message, extraData);
                break;
            case Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH:
                simulateCreateNavigationErrorEvent(NAVI_ERROR_MULTIPLE_MAP_NOT_MATCH,
                        "Multiple map not match!");
                break;
            case Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT:
                simulateCreateNavigationErrorEvent(NAVI_ERROR_MULTIPLE_LORA_DISCONNECT,
                        "Lora disconnect!");
                break;
            case Definition.STATUS_NAVI_MULTI_VERSION_NOT_MATCH:
                simulateCreateNavigationErrorEvent(NAVI_ERROR_MULTIPLE_VERSION_NOT_MATCH,
                        "Multiple version not match");
                break;
            case Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL:
                simulateCreateNavigationErrorEvent(NAVI_ERROR_MULTIPLE_LORA_CONFIG_FAIL,
                        "Multiple version not match");
                break;
            case Definition.STATUS_NAVI_WHEEL_SLIP:
                processResult(ComponentError.ERROR_NAVI_WHEEL_SLIP, message, extraData);
                break;

            //----------  ERROR  ----------//
            case Definition.ERROR_NOT_ESTIMATE:
                processResult(ComponentError.ERROR_NOT_ESTIMATE,
                        "navigation not estimate", extraData);
                break;
            case Definition.ERROR_IN_DESTINATION:
                processResult(ComponentError.ERROR_NAVIGATION_ALREADY_IN_DESTINATION,
                        "already in destination", extraData);
                break;
            case Definition.ERROR_DESTINATION_NOT_EXIST:
                processResult(ComponentError.ERROR_DESTINATION_NOT_EXIST,
                        "destination not exist", extraData);
                break;
            case Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE:
                processResult(ComponentError.ERROR_DESTINATION_CAN_NOT_ARRIVE,
                        "navigation moving time out", extraData);
                break;
            case Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT:
                processResult(ComponentError.ERROR_MULTI_ROBOT_WAITING_TIMEOUT);
                break;
            case Definition.ERROR_NAVIGATION_AVOID_TIMEOUT:
                processResult(ComponentError.ERROR_NAVIGATION_AVOID_TIMEOUT,
                        "navigation avoid time out", "");
            case Definition.ACTION_RESPONSE_ALREADY_RUN:
                processResult(ComponentError.ERROR_REQUEST_RES_BUSY);
                break;
            case Definition.ACTION_RESPONSE_REQUEST_RES_ERROR:
                processResult(ComponentError.ERROR_REQUEST_RES_FAILED);
                break;
            case Definition.ERROR_WHEEL_OVER_CURRENT_RUN_OUT:
                processResult(ComponentError.ERROR_WHEEL_OVER_CURRENT_RUN_OUT,
                        "wheel over current retry count run out", extraData);
                break;
            case Definition.ACTION_RESPONSE_RES_UNAVAILBALE:
                processResult(ComponentError.ERROR_OPEN_RADAR_FAILED,
                        "res unavailable: " + message, extraData);
                break;
            default:
                processResult(status, message, extraData);
                break;
        }
    }


    private void navigationStatusUpdate(int status, String data, String extraData) {
        switch (status) {
            case Definition.STATUS_GOAL_OCCLUDED:
            case Definition.STATUS_NAVI_AVOID:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_START,
                        "navigation avoid start", extraData);
                break;
            case Definition.STATUS_GOAL_OCCLUDED_END:
            case Definition.STATUS_NAVI_AVOID_END:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_END,
                        "navigation avoid end", extraData);
                break;
            case Definition.STATUS_NAVI_OBSTACLES_AVOID:
                updateStatus(ComponentStatus.STATUS_OBSTACLES_AVOID,
                        "pause to obstacles avoid", extraData);
                break;
            case Definition.STATUS_NAVI_MULTI_ROBOT_WAITING:
                updateStatus(ComponentStatus.STATUS_NAVI_MULTI_ROBOT_WAITING,
                        "navigation multi robot waiting");
                break;
            case Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END:
                updateStatus(ComponentStatus.STATUS_NAVI_MULTI_ROBOT_WAITING_END,
                        "navigation multi robot waiting end");
                break;
            case Definition.STATUS_NAVI_GO_STRAIGHT:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_GO_STRAIGHT, data,
                        extraData);
                break;
            case Definition.STATUS_NAVI_TURN_LEFT:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_TURN_LEFT, data,
                        extraData);
                break;
            case Definition.STATUS_NAVI_TURN_RIGHT:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_TURN_RIGHT, data,
                        extraData);
                break;
            case Definition.STATUS_NAVI_SET_PRIORITY_FAILED:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_SET_PRIORITY_FAILED, data,
                        extraData);
                break;
            default:
                updateStatus(status, data, extraData);
                break;
        }
    }
}
