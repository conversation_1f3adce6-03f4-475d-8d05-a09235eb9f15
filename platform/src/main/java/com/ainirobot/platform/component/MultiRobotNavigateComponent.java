package com.ainirobot.platform.component;

import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.ainirobot.platform.data.TestManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Timer;
import java.util.TimerTask;

/**
 * 多机导航组件：实现逻辑和NavigationComponent相同
 * 区别是仅仅多一个控制
 *
 */
public class MultiRobotNavigateComponent extends Component{

    /*default values*/
    private static final double DEFAULT_COORDINATE_DEVIATION = 0.5;
    private static final long DEFAULT_MOVING_TIMEOUT_TIME = 20 * 1000;
    private static final long DEFAULT_AVOID_INTERVAL_TIME = 5 * 1000;
    private static final int DEFAULT_MAX_AVOID_COUNT = 5;
    private static final long GET_DISTANCE_DELAY_TIME = 0;
    private static final int DEFAULT_ESTIMATE_RETRY_COUNT = 5;
    private static final boolean DEFAULT_AUTO_RESET_ESTIMATE = true;
    private static final double DEFAULT_NEAR_DISTANCE = 3.0d;
    private static final int DEFAULT_NAVI_TYPE = 1;
    private static final int DEFAULT_NAVI_MODE = 0;

    /**
     * 获取距离默认频率 1s/次
     */
    private static final long GET_DISTANCE_INTERVAL_TIME = 1000;

    /**
     * 获取距离默认最小频率 200ms/次
     */
    private static final long GET_DISTANCE_MIN_INTERVAL_TIME = 200;

    /**
     * 无效距离，初始值
     */
    private static final float DISTANCE_INVALID_VALUE = -1;

    /**
     * 导航如果无法到达目的地中心点，默认到达目的地的范围
     */
    private static final double NAVIGATION_DEFAULT_DESTINATION_RANGE = 0.0D;

    /**
     * 轮子过流后尝试恢复次数
     */
    private static final int WHEEL_OVER_CURRENT_RETRY_DEFAULT_COUNT = 0;

    /**
     * 是否适应导航结束时朝向的角度，默认 false:即归正目标点设定角度
     */
    private boolean mIsAdjustAngle;

    /**
     * 多机避障等待默认超时时间，默认300s
     */
    private static final long DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT = 300 * 1000;
    /**
     * 导航任务默认优先级 值越大优先级越高，多机场景下使用
     */
    private static final int DEFAULT_NAVIGAITON_TASK_PRIORITY = 0;
    private String mDestination;
    private int mMaxAvoidCount;
    private long mAvoidIntervalTime;
    private long mGetDistanceIntervalTime;
    private int mAvoidCounter = 0;
    private float mLinearSpeed;
    private float mAngularSpeed;
    private double mLinearAcceleration;
    private double mAngularAcceleration;
    private int mRetryCount;
    private boolean mAutoResetEstimate = DEFAULT_AUTO_RESET_ESTIMATE;
    private Timer mAvoidTimer;
    private JSONObject mParams;
    private ResetEstimateComponent mResetEstimateComponent;
    private double mCoordinateDeviation;
    private long mTimeout;
    private Timer mGetDistanceTimer;
    private MultiRobotNavigateComponent.OnStopSuccessListener mStopSuccessListener;
    private NavigationState mNavigationState = NavigationState.IDLE;
    private double mDestinationX;
    private double mDestinationY;
    private volatile float mCurrentDistance = DISTANCE_INVALID_VALUE;
    private boolean mIsNeedAvoidNotifyImmediately;
    private double mDestinationRange = 0.0D;
    private int mWheelOverCurrentRetryCount = 0;
    private long mMultiWaitTimeout;
    private int mTaskPriority;
    private int mNaviType = 1;
    private float pose_x;
    private float pose_y;
    private float pose_theta;
    private int mStartModeLevel;
    private int mBrakeModeLevel;
    private double mObsDistance;
    private double mPosTolerance;
    private double mAngleTolerance;

    private static final int BASE_NAVIGATION_ERROR = -20010000;

    private enum NavigationState {
        IDLE,
        NAVIGATION,
        RESET_ESTIMATE,
        DESTINATION_NEAR
    }

    public MultiRobotNavigateComponent(String name) {
        this(name, Looper.myLooper());
    }

    public MultiRobotNavigateComponent(String name, Looper looper) {
        super(name, looper);
        mResetEstimateComponent = new ResetEstimateComponent(mName, mLooper);
    }

    @Override
    public void onStart(String params) {
        Log.d(mName, "onStart : " + params);
        try {
            mParams = new JSONObject(params);
            mNaviType = mParams.optInt(ComponentParams.Navigation.PARAM_NAVIGATION_TYPE, ComponentParams.Navigation.TYPE_DESTINATION);
            pose_x = Float.parseFloat(mParams.optString(ComponentParams.Navigation.PARAM_POSE_X, "0f"));
            pose_y = Float.parseFloat(mParams.optString(ComponentParams.Navigation.PARAM_POSE_Y, "0f"));
            pose_theta = Float.parseFloat(mParams.optString(ComponentParams.Navigation.PARAM_POSE_THETA, "0f"));
            mDestination = mParams.optString(ComponentParams.Navigation.PARAM_DESTINATION);
            mCoordinateDeviation = mParams.optDouble(ComponentParams.Navigation.PARAM_COORDINATE_DEVIATION,
                    DEFAULT_COORDINATE_DEVIATION);
            mTimeout = mParams.optLong(ComponentParams.Navigation.PARAM_MOVING_TIMEOUT_TIME,
                    DEFAULT_MOVING_TIMEOUT_TIME);
            mMaxAvoidCount = mParams.optInt(ComponentParams.Navigation.PARAM_MAX_AVOID_COUNT,
                    DEFAULT_MAX_AVOID_COUNT);
            mAvoidIntervalTime = mParams.optLong(ComponentParams.Navigation.PARAM_AVOID_INTERVAL_TIME,
                    DEFAULT_AVOID_INTERVAL_TIME);
            mGetDistanceIntervalTime = mParams.optLong(ComponentParams.Navigation.PARAM_GET_DISTANCE_INTERVAL_TIME,
                    GET_DISTANCE_INTERVAL_TIME);
            if (mGetDistanceIntervalTime < GET_DISTANCE_MIN_INTERVAL_TIME) {
                mGetDistanceIntervalTime = GET_DISTANCE_MIN_INTERVAL_TIME;
            }
            mAutoResetEstimate = mParams.optBoolean(ComponentParams.Navigation.PARAM_AUTO_RESET_ESTIMATE,
                    DEFAULT_AUTO_RESET_ESTIMATE);
            mIsAdjustAngle = mParams.optBoolean(ComponentParams.Navigation.PARAM_IS_ADJUST_ANGLE, false);
            mRetryCount = mParams.optInt(ComponentParams.ResetEstimate.PARAM_RESET_ESTIMATE_COUNT
                    , DEFAULT_ESTIMATE_RETRY_COUNT);
            mStartModeLevel = mParams.optInt(ComponentParams.Navigation.PARAM_START_MODE_LEVEL,
                    DEFAULT_NAVI_MODE);
            mBrakeModeLevel = mParams.optInt(ComponentParams.Navigation.PARAM_BRAKE_MODE_LEVEL,
                    DEFAULT_NAVI_MODE);

            // for test
            float testAngularSpeed = Settings.Global.getFloat(BaseApplication.getContext()
                    .getContentResolver(), TestManager.TEST_SETTING_NAVIGATION_ANGULAR_SPEED, -1);
            float testAngularAcceleration = Settings.Global.getFloat(BaseApplication.getContext()
                    .getContentResolver(), TestManager.TEST_SETTING_NAVIGATION_ANGULAR_ACCELERATION, -1);
            float testLinearSpeed = Settings.Global.getFloat(BaseApplication.getContext()
                    .getContentResolver(), TestManager.TEST_SETTING_NAVIGATION_LINEAR_SPEED, -1);
            float testLinearAcceleration = Settings.Global.getFloat(BaseApplication.getContext()
                    .getContentResolver(), TestManager.TEST_SETTING_NAVIGATION_LINEAR_ACCELERATION, -1);
            if (testLinearSpeed > 0) {
                mLinearSpeed = testLinearSpeed;
            } else {
                mLinearSpeed = (float) mParams.optDouble(ComponentParams.Navigation.PARAM_LINEAR_SPEED
                        , SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED);
                mLinearSpeed = rectifyLinearSpeed(mLinearSpeed);
            }
            if (testAngularSpeed > 0) {
                mAngularSpeed = testAngularSpeed;
            } else {
                mAngularSpeed = (float) mParams.optDouble(ComponentParams.Navigation.PARAM_ANGULAR_SPEED
                        , SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED);
                mAngularSpeed = rectifyAngularSpeed(mAngularSpeed);
            }
            if (testLinearAcceleration > 0) {
                mLinearAcceleration = testLinearAcceleration;
            } else {
                mLinearAcceleration = mParams.optDouble(ComponentParams.Navigation.PARAM_LINEAR_ACCELERATION, 0);
            }
            if (testAngularAcceleration > 0) {
                mAngularAcceleration = testAngularAcceleration;
            } else {
                mAngularAcceleration = mParams.optDouble(ComponentParams.Navigation.PARAM_ANGULAR_ACCELERATION, 0);
            }
            mIsNeedAvoidNotifyImmediately = mParams.optBoolean(ComponentParams.Navigation.PARAM_IS_NEED_AVOID_NOTIFY_IMMEDIATELY);
            mDestinationRange = mParams.optDouble(ComponentParams.Navigation.PARAM_DESTINATION_RANGE, NAVIGATION_DEFAULT_DESTINATION_RANGE);
            mWheelOverCurrentRetryCount = mParams.optInt(ComponentParams.Navigation.PARAM_WHEEL_OVER_CURRENT_RETRY_COUNT, WHEEL_OVER_CURRENT_RETRY_DEFAULT_COUNT);
            if (mWheelOverCurrentRetryCount < WHEEL_OVER_CURRENT_RETRY_DEFAULT_COUNT) {
                mWheelOverCurrentRetryCount = WHEEL_OVER_CURRENT_RETRY_DEFAULT_COUNT;
            }
            mMultiWaitTimeout = mParams.optLong(ComponentParams.Navigation.PARAM_MULTI_ROBOT_WAITING_TIMEOUT_TIME,
                    DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT);
            mTaskPriority = mParams.optInt(ComponentParams.Navigation.PARAM_PRIORITY, DEFAULT_NAVIGAITON_TASK_PRIORITY);
            Log.d(mName, "onStart, Speed : " + mLinearSpeed + ", " + mAngularSpeed);

            mObsDistance = mParams.optDouble(ComponentParams.Navigation.PARAM_OBS_DISTANCE, 0);
            mPosTolerance = mParams.optDouble(ComponentParams.Navigation.PARAM_POS_TOLERANCE, 0);
            mAngleTolerance = mParams.optDouble(ComponentParams.Navigation.PARAM_ANGLE_TOLERANCE, 0);

            if (!(mNaviType == ComponentParams.Navigation.TYPE_POSE || mNaviType == ComponentParams.Navigation.TYPE_DESTINATION)) {
                processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR, "navi type param error", "");
                return;
            }

            if (mNaviType == ComponentParams.Navigation.TYPE_DESTINATION && TextUtils.isEmpty(mDestination)) {
                processResult(ComponentError.ERROR_PARAMS_PLACE_NAME_INVALID);
            } else if (mNaviType == ComponentParams.Navigation.TYPE_POSE && pose_theta == 0 && pose_x == 0 && pose_y == 0) {
                processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR, "pose parse error", "");
            } else {
                startNavigation(true);
                registerEventStatusListener();
            }
        } catch (Exception e) {
            e.printStackTrace();
            processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR, "json parse exception", "");
        }
    }

    private void startNavigation(final boolean firstStart) {
        Log.d(mName, "startNavigation");
        mNavigationState = NavigationState.NAVIGATION;

        ActionListener listener = new ActionListener() {
            @Override
            public void onResult(int status, String responseString, String extraData) {
                Log.d(mName, "startNavigation onResult status: " + status
                        + ", responseStr: " + responseString
                        + ", extraData: " + extraData);
                switch (status) {
                    case Definition.RESULT_OK:
                        processResult(ComponentResult.RESULT_NAVIGATION_ARRIVED);
                        break;
                    case Definition.ACTION_RESPONSE_STOP_SUCCESS:
                        if (mStopSuccessListener != null) {
                            mStopSuccessListener.onStopSuccess();
                        }
                        break;
                    case Definition.RESULT_FAILURE:
                        processResult(ComponentResult.RESULT_NAVIGATION_FAILURE, "", extraData);
                        break;
                    default:
                        processResult(status, responseString, extraData);
                        break;
                }
            }

            @Override
            public void onError(int errorCode, String errorString, String extraData) {
                Log.d(mName, "startNavigation onError : "
                        + errorCode + ", " + errorString + ", " + extraData);
                switch (errorCode) {
                    case Definition.ERROR_NOT_ESTIMATE:
                        processResult(ComponentError.ERROR_NOT_ESTIMATE,
                                "navigation not estimate", extraData);
                        break;
                    case Definition.ERROR_IN_DESTINATION:
                        processResult(ComponentError.ERROR_NAVIGATION_ALREADY_IN_DESTINATION,
                                "already in destination", extraData);
                        break;
                    case Definition.ERROR_DESTINATION_NOT_EXIST:
                        processResult(ComponentError.ERROR_DESTINATION_NOT_EXIST,
                                "destination not exist", extraData);
                        break;
                    case Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE:
                        processResult(ComponentError.ERROR_DESTINATION_CAN_NOT_ARRIVE,
                                "navigation moving time out", extraData);
                        break;
                    case Definition.ACTION_RESPONSE_ALREADY_RUN:
                        processResult(ComponentError.ERROR_REQUEST_RES_BUSY);
                        break;
                    case Definition.ACTION_RESPONSE_REQUEST_RES_ERROR:
                        processResult(ComponentError.ERROR_REQUEST_RES_FAILED);
                        break;
                    case Definition.ERROR_WHEEL_OVER_CURRENT_RUN_OUT:
                        processResult(ComponentError.ERROR_WHEEL_OVER_CURRENT_RUN_OUT,
                                "wheel over current retry count run out", extraData);
                        break;
                    case Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT:
                        processResult(ComponentError.ERROR_MULTI_ROBOT_WAITING_TIMEOUT);
                        break;
                    case Definition.ACTION_RESPONSE_RES_UNAVAILBALE:
                        processResult(ComponentError.ERROR_OPEN_RADAR_FAILED,
                                "res unavailable: " + errorString, extraData);
                        break;
                    default:
                        processResult(errorCode, errorString, extraData);
                        break;
                }
            }

            @Override
            public void onStatusUpdate(int status, String data, String extraData) {
//                Log.d(mName, "startNavigation onStatusUpdate : "
//                        + status + ", " + data + ", " + extraData);
                switch (status) {
                    case Definition.STATUS_NAVI_OUT_MAP:
                        processResult(ComponentError.ERROR_NAVIGATION_OUT_MAP, "", extraData);
                        break;
                    case Definition.STATUS_NAVI_GLOBAL_PATH_FAILED:
                        processResult(ComponentError.ERROR_NAVIGATION_GLOBAL_PATH_FAILED
                                , "global path failed", extraData);
                        break;
                    case Definition.STATUS_GOAL_OCCLUDED:
                    case Definition.STATUS_NAVI_AVOID:
                        processStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_START,
                                "navigation avoid start", extraData);
                        if (mIsNeedAvoidNotifyImmediately) {
                            processStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_IMMEDIATELY,
                                    "navigation avoid start", extraData);
                        }
                        startAvoidTimer();
                        break;
                    case Definition.STATUS_GOAL_OCCLUDED_END:
                    case Definition.STATUS_NAVI_AVOID_END:
                        if (cancelAvoidTimer()) {
                            processStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_END,
                                    "navigation avoid end", extraData);
                        }
                        break;
                    case Definition.STATUS_NAVI_OBSTACLES_AVOID:
                        processStatus(ComponentStatus.STATUS_OBSTACLES_AVOID,
                                "pause to obstacles avoid", extraData);
                        break;
                    case Definition.STATUS_START_NAVIGATION:
                        if (firstStart) {
                            processStatus(ComponentStatus.STATUS_START_NAVIGATION,
                                    "start navigation");
                            startGetDistanceInterval();
                        }
                        break;
                    case Definition.STATUS_NAVI_MULTI_ROBOT_WAITING:
                        processStatus(ComponentStatus.STATUS_NAVI_MULTI_ROBOT_WAITING,
                                "navigation multi robot waiting");
                        break;
                    case Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END:
                        processStatus(ComponentStatus.STATUS_NAVI_MULTI_ROBOT_WAITING_END,
                                "navigation multi robot waiting end");
                        break;
                    case Definition.STATUS_NAVI_GO_STRAIGHT:
                        processStatus(ComponentStatus.STATUS_NAVIGATION_GO_STRAIGHT, data,
                                extraData);
                        break;
                    case Definition.STATUS_NAVI_TURN_LEFT:
                        processStatus(ComponentStatus.STATUS_NAVIGATION_TURN_LEFT, data,
                                extraData);
                        break;
                    case Definition.STATUS_NAVI_TURN_RIGHT:
                        processStatus(ComponentStatus.STATUS_NAVIGATION_TURN_RIGHT, data,
                                extraData);
                        break;
                    case Definition.STATUS_NAVI_SET_PRIORITY_FAILED:
                        processStatus(ComponentStatus.STATUS_NAVIGATION_SET_PRIORITY_FAILED, data,
                                extraData);
                        break;
                    case Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH:
                        processResult(ComponentError.ERROR_MULTIPLE_MODE_ERROR,
                                "multiple map not match", extraData);
                        break;
                    case Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT:
                        processResult(ComponentError.ERROR_MULTIPLE_MODE_ERROR,
                                "Lora disconnect!", extraData);
                        break;
                    case Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL:
                        processResult(ComponentError.ERROR_MULTIPLE_MODE_ERROR,
                                "Lora config fail!", extraData);
                        break;
                    case Definition.STATUS_NAVI_MULTI_VERSION_NOT_MATCH:
                        processResult(ComponentError.ERROR_MULTIPLE_MODE_ERROR,
                                "multiple robot version not match", extraData);
                        break;
                    case Definition.STATUS_NAVI_WHEEL_SLIP:
                        processResult(ComponentError.ERROR_NAVI_WHEEL_SLIP,
                                "navigation wheel slip", extraData);
                        break;
                    default:
                        processStatus(status, data, extraData);
                        break;

                }
            }

        };

        if (mNaviType == ComponentParams.Navigation.TYPE_DESTINATION) {
            mApi.startNavigation(mDestination, mCoordinateDeviation, mObsDistance, mTimeout,
                    mLinearSpeed, mAngularSpeed, mIsAdjustAngle, mDestinationRange,
                    mWheelOverCurrentRetryCount, mMultiWaitTimeout, mTaskPriority, mLinearAcceleration,
                    mAngularAcceleration, Definition.NAVIGATE_DEFAULT, mStartModeLevel,
                    mBrakeModeLevel, mPosTolerance, mAngleTolerance, listener);
        } else {
            Pose pose = new Pose();
            pose.setX(pose_x);
            pose.setY(pose_y);
            pose.setTheta(pose_theta);
            mApi.startNavigation(pose, mCoordinateDeviation, mObsDistance, mIsAdjustAngle, mTimeout,
                    mLinearSpeed, mAngularSpeed, mWheelOverCurrentRetryCount, mTaskPriority,
                    mLinearAcceleration, mAngularAcceleration, Definition.NAVIGATE_DEFAULT,
                    mStartModeLevel, mBrakeModeLevel, mPosTolerance, mAngleTolerance, listener);
        }


    }

    private void registerEventStatusListener() {
        mApi.registerStatusListener(Definition.STATUS_EVENT, mStatusListener);
    }

    private StatusListener mStatusListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
            if (!isAlive()) {
                return;
            }
            try {
                JSONObject json = new JSONObject(data);
                String estimateLost = json.optString(Definition.HW_NAVI_ESTIMATE_LOST);
                if (!TextUtils.isEmpty(estimateLost)) {
                    handleEstimateLost(type, data);
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    };

    private void handleEstimateLost(String type, final String data) {
        Log.d(mName, "handleEstimateLost, mNavigationState= " + mNavigationState);
        if (mAutoResetEstimate) {
            if (mNavigationState != NavigationState.RESET_ESTIMATE) {
                mNavigationState = NavigationState.RESET_ESTIMATE;
                stopNavigationAsync(new OnStopSuccessListener() {
                    @Override
                    void onStopSuccess() {
                        super.onStopSuccess();
                        startResetEstimate();
                    }
                });
            }
        } else {
            processStatus(ComponentStatus.STATUS_ESTIMATE_LOST, data);
        }
    }

    private void startResetEstimate() {
        Log.d(mName, "startResetEstimate");
        try {
            mResetEstimateComponent.setComponentListener(mResetEstimateComponentListener);
            mResetEstimateComponent.setStatusListener(mResetEstimateComponentStatus);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(ComponentParams.ResetEstimate.PARAM_RESET_ESTIMATE_COUNT, mRetryCount);
            mResetEstimateComponent.start(jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void stopResetEstimate() {
        Log.d(mName, "stopResetEstimate");
        mResetEstimateComponent.setComponentListener(null);
        mResetEstimateComponent.setStatusListener(null);
        mResetEstimateComponent.stop(100);
    }

    private ComponentListener mResetEstimateComponentListener = new ComponentListener() {
        @Override
        public void onFinish(int result, String message, String extraData) {
            Log.d(mName, "onFinish result: " + result + ", message: " + message);
            switch (result) {
                case ComponentResult.RESULT_SUCCESS:
                    if (isAlive()) {
                        startNavigation(false);
                        processStatus(ComponentStatus.STATUS_NAVIGATION_RESET_ESTIMATE_SUCCESS
                                , "navigation reset estimate success", extraData);
                    }
                    break;
                case ComponentResult.RESULT_RESET_ESTIMATE_FAIL:
                    processResult(ComponentError.ERROR_NAVIGATION_RESET_ESTIMATE_FAIL, "", extraData);
                    break;
                default:
                    processStatus(result, message, extraData);
                    break;
            }
        }
    };

    private ComponentStatusListener mResetEstimateComponentStatus = new ComponentStatusListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            Log.d(mName, "onStatusUpdate status: " + status + ", data: " + data);
            processStatus(status, data, extraData);
        }
    };

    private void stopNavigationAsync(OnStopSuccessListener onStopSuccessListener) {
        this.mStopSuccessListener = onStopSuccessListener;
        mApi.stopNavigation();
    }

    private abstract class OnStopSuccessListener {
        void onStopSuccess() {
            mStopSuccessListener = null;
        }
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.d(mName, "onUpdate " + intent + ", " + params);
    }

    @Override
    protected void onStop(int reason) {
        onStop(reason,false);
    }

    @Override
    public void onStop(int reason, boolean isCancelStopCmd) {
        Log.d(mName, "onStop reason: " + reason+", isCancelStopCmd : "+isCancelStopCmd);
        mNavigationState = NavigationState.IDLE;
        mCurrentDistance = DISTANCE_INVALID_VALUE;
        if (isCancelStopCmd){
            mApi.stopNavigation(true);
        }else {
            mApi.stopNavigation();
        }
        mApi.unregisterStatusListener(mStatusListener);
        mApi.unregisterStatusListener(mPoseListener);
        stopResetEstimate();
        cancelGetDistanceTimer();
        cancelAvoidTimer();
    }

    private void startGetDistanceInterval() {
        Log.d(mName, "Start get distance, interval time : " + mGetDistanceIntervalTime);
        if (mGetDistanceTimer == null) {
            mGetDistanceTimer = new Timer();
            mGetDistanceTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    Log.d(mName, "Get distance, mNavigationState= " + mNavigationState + ", distance:" + mCurrentDistance);
                    if (mCurrentDistance >= 0
                            && mNavigationState != NavigationState.RESET_ESTIMATE
                            && mNavigationState != NavigationState.IDLE) {
                        processStatus(ComponentStatus.STATUS_DISTANCE_WITH_DESTINATION,
                                String.valueOf(mCurrentDistance));
                        processNearDestination(mCurrentDistance);
                    }
                }
            }, GET_DISTANCE_DELAY_TIME, mGetDistanceIntervalTime);
        }

        mApi.getLocation(mDestination, mLocationListener);
    }

    private CommandListener mLocationListener = new CommandListener() {
        @Override
        public void onResult(int result, String message, String extraData) {
            super.onResult(result, message, extraData);
            Log.w(mName, "getLocation : " + result + ", " + message + ", " + mNavigationState);
            switch (result) {
                case Definition.RESULT_OK:
                    try {
                        JSONObject jsonObject = new JSONObject(message);
                        mDestinationX = jsonObject.optDouble("px", 0.0f);
                        mDestinationY = jsonObject.optDouble("py", 0.0f);

                        if (mNavigationState != NavigationState.IDLE)
                            mApi.registerStatusListener(Definition.STATUS_POSE, mPoseListener);
                    } catch (JSONException e) {
                        Log.e(mName, Log.getStackTraceString(e));
                    }
                    break;
                default:
                    break;
            }
        }
    };

    private StatusListener mPoseListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
//            Log.w(mName, "update pose : " + data);
            try {
                JSONObject jsonObject = new JSONObject(data);
                double selfX = jsonObject.optDouble("px", 0.0f);
                double selfY = jsonObject.optDouble("py", 0.0f);
                mCurrentDistance = (float) Math.sqrt(Math.pow(Math.abs(selfX - mDestinationX), 2)
                        + Math.pow(Math.abs(selfY - mDestinationY), 2));
//                Log.d(mName, "calculate distance : " + mCurrentDistance);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    };

    private void cancelGetDistanceTimer() {
        if (mGetDistanceTimer != null) {
            mGetDistanceTimer.cancel();
            mGetDistanceTimer = null;
        }
    }

    private void processNearDestination(float distance) {
        if (mNavigationState == NavigationState.DESTINATION_NEAR) {
            return;
        }
        if (distance > mCoordinateDeviation && distance < DEFAULT_NEAR_DISTANCE) {
            mNavigationState = NavigationState.DESTINATION_NEAR;
            processStatus(ComponentStatus.STATUS_NAVIGATION_NEAR_DESTINATION,
                    "near destination");
        }
    }

    private void startAvoidTimer() {
        if (mAvoidTimer == null) {
            mAvoidCounter = 0;
            mAvoidTimer = new Timer();
            mAvoidTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    mAvoidCounter++;
                    processStatus(ComponentStatus.STATUS_NAVIGATION_AVOID,
                            String.valueOf(mAvoidCounter));
                    if (mAvoidCounter == mMaxAvoidCount) {
                        cancelAvoidTimer();
                        processResult(ComponentError.ERROR_NAVIGATION_AVOID_TIMEOUT,
                                "navigation avoid time out", "");
                    }
                }
            }, mAvoidIntervalTime, mAvoidIntervalTime);
        }
    }

    private boolean cancelAvoidTimer() {
        if (mAvoidTimer != null) {
            mAvoidTimer.cancel();
            mAvoidTimer = null;
            return true;
        }
        return false;
    }

    private void processStatus(int status, String data) {
        processStatus(status, data, "");
    }

    private void processStatus(int status, final String data, String extraData) {
        if (!isAlive()) {
            return;
        }
        updateStatus(status, data, extraData);
    }

    private void processResult(int result) {
        processResult(result, "", "");
    }

    private void processResult(int result, String message, String extraData) {
        if (!isAlive()) {
            return;
        }
        stop(500, result, message, extraData);
    }
}
