package com.ainirobot.platform.component;

import android.os.Looper;
import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Invoker;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.platform.RobotApiProxy;
import com.ainirobot.platform.component.definition.ComponentDef;
import com.ainirobot.platform.component.definition.ComponentResult;

/**
 * Internal functional component
 */
public abstract class Component implements Invoker {

    public volatile String mUid = "";
    protected static final long STOP_TIMEOUT = 500;

    public interface ComponentListener {
        void onFinish(int result, String message, String extraData);
    }

    public interface ComponentStatusListener {
        void onStatusUpdate(int status, String data, String extraData);
    }

    private enum Status {
        IDLE,
        RUNNING,
        PAUSE,
    }

    private Status mStatus = Status.IDLE;
    protected String mName;
    private ComponentListener mComponentListener;
    private ComponentStatusListener mComponentStatusListener;

    protected RobotApiProxy mApi;
    protected Looper mLooper;

    public Component(String name, Looper looper) {
        if (looper == null) {
            mLooper = Looper.myLooper();
        } else {
            mLooper = looper;
        }
        if (looper == null) {
            mLooper = Looper.getMainLooper();
        }
        mApi = new RobotApiProxy(this);
        if (TextUtils.isEmpty(name)) {
            mName = getClass().getSimpleName();
        } else {
            mName = name + "-" + getClass().getSimpleName();
        }
    }

    public synchronized final boolean start(@NonNull String params) {
        if (mStatus != Status.IDLE) {
            Log.d(mName, mName + " current already running");
            return false;
        }
        mStatus = Status.RUNNING;
        onStart(params);
        return true;
    }

    public synchronized final void updateParams(String intent, @NonNull String params) {
        if (!isAlive()) {
            return;
        }
        onUpdate(intent, params);
    }

    public final void stop(long timeout) {
        stop(timeout, ComponentResult.RESULT_INTERRUPT, null, "");
    }

    public final void stop(long timeout, boolean isCancelStopCmd) {
        stop(timeout, ComponentResult.RESULT_INTERRUPT, null, "", isCancelStopCmd);
    }

    public final boolean updateStatus(int status, String data) {
        return updateStatus(status, data, "");
    }

    public final boolean updateStatus(int status, String data, String extraData) {
        if (!isAlive()) {
            return false;
        }
//        Log.v(mName, "updateStatus status: " + status + ", data: " + data + ", extraData: " + extraData);
        if (mComponentStatusListener != null) {
            mComponentStatusListener.onStatusUpdate(status, data, extraData);
            return true;
        }
        return false;
    }

    protected final synchronized void stop(long timeout, int result, String message) {
        stop(timeout, result, message, "");
    }

    protected final synchronized void stop(long timeout, int result, String message, String extraData) {
        Log.d(mName, "stop timeout: " + timeout + ", result: " + result
                + ", message: " + message + ", extraData: " + extraData
                + ", isAlive : " + isAlive());
        if (!isAlive()) {
            return;
        }
        mStatus = Status.PAUSE;
        onStop(result);
        mApi.stop(timeout);
        mStatus = Status.IDLE;

//        if (mComponentListener != null) {
//            mComponentListener.onFinish(result, message);
//        }

        asyncResponse(result, message, extraData);
    }

    protected final synchronized void stop(long timeout, int result, String message, String extraData, boolean isCancelStopCommand) {
        Log.d(mName, "stop timeout: " + timeout + ", result: " + result
                + ", message: " + message + ", extraData: " + extraData
                + ", isAlive : " + isAlive()+", isCancelStopCommand : "+ isCancelStopCommand);
        if (!isAlive()) {
            return;
        }
        mStatus = Status.PAUSE;
        onStop(result, isCancelStopCommand);
        mApi.stop(timeout);
        mStatus = Status.IDLE;

//        if (mComponentListener != null) {
//            mComponentListener.onFinish(result, message);
//        }

        asyncResponse(result, message, extraData);
    }

    private void asyncResponse(final int result, final String message, final String extraData) {
        Log.d(mName, "asyncResponse result: " + result
                + ", mComponentListener: " + (mComponentListener != null));
        if (mComponentListener != null) {
            DelayTask.submit(new Runnable() {
                @Override
                public void run() {
                    Log.d(mName, "asyncResponse delayTask result: " + result
                            + ", mComponentListener: " + (mComponentListener != null));
                    if (mComponentListener != null) {
                        mComponentListener.onFinish(result, message, extraData);
                    }
                }
            }, 0);
        }
    }

    public void setComponentListener(ComponentListener listener) {
        Log.v(mName, "setComponentListener listener: " + (listener != null));
        this.mComponentListener = listener;
    }

    public void setStatusListener(ComponentStatusListener listener) {
        Log.v(mName, "setStatusListener listener: " + (listener != null));
        this.mComponentStatusListener = listener;
    }

    @Override
    public final boolean isAlive() {
        return mStatus == Status.RUNNING;
    }

    protected abstract void onStart(String params);

    protected abstract void onUpdate(String intent, String params);

    protected abstract void onStop(int reason);

    protected void onStop(int reason,boolean isCancelStopCmd){
    }

    /**
     * 纠偏线速度
     */
    protected float rectifyLinearSpeed(float speed) {
        if (speed < ComponentDef.LINEAR_SPEED_MIN) {
            speed = ComponentDef.LINEAR_SPEED_MIN;
        } else if (speed > ComponentDef.LINEAR_SPEED_MAX) {
            speed = ComponentDef.LINEAR_SPEED_MAX;
        }
        return speed;
    }

    /**
     * 纠偏角速度
     */
    protected float rectifyAngularSpeed(float speed) {
        if (speed < ComponentDef.ANGULAR_SPEED_MIN) {
            speed = ComponentDef.ANGULAR_SPEED_MIN;
        } else if (speed > ComponentDef.ANGULAR_SPEED_MAX) {
            speed = ComponentDef.ANGULAR_SPEED_MAX;
        }
        return speed;
    }

}
