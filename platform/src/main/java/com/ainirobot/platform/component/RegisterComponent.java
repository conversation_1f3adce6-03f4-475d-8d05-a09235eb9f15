/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class RegisterComponent extends Component {

    private static final int STOP_TIMEOUT = 500;
    private static final int DEFAULT_PERSON_APPEAR_TIMEOUT = 2000;
    private static final int DEFAULT_RECOGNIZE_TIMEOUT = 800;

    private String mPersonName;
    private String mPhotoPath;
    private long mRecognizeTimeout;
    private long mPersonAppearTimeout;
    private String mRegisterId;
    private String welcomeContent;

    private PersonAppearComponent mComponent;

    public RegisterComponent(String name) {
        this(name, Looper.myLooper());
    }

    public RegisterComponent(String name, Looper looper) {
        super(name, looper);
        mComponent = new PersonAppearComponent(mName, mLooper);
    }

    @Override
    protected void onStart(String params) {
        Log.d(mName, "onStart params:" + params);
        try {
            JSONObject jsonObject = new JSONObject(params);
            mPersonName = jsonObject.optString(ComponentParams.Register.PARAM_PERSON_NAME, null);
            welcomeContent = jsonObject.optString(ComponentParams.Register.WELCOME_CONTENT, null);
            if (TextUtils.isEmpty(mPersonName)) {
                processRegisterStop(ComponentError.ERROR_PARAMS_GUEST_NAME_INVALID,null);
                return;
            }
            mPhotoPath = jsonObject.optString(ComponentParams.Register.PARAM_PHOTO_PATH, null);
            mPersonAppearTimeout = jsonObject.optLong(ComponentParams.Register.PARAM_PERSON_APPEAR_TIMEOUT,
                    DEFAULT_PERSON_APPEAR_TIMEOUT);
            mRecognizeTimeout = jsonObject.optLong(ComponentParams.Register.PARAM_RECOGNIZE_TIMEOUT,
                    DEFAULT_RECOGNIZE_TIMEOUT);
            if (checkFileExist(mPhotoPath)) {
                startRegister();
            } else {
                startRecognize();
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private boolean checkFileExist(String src) {
        if (TextUtils.isEmpty(src)) {
            return false;
        }
        File file = new File(src);
        return file.exists() && file.isFile();
    }

    private void startRecognize() {
        JSONObject json = new JSONObject();
        try {
            json.put(ComponentParams.PersonAppear.PARAM_TIMEOUT, mPersonAppearTimeout);
            json.put(ComponentParams.PersonAppear.PARAM_RECOGNIZE_TIMEOUT, mRecognizeTimeout);
            json.put(ComponentParams.PersonAppear.PARAM_IS_NEED_RECOGNIZE, true);
            json.put(ComponentParams.PersonAppear.PARAM_IS_NEED_DELETE_PIC, false);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        mComponent.setComponentListener(mComponentListener);
        mComponent.setStatusListener(mComponentStatusListener);
        mComponent.start(json.toString());
    }

    private Component.ComponentListener mComponentListener = new Component.ComponentListener() {
        @Override
        public void onFinish(int reason, String message, String extraData) {
            Log.d(mName, "RecognizeComponentListener onFinish reason: " + reason
                    + ", result: " + message);
            if (reason == ComponentResult.RESULT_SUCCESS) {
                Gson gson = new Gson();
                Person person = gson.fromJson(message, Person.class);
                if (person != null) {
                    mRegisterId = person.getUserId();
                    if (TextUtils.isEmpty(mRegisterId)) {
                        startRegister();
                    } else {
                        String role = person.getRole();
                        if (TextUtils.equals(role, PersonRole.getValue(PersonRole.ROLE_VIP))) {
                            processRegisterStop(ComponentError.ERROR_VIP_MODIFY_NAME_FORBID,message);
                        } else if (TextUtils.equals(role, PersonRole.getValue(PersonRole.ROLE_STAFF)) || person.isStaff()) {
                            processRegisterStop(ComponentError.ERROR_STAFF_MODIFY_NAME_FORBID,message);
                        } else {
                            startModifyName();
                        }
                    }
                }
            } else {
                processRegisterStop(reason,message);
            }
        }
    };

    private Component.ComponentStatusListener mComponentStatusListener = new ComponentStatusListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            if (status == ComponentStatus.STATUS_PICTURE_PATH) {
                Log.d(mName, "get photo path:" + data);
                mPhotoPath = data;
            }
        }
    };

    private void startRegister() {
        Log.d(mName, "startRegister:name=" + mName + ",photoPath=" + mPhotoPath);
        List<String> path = new ArrayList<>();
        path.add(mPhotoPath);
        mApi.remoteRegister(mPersonName, path, welcomeContent, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(mName, "startRegister result:" + message);
                try {
                    JSONObject jsonObject = new JSONObject(message);
                    int code = jsonObject.optInt("code", -1);
                    switch (code) {
                        case 0:
                            processRegisterStop(ComponentResult.RESULT_REGISTER_SUCCESS,message);
                            break;
                        case Definition.REMOTE_CODE_FACE_INVALID:
                            processRegisterStop(ComponentError.ERROR_REGISTER_PICTURE_INVALID,message);
                            break;
                        default:
                            processRegisterStop(ComponentError.ERROR_REGISTER_FAILED,message);
                            break;
                    }
                } catch (JSONException e) {
                    processRegisterStop(ComponentError.ERROR_REGISTER_FAILED,message);
                }
            }
        });
    }

    private void startModifyName() {
        mApi.remoteModifyDetectName(mRegisterId, mPersonName, welcomeContent, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(mName, "remoteModifyDetectName result:" + message);
                try {
                    JSONObject jsonObject = new JSONObject(message);
                    int code = jsonObject.optInt("code");
                    String data = jsonObject.optString("data");
                    if (code == 0 && !TextUtils.isEmpty(data)) {
                        processRegisterStop(ComponentResult.RESULT_MODIFY_NAME_SUCCESS, message);
                    } else {
                        processRegisterStop(ComponentError.ERROR_MODIFY_NAME_FAILED, message);
                    }
                } catch (JSONException e) {
                    processRegisterStop(ComponentError.ERROR_MODIFY_NAME_FAILED, message);
                }
            }
        });
    }

    @Override
    protected void onUpdate(String intent, String params) {
    }

    @Override
    protected void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        if (!TextUtils.isEmpty(mPhotoPath)) {
            File file = new File(mPhotoPath);
            if (file.exists() && file.isFile()) {
                file.delete();
            }
        }
        mComponent.stop(STOP_TIMEOUT);
    }

    private void processRegisterStop(int result,String message) {
        Log.d(mName, "processRegisterStop.result:" + result+",message:"+message);
        stop(STOP_TIMEOUT, result, message);
    }

    public static enum PersonRole {
        ROLE_STAFF("staff"),
        ROLE_ROOT("root"),
        ROLE_ADMIN("admin"),
        ROLE_VISITOR("visitor"),
        ROLE_TOURIST("tourist"),
        ROLE_STRANGER("stranger"),
        ROLE_VIP("vip"),
        ROLE_GUEST("guest");

        private String role;

        private PersonRole(String v) {
            this.role = v;
        }

        public static String getValue(PersonRole personRole) {
            return personRole.role;
        }
    }
}
