package com.ainirobot.platform.component;

import android.os.Looper;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.bean.UvcResultBean;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

/**
 * 托盘摄像头组件-提供的功能
 * 1、启动摄像头(包括单次识别和持续识别);
 * 2、停止持续识别;
 */
public class UvcCameraComponent extends Component {

    private Gson mGson = new Gson();
    private String startType = ComponentParams.UvcCamera.PARAM_START_CLASSIFY_SINGLE; // 默认单次识别

    public UvcCameraComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    protected void onStart(String params) {
        Log.i(mName, "onStart params:" + params);

        try {
            JSONObject json = new JSONObject(params);
            startType = json.optString(ComponentParams.UvcCamera.PARAM_START_CLASSIFY_TYPE, ComponentParams.UvcCamera.PARAM_START_CLASSIFY_SINGLE);
        } catch (JSONException e) {
            e.printStackTrace();
            startType = ComponentParams.UvcCamera.PARAM_START_CLASSIFY_SINGLE;
        }

        continueClassify();
    }

    private void continueClassify() {
        mApi.uvcContinueClassify(new CommandListener() {

            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(mName, "continueClassify onResult result : " + result + ",message : " + message + ", extraData : " + extraData);
                if (Definition.SUCCEED.equals(message)) {
                    //持续识别开启成功后
                } else {
                    processUvcCameraResult(ComponentError.ERROR_CAMERA_DATA_ERROR, message);
                }
            }


            @Override
            public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                super.onError(errorCode, errorString, extraData);
                Log.d(mName, "continueClassify onError errorCode : " + errorCode + ",errorString : " + errorString + ", extraData : " + extraData);
                processUvcCameraResult(ComponentError.ERROR_API_REQUEST_ERROR, errorString);
            }
        });

        mApi.registerStatusListener(Definition.STATUS_UVC_CONTINUE_CLASSIFY, uvcStatusListener);
    }

    private StatusListener uvcStatusListener = new StatusListener() {

        @Override
        public void onStatusUpdate(String type, String data) throws RemoteException {
            super.onStatusUpdate(type, data);
            Log.d(mName, "type : " + type + ", data : " + data);

            // 处理持续识别结果
            List<UvcResultBean> beans = null;
            if (!TextUtils.isEmpty(data)) {
                beans = mGson.fromJson(data, new TypeToken<List<UvcResultBean>>() {
                }.getType());
            }

            if (startType.equals(ComponentParams.UvcCamera.PARAM_START_CLASSIFY_SINGLE)) {// 处理单次识别状态
                if (beans != null && beans.size() > 0) {
                    processUvcCameraResult(ComponentResult.RESULT_SUCCESS, mGson.toJson(beans));
                } else {
                    processUvcCameraResult(ComponentError.ERROR_CAMERA_DATA_ERROR, "data is empty");
                }
            } else {// 处理持续识别状态
                if (beans != null && beans.size() > 0) {
                    processUvcCameraStatus(ComponentResult.RESULT_SUCCESS, mGson.toJson(beans));
                }
            }
        }
    };

    private void processUvcCameraResult(int reason, String message) {
        stop(STOP_TIMEOUT, reason, message);
    }

    private void processUvcCameraStatus(int status, String data) {
        updateStatus(status, data);
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.i(mName, "onUpdate params:" + params);
    }

    @Override
    protected void onStop(int reason) {
        if (ComponentParams.UvcCamera.PARAM_START_CLASSIFY_CONTINUE.equals(startType)) { // 停止持续识别
            mApi.uvcStopContinueClassify(null);
        }
        mApi.unregisterStatusListener(uvcStatusListener);
        uvcStatusListener = null;
        Log.i(mName, "onStop reason:" + reason + ", startType : " + startType);
    }

}
