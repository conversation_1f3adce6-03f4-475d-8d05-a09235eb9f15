package com.ainirobot.platform.component;

import android.os.Looper;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.light.ProTrayLedBean;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.List;

/**
 * Pro托盘三层指示灯的控制
 */
public class ProTrayLEDComponent extends Component {

    private Gson mGson = new Gson();

    public ProTrayLEDComponent(String name) {
        this(name, Looper.myLooper());
    }

    public ProTrayLEDComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    protected void onStart(String params) {
        Log.d(mName, "onStart params:" + params);
        String ledFloors = "";
        try {
            JSONObject json = new JSONObject(params);
            ledFloors = json.optString(ComponentParams.ProTrayLED.PARAM_TRAY_LED_FLOOR, "");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        handleTrayLedControl(ledFloors);
    }

    private void handleTrayLedControl(String ledFloors) {
        Type type = new TypeToken<List<ProTrayLedBean>>() {
        }.getType();
        List<ProTrayLedBean> ledBeanList = mGson.fromJson(ledFloors, type);
        Log.d(mName, "ledBeanList : "+ ledBeanList);

        if (ProductInfo.isSlimProduct()) {
            for (ProTrayLedBean bean : ledBeanList) {
                if (bean != null) {
                    if (bean.isSwitch()) {
//                        Log.d(mName, "zhujie===ledBeanList : "+ bean.getFloorId());
//                        setTrayLed(bean.getFloorId());
                        break;
                    }
                }
            }
        } else {
            for (ProTrayLedBean bean : ledBeanList) {
                if (bean != null) {
                    setTrayLight(bean.getFloorId(), bean.isSwitch());
                }
            }
        }
        processResult(ComponentResult.RESULT_SUCCESS,"");
    }

    private void setTrayLight(int floorId, boolean on) {
        switch (floorId){
            case 1:
                mApi.setTrayLightEffect(on ? Definition.TRAY_LIGHT_EFFECT_TRAY01ON : Definition.TRAY_LIGHT_EFFECT_TRAY01OFF, null);
                break;
            case 2:
                mApi.setTrayLightEffect(on ? Definition.TRAY_LIGHT_EFFECT_TRAY02ON : Definition.TRAY_LIGHT_EFFECT_TRAY02OFF, null);
                break;
            case 3:
                mApi.setTrayLightEffect(on ? Definition.TRAY_LIGHT_EFFECT_TRAY03ON : Definition.TRAY_LIGHT_EFFECT_TRAY03OFF, null);
                break;
        }
    }

    private void setTrayLed(int floorId) {
        switch (floorId) {
            case 1:
                mApi.setTrayLedEffect(Definition.TRAY_LED_EFFECT_TRAY1, null);
                break;
            case 2:
                mApi.setTrayLedEffect(Definition.TRAY_LED_EFFECT_TRAY2, null);
                break;
            default:
                mApi.setTrayLedEffect(Definition.LED_EFFECT_ALLOFF, null);
                break;
        }
    }

    private void processResult(int reason, String message) {
        stop(STOP_TIMEOUT, reason, message);
    }

    private void processUvcCameraStatus(int status, String data) {
        updateStatus(status, data);
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.i(mName, "onUpdate params:" + params);
    }

    @Override
    protected void onStop(int reason) {
        mGson = null;
        Log.i(mName, "onStop reason:" + reason);
    }

}
