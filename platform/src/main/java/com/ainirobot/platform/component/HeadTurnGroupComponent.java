package com.ainirobot.platform.component;/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.HeadTurnBean;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.List;

public class HeadTurnGroupComponent extends Component {

    private static final long STOP_TIMEOUT = 200;
    private static final int MSG_NEXT = 0x1;
    private static final long DEFAULT_INTERVAL_TIME = 0;

    private List<HeadTurnBean> mBeanList;
    private HeadTurnBean mBean;
    private HeadTurnComponent mComponent;
    private long mIntervalTime;
    protected Handler mHandler;
    private Gson mGson;

    public HeadTurnGroupComponent(String name) {
        this(name, Looper.myLooper());
    }

    public HeadTurnGroupComponent(String name, Looper looper) {
        super(name, looper);
        mHandler = new HeadTurnGroupHandler(this, mLooper);
        mComponent = new HeadTurnComponent(mName, mLooper);
        mGson = new Gson();
    }

    private static class HeadTurnGroupHandler extends Handler {

        private WeakReference<HeadTurnGroupComponent> mComponent;

        private HeadTurnGroupHandler(HeadTurnGroupComponent component, Looper looper) {
            super(looper);
            mComponent = new WeakReference<>(component);
        }

        @Override
        public void handleMessage(Message msg) {
            HeadTurnGroupComponent component = mComponent.get();
            if (component == null || !component.isAlive()) {
                return;
            }
            Log.d(mComponent.get().mName, "handleMessage what: " + msg.what);
            switch (msg.what) {
                case MSG_NEXT:
                    component.startTurnHeadNext();
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    protected void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            String headTurnStr = json.optString(ComponentParams.HeadTurnGroup.PARAM_HEAD_TURN_GROUP_BEAN,
                    "");
            if (TextUtils.isEmpty(headTurnStr)) {
                processResult(ComponentError.ERROR_PARAMS_HEAD_TURN_BEAN_GROUP_INVALID);
                return;
            }

            mBeanList = mGson.fromJson(headTurnStr, new TypeToken<List<HeadTurnBean>>(){}.getType());
            if (mBeanList == null || mBeanList.size() <= 0) {
                processResult(ComponentError.ERROR_PARAMS_HEAD_TURN_BEAN_GROUP_INVALID);
                return;
            }
            mIntervalTime = json.optLong(ComponentParams.HeadTurnGroup.PARAM_INTERVAL_TIME,
                    DEFAULT_INTERVAL_TIME);
            startTurnHeadNext();
        } catch (JSONException e) {
            processResult(ComponentError.ERROR_PARAMS_HEAD_TURN_BEAN_GROUP_INVALID);
        }
    }

    private void startTurnHeadNext() {
        if (mBeanList.size() <= 0) {
            processResult(ComponentResult.RESULT_HEAD_TURN_GROUP_SUCCESS);
            return;
        }
        mBean = mBeanList.remove(0);
        Log.d(mName, "startTurnHeadNext bean: " + mBean);
        mComponent.setComponentListener(mComponentListener);
        mComponent.setStatusListener(mComponentStatusListener);
        JSONObject json = new JSONObject();
        try {
            json.put(ComponentParams.HeadTurn.PARAM_HEAD_TURN_BEAN, mGson.toJson(mBean));
            mComponent.start(json.toString());
            mComponent.updateStatus(ComponentStatus.STATUS_HEAD_TURN_START, mGson.toJson(mBean));
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private ComponentListener mComponentListener = new ComponentListener() {
        @Override
        public void onFinish(int result, String message, String extraData) {
            if (isAlive()) {
                Log.d(mName, "startTurnHeadNext onFinish result: " + result
                        + ", message: " + message);
                processItemResult(result);
            }
        }
    };

    private ComponentStatusListener mComponentStatusListener = new ComponentStatusListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            if (isAlive()) {
                Log.d(mName, "startTurnHeadNext onStatusUpdate status: " + status
                        + ", data: " + data);
            }
        }
    };

    private void processItemResult(int result) {
        Log.d(mName, "processItemResult result: " + result + ", mBean: " + mBean.toString() + ", hashCode: " + this.hashCode());
        updateStatus(ComponentStatus.STATUS_HEAD_TURN_END, mGson.toJson(mBean));
        mHandler.sendEmptyMessageDelayed(MSG_NEXT, mIntervalTime);
    }

    private void processResult(int result) {
        Log.d(mName, "processResult result: " + result);
        stop(STOP_TIMEOUT, result, null);
    }

    @Override
    protected void onUpdate(String intent, String params) {
    }

    @Override
    protected void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        if (mComponent != null) {
            mComponent.setComponentListener(null);
            mComponent.setStatusListener(null);
            mComponent.stop(STOP_TIMEOUT);
        }
        mHandler.removeMessages(MSG_NEXT);
        mApi.moveHead(Definition.JSON_HEAD_RELATIVE, Definition.JSON_HEAD_RELATIVE,
                0, 0, null);
    }
}
