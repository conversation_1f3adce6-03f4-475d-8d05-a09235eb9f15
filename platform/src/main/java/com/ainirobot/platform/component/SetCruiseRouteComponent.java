package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.CruiseRouteBean;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;


/**
 * Created by Orion on 2019/5/9.
 */
public class SetCruiseRouteComponent extends Component {

    private static final int STOP_TIMEOUT = 500;
    private static final String DEFAULT_ROUTE_NAME = "CruiseRoute";

    private String mRouteName;
    private List<String> mRouteList;
    private Gson mGson;
    private JSONObject mParams;

    public SetCruiseRouteComponent(String name) {
        this(name, Looper.myLooper());
    }

    public SetCruiseRouteComponent(String name, Looper looper) {
        super(name, looper);
        mGson = new Gson();
    }

    @Override
    protected void onStart(String params) {
        try {
            mParams = new JSONObject(params);
            mRouteName = mParams.optString(ComponentParams.SetCruiseRoute.PARAM_CRUISE_ROUTE_NAME
                    , DEFAULT_ROUTE_NAME);
            String pointList = mParams.optString(ComponentParams.SetCruiseRoute.PARAM_CRUISE_ROUTE_LIST);
            if (!TextUtils.isEmpty(pointList)) {
                mRouteList = mGson.fromJson(pointList, new TypeToken<List<String>>() {
                }.getType());
            }
            if (mRouteList == null || mRouteList.size() <= 1) {
                processResult(ComponentError.ERROR_PARAMS_CRUISE_ROUTE_INVALID
                        , "route list invalid");
            } else {
                checkMap();
            }
        } catch (JSONException e) {
            e.printStackTrace();
            processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR, "json parse exception");
        }
    }

    private void checkMap() {
        mApi.getMapName(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(mName, "getMapName onResult : " + result + " || " + message);
                if (isCommResultOk(result, message)) {
                    setCruiseRoute();
                } else {
                    processResult(ComponentError.ERROR_NAVIGATION_NO_MAP
                            , "please create map first");
                }
            }
        });
    }

    private void setCruiseRoute() {
        CruiseRouteBean route = new CruiseRouteBean();
        route.setRouteName(mRouteName);
        route.setCoordinate(mGson.toJson(mRouteList));

        mApi.setNaviCruiseRoute(mGson.toJson(route), new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(mName, "setNaviCruiseRoute onResult : " + result + " : " + message);
                if (isCommResultOk(result, message)) {
                    processResult(ComponentResult.RESULT_SUCCESS, "set route success");
                } else {
                    processResult(ComponentError.ERROR_SET_CRUISE_ROUTE_FAILED
                            , "set route failed");
                }
            }
        });
    }

    private boolean isCommResultOk(int result, String message) {
        return (result == Definition.RESULT_OK && !TextUtils.isEmpty(message)
                && !"timeout".equals(message) && !"failed".equals(message));
    }

    @Override
    protected void onUpdate(String intent, String params) {
    }

    @Override
    protected void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
    }

    private void processResult(int result, String message) {
        if (!isAlive())
            return;
        stop(STOP_TIMEOUT, result, message);
    }

}
