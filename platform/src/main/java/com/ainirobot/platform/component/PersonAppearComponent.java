/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.component;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.person.PersonApi;
import com.ainirobot.coreservice.client.person.PersonListener;
import com.ainirobot.coreservice.client.person.PersonUtils;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentParams.PersonAppear;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.google.gson.Gson;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.List;

/**
 * 人脸识别功能组件，找人+识别
 * 默认只本地找人不做云端识别，云端识别参数{@code PersonAppear.PARAM_IS_NEED_RECOGNIZE}
 */
public class PersonAppearComponent extends Component {

    private static final int MSG_UPDATE_BEST_PERSON = 0x01;
    private static final int MSG_PERSON_APPEAR_TIMEOUT = 0x02;
    private static final int MSG_CONTINUE_SEARCH_FACE = 0x03;
    private static final double DEFAULT_MAX_DISTANCE = 3;
    private static final double DEFAULT_MAX_FACE_ANGLE_X = 60;
    private static final long INVALID_TIME = Long.MAX_VALUE;
    private static final long DEFAULT_INCOMPLETE_FACE_CACHE_TIMEOUT = 3000;
    private static final long DEFAULT_APPEAR_TIMEOUT = 7000;
    private static final long DEFAULT_RECOGNIZE_TIMEOUT = 2000;
    private static final long STOP_TIMEOUT = 100;

    private AppearHandler mHandler;
    private int mPersonId;
    private String mPersonName;
    private double mMaxDistance;
    private double mMaxFaceAngleX;
    private boolean mIsNeedInCompleteFace;
    private boolean mIsNeedBody;
    private Person mCurrentPerson;
    private long mIncompleteFaceCacheTime;
    private long mInCompleteFaceCacheTimer = INVALID_TIME;
    private boolean mIsNeedRecognize;
    private boolean mIsRecognizeNeedDeletePic;
    private long mRecognizeTimeout;
    private long mAppearTimeout;
    private AppearMode mCurrentMode;
    private AppearState mCurrentState;
    private RecognizeComponent mRecognizeComponent;

    public PersonAppearComponent(String name) {
        this(name, Looper.myLooper());
    }

    public PersonAppearComponent(String name, Looper looper) {
        super(name, looper);
        mHandler = new AppearHandler(this, mLooper);
        mRecognizeComponent = new RecognizeComponent(mName, mLooper);
    }

    private enum AppearMode {
        ANY, SPECIAL_ID, SPECIAL_NAME
    }

    private enum AppearState {
        IDLE, RECOGNIZE
    }

    private static class AppearHandler extends Handler {

        private WeakReference<PersonAppearComponent> mComponent;

        private AppearHandler(PersonAppearComponent component, Looper looper) {
            super(looper);
            mComponent = new WeakReference<>(component);
        }

        @Override
        public void handleMessage(Message msg) {
            PersonAppearComponent component = mComponent.get();
            if (component == null || !component.isAlive()) {
                return;
            }
            switch (msg.what) {
                case MSG_UPDATE_BEST_PERSON:
                    Person bestPerson = (Person) msg.obj;
                    switch (component.mCurrentMode) {
                        case SPECIAL_NAME:
                            if (component.mCurrentState == AppearState.IDLE) {
                                component.mCurrentState = AppearState.RECOGNIZE;
                                component.startRecognize(bestPerson.getId());
                            }
                            break;
                        case SPECIAL_ID:
                            component.handleDetectResult(bestPerson);
                            break;
                        case ANY:
                            if (PersonUtils.isCompleteFace(bestPerson) || (PersonUtils.isBody(bestPerson) && component.mIsNeedBody)) {
                                component.handleDetectResult(bestPerson);
                            } else {
                                component.handleNoTrackDetectResult(bestPerson);
                            }
                            break;
                        default:
                            break;
                    }
                    break;
                case MSG_PERSON_APPEAR_TIMEOUT:
                    component.processAppearResult(ComponentResult.RESULT_TIMEOUT);
                    break;
                case MSG_CONTINUE_SEARCH_FACE:
                    component.continueSearchFace();
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            mPersonId = json.optInt(PersonAppear.PARAM_PERSON_ID, -1);
            mPersonName = json.optString(PersonAppear.PARAM_PERSON_NAME, "");
            if (!TextUtils.isEmpty(mPersonName)) {
                mCurrentMode = AppearMode.SPECIAL_NAME;
            } else if (mPersonId >= 0) {
                mCurrentMode = AppearMode.SPECIAL_ID;
            } else {
                mCurrentMode = AppearMode.ANY;
            }
            mIsNeedInCompleteFace = json.optBoolean(PersonAppear.PARAM_IS_NEED_INCOMPLETE_FACE,
                    false);
            mIncompleteFaceCacheTime = json.optLong(PersonAppear.PARAM_INCOMPLETE_FACE_CACHE_TIME,
                    DEFAULT_INCOMPLETE_FACE_CACHE_TIMEOUT);
            mMaxDistance = json.optDouble(PersonAppear.PARAM_MAX_DISTANCE,
                    DEFAULT_MAX_DISTANCE);
            mMaxFaceAngleX = json.optDouble(PersonAppear.PARAM_MAX_FACE_ANGLE_X,
                    DEFAULT_MAX_FACE_ANGLE_X);
            mIsNeedBody = json.optBoolean(PersonAppear.PARAM_IS_NEED_BODY, false);
            mIsNeedRecognize = json.optBoolean(PersonAppear.PARAM_IS_NEED_RECOGNIZE, false);
            mIsRecognizeNeedDeletePic = json.optBoolean(PersonAppear.PARAM_IS_NEED_DELETE_PIC, true);
            mRecognizeTimeout = json.optLong(PersonAppear.PARAM_RECOGNIZE_TIMEOUT,
                    DEFAULT_RECOGNIZE_TIMEOUT);
            mAppearTimeout = json.optLong(PersonAppear.PARAM_TIMEOUT, DEFAULT_APPEAR_TIMEOUT);
            if (mAppearTimeout <= 0) {
                mAppearTimeout = DEFAULT_APPEAR_TIMEOUT;
            }
        } catch (JSONException e) {
            mPersonId = -1;
            mPersonName = null;
            mCurrentMode = AppearMode.ANY;
            mIsNeedInCompleteFace = false;
            mIncompleteFaceCacheTime = DEFAULT_INCOMPLETE_FACE_CACHE_TIMEOUT;
            mMaxDistance = DEFAULT_MAX_DISTANCE;
            mMaxFaceAngleX = DEFAULT_MAX_FACE_ANGLE_X;
            mIsNeedBody = false;
            mIsNeedRecognize = false;
            mIsRecognizeNeedDeletePic = true;
            mRecognizeTimeout = DEFAULT_RECOGNIZE_TIMEOUT;
            mAppearTimeout = DEFAULT_APPEAR_TIMEOUT;
        }
        Log.d(mName, "onStart currentMode: " + mCurrentMode);
        mCurrentState = AppearState.IDLE;
        startAppearTimer();
        mCurrentPerson = null;
        mInCompleteFaceCacheTimer = INVALID_TIME;
        initPersonListener();
    }

    private void initPersonListener() {
        boolean result = PersonApi.getInstance().registerPersonListener(mPersonListener);
        if (!result) {
            processAppearResult(ComponentError.ERROR_OPEN_PERSON_DETECT_FAILED);
        }
    }

    private PersonListener mPersonListener = new PersonListener() {
        @Override
        public void personChanged() {

            Person bestPerson = null;
            switch (mCurrentMode) {
                case SPECIAL_NAME:
                    bestPerson = PersonUtils.getBestFace(PersonApi.getInstance().getCompleteFaceList(),
                            mMaxDistance, mMaxFaceAngleX);
                    break;
                case SPECIAL_ID:
                    bestPerson = PersonUtils.getSpecialPerson(PersonApi.getInstance()
                            .getCompleteFaceList(mMaxDistance), mPersonId);
                    break;
                case ANY:
                    List<Person> faceList;
                    if (mIsNeedInCompleteFace) {
                        faceList = PersonApi.getInstance().getAllFaceList();
                    } else {
                        faceList = PersonApi.getInstance().getCompleteFaceList();
                    }
                    bestPerson = PersonUtils.getBestFace(faceList, mMaxDistance,
                            mMaxFaceAngleX);
                    if (bestPerson == null && mIsNeedBody) {
                        bestPerson = PersonUtils.getBestBody(PersonApi.getInstance().getAllBodyList(),
                                mMaxDistance);
                    }
                    break;
                default:
                    break;
            }

            if (bestPerson != null) {
                mHandler.obtainMessage(MSG_UPDATE_BEST_PERSON, bestPerson).sendToTarget();
            }
        }
    };

    private void handleDetectResult(Person person) {
        Log.d(mName, "handle detect result person: " + person);
        if (mCurrentPerson == null
                || person.getId() != mCurrentPerson.getId()) {
            mCurrentPerson = person;
            if (mCurrentState == AppearState.IDLE) {
                if (mIsNeedRecognize) {
                    mCurrentState = AppearState.RECOGNIZE;
                    startRecognize(person.getId());
                } else {
                    processAppearSuccess(person);
                }
            } else {
                Log.e(mName, "handleDetectResult state error: " + mCurrentState);
            }
        }
    }

    private void handleNoTrackDetectResult(Person person) {
        Log.i(mName, "handleNoTrackDetectResult person: " + person);
        startAppearTimer();
        if (mCurrentPerson == null) {
            mCurrentPerson = person;
            mInCompleteFaceCacheTimer = System.currentTimeMillis();
        } else if (!isNotNeedTrackedDetectTimeout()) {
            mCurrentPerson = person;
        } else {
            mCurrentPerson = person;
            mInCompleteFaceCacheTimer = INVALID_TIME;
            processAppearSuccess(person);
        }
    }

    private void startRecognize(int personId) {
        JSONObject json = new JSONObject();
        try {
            json.put(ComponentParams.Recognize.PARAM_PERSON_ID, personId);
            json.put(ComponentParams.Recognize.PARAM_IS_NEED_DELETE_PIC, mIsRecognizeNeedDeletePic);
            json.put(ComponentParams.Recognize.PARAM_TIMEOUT, mRecognizeTimeout);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        mRecognizeComponent.setComponentListener(mRecognizeComponentListener);
        mRecognizeComponent.setStatusListener(mRecognizeStatusListener);
        mRecognizeComponent.start(json.toString());
    }

    private Component.ComponentListener mRecognizeComponentListener = new Component.ComponentListener() {
        @Override
        public void onFinish(int result, String message, String extraData) {
            if (!isAlive()) {
                return;
            }
            Log.d(mName, "RecognizeComponentListener onFinish result: " + result
                    + ", message: " + message);
            Person person;
            Gson gson = new Gson();
            mCurrentState = AppearState.IDLE;
            switch (result) {
                case ComponentResult.RESULT_SUCCESS:
                    person = gson.fromJson(message, Person.class);
                    if (mCurrentMode == AppearMode.SPECIAL_NAME) {
                        if (person != null && mPersonName.equals(person.getName())) {
                            mCurrentPerson = person;
                            processAppearSuccess(person);
                        }
                    } else {
                        mCurrentPerson = person;
                        processAppearSuccess(person);
                    }
                    break;
                default:
                    if (mCurrentMode != AppearMode.SPECIAL_NAME) {
                        person = gson.fromJson(message, Person.class);
                        processAppearSuccess(person);
                    }
                    break;
            }
        }
    };

    private Component.ComponentStatusListener mRecognizeStatusListener = new ComponentStatusListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            Log.d(mName, "RecognizeStatusListener onStatusUpdate status: " + status
                    + ", data: " + data);
            if (PersonAppearComponent.this.isAlive()) {
                PersonAppearComponent.this.processAppearStatus(status, data);
            }
        }
    };

    private void stopRecognize() {
        if (mRecognizeComponent != null) {
            mRecognizeComponent.setComponentListener(null);
            mRecognizeComponent.stop(STOP_TIMEOUT);
        }
    }

    private void processAppearSuccess(Person person) {
        double personAngle = person.getAngleInView();
        double personDistance = person.getDistance();
        if (ProductInfo.isMiniProduct()) {
            processAppearResult(ComponentResult.RESULT_SUCCESS);
        } else {
            mApi.hasObstacleInArea(personAngle - 20, personAngle + 20,
                    personDistance - 0.5, personDistance + 0.5, new CommandListener() {
                        @Override
                        public void onResult(int result, String message) {
                            if (!isAlive()) {
                                return;
                            }
                            Log.i(mName, "PersonAppear  hasObstacleInArea result: " + result + " message= " + message);
                            if ("true".equals(message)) {
                                processAppearResult(ComponentResult.RESULT_SUCCESS);
                            } else {
                                processAppearStatus(ComponentStatus.STATUS_HAS_FACE_NO_OBSTACLE, "detect face but no obstacle");
                            }
                        }
                    });
        }
    }
    
    private void processAppearResult(int result) {
        Log.d(mName, "processAppearResult result: " + result
                + ", currentPerson: " + mCurrentPerson);
        destroyAppearTimer();
        stop(300, result, mCurrentPerson == null ? null : mCurrentPerson.toGson());
    }

    private void processAppearStatus(int status, String data) {
        Log.d(mName, "processAppearStatus status: " + status
                + ", data: " + data);
        updateStatus(status, data);
    }

    private void startAppearTimer() {
//        Log.v(mName, "startAppearTimer");
        mHandler.removeMessages(MSG_PERSON_APPEAR_TIMEOUT);
        mHandler.sendEmptyMessageDelayed(MSG_PERSON_APPEAR_TIMEOUT, mAppearTimeout);
    }


    private void destroyAppearTimer() {
        Log.d(mName, "destroyAppearTimer");
        mHandler.removeMessages(MSG_PERSON_APPEAR_TIMEOUT);
    }

    private boolean isNotNeedTrackedDetectTimeout() {
        long currentTime = System.currentTimeMillis();
        return mInCompleteFaceCacheTimer != INVALID_TIME
                && (currentTime - mInCompleteFaceCacheTimer > mIncompleteFaceCacheTime);
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.d(mName, "onUpdate params: " + params);
        switch (intent) {
            case PersonAppear.UPDATE_CONTINUE_SEARCH_FACE:
                mHandler.sendEmptyMessage(MSG_CONTINUE_SEARCH_FACE);
                break;
            default:
                break;
        }
    }

    private void continueSearchFace() {
        if (mCurrentState == AppearState.IDLE || mCurrentState == AppearState.RECOGNIZE) {
            startAppearTimer();
        }
    }

    @Override
    public void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        PersonApi.getInstance().unregisterPersonListener(mPersonListener);
        mHandler.removeMessages(MSG_CONTINUE_SEARCH_FACE);
        mHandler.removeMessages(MSG_UPDATE_BEST_PERSON);
        mHandler.removeMessages(MSG_PERSON_APPEAR_TIMEOUT);
        stopRecognize();
    }
}
