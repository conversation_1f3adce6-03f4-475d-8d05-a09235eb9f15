/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.component;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.person.PersonApi;
import com.ainirobot.coreservice.client.person.PersonListener;
import com.ainirobot.coreservice.client.person.PersonUtils;
import com.ainirobot.platform.bi.wrapper.utils.ShareDataUtils;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentParams.PersonDisappear;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.google.gson.Gson;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

/**
 * 人丢失检测组件
 */
public class PersonDisappearComponent extends Component {

    private static final int MSG_UPDATE_BEST_PERSON = 0x01;
    private static final int MSG_PERSON_APPEAR_TIMEOUT = 0x02;
    private static final int MSG_CONTINUE_SEARCH_FACE = 0x03;
    private static final int MSG_LOST_TIMEOUT = 0x04;
    private static final int MSG_OBSTACLE_DISAPPEAR_TIMEOUT = 0x05;
    private static final double DEFAULT_MAX_DISTANCE = 3;
    private static final double DEFAULT_MAX_FACE_ANGLE_X = 60;
    private static final long DEFAULT_LOST_TIMEOUT = 0;
    private static final long DEFAULT_DISAPPEAR_TIMEOUT = 7000;
    private static final long DEFAULT_RECOGNIZE_TIMEOUT = 2000;

    private DisappearHandler mHandler;
    private int mPersonId;
    private String mPersonName;
    private double mMaxDistance;
    private double mMaxFaceAngleX;
    private boolean mIsNeedInCompleteFace;
    private boolean mIsNeedBody;
    private Person mCurrentPerson;
    private long mLostTimeout;
    private long mDisappearTimeout;
    private DisappearMode mCurrentMode;
    private DisappearState mCurrentState;
    private RecognizeComponent mRecognizeComponent;
    private Timer obstacleDetectTimer;

    public PersonDisappearComponent(String name) {
        this(name, Looper.myLooper());
    }

    public PersonDisappearComponent(String name, Looper looper) {
        super(name, looper);
        mHandler = new DisappearHandler(this, mLooper);
        mRecognizeComponent = new RecognizeComponent(mName, mLooper);
        obstacleDetectTimer = new Timer();
    }

    public void startObstacleDetectTimer() {
        stopObstacleDetectTimer();
        obstacleDetectTimer = new Timer();
        obstacleDetectTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                obstacleDetect();
            }
        }, 0, 1000);  // 延迟0毫秒开始，每1000毫秒（1秒）执行一次
    }

    public void stopObstacleDetectTimer() {
        if (obstacleDetectTimer != null) {
            obstacleDetectTimer.cancel();
            obstacleDetectTimer = null;
        }
    }

    private void obstacleDetect() {
        mApi.hasObstacleInArea(-30, 30,
                0.1, 1, new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        if (!isAlive()) {
                            return;
                        }
                        Log.i(mName, "hasObstacleInArea result: " + result + " message= " + message);
                        if ("true".equals(message)) {
                            startObstacleDisappearTimer();
                        }
                    }
                });
    }

    private enum DisappearMode {
        ANY, SPECIAL_ID, SPECIAL_NAME
    }

    private enum DisappearState {
        IDLE, RECOGNIZE
    }

    private static class DisappearHandler extends Handler {

        private WeakReference<PersonDisappearComponent> mComponent;

        private DisappearHandler(PersonDisappearComponent component, Looper looper) {
            super(looper);
            mComponent = new WeakReference<>(component);
        }

        @Override
        public void handleMessage(Message msg) {
            PersonDisappearComponent component = mComponent.get();
            if (component == null || !component.isAlive()) {
                return;
            }
            switch (msg.what) {
                case MSG_UPDATE_BEST_PERSON:
                    Person bestPerson = (Person) msg.obj;
                    switch (component.mCurrentMode) {
                        case SPECIAL_NAME:
                            if (component.mCurrentState == DisappearState.IDLE) {
                                component.mCurrentState = DisappearState.RECOGNIZE;
                                component.startRecognize(bestPerson.getId());
                            }
                            break;
                        case SPECIAL_ID:
                        case ANY:
                            component.startDisappearTimer();
                            component.startLostTimer();
                            break;
                        default:
                            break;
                    }
                    break;
                case MSG_LOST_TIMEOUT:
                    component.updateStatus(ComponentStatus.STATUS_PERSON_LOST_TIMEOUT, "");
                    break;
                case MSG_PERSON_APPEAR_TIMEOUT:
                case MSG_OBSTACLE_DISAPPEAR_TIMEOUT:
                    component.processDisappearResult(ComponentResult.RESULT_TIMEOUT);
                    break;
                case MSG_CONTINUE_SEARCH_FACE:
                    component.continueSearchFace();
                default:
                    break;
            }
        }
    }

    @Override
    public void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            mPersonId = json.optInt(PersonDisappear.PARAM_PERSON_ID, -1);
            mPersonName = json.optString(PersonDisappear.PARAM_PERSON_NAME, "");
            if (!TextUtils.isEmpty(mPersonName)) {
                mCurrentMode = DisappearMode.SPECIAL_NAME;
            } else if (mPersonId >= 0) {
                mCurrentMode = DisappearMode.SPECIAL_ID;
            } else {
                mCurrentMode = DisappearMode.ANY;
            }
            mIsNeedInCompleteFace = json.optBoolean(PersonDisappear.PARAM_IS_NEED_INCOMPLETE_FACE,
                    false);
            mMaxDistance = json.optDouble(PersonDisappear.PARAM_MAX_DISTANCE,
                    DEFAULT_MAX_DISTANCE);
            mMaxFaceAngleX = json.optDouble(PersonDisappear.PARAM_MAX_FACE_ANGLE_X,
                    DEFAULT_MAX_FACE_ANGLE_X);
            mIsNeedBody = json.optBoolean(PersonDisappear.PARAM_IS_NEED_BODY, false);
            mLostTimeout = json.optLong(PersonDisappear.PARAM_LOST_TIMEOUT, DEFAULT_LOST_TIMEOUT);
            mDisappearTimeout = json.optLong(PersonDisappear.PARAM_DISAPPEAR_TIMEOUT, DEFAULT_DISAPPEAR_TIMEOUT);
            if (mDisappearTimeout <= 0) {
                mDisappearTimeout = DEFAULT_DISAPPEAR_TIMEOUT;
            }
            if (mLostTimeout <= 0 || mLostTimeout >= mDisappearTimeout) {
                mLostTimeout = DEFAULT_LOST_TIMEOUT;
            }
        } catch (JSONException e) {
            mPersonId = -1;
            mPersonName = null;
            mCurrentMode = DisappearMode.ANY;
            mIsNeedInCompleteFace = false;
            mMaxDistance = DEFAULT_MAX_DISTANCE;
            mMaxFaceAngleX = DEFAULT_MAX_FACE_ANGLE_X;
            mIsNeedBody = false;
            mLostTimeout = DEFAULT_LOST_TIMEOUT;
            mDisappearTimeout = DEFAULT_DISAPPEAR_TIMEOUT;
        }
        Log.d(mName, "onStart currentMode: " + mCurrentMode);
        mCurrentState = DisappearState.IDLE;
        ShareDataUtils.clearMaxPersonNum();
        startLostTimer();
        startDisappearTimer();
        startObstacleDisappearTimer();
        startObstacleDetectTimer();
        mCurrentPerson = null;
        initPersonListener();
    }

    private void initPersonListener() {
        boolean result = PersonApi.getInstance().registerPersonListener(mPersonListener);
        if (!result) {
            processDisappearResult(ComponentError.ERROR_OPEN_PERSON_DETECT_FAILED);
        }
    }

    private PersonListener mPersonListener = new PersonListener() {
        @Override
        public void personChanged() {

            Person bestPerson = null;
            switch (mCurrentMode) {
                case SPECIAL_NAME:
                    bestPerson = PersonUtils.getBestFace(PersonApi.getInstance().getCompleteFaceList(),
                            mMaxDistance, mMaxFaceAngleX);
                    break;
                case SPECIAL_ID:
                    bestPerson = PersonUtils.getSpecialPerson(PersonApi.getInstance()
                            .getCompleteFaceList(mMaxDistance), mPersonId);
                    break;
                case ANY:
                    List<Person> faceList;
                    if (mIsNeedInCompleteFace) {
                        faceList = PersonApi.getInstance().getAllFaceList();
                    } else {
                        faceList = PersonApi.getInstance().getCompleteFaceList();
                    }
                    bestPerson = PersonUtils.getBestFace(faceList, mMaxDistance,
                            mMaxFaceAngleX);
                    if (bestPerson == null && mIsNeedBody) {
                        bestPerson = PersonUtils.getBestBody(PersonApi.getInstance().getAllBodyList(),
                                mMaxDistance);
                    }
                    break;
                default:
                    break;
            }

            List<Person> personList = PersonApi.getInstance().getAllFaceList();
            ShareDataUtils.setMaxPersonNum(personList == null ? 0 : personList.size());

            if (bestPerson != null) {
                mHandler.obtainMessage(MSG_UPDATE_BEST_PERSON, bestPerson).sendToTarget();
            }
        }
    };

    private void startRecognize(int personId) {
        JSONObject json = new JSONObject();
        try {
            json.put(ComponentParams.Recognize.PARAM_PERSON_ID, personId);
            json.put(ComponentParams.Recognize.PARAM_TIMEOUT, DEFAULT_RECOGNIZE_TIMEOUT);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        mRecognizeComponent.setComponentListener(mRecognizeComponentListener);
        mRecognizeComponent.start(json.toString());
    }

    private Component.ComponentListener mRecognizeComponentListener = new Component.ComponentListener() {
        @Override
        public void onFinish(int reason, String result, String extraData) {
            Log.d(mName, "RecognizeComponentListener onFinish reason: " + reason
                    + ", result: " + result);
            Person person;
            mCurrentState = DisappearState.IDLE;
            if (!TextUtils.isEmpty(result)) {
                if (mCurrentMode == DisappearMode.SPECIAL_NAME) {
                    Gson gson = new Gson();
                    person = gson.fromJson(result, Person.class);
                    if (person != null && mPersonName.equals(person.getName())) {
                        startDisappearTimer();
                        startLostTimer();
                    }
                }
            }
        }
    };

    private void stopRecognize() {
        if (mRecognizeComponent != null) {
            mRecognizeComponent.setComponentListener(null);
            mRecognizeComponent.stop(100);
        }
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.d(mName, "onUpdate params: " + params);
        switch (intent) {
            case PersonDisappear.UPDATE_CONTINUE_SEARCH_FACE:
                mHandler.sendEmptyMessage(MSG_CONTINUE_SEARCH_FACE);
                break;
            default:
                break;
        }
    }

    private void continueSearchFace() {
        if (mCurrentState == DisappearState.IDLE || mCurrentState == DisappearState.RECOGNIZE) {
            startDisappearTimer();
            startLostTimer();
        }
    }

    @Override
    public void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        stopRecognize();
        PersonApi.getInstance().unregisterPersonListener(mPersonListener);
        destroyDisappearTimer();
        destroyObstacleDisappearTimer();
        stopObstacleDetectTimer();
        destroyLostTimer();
        mHandler.removeMessages(MSG_LOST_TIMEOUT);
        mHandler.removeMessages(MSG_PERSON_APPEAR_TIMEOUT);
        mHandler.removeMessages(MSG_UPDATE_BEST_PERSON);
        mHandler.removeMessages(MSG_CONTINUE_SEARCH_FACE);
    }

    private void processDisappearResult(int result) {
        Log.d(mName, "processDisappearResult");
        destroyDisappearTimer();
        destroyObstacleDisappearTimer();
        stopObstacleDetectTimer();
        stop(300, result,
                mCurrentPerson == null ? null : mCurrentPerson.toGson());
    }

    private void startDisappearTimer() {
//        Log.v(mName, "startDisappearTimer mDisappearTimeout: " + mDisappearTimeout);
        mHandler.removeMessages(MSG_PERSON_APPEAR_TIMEOUT);
        mHandler.sendEmptyMessageDelayed(MSG_PERSON_APPEAR_TIMEOUT, mDisappearTimeout);
    }

    private void startObstacleDisappearTimer() {
        mHandler.removeMessages(MSG_OBSTACLE_DISAPPEAR_TIMEOUT);
        mHandler.sendEmptyMessageDelayed(MSG_OBSTACLE_DISAPPEAR_TIMEOUT, mDisappearTimeout);
    }

    private void destroyDisappearTimer() {
        Log.d(mName, "destroyDisappearTimer");
        mHandler.removeMessages(MSG_PERSON_APPEAR_TIMEOUT);
    }
    private void destroyObstacleDisappearTimer() {
        Log.d(mName, "destroyObstacleDisappearTimer");
        mHandler.removeMessages(MSG_OBSTACLE_DISAPPEAR_TIMEOUT);
    }

    private void startLostTimer() {
//        Log.v(mName, "startLostTimer mLostTimeout: " + mLostTimeout);
        mHandler.removeMessages(MSG_LOST_TIMEOUT);
        if (mLostTimeout <= 0) {
            return;
        }
        mHandler.sendEmptyMessageDelayed(MSG_LOST_TIMEOUT, mLostTimeout);
    }

    private void destroyLostTimer() {
        Log.d(mName, "destroyLostTimer");
        mHandler.removeMessages(MSG_LOST_TIMEOUT);
    }
}
