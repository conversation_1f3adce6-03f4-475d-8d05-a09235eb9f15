/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.platform.component;

import static com.ainirobot.platform.component.definition.ComponentResult.RESULT_NAVIGATION_ARRIVED;

import android.os.Looper;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.platform.bean.MultiFloorPoseInfo;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.ainirobot.platform.utils.GsonUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class NavigationAdvanceComponent extends Component {
    private String mNavPlace;
    private int mTargetFloor;
    private String mTargetMapName;
    private String mTargetAvailableElevator;
    private float mLinearSpeed;
    private float mAngularSpeed;
    private List<MultiFloorInfo> multiFloorInfoList = new ArrayList<>();
    private final String START_TAG = "delayStartTimer";
    private volatile State mCurrentState = State.IDLE;

    private enum State {
        IDLE,
        CHECKING_GATE_PASSING,
        NAVIGATING_GATE,
        CHECKING_ELEVATOR_GATE,
        NAVIGATING_ELEVATOR,
        NAVIGATING

    }

    public NavigationAdvanceComponent(String name, Looper looper) {
        super(name, looper);
    }

    public NavigationAdvanceComponent(String mName) {
        super(mName, Looper.myLooper());
    }

    @Override
    protected void onStart(String params) {
        Log.i(mName, "onStart : " + params);
        mCurrentState = State.IDLE;
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(params);
            mNavPlace = jsonObject.optString(ComponentParams.Navigation.PARAM_DESTINATION);
            mTargetFloor = jsonObject.optInt(ComponentParams.Navigation.PARAM_TARGET_FLOOR);
            mTargetMapName = jsonObject.optString(ComponentParams.Navigation.PARAM_TARGET_MAP_NAME);
            mTargetAvailableElevator = jsonObject.optString(ComponentParams.Navigation.PARAM_TARGET_AVAILABLE_ELEVATOR);
            mLinearSpeed = (float) jsonObject.optDouble(ComponentParams.Navigation.PARAM_LINEAR_SPEED
                    , SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED);
            mLinearSpeed = rectifyLinearSpeed(mLinearSpeed);
            mAngularSpeed = (float) jsonObject.optDouble(ComponentParams.Navigation.PARAM_ANGULAR_SPEED
                    , SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED);
            mAngularSpeed = rectifyAngularSpeed(mAngularSpeed);

            isEstimate();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void isEstimate() {
        mApi.isRobotEstimate(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(mName, "isEstimate result:" + result + " message:" + message);
                if (result == Definition.RESULT_OK && Definition.RESULT_TRUE.equals(message)) {
                    getMultiFloorData();
                } else {
                    stop(ComponentError.ERROR_NOT_ESTIMATE, "navigation not estimate", "");
                }
            }
        });
    }

    private void getMultiFloorData() {
        mApi.getMultiFloorConfigAndPose(new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Type type = new TypeToken<ArrayList<MultiFloorInfo>>() {}.getType();
                try {
                    Log.d(mName, "getMultiFloorConfigAndPose result: " + result + ", message: " + message);
                    multiFloorInfoList = new Gson().fromJson(message, type);

                    List<MultiFloorPoseInfo> targetDestinations = new ArrayList<>();
                    targetDestinations.add(new MultiFloorPoseInfo(mNavPlace, mTargetFloor));
                    addElevatorGateDestination(targetDestinations);
                } catch (Exception e) {
                    Log.e(mName, "Exception in getMultiFloorConfigAndPose: ", e);
                }
            }
        });
    }

    private boolean robotIsInElevator() {
        String alreadyInElevator = mApi.isAlreadyInElevator();
        Log.d(mName, "checkIsInElevator: " + alreadyInElevator);
        Map<String, Object> map = GsonUtil.fromJson(alreadyInElevator, Map.class);
        Double resultDouble = (Double) map.get(Definition.JSON_TASK_EXEC_RESULT);
        int result = resultDouble.intValue();
        return result == Definition.RESULT_ROBOT_IN_ELEVATOR;
    }

    private void addElevatorGateDestination(final List<MultiFloorPoseInfo> targetDestinations) {
        mApi.getMapName(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(mName, "getMapName onResult : " + result + " || " + message);

                if (!message.equals(mTargetMapName)) {
                    int currentFloorIndex = getMapFloorIndex(message);
                    // 不同楼层
                    Log.d(mName, "Selected pose is on a different floor, currentFloorIndex: " + currentFloorIndex);
                    String elevatorGate = getElevatorGate();
                    targetDestinations.add(0, new MultiFloorPoseInfo(elevatorGate, mTargetFloor, true));

                    if (!robotIsInElevator()) {
                        targetDestinations.add(0, new MultiFloorPoseInfo(elevatorGate, currentFloorIndex, true));
                    }
                }
                getGatePassingRoute(targetDestinations);
            }
        });
    }

    private int getMapFloorIndex(String mapName) {
        for (MultiFloorInfo floorInfo : multiFloorInfoList) {
            if (mapName.equals(floorInfo.getMapName())) {
                return floorInfo.getFloorIndex();
            }
        }
        Log.d(mName, mapName + " getMapFloorIndex: not found");
        return -1;
    }

    private String getElevatorGate() {
        String elevatorGate = mTargetAvailableElevator + '-' +Definition.ELEVATOR_ENTER_POSE;
        return elevatorGate;
    }

    private void getGatePassingRoute(final List<MultiFloorPoseInfo> targetDestinations) {
        Log.d(mName, "checkGatePassingNew: " + targetDestinations);
        updateStatus(ComponentStatus.STATUS_NAVIGATION_CHECK_GATE, "check is need pass gate", "");
        mCurrentState = State.CHECKING_GATE_PASSING;
        final MultiFloorPoseInfo firstDestination = targetDestinations.get(0);
        Log.d(mName, "getGatePassingRoute: firstDestination: " + firstDestination);
        mApi.getGatePassingRoute(firstDestination.poseName, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(mName, "getGatePassingRoute result: " + result + ", message: " + message + ", mCurrentState: " + mCurrentState);
                if (mCurrentState != State.CHECKING_GATE_PASSING) return;
                try {
                    if (result == 1 && !TextUtils.isEmpty(message)) {
                        if (TextUtils.equals(message, Definition.FAILED)) {
                            // 没有配置闸机路线的时候会走到这里
                            Log.d(mName, "getGatePassingRoute failed, Gate passing is not needed, navigating directly");
                            startElevatorNavigation(targetDestinations, null);
                        } else {
                            Gson gson = new Gson();
                            Type listType = new TypeToken<List<Pose>>() {
                            }.getType();
                            List<Pose> poseList = gson.fromJson(message, listType);

                            if (TextUtils.equals(message, Definition.FAILED)
                                    || (poseList = gson.fromJson(message, listType)) == null
                                    || poseList.size() != 2) {
                                // 不需要经过闸机，直接导航
                                Log.d(mName, "Gate passing is not needed, navigating directly");
                                startElevatorNavigation(targetDestinations, null);
                            } else {
                                Pose firstPose = poseList.get(0);
                                Pose secondPose = poseList.get(1);
                                double distanceFirst = RobotApi.getInstance().getPlaceOrPoseDistance(Definition.GATE_ENTER, firstPose);
                                double distanceSecond = RobotApi.getInstance().getPlaceOrPoseDistance(Definition.GATE_ENTER, secondPose);
                                if (distanceFirst > distanceSecond) {
                                    firstPose.setName(Definition.GATE_OUTER);
                                    secondPose.setName(Definition.GATE_ENTER);
                                } else {
                                    firstPose.setName(Definition.GATE_ENTER);
                                    secondPose.setName(Definition.GATE_OUTER);
                                }

                                // 需要经过闸机
                                Log.d(mName, "Gate passing is needed for destination: " + firstDestination);
                                boolean isReversePoseTheta = TextUtils.equals(firstPose.getName(), Definition.GATE_OUTER);
                                targetDestinations.add(0, new MultiFloorPoseInfo(firstPose.getName(), firstDestination.floorIndex, true, isReversePoseTheta));
                                startElevatorNavigation(targetDestinations, poseList);
                            }
                        }
                    } else {
                        // 获取失败，界面显示失败信息
                        Log.e(mName, "Failed to get gate passing route, cancelling navigation");
                        updateStatus(ComponentStatus.STATUS_NAVIGATION_GET_GATE_ROUTE_FAILED, "get gate route failed", "");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    Log.e(mName, "Exception in checkGatePassing: ", e);
                    updateStatus(ComponentStatus.STATUS_NAVIGATION_GET_GATE_ROUTE_FAILED, "get gate route failed", "");
                }
            }
        });
    }

    private void startGateNavigation(final List<MultiFloorPoseInfo> targetDestinations, Pose firstPose, Pose secondPose) {
        Log.d(mName, "startGateNavigationNew: targetDestinations: " + targetDestinations + ", firstPose: " + firstPose + ", secondPose: " + secondPose);
        //final MultiFloorPoseInfo gateDestination = targetDestinations.get(0);;
        updateStatus(ComponentStatus.STATUS_NAVIGATION_START_PASS_GATE, "start pass gate", "");
        mCurrentState = State.NAVIGATING_GATE;
        mApi.startGateNavgation(firstPose, secondPose, secondPose.getName(), mLinearSpeed, mAngularSpeed, new ActionListener() {
            @Override
            public void onResult(int status, String message, String extraData) throws RemoteException {
                Log.d(mName, "startGateNavigation result: " + status + ", message: " + message + " , currentStatus: " + mCurrentState);
                if (mCurrentState != State.NAVIGATING_GATE) return;
                if (status == Definition.RESULT_OK) {
                    // 过闸机成功，继续导航到目的地
                    Log.d(mName, "Gate navigation successful, proceeding to target destination: " + secondPose.getName());
                    targetDestinations.remove(0);
                    if (targetDestinations.isEmpty()) {
                        Log.e(mName, "Target destination is empty, cancelling navigation");
                        updateIdleState("到达目的地：" + secondPose.getName());
                        navigationSuccess();
                    } else {
                        startElevatorNavigation(targetDestinations, null);
                    }
                } else {
                    // 过闸机失败
                    Log.e(mName, "Gate navigation failed");
                    updateStatus(ComponentStatus.STATUS_NAVIGATION_PASS_GATE_FAILED, "pass gate failed", "");
                }
            }

            @Override
            public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                Log.e(mName, "startGateNavigation onError: " + errorCode + ", errorString: " + errorString + ", extraData: " + extraData);
                updateStatus(ComponentStatus.STATUS_NAVIGATION_PASS_GATE_FAILED, "pass gate failed, errorCode=" + errorString + " errorString=" + errorString, extraData);
            }

            @Override
            public void onStatusUpdate(int status, String data, String extraData) throws RemoteException {
                if (isIgnoreStatus(status)) {
                    return;
                }
                Log.d(mName, "startGateNavigation onStatusUpdate: " + status + ", data: " + data + ", extraData: " + extraData);
                if (status == Definition.STATUS_ARRIVED_GATE_SECOND_POSE) {
                    updateStatus(ComponentStatus.STATUS_NAVIGATION_END_PASS_GATE, "pass gate success", extraData);
                    mCurrentState = State.NAVIGATING_GATE;
                } else if (status == Definition.STATUS_GOAL_OCCLUDED || status == Definition.STATUS_NAVI_AVOID) {
                    updateStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_START,
                            "navigation avoid start", extraData);
                }
            }
        });
    }

    private boolean isIgnoreStatus(int status) {
        return status == Definition.STATUS_NAVI_GO_STRAIGHT
                || status == Definition.STATUS_DISTANCE_WITH_DESTINATION
                || status == Definition.STATUS_NAVI_TURN_LEFT
                || status == Definition.STATUS_NAVI_TURN_RIGHT;
    }

    private void startElevatorNavigation(final List<MultiFloorPoseInfo> targetDestinations, final List<Pose> gatePoses) {
        mCurrentState = State.NAVIGATING_ELEVATOR;
        if (targetDestinations.size() >= 2) {
            final MultiFloorPoseInfo firstDestination = targetDestinations.get(0);
            final MultiFloorPoseInfo secondDestination = targetDestinations.get(1);
            if (firstDestination.poseName.endsWith(Definition.ELEVATOR_ENTER_POSE) &&
                    secondDestination.poseName.endsWith(Definition.ELEVATOR_ENTER_POSE) ) {
                targetDestinations.remove(0);
            }
        }
        if (targetDestinations.size() == 1) {
            updateStatus(ComponentStatus.STATUS_ARRIVE_TARGET_FLOOR,
                    "navigation arrive target floor");
        }

        final MultiFloorPoseInfo destinations = targetDestinations.get(0);
        mApi.startElevatorNavigation(
                destinations.poseName, destinations.floorIndex, destinations.isAdjustAngle,
                mLinearSpeed, mAngularSpeed, destinations.isReversePoseTheta,
                new ActionListener() {
                    @Override
                    public void onResult(int status, String message, String extraData) throws RemoteException {
                        Log.e(mName, "onResult: " + status + " responseString：" + message + " extraData：" + extraData);
                        if (mCurrentState != State.NAVIGATING_ELEVATOR) return;
                        if (status == Definition.RESULT_OK) {
                            // 乘梯成功
                            Log.d(mName, "Elevator navigation successful, checking gate passing for final destination");
                            onElevatorNavigationSuccess(targetDestinations, gatePoses);
                        } else {
                            // 乘梯失败
                            Log.e(mName, "Elevator navigation failed");
                            handlerResult(status, message, extraData);
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                        Log.e(mName, "onError: " + errorCode + " errorString：" + errorString + " extraData：" + extraData);
                        if (errorCode == Definition.ERROR_IN_DESTINATION) {
                            Log.d(mName, "Elevator navigation successful, is in destination, checking gate passing for final destination");
                            onElevatorNavigationSuccess(targetDestinations, gatePoses);
                        } else {
                            handlerError(errorCode, errorString, extraData);
                        }
                    }

                    @Override
                    public void onStatusUpdate(int status, String data, String extraData) throws RemoteException {
                        Log.e(mName, "onStatusUpdate: " + status + " data：" + data + " extraData：" + extraData);
                        handlerStatusUpdate(status, data, extraData);
                    }
                });
    }

    private void onElevatorNavigationSuccess(List<MultiFloorPoseInfo> targetDestinations, List<Pose> gatePoses) {
        targetDestinations.remove(0);

        if (targetDestinations.isEmpty()) {
            Log.e(mName, "Target destination is empty, cancelling navigation");
            updateIdleState("");
            navigationSuccess();
        } else {
            if (gatePoses != null && !gatePoses.isEmpty()) {
                Pose firstPose = gatePoses.get(0);
                Pose secondPose = gatePoses.get(1);
                startGateNavigation(targetDestinations, firstPose, secondPose);
            } else {
                getGatePassingRoute(targetDestinations);
            }
        }
    }

    private void updateIdleState(String msg) {
        mCurrentState = State.IDLE;
    }
    private void navigationSuccess() {
        Log.d(mName, "navigationSuccess");
        stop(RESULT_NAVIGATION_ARRIVED, "", "");
    }

    private void handlerError(int errorCode, String message, String extraData) {
        switch (errorCode) {
            case Definition.ERROR_NOT_ESTIMATE:
                stop(ComponentError.ERROR_NOT_ESTIMATE,
                        "navigation not estimate", extraData);
                break;
            case Definition.ERROR_IN_DESTINATION:
                stop(ComponentError.ERROR_NAVIGATION_ALREADY_IN_DESTINATION,
                        "already in destination", extraData);
                break;
            case Definition.ERROR_DESTINATION_NOT_EXIST:
                stop(ComponentError.ERROR_DESTINATION_NOT_EXIST,
                        "destination not exist", extraData);
                break;
            case Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE:
                stop(ComponentError.ERROR_DESTINATION_CAN_NOT_ARRIVE,
                        "navigation moving time out", extraData);
                break;
            case Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT:
                stop(ComponentError.ERROR_MULTI_ROBOT_WAITING_TIMEOUT);
                break;
            case Definition.ERROR_NAVIGATION_AVOID_TIMEOUT:
                stop(ComponentError.ERROR_NAVIGATION_AVOID_TIMEOUT,
                        "navigation avoid time out", "");
            case Definition.ACTION_RESPONSE_ALREADY_RUN:
                stop(ComponentError.ERROR_REQUEST_RES_BUSY);
                break;
            case Definition.ACTION_RESPONSE_REQUEST_RES_ERROR:
                stop(ComponentError.ERROR_REQUEST_RES_FAILED);
                break;
            case Definition.ERROR_WHEEL_OVER_CURRENT_RUN_OUT:
                stop(ComponentError.ERROR_WHEEL_OVER_CURRENT_RUN_OUT,
                        "wheel over current retry count run out", extraData);
                break;
            case Definition.ACTION_RESPONSE_RES_UNAVAILBALE:
                stop(ComponentError.ERROR_OPEN_RADAR_FAILED,
                        "res unavailable: " + message, extraData);
                break;
            default:
                stop(errorCode, message, extraData);
                break;
        }
    }

    private void handlerStatusUpdate(int status, String data, String extraData) {
        switch (status) {
            case Definition.STATUS_GOAL_OCCLUDED:
            case Definition.STATUS_NAVI_AVOID:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_START,
                        "navigation avoid start", extraData);
                break;
            case Definition.STATUS_GOAL_OCCLUDED_END:
            case Definition.STATUS_NAVI_AVOID_END:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_END,
                        "navigation avoid end", extraData);
                break;
            case Definition.STATUS_NAVI_OBSTACLES_AVOID:
                updateStatus(ComponentStatus.STATUS_OBSTACLES_AVOID,
                        "pause to obstacles avoid", extraData);
                break;
            case Definition.STATUS_START_NAVIGATION:
                updateStatus(ComponentStatus.STATUS_START_NAVIGATION,
                        "start navigation");
                break;
            case Definition.STATUS_NAVI_MULTI_ROBOT_WAITING:
                updateStatus(ComponentStatus.STATUS_NAVI_MULTI_ROBOT_WAITING,
                        "navigation multi robot waiting");
                break;
            case Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END:
                updateStatus(ComponentStatus.STATUS_NAVI_MULTI_ROBOT_WAITING_END,
                        "navigation multi robot waiting end");
                break;
            case Definition.STATUS_NAVI_GO_STRAIGHT:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_GO_STRAIGHT, data,
                        extraData);
                break;
            case Definition.STATUS_NAVI_TURN_LEFT:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_TURN_LEFT, data,
                        extraData);
                break;
            case Definition.STATUS_NAVI_TURN_RIGHT:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_TURN_RIGHT, data,
                        extraData);
                break;
            case Definition.STATUS_NAVI_SET_PRIORITY_FAILED:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_SET_PRIORITY_FAILED, data,
                        extraData);
                break;
            default:
                updateStatus(status, data, extraData);
                break;
        }
    }

    private void handlerResult(int status, String message, String extraData) {
        switch (status) {
            case Definition.RESULT_OK:
                break;
            case Definition.ACTION_RESPONSE_STOP_SUCCESS:
                stop(status, message + " 停止成功", extraData);
                break;
            case Definition.RESULT_FAILURE:
                stop(status, message + " 导航失败", extraData);
                break;
            case Definition.STATUS_NAVI_OUT_MAP:
                stop(ComponentError.ERROR_NAVIGATION_OUT_MAP, message, extraData);
                break;
            case Definition.STATUS_NAVI_GLOBAL_PATH_FAILED:
                stop(ComponentError.ERROR_NAVIGATION_GLOBAL_PATH_FAILED, message, extraData);
                break;
            case Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH:
            case Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT:
            case Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL:
            case Definition.STATUS_NAVI_MULTI_VERSION_NOT_MATCH:
                stop(ComponentError.ERROR_MULTIPLE_MODE_ERROR, message, extraData);
                break;
            case Definition.STATUS_NAVI_WHEEL_SLIP:
                stop(ComponentError.ERROR_NAVI_WHEEL_SLIP, message, extraData);
                break;
            default:
                stop(status, message, extraData);
                break;
        }
    }

    @Override
    protected void onUpdate(String intent, String params) {

    }

    @Override
    protected void onStop(int reason) {
        DelayTask.cancel(START_TAG);
        mApi.stopElevatorNavigation();
        mCurrentState = State.IDLE;
    }

    private void stop(int result, String message, String extraData) {
        stop(STOP_TIMEOUT, result, message, extraData);
    }
}
