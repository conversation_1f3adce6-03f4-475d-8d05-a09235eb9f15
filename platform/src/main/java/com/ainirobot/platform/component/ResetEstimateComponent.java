/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams.ResetEstimate;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 重定位
 * 运动过程中底盘漂移，停止运动后再开始重定位，成功概率较高，
 * 而运动过程中直接触发重定位，成功概率较低
 */
public class ResetEstimateComponent extends Component {

    private static final long STOP_TIMEOUT = 50;

    /**
     * 默认重定位尝试次数
     */
    private static final int DEFAULT_RETRY_COUNT = 5;

    /**
     * 重定位最小次数
     */
    private static final int MIN_RETRY_COUNT = 1;

    /**
     * 重定位最大次数
     */
    private static final int MAX_RETRY_COUNT = 100;

    private static final int DEFAULT_RETRY_INTERVAL = 0;
    private static final int DEFAULT_ESTIMATE_TYPE = -1;

    private int mRetryCount;
    private long mRetryInterval;
    private int mEstimateType;
    private Pose mPose;
    private int mIndex = 0;

    public ResetEstimateComponent(String name) {
        this(name, Looper.myLooper());
    }

    public ResetEstimateComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    protected void onStart(String params) {
        Log.i(mName, "onStart, params= " + params);
        try {
            JSONObject jsonParams = new JSONObject(params);
            mRetryCount = jsonParams.optInt(ResetEstimate.PARAM_RESET_ESTIMATE_COUNT, DEFAULT_RETRY_COUNT);
            mRetryInterval = jsonParams.optInt(ResetEstimate.PARAM_RESET_ESTIMATE_INTERVAL, DEFAULT_RETRY_INTERVAL);
            mEstimateType = jsonParams.optInt(ResetEstimate.PARAM_RESET_ESTIMATE_TYPE, DEFAULT_ESTIMATE_TYPE);
            String estimateName = jsonParams.optString(ResetEstimate.PARAM_RESET_ESTIMATE_NAME);
            String estimatePose = jsonParams.optString(ResetEstimate.PARAM_RESET_ESTIMATE_POSE);
            if (mRetryInterval < 0) {
                mRetryInterval = DEFAULT_RETRY_INTERVAL;
            }
            rectifyRetryCount();
            mPose = new Pose();
            if (!TextUtils.isEmpty(estimateName)) {
                mPose.setName(estimateName);
            }
            if (!TextUtils.isEmpty(estimatePose)) {
                try {
                    JSONObject jsonObject = new JSONObject(estimatePose);
                    if (!jsonObject.has("x") || !jsonObject.has("y")
                            || !jsonObject.has("theta")) {
                        return;
                    }
                    mPose.setX((float) jsonObject.optDouble("x"));
                    mPose.setY((float) jsonObject.optDouble("y"));
                    mPose.setTheta((float) jsonObject.optDouble("theta"));
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
            mIndex = 0;
            if (DEFAULT_ESTIMATE_TYPE != mEstimateType) {
                startResetEstimate();
            } else {
                isRobotHasVision();
            }
        } catch (JSONException e) {
            e.printStackTrace();
            processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR, "json parse exception");
        }
    }

    /**
     * 纠偏重试次数参数
     */
    private void rectifyRetryCount() {
        if (mRetryCount < MIN_RETRY_COUNT) {
            mRetryCount = MIN_RETRY_COUNT;
        } else if (mRetryCount > MAX_RETRY_COUNT) {
            mRetryCount = MAX_RETRY_COUNT;
        }
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.i(mName, "onUpdate, params= " + params);
    }

    @Override
    protected void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
    }

    private void startResetEstimate() {
        mIndex++;
        Log.d(mName, "startResetEstimate index: " + mIndex + ", retryCount: " + mRetryCount);
        processStatus(ComponentStatus.STATUS_RESET_ESTIMATE_START, "reset estimate start");
        mApi.setChassisRelocation(mEstimateType, mPose, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(mName, "setChassisRelocation, result= " + result + " message= " + message
                        + ", index: " + mIndex + ", isAlive: " + isAlive());
                if (!isAlive()) {
                    return;
                }
                if ("succeed".equals(message)) {
                    processResult(ComponentResult.RESULT_SUCCESS, "estimate success");
                } else {
                    if (mIndex >= mRetryCount) {
                        processResult(ComponentResult.RESULT_RESET_ESTIMATE_FAIL, "estimate fail");
                    } else {
                        processStatus(ComponentStatus.STATUS_RESET_ESTIMATE_SINGLE_FAILED, "");
                        DelayTask.cancel(mName);
                        DelayTask.submit(mName, new Runnable() {
                            @Override
                            public void run() {
                                startResetEstimate();
                            }
                        }, mRetryInterval);
                    }
                }
            }
        });
    }

    private void isRobotHasVision() {
        Log.i(mName, "isRobotHasVision");
        mApi.isRobotHasVision(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                if (!isAlive()) {
                    return;
                }
                Log.i(mName, "isRobotHasVision result: " + result + " message= " + message);
                if ("true".equals(message)) {
                    getNavigationConfig();
                } else {
                    startPoseEstimate();
                }
            }
        });
    }

    private void getNavigationConfig() {
        Log.i(mName, "getNavigationConfig");
        mApi.getNavigationConfig(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                if (!isAlive()) {
                    return;
                }
                Log.i(mName, "getNavigationConfig result: " + result + " message= " + message);
                boolean enableFishEye = false;
                try {
                    JSONObject jsonObject = new JSONObject(message);
                    enableFishEye = jsonObject.optBoolean("enableFishEye", false);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                if (enableFishEye) {
                    mEstimateType = Definition.RelocationMode.VISION.getValue();
                    startResetEstimate();
                } else {
                    startPoseEstimate();
                }
            }
        });
    }

    private void startPoseEstimate() {
        mIndex++;
        Log.d(mName, "startPoseEstimate index: " + mIndex + ", retryCount: " + mRetryCount);
        processStatus(ComponentStatus.STATUS_RESET_ESTIMATE_START, "reset estimate start");
        mApi.resetPoseEstimate(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                if (!isAlive()) {
                    return;
                }
                if ("succeed".equals(message)) {
                    processResult(ComponentResult.RESULT_SUCCESS, "estimate success");
                } else {
                    if (mIndex >= mRetryCount) {
                        processResult(ComponentResult.RESULT_RESET_ESTIMATE_FAIL, "estimate fail");
                    } else {
                        processStatus(ComponentStatus.STATUS_RESET_ESTIMATE_SINGLE_FAILED, "");
                        DelayTask.cancel(mName);
                        DelayTask.submit(mName, new Runnable() {
                            @Override
                            public void run() {
                                startPoseEstimate();
                            }
                        }, mRetryInterval);
                    }
                }
            }
        });
    }

    private void processResult(int result, String message) {
        Log.i(mName, "processResult result: " + result + " message= " + message);
        stop(STOP_TIMEOUT, result, message);
    }

    private void processStatus(int status, String result) {
        Log.i(mName, "processStatus status: " + status + ", result: " + result);
        if (!isAlive()) {
            return;
        }
        updateStatus(status, result);
    }

}
