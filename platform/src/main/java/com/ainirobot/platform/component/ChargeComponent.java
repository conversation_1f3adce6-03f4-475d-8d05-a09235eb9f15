/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.component;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;

import static com.ainirobot.coreservice.client.Definition.STATUS_BMS_WARNING;

public class ChargeComponent extends Component {

    private static final int MSG_NAVIGATION_TIMEOUT = 0x1;
    private static final int MSG_CHARGE_TIMEOUT = 0x2;
    private static final int MSG_GET_CHARGE_STATUS = 0x3;
    private static final long DEFAULT_TIMEOUT = 30 * 1000;
    private static final double DEFAULT_AVOID_DISTANCE = 2;
    private static final long DEFAULT_AVOID_TIMEOUT = 30 * 1000;
    private static final double COORDINATE_DEVIATION = 0.1f;
    private static final long STOP_TIMEOUT = 500;
    private static final long AUTO_CHARGE_TIMEOUT = 40 * 1000;
    private long mTimeout;
    private double mAvoidDistance;
    private long mAvoidTimeout;
    private Handler mHandler;
    private NavigationComponent mNavigationComponent;
    private RecoverPositiveComponent mRecoverPositiveComponent;
    private ChargeStatus mStatus = ChargeStatus.idle;
    private int mBmsStatus;

    private enum ChargeStatus {
        idle, navigation, go_charge, charge
    }

    public ChargeComponent(String name) {
        this(name, Looper.myLooper());
    }

    public ChargeComponent(String name, Looper looper) {
        super(name, looper);
        mHandler = new ChargeHandler(this);
        mNavigationComponent = new NavigationComponent(mName, mLooper);
        mRecoverPositiveComponent = new RecoverPositiveComponent(mName, mLooper);
    }

    private static class ChargeHandler extends Handler {
        private WeakReference<ChargeComponent> mComponent;

        private ChargeHandler(ChargeComponent component) {
            super();
            mComponent = new WeakReference<>(component);
        }

        @Override
        public void handleMessage(Message msg) {
            ChargeComponent component = mComponent.get();
            if (component == null || !component.isAlive()) {
                return;
            }
            Log.v(mComponent.get().mName, "handleMessage msg: " + msg.what);
            switch (msg.what) {
                case MSG_NAVIGATION_TIMEOUT:
                    break;
                case MSG_CHARGE_TIMEOUT:
                    switch (component.mStatus) {
                        case navigation:
                            break;
                        case go_charge:
                            break;
                        case charge:
                            break;
                        default:
                            break;
                    }
                    break;
                case MSG_GET_CHARGE_STATUS:
                    component.getChargeStatus();
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    protected void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            mTimeout = json.optLong(ComponentParams.Charge.PARAM_TIMEOUT, DEFAULT_TIMEOUT);
            mAvoidDistance = json.optDouble(ComponentParams.Charge.PARAM_AVOID_DISTANCE,
                    DEFAULT_AVOID_DISTANCE);
            mAvoidTimeout = json.optLong(ComponentParams.Charge.PARAM_AVOID_TIMEOUT,
                    DEFAULT_AVOID_TIMEOUT);
        } catch (JSONException e) {
            mTimeout = DEFAULT_TIMEOUT;
            mAvoidDistance = DEFAULT_AVOID_DISTANCE;
            mAvoidTimeout = DEFAULT_AVOID_TIMEOUT;
        }
        mHandler.sendEmptyMessage(MSG_NAVIGATION_TIMEOUT);
        isEstimate();
    }

    private void isEstimate() {
        mApi.isRobotEstimate(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(mName, "isEstimate result:" + result + " message:" + message);
                if (result == Definition.RESULT_OK && "true".equals(message)) {
                    mApi.moveHead(Definition.JSON_HEAD_ABSOLUTE, Definition.JSON_HEAD_ABSOLUTE,
                            Definition.DEFAULT_HORIZONTAL_ANGLE, Definition.DEFAULT_VERTICAL_ANGLE,
                            null);
                    isRobotInLocation();
                } else {
                    processResult(ComponentError.ERROR_NOT_ESTIMATE, "not estimate");
                }
            }
        });
    }

    private void isRobotInLocation() {
        mApi.isRobotInLocation(Definition.START_BACK_CHARGE_POSE, COORDINATE_DEVIATION,
                new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(mName, "isEstimate result:" + result + " message:" + message);
                if (result == Definition.RESULT_OK) {
                    try {
                        JSONObject json = new JSONObject(message);
                        boolean isInLocation = json.optBoolean(Definition.JSON_NAVI_IS_IN_LOCATION,
                                false);
                        if (isInLocation) {
                            startRecoverPositive();
                        } else {
                            startNavigation();
                        }
                    } catch (JSONException e) {
                        processResult(ComponentError.ERROR_GET_CHARGE_POSE_FAILED,
                                "get charge pose failed");
                    }
                } else {
                    processResult(ComponentError.ERROR_GET_CHARGE_POSE_FAILED,
                            "get charge pose failed");
                }
            }
        });
    }

    private void startRecoverPositive() {
        Log.d(mName, "startRecoverPositive");
        JSONObject json = new JSONObject();
        try {
            json.put(ComponentParams.RecoverPositive.PARAM_PLACE_NAME, Definition.START_BACK_CHARGE_POSE);
            mRecoverPositiveComponent.setComponentListener(mRecoverPositiveListener);
            mRecoverPositiveComponent.start(json.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private Component.ComponentListener mRecoverPositiveListener = new Component.ComponentListener() {
        @Override
        public void onFinish(int result, String message, String extraData) {
            Log.d(mName, "RecoverPositiveListener onFinish result: " + result
                    + ", message: " + message);
            switch (result) {
                case ComponentError.ERROR_NOT_ESTIMATE:
                    processResult(ComponentError.ERROR_NOT_ESTIMATE, "not estimate");
                    break;
                case ComponentError.ERROR_PLACE_NOT_EXIST:
                    processResult(ComponentError.ERROR_GET_CHARGE_POSE_FAILED,
                            "get charge pose failed");
                    break;
                case ComponentResult.RESULT_SUCCESS:
                    startCharge();
                    break;
                case ComponentError.ERROR_TOO_FAR_WITH_PLACE:
                    startNavigation();
                    break;
                case ComponentError.ERROR_RECOVER_POSITIVE_FAILED:
                default:
                    processResult(result, message);
                    break;
            }
        }
    };

    private void stopRecoverPositive() {
        if (mRecoverPositiveComponent != null) {
            mRecoverPositiveComponent.setComponentListener(null);
            mRecoverPositiveComponent.stop(STOP_TIMEOUT);
        }
    }

    private void startNavigation() {
        Log.i(mName, "startNavigation");
        mNavigationComponent = new NavigationComponent(mName);
        mNavigationComponent.setComponentListener(mNavigationFinishListener);
        mNavigationComponent.setStatusListener(mNavigationStatusListener);
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(ComponentParams.Navigation.PARAM_DESTINATION, Definition.START_BACK_CHARGE_POSE);
            jsonObject.put(ComponentParams.Navigation.PARAM_COORDINATE_DEVIATION, COORDINATE_DEVIATION);
            mNavigationComponent.start(jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private Component.ComponentListener mNavigationFinishListener =
            new ComponentListener() {
                @Override
                public void onFinish(int result, String message, String extraData) {
                    Log.i(mName, "onFinish result: " + result + ", message: " + message);
                    switch (result) {
                        case ComponentError.ERROR_NAVIGATION_ALREADY_IN_DESTINATION:
                        case ComponentResult.RESULT_NAVIGATION_ARRIVED:
                            ChargeComponent.this.startCharge();
                            break;
                        case ComponentError.ERROR_DESTINATION_CAN_NOT_ARRIVE:
                            ChargeComponent.this.processResult(ComponentError.ERROR_CHARGE_POINT_CAN_NOT_ARRIVE,
                                    "charge point cannot arrive");
                            break;
                        case ComponentError.ERROR_DESTINATION_NOT_EXIST:
                            ChargeComponent.this.processResult(ComponentError.ERROR_GET_CHARGE_POSE_FAILED,
                                    "get charge pose failed");
                            break;
                        case ComponentError.ERROR_NOT_ESTIMATE:
                        case ComponentResult.RESULT_NAVIGATION_FAILURE:
                        case ComponentError.ERROR_NAVIGATION_AVOID_TIMEOUT:
                        case ComponentError.ERROR_REQUEST_RES_BUSY:
                        case ComponentError.ERROR_REQUEST_RES_FAILED:
                        case ComponentError.ERROR_NAVIGATION_RESET_ESTIMATE_FAIL:
                        case ComponentError.ERROR_NAVIGATION_OUT_MAP:
                        case ComponentError.ERROR_NAVIGATION_GLOBAL_PATH_FAILED:
                        default:
                            ChargeComponent.this.processResult(result, message);
                            break;
                    }
                }
            };

    private Component.ComponentStatusListener mNavigationStatusListener =
            new ComponentStatusListener() {
                @Override
                public void onStatusUpdate(int status, String data, String extraData) {
                    Log.i(mName, "onStatus status: " + status + ", data: " + data);
                    switch (status) {
                        case ComponentStatus.STATUS_START_NAVIGATION:
                        case ComponentStatus.STATUS_OBSTACLES_AVOID:
                        case ComponentStatus.STATUS_NAVIGATION_AVOID:
                        case ComponentStatus.STATUS_DISTANCE_WITH_DESTINATION:
                        case ComponentStatus.STATUS_NAVIGATION_AVOID_END:
                        case ComponentStatus.STATUS_ESTIMATE_LOST:
                        default:
                            ChargeComponent.this.processStatus(status, data);
                            break;
                    }
                }
            };

    private void stopNavigation() {
        if (mNavigationComponent != null) {
            mNavigationComponent.setComponentListener(null);
            mNavigationComponent.setStatusListener(null);
            mNavigationComponent.stop(STOP_TIMEOUT);
        }
    }

    private void startCharge() {
        registerBmsObserver();
        mApi.switchChargeMode();
        mApi.startCharge();
        mHandler.removeMessages(MSG_NAVIGATION_TIMEOUT);
        mHandler.sendEmptyMessageDelayed(MSG_GET_CHARGE_STATUS, Definition.SECOND);
        mHandler.sendEmptyMessageDelayed(MSG_CHARGE_TIMEOUT, AUTO_CHARGE_TIMEOUT);
    }

    private void registerBmsObserver() {
        mApi.registerStatusListener(STATUS_BMS_WARNING, new StatusListener() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.i(mName, "registerBmsObserver onStatusUpdate data: " + data);
                try {
                    JSONObject jsonObject = new JSONObject(data);
                    mBmsStatus = jsonObject.optInt("status");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void getChargeStatus() {
        mApi.getChargeStatus(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(mName, "getChargeStatus result: " + result + ", message: " + message);
                int status = Integer.parseInt(message);
                if (status == Definition.AUTOCHARGE_STATUS_LOCKED
                        || status == Definition.AUTOCHARGE_STATUS_DONE) {
                    processResult(ComponentResult.RESULT_SUCCESS, "change success");
                    return;
                }
            }
        });
    }

    @Override
    protected void onUpdate(String intent, String params) {
    }

    private void processStatus(int status, String data) {
        Log.d(mName, "processStatus status: " + status + ", data: " + data);
        updateStatus(status, data);
    }

    private void processResult(int result, String message) {
        Log.d(mName, "processResult result: " + result + ", message: " + message);
        stop(STOP_TIMEOUT, result, message);
    }

    @Override
    protected void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        stopRecoverPositive();
        stopNavigation();
        mHandler.removeMessages(MSG_CHARGE_TIMEOUT);
        mStatus = ChargeStatus.idle;
    }
}
