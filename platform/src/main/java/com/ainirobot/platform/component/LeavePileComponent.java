package com.ainirobot.platform.component;

import android.os.Looper;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.react.reactnative.component.skillcomponent.ResouceType;

import org.json.JSONException;
import org.json.JSONObject;

/*
 * 离桩组件-提供的功能
 * 1、取消雷达系统接管
 * 2、启动雷达
 * 3、离桩
 */
public class LeavePileComponent extends Component {
   /*
    *离桩速度/离桩距离
    */
   private static final float DEFAULT_MOVE_SPEEND=0.7f;
   private static final float DEFALUT_MOVE_DISTANCE=0.2f;
   private static final long STOP_TIMEOUT = 10000;
   private float mSpeed;
   private float mDistance;

   public LeavePileComponent(String name) {
       this(name, Looper.myLooper());
   }

   public LeavePileComponent(String name, Looper looper) {
       super(name, looper);
   }

   @Override
   protected void onStart(String params) {
       Log.i(mName, "onStart params:" + params);
       try {
           JSONObject json = new JSONObject(params);
           mDistance = (float) json.optDouble(ComponentParams.LeavePile.PARAM_LEAVE_PILE_DISTANCE,
                   DEFALUT_MOVE_DISTANCE);
           mSpeed =(float) json.optDouble(ComponentParams.LeavePile.PARAM_LEAVE_PILE_SPEED,
                   DEFAULT_MOVE_SPEEND);
       } catch (JSONException e) {
           mDistance = DEFALUT_MOVE_DISTANCE;
           mSpeed = DEFAULT_MOVE_SPEEND;
       }
       leaveChargingPile();
   }

   private void leaveChargingPile(){
      mApi.leaveChargingPile(mSpeed,mDistance,new CommandListener(){
          @Override
          public void onResult(int result, String message) {
              Log.i(mName, "LeavePile result:" + result + " message:" + message);
              if (result == Definition.RESULT_OK) {//离桩成功
                  processLeavePileResult(ComponentResult.RESULT_SUCCESS,message);
              } else {//启动雷达失败
                  processLeavePileResult(ComponentError.ERROR_LEAVE_PILE_OPEN_RADAR_FAIL,message);
              }
          }
          @Override
          public void onError(int errorCode, String errorString) throws RemoteException {
              Log.i(mName, "LeavePile onError:" + errorCode + " errorString:" + errorString);
              if(errorCode== Definition.RESULT_FAILURE_MOTION_AVOID_STOP) {//前方有障碍物
                  processLeavePileResult(ComponentError.ERROR_LEAVE_PILE_MOTION_AVOID_FAIL, errorString);
              }else if(errorCode== Definition.RESULT_FAILURE_TIMEOUT){ //离桩超时
                  processLeavePileResult(ComponentError.ERROR_LEAVE_PILE_TIMEOUT_FAIL,errorString);
              }else if(errorCode== Definition.STATUS_LEAVE_PILE_FAILURE_WIRE_CHARGING){ //线充模式禁止离桩
                  processLeavePileResult(ComponentError.ERROR_LEAVE_PILE_FAIL_WIRE_CHARGING,errorString);
              } else {
                  processLeavePileResult(errorCode,errorString);
              }
          }

          @Override
          public void onStatusUpdate(int status, String data) {
              processLeavePileStatus(status,data);
          }
      });
   }

   private void processLeavePileResult(int reason,String message){
       stop(STOP_TIMEOUT, reason, "");
   }

   private void processLeavePileStatus(int status,String data) {
       updateStatus(status, data);
   }

   @Override
   protected void onUpdate(String intent, String params) {
       Log.i(mName, "onUpdate params:" + params);
   }

   @Override
   protected void onStop(int reason) {
       Log.i(mName, "onStop reason:" + reason);
   }
}
