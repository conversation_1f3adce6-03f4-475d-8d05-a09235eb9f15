package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.CruiseRouteBean;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by Orion on 2019/5/9.
 */
public class GetCruiseRouteComponent extends Component {

    private static final int STOP_TIMEOUT = 500;

    public GetCruiseRouteComponent(String name) {
        this(name, Looper.myLooper());
    }

    public GetCruiseRouteComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    protected void onStart(String params) {
        Log.i(mName, "onStart:" + params);
        checkMap();
    }

    private void checkMap() {
        mApi.getMapName(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(mName, "getMapName onResult : " + result + " || " + message);
                if (isCommResultOk(result, message)) {
                    getNaviCruiseRoute(message);
                } else {
                    processResult(ComponentError.ERROR_NAVIGATION_NO_MAP, "please create map first");
                }
            }
        });
    }

    private void getNaviCruiseRoute(String mapName) {
        Log.d(mName, "getNaviCruiseRoute, mapName= " + mapName);
        JSONObject object = new JSONObject();
        try {
            object.put("map_name", mapName);
            mApi.getNaviCruiseRoute(object.toString(), new CommandListener() {
                @Override
                public void onResult(int result, String message) {
                    Log.d(mName, "getNaviCruiseRoute onResult : " + result + " || " + message);
                    handleCruiseRoute(result, message);
                }
            });
        } catch (JSONException e) {
            e.printStackTrace();
            processResult(ComponentError.ERROR_NAVIGATION_NO_MAP, "please create map first");
        }
    }

    private void handleCruiseRoute(int result, String message) {
        Gson gson = new Gson();
        CruiseRouteBean route = null;
        try {
            route = gson.fromJson(message, CruiseRouteBean.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (isCommResultOk(result, message)) {
            if (route != null && !TextUtils.isEmpty(route.getCoordinate())) {
                processResult(ComponentResult.RESULT_SUCCESS, message);
            } else {
                processResult(ComponentError.ERROR_NO_CRUISE_ROUTE, "no cruise route");
            }
        } else {
            processResult(ComponentError.ERROR_NO_CRUISE_ROUTE, "no cruise route");
        }
    }

    private boolean isCommResultOk(int result, String message) {
        return (result == Definition.RESULT_OK && !TextUtils.isEmpty(message)
                && !"timeout".equals(message) && !"failed".equals(message));
    }

    @Override
    protected void onUpdate(String intent, String params) {
    }

    @Override
    protected void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
    }

    private void processResult(int result, String message) {
        if (!isAlive())
            return;
        stop(STOP_TIMEOUT, result, message);
    }

}
