/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.component;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.person.PersonApi;
import com.ainirobot.coreservice.client.person.PersonListener;
import com.ainirobot.coreservice.client.person.PersonUtils;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentParams.WakeupAndPreWakeupStartCheck;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.ainirobot.platform.utils.SettingsUtils;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.Iterator;
import java.util.List;

public class WakeupAndPreWakeupStartCheckComponent extends Component {

    private static final int MSG_UPDATE_BEST_PERSON = 0x1;
    private static final int MSG_PRE_WAKEUP_ENABLE = 0x2;
    private static final double DEFAULT_MAX_DISTANCE = 3;
    private static final double DEFAULT_WAKEUP_FACE_DISTANCE = 1.3;
    private static final double DEFAULT_WAKEUP_FACE_ANGLE_X = 45;
    private static final long INVALID_TIME = Long.MAX_VALUE;
    private static final long DEFAULT_INCOMPLETE_FACE_WAKEUP_CACHE_TIMEOUT = 3000;
    private static final long DEFAULT_PRE_WAKEUP_INTERVAL_TIME = 20 * 1000;
    private static final long DEFAULT_RECOGNIZE_TIMEOUT = 2000;

    private AppearHandler mHandler;
    private boolean mIsNeedPreWakeup;
    private double mMaxDistance;
    private double mWakeupFaceDistance;
    private double mWakeupFaceAngleX;
    private boolean mIsNeedInCompleteFace;
    private boolean mIsWakeupNeedRecognize;
    private boolean mIsPreWakeupNeedInCompleteFace;
    private boolean mIsPreWakeupNeedBody;
    private long mIncompleteFaceWakeupCacheTimeout;
    private long mInCompleteFaceWakeupCacheTimer = INVALID_TIME;
    private long mRecognizeTimeout;
    private long mPreWakeupIntervalTime;
    private Person mCurrentPerson;
    private boolean mWakeupEnable;
    private boolean mPreWakeupEnable;
    private boolean misWakeupNeedRecognize;
    private RecognizeComponent mRecognizeComponent;
    private final Object mWakeupAndPreWakeupLock = new Object();

    public WakeupAndPreWakeupStartCheckComponent(String name) {
        this(name, Looper.myLooper());
    }

    public WakeupAndPreWakeupStartCheckComponent(String name, Looper looper) {
        super(name, looper);
        mHandler = new AppearHandler(this, mLooper);
        mRecognizeComponent = new RecognizeComponent(mName);
    }

    private static class AppearHandler extends Handler {

        private WeakReference<WakeupAndPreWakeupStartCheckComponent> mComponent;

        private AppearHandler(WakeupAndPreWakeupStartCheckComponent component, Looper looper) {
            super(looper);
            mComponent = new WeakReference<>(component);
        }

        @Override
        public void handleMessage(Message msg) {
            WakeupAndPreWakeupStartCheckComponent component = mComponent.get();
            if (component == null || !component.isAlive()) {
                return;
            }
//            Log.v(mComponent.get().mName, "handleMessage what: " + msg.what + ", obj: " + msg.obj);
            switch (msg.what) {
                case MSG_UPDATE_BEST_PERSON:
                    if (component.isAlive()) {
                        if (msg.obj == null) {
                            return;
                        }
                        List<Person> personList = (List<Person>) msg.obj;
                        List<Person> wakeupFaceList = component.mIsNeedInCompleteFace
                                ? PersonUtils.getAllFaces(personList)
                                : PersonUtils.getAllCompleteFaces(personList);
                        Person wakeupPerson = PersonUtils.getBestFace(wakeupFaceList, component.mWakeupFaceDistance,
                                component.mWakeupFaceAngleX);
                        if (wakeupPerson != null) {
                            if (PersonUtils.isCompleteFace(wakeupPerson)) {
                                component.handleDetectResult(wakeupPerson);
                            } else {
                                component.handleNoTrackDetectResult(wakeupPerson);
                            }
                        } else {
                            Person person = PersonUtils.getBestFace(component
                                    .mIsPreWakeupNeedInCompleteFace ? PersonApi.getInstance()
                                    .getAllFaceList(component.mMaxDistance) : PersonApi
                                    .getInstance().getCompleteFaceList(component.mMaxDistance));
                            if (person == null) {
                                person = PersonUtils.getBestBody(PersonApi.getInstance()
                                        .getAllBodyList(), component.mMaxDistance);
                            }
                            if (person != null && person.isFakeFace()) {
                                return;
                            }
                            component.processPreWakeupStatus(person);
                        }
                    }
                    break;
                case MSG_PRE_WAKEUP_ENABLE:
                    component.mPreWakeupEnable = true;
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            mIsNeedPreWakeup = json.optBoolean(WakeupAndPreWakeupStartCheck.PARAM_IS_NEED_PRE_WAKEUP,
                    true);
            mMaxDistance = json.optDouble(WakeupAndPreWakeupStartCheck.PARAM_MAX_DISTANCE,
                    DEFAULT_MAX_DISTANCE);
            mWakeupFaceDistance = json.optDouble(WakeupAndPreWakeupStartCheck.PARAM_WAKEUP_FACE_DISTANCE,
                    DEFAULT_WAKEUP_FACE_DISTANCE);
            mWakeupFaceAngleX = json.optDouble(WakeupAndPreWakeupStartCheck.PARAM_WAKEUP_FACE_ANGLE_X,
                    DEFAULT_WAKEUP_FACE_ANGLE_X);
            mIsNeedInCompleteFace = json.optBoolean(WakeupAndPreWakeupStartCheck.PARAM_IS_NEED_INCOMPLETE_FACE,
                    true);
            mIsWakeupNeedRecognize = json.optBoolean(WakeupAndPreWakeupStartCheck
                    .PARAM_IS_WAKEUP_NEED_RECOGNIZE, true);
            mIncompleteFaceWakeupCacheTimeout = json.optLong(WakeupAndPreWakeupStartCheck
                            .PARAM_INCOMPLETE_FACE_CACHE_TIME,
                    DEFAULT_INCOMPLETE_FACE_WAKEUP_CACHE_TIMEOUT);
            mRecognizeTimeout = json.optLong(WakeupAndPreWakeupStartCheck.PARAM_RECOGNIZE_TIMEOUT,
                    DEFAULT_RECOGNIZE_TIMEOUT);
            mIsPreWakeupNeedInCompleteFace = json.optBoolean(WakeupAndPreWakeupStartCheck.PARAM_IS_PRE_WAKEUP_NEED_INCOMPLETE_FACE,
                    true);
            mIsPreWakeupNeedBody = json.optBoolean(WakeupAndPreWakeupStartCheck.PARAM_IS_PRE_WAKEUP_NEED_BODY,
                    true);
            mPreWakeupIntervalTime = json.optLong(WakeupAndPreWakeupStartCheck.PARAM_PRE_WAKEUP_INTERVAL_TIME,
                    DEFAULT_PRE_WAKEUP_INTERVAL_TIME);
            misWakeupNeedRecognize = json.optBoolean(WakeupAndPreWakeupStartCheck.PARAM_IS_WAKEUP_NEED_RECOGNIZE);
        } catch (JSONException e) {
            mIsNeedPreWakeup = true;
            mIsWakeupNeedRecognize = true;
            mMaxDistance = DEFAULT_MAX_DISTANCE;
            mWakeupFaceDistance = DEFAULT_WAKEUP_FACE_DISTANCE;
            mWakeupFaceAngleX = DEFAULT_WAKEUP_FACE_ANGLE_X;
            mIsNeedInCompleteFace = true;
            mIncompleteFaceWakeupCacheTimeout = DEFAULT_INCOMPLETE_FACE_WAKEUP_CACHE_TIMEOUT;
            mRecognizeTimeout = DEFAULT_RECOGNIZE_TIMEOUT;
            mIsPreWakeupNeedInCompleteFace = true;
            mIsPreWakeupNeedBody = true;
            mPreWakeupIntervalTime = DEFAULT_PRE_WAKEUP_INTERVAL_TIME;
        }
        mCurrentPerson = null;
        mWakeupEnable = true;
        mPreWakeupEnable = true;
        mInCompleteFaceWakeupCacheTimer = INVALID_TIME;
        initPersonListener();
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.d(mName, "onUpdate params: " + params);
    }

    private void initPersonListener() {
        boolean result = PersonApi.getInstance().registerPersonListener(mPersonListener);
        if (!result) {
            processWakeupResult(ComponentError.ERROR_OPEN_PERSON_DETECT_FAILED);
        }
    }

    private PersonListener mPersonListener = new PersonListener() {
        @Override
        public void personChanged() {
            List<Person> personList;
            if (mIsPreWakeupNeedBody) {
                personList = PersonApi.getInstance().getAllPersons(mMaxDistance);
            } else {
                personList = PersonApi.getInstance().getAllFaceList(mMaxDistance);
            }

            if (!mIsNeedInCompleteFace && !mIsPreWakeupNeedInCompleteFace) {
                personList = PersonUtils.getAllCompleteFaces(personList);
            }

            if (personList == null) {
                return;
            }

            Iterator<Person> iterator = personList.iterator();
            while (iterator.hasNext()) {
                Person person = iterator.next();
                //移除非活体数据
                if (!person.isLiveNess()) {
                    iterator.remove();
                }
            }

            if (personList.isEmpty()) {
                return;
            }

            mHandler.obtainMessage(MSG_UPDATE_BEST_PERSON, personList).sendToTarget();
        }
    };

    private void handleDetectResult(Person person) {
        synchronized (mWakeupAndPreWakeupLock) {
            Log.d(mName, "handle detect result person: " + person + ", wakeupEnable: " + mWakeupEnable);
            if (mWakeupEnable) {
                mWakeupEnable = false;
                mCurrentPerson = person;
                //人脸识别合规处理，屏蔽唤醒中识别人脸
                Log.d(mName, " mIsWakeupNeedRecognize = " + mIsWakeupNeedRecognize + "  " + SettingsUtils.isFaceWList());
                if (mIsWakeupNeedRecognize && SettingsUtils.isFaceWList()) {
                    startRecognize(mCurrentPerson.getId());
                } else {
                    processWakeupSuccess(person);
                }
            }
        }
    }

    private void handleNoTrackDetectResult(Person person) {
        synchronized (mWakeupAndPreWakeupLock) {
            Log.d(mName, "handleNoTrackDetectResult person: " + person + ", wakeupEnable: " + mWakeupEnable);
            if (mWakeupEnable) {
                if (mCurrentPerson == null) {
                    mCurrentPerson = person;
                    mInCompleteFaceWakeupCacheTimer = System.currentTimeMillis();
                } else if (!isNotNeedTrackedDetectTimeout()) {
                    mCurrentPerson = person;
                } else {
                    mWakeupEnable = false;
                    mCurrentPerson = person;
                    mInCompleteFaceWakeupCacheTimer = INVALID_TIME;
                    processWakeupSuccess(person);
                }
            }
        }
    }

    private void startRecognize(int personId) {
        JSONObject json = new JSONObject();
        try {
            json.put(ComponentParams.Recognize.PARAM_PERSON_ID, personId);
            json.put(ComponentParams.Recognize.PARAM_TIMEOUT, mRecognizeTimeout);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        mRecognizeComponent.setComponentListener(mRecognizeComponentListener);
        mRecognizeComponent.setStatusListener(mRecognizeStatusListener);
        mRecognizeComponent.start(json.toString());
    }

    private Component.ComponentListener mRecognizeComponentListener = new Component.ComponentListener() {
        @Override
        public void onFinish(int result, String message, String extraData) {
            Log.d(mName, "RecognizeComponentListener onFinish result: " + result
                    + ", message: " + message);
            Person person;
            if (!TextUtils.isEmpty(message)) {
                Gson gson = new Gson();
                person = gson.fromJson(message, Person.class);
                if (person != null) {
                    mCurrentPerson = person;
                }
                processWakeupSuccess(person);
            } else {
                updateStatus(ComponentStatus.STATUS_RECOGNIZE_FAILED, "status recognize failed");
            }
        }
    };

    private Component.ComponentStatusListener mRecognizeStatusListener = new ComponentStatusListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            Log.d(mName, "RecognizeStatusListener onStatusUpdate status: " + status
                    + ", data: " + data);
            if (WakeupAndPreWakeupStartCheckComponent.this.isAlive()) {
                updateStatus(status, data);
            }
        }
    };

    private void stopRecognize() {
        mRecognizeComponent.setComponentListener(null);
        mRecognizeComponent.stop(50);
    }

    private void processPreWakeupStatus(Person person) {
        synchronized (mWakeupAndPreWakeupLock) {
            Log.v(mName, "processPreWakeupStatus preWakeupEnable: " + mPreWakeupEnable
                    + ", mIsNeedPreWakeup: " + mIsNeedPreWakeup);
            if (mPreWakeupEnable && mIsNeedPreWakeup) {
                mPreWakeupEnable = false;
                updateStatus(ComponentStatus.STATUS_PRE_WAKEUP, person == null ? null : person.toGson());
                mHandler.sendEmptyMessageDelayed(MSG_PRE_WAKEUP_ENABLE, mPreWakeupIntervalTime);
            }
        }
    }

    private void processWakeupSuccess(Person person) {
        double personAngle = person.getAngleInView();
        double personDistance = person.getDistance();
        if (ProductInfo.isMiniProduct()) {
            processWakeupResult(ComponentResult.RESULT_SUCCESS);
        } else {
            mApi.hasObstacleInArea(personAngle - 20, personAngle + 20,
                    personDistance - 0.5, personDistance + 0.5, new CommandListener() {
                        @Override
                        public void onResult(int result, String message) {
                            if (!isAlive()) {
                                return;
                            }
                            Log.i(mName, "PersonAppear  hasObstacleInArea result: " + result + " message= " + message);
                            if ("true".equals(message)) {
                                processWakeupResult(ComponentResult.RESULT_SUCCESS);
                            } else {
                                mWakeupEnable = true;
                                updateStatus(ComponentStatus.STATUS_HAS_FACE_NO_OBSTACLE, "detect face but no obstacle");
                            }
                        }
                    });
        }
    }

    private void processWakeupResult(int result) {
        synchronized (mWakeupAndPreWakeupLock) {
            Log.d(mName, "processWakeupResult result: " + result + ", currentPerson: " + mCurrentPerson);
            PersonApi.getInstance().unregisterPersonListener(mPersonListener);
            mHandler.removeMessages(MSG_UPDATE_BEST_PERSON);
            mHandler.removeMessages(MSG_PRE_WAKEUP_ENABLE);
            stop(50, result, mCurrentPerson == null ? null : mCurrentPerson.toGson());
        }
    }

    private boolean isNotNeedTrackedDetectTimeout() {
        long currentTime = System.currentTimeMillis();
        return mInCompleteFaceWakeupCacheTimer != INVALID_TIME
                && (currentTime - mInCompleteFaceWakeupCacheTimer > mIncompleteFaceWakeupCacheTimeout);
    }

    @Override
    public void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        mInCompleteFaceWakeupCacheTimer = INVALID_TIME;
        stopRecognize();
        PersonApi.getInstance().unregisterPersonListener(mPersonListener);
        mHandler.removeMessages(MSG_UPDATE_BEST_PERSON);
        mHandler.removeMessages(MSG_PRE_WAKEUP_ENABLE);
    }
}
