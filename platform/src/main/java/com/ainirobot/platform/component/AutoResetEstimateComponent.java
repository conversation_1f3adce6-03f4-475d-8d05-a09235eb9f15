/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams.ResetEstimate;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * 监听底盘定位丢失事件，自动触发重定位
 */
public class AutoResetEstimateComponent extends Component {

    private static final int DEFAULT_RETRY_COUNT = 5;
    private static final long STOP_TIMEOUT = 50;
    private static final int DEFAULT_RETRY_INTERVAL = 0;
    private static final int DEFAULT_ESTIMATE_TYPE = -1;

    private int mRetryCount;
    private long mRetryInterval;
    private int mEstimateType;
    private String mEstimateName;
    private String mEstimatePose;
    private volatile boolean mIsEstimating = false;
    private Component mResetEstimateComponent;

    public AutoResetEstimateComponent(String name) {
        this(name, Looper.myLooper());
    }

    public AutoResetEstimateComponent(String name, Looper looper) {
        super(name, looper);
        mResetEstimateComponent = new ResetEstimateComponent(mName, mLooper);
    }

    @Override
    protected void onStart(String params) {
        Log.i(mName, "onStart, params= " + params);
        try {
            JSONObject json = new JSONObject(params);
            mRetryCount = json.optInt(ResetEstimate.PARAM_RESET_ESTIMATE_COUNT, DEFAULT_RETRY_COUNT);
            mRetryInterval = json.optInt(ResetEstimate.PARAM_RESET_ESTIMATE_INTERVAL, DEFAULT_RETRY_INTERVAL);
            mEstimateType = json.optInt(ResetEstimate.PARAM_RESET_ESTIMATE_TYPE, DEFAULT_ESTIMATE_TYPE);
            mEstimateName = json.optString(ResetEstimate.PARAM_RESET_ESTIMATE_NAME);
            mEstimatePose = json.optString(ResetEstimate.PARAM_RESET_ESTIMATE_POSE);
            if (mRetryInterval < 0) {
                mRetryInterval = DEFAULT_RETRY_INTERVAL;
            }
            mApi.getRobotStatus(Definition.STATUS_EVENT, mStatusListener);
            mApi.getRobotStatus(Definition.STATUS_POSE_ESTIMATE, mStatusListener);
            registerEventStatusListener();
        } catch (JSONException e) {
            e.printStackTrace();
            processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR, "json parse exception");
        }
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.i(mName, "onUpdate, params= " + params);
    }

    @Override
    protected void onStop(int reason) {
        Log.i(mName, "onStop reason: " + reason);
        mIsEstimating = false;
        mApi.unregisterStatusListener(mStatusListener);
        stopResetEstimate();
    }

    private void registerEventStatusListener() {
        mApi.registerStatusListener(Definition.STATUS_EVENT, mStatusListener);
        mApi.registerStatusListener(Definition.STATUS_POSE_ESTIMATE, mStatusListener);
    }

    StatusListener mStatusListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
            if (!isAlive() || TextUtils.isEmpty(data)) {
                return;
            }

            if (Definition.STATUS_EVENT.equals(type)) {
                try {
                    JSONObject json = new JSONObject(data);
                    String estimateLost = json.optString(Definition.HW_NAVI_ESTIMATE_LOST);
                    if (!TextUtils.isEmpty(estimateLost)) {
                        if (!mIsEstimating) {
                            mIsEstimating = true;
                            startResetEstimate();
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else if (Definition.STATUS_POSE_ESTIMATE.equals(type)) {
                try {
                    JSONObject json = new JSONObject(data);
                    boolean isPoseEstimate = json.optBoolean("isPoseEstimate");
                    if (!isPoseEstimate) {
                        if (!mIsEstimating) {
                            mIsEstimating = true;
                            startResetEstimate();
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
    };

    private void startResetEstimate() {
        try {
            processStatus(ComponentStatus.STATUS_AUTO_RESET_ESTIMATE_START, "");
            mResetEstimateComponent.setComponentListener(mResetEstimateComponentListener);
            mResetEstimateComponent.setStatusListener(mResetEstimateComponentStatus);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(ResetEstimate.PARAM_RESET_ESTIMATE_COUNT, mRetryCount);
            jsonObject.put(ResetEstimate.PARAM_RESET_ESTIMATE_TYPE, mEstimateType);
            jsonObject.put(ResetEstimate.PARAM_RESET_ESTIMATE_POSE, mEstimatePose);
            jsonObject.put(ResetEstimate.PARAM_RESET_ESTIMATE_NAME, mEstimateName);
            jsonObject.put(ResetEstimate.PARAM_RESET_ESTIMATE_INTERVAL, mRetryInterval);
            mResetEstimateComponent.start(jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void stopResetEstimate() {
        Log.d(mName, "stopResetEstimate");
        mResetEstimateComponent.setComponentListener(null);
        mResetEstimateComponent.setStatusListener(null);
        mResetEstimateComponent.stop(100);
    }

    private ComponentListener mResetEstimateComponentListener = new ComponentListener() {
        @Override
        public void onFinish(int result, String message, String extraData) {
            Log.i(mName, "onFinish result: " + result + ", message: " + message);
            mIsEstimating = false;
            switch (result) {
                case ComponentResult.RESULT_SUCCESS:
                    processStatus(ComponentStatus.STATUS_AUTO_RESET_ESTIMATE_SUCCESS, message);
                    break;

                case ComponentResult.RESULT_RESET_ESTIMATE_FAIL:
                    processStatus(ComponentStatus.STATUS_AUTO_RESET_ESTIMATE_FAIL, message);
                    break;

                default:
                    processStatus(result, message);
                    break;
            }
        }
    };

    private ComponentStatusListener mResetEstimateComponentStatus = new ComponentStatusListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            Log.i(mName, "onStatusUpdate status: " + status + ", data: " + data);
            processStatus(status, data);
        }
    };

    private void processResult(int result, String message) {
        Log.i(mName, "processTrackResult result: " + result + " message= " + message);
        stop(STOP_TIMEOUT, result, message, "");
    }

    private void processStatus(int status, String result) {
        Log.i(mName, "processStatus status: " + status + ", result: " + result);
        if (!isAlive()) {
            return;
        }
        updateStatus(status, result);
    }

}
