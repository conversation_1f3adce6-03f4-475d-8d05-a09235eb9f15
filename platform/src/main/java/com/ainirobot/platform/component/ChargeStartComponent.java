/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.component;

import android.os.Looper;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.ainirobot.platform.data.RobotInfo;
import com.ainirobot.platform.utils.GsonUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;

public class ChargeStartComponent extends Component {

    private static final long STOP_TIMEOUT = 500;
    private static final long DEFAULT_CHARGE_TIMEOUT = 3 * Definition.MINUTE;
    private static final double DEFAULT_AVOID_DISTANCE = 0.1;
    private static final long DEFAULT_AVOID_TIMEOUT = 20 * Definition.SECOND;
    private static final long DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT = 300 * 1000;
    private long mChargeTimeout;
    private double mAvoidDistance;
    private long mAvoidTimeout;
    private long mMultiWaitTimeout;

    private MultiFloorInfo mCurrentFloorInfo;   //当前楼层信息
    private MultiFloorInfo mChargeFloorInfo;      //充电楼层信息

    private static final int BASE_NAVIGATION_ERROR = -20010000;
    /**
     * Lora配置异常，此状态在NavigationService内定义
     */
    private static final int NAVI_ERROR_MULTIPLE_LORA_CONFIG_FAIL = BASE_NAVIGATION_ERROR - 1;
    /**
     * 多机地图不匹配，此状态在NavigationService内定义
     */
    private static final int NAVI_ERROR_MULTIPLE_MAP_NOT_MATCH = BASE_NAVIGATION_ERROR - 2;
    /**
     * 多机Lora断连，此状态在NavigationService内定义
     */
    private static final int NAVI_ERROR_MULTIPLE_LORA_DISCONNECT = BASE_NAVIGATION_ERROR - 3;

    private static final int NAVI_ERROR_MULTIPLE_VERSION_NOT_MATCH = BASE_NAVIGATION_ERROR - 4;

    public ChargeStartComponent(String name) {
        this(name, Looper.myLooper());
    }

    public ChargeStartComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    protected void onStart(String params) {
        Log.i(mName, "onStart params:" + params);
        try {
            JSONObject json = new JSONObject(params);
            mChargeTimeout = json.optLong(ComponentParams.ChargeStart.PARAM_CHARGE_TIMEOUT,
                    DEFAULT_CHARGE_TIMEOUT);
            mAvoidDistance = json.optDouble(ComponentParams.ChargeStart.PARAM_AVOID_DISTANCE,
                    DEFAULT_AVOID_DISTANCE);
            mAvoidTimeout = json.optLong(ComponentParams.ChargeStart.PARAM_AVOID_TIMEOUT,
                    DEFAULT_AVOID_TIMEOUT);
            mMultiWaitTimeout = json.optLong(ComponentParams.ChargeStart.PARAM_MULTI_WAIT_TIMEOUT,
                    DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT);
        } catch (JSONException e) {
            mChargeTimeout = DEFAULT_CHARGE_TIMEOUT;
            mAvoidDistance = DEFAULT_AVOID_DISTANCE;
            mAvoidTimeout = DEFAULT_AVOID_TIMEOUT;
            mMultiWaitTimeout = DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT;
        }
        isEstimate();
    }

    private void isEstimate() {
        mApi.isRobotEstimate(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(mName, "isEstimate result:" + result + " message:" + message);
                if (result == Definition.RESULT_OK && Definition.RESULT_TRUE.equals(message)) {
                    if (isEnableMultiNavigation()) {
                        getMultiFloorInfo();
                    } else {
                        checkChargePile();
                    }
                } else {
                    processChargeStartResult(ComponentError.ERROR_NOT_ESTIMATE);
                }
            }
        });
    }

    /**
     * 是否开启了电梯功能
     */
    private boolean isEnableMultiNavigation() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED)
                == Definition.ROBOT_SETTING_ENABLE;
    }

    private void getMultiFloorInfo() {
        mChargeFloorInfo = RobotInfo.getChargeFloorMultiInfo();
        mCurrentFloorInfo = RobotInfo.getCurrentMultiFloorInfo();
        if (null == mChargeFloorInfo || TextUtils.isEmpty(mChargeFloorInfo.getMapName())) {
            processChargeStartResult(ComponentResult.RESULT_ELEVATOR_CONFIG_INVALID);
        }
        if (null == mCurrentFloorInfo || TextUtils.isEmpty(mChargeFloorInfo.getMapName())) {
            processChargeStartResult(ComponentResult.RESULT_ELEVATOR_CONFIG_INVALID);
        }

        if (currentIsChargeFloor() && !robotIsInElevator()) {
            checkChargePile();
        } else {
            startElevatorNavigation(); //乘梯导航
        }
    }

    /**
     * 当前是否为充电楼层
     */
    private boolean currentIsChargeFloor() {
        return TextUtils.equals(mCurrentFloorInfo.getMapName(), mChargeFloorInfo.getMapName());
    }

    private boolean robotIsInElevator() {
        String alreadyInElevator = mApi.isAlreadyInElevator();
        Log.d(mName, "checkIsInElevator: " + alreadyInElevator);
        Map<String, Object> map = GsonUtil.fromJson(alreadyInElevator, Map.class);
        Double resultDouble = (Double) map.get(Definition.JSON_TASK_EXEC_RESULT);
        int result = resultDouble.intValue();
        return result == Definition.RESULT_ROBOT_IN_ELEVATOR;
    }

    private void startElevatorNavigation() {
        int targetFloor = mChargeFloorInfo.getFloorIndex();
        //不在充电楼层，先跨层导航到充电楼层的"电梯口"，再回充
        String multiDestination = mChargeFloorInfo.getAvailableElevators().get(0) + '-' +Definition.ELEVATOR_ENTER_POSE;

        mApi.startElevatorNavigation(
                multiDestination, targetFloor, true,
                new ActionListener() {
                    @Override
                    public void onResult(int status, String message, String extraData) throws RemoteException {
                        Log.e(mName, "onResult: " + status + " responseString：" + message + " extraData：" + extraData);
                        if (status == Definition.RESULT_OK) {
                            // 乘梯成功
                            Log.d(mName, "Elevator navigation successful, checking gate passing for final destination");
                            onElevatorNavigationSuccess();
                        } else {
                            // 乘梯失败
                            Log.e(mName, "Elevator navigation failed");
                            processChargeStartResult(status);
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                        Log.e(mName, "onError: " + errorCode + " errorString：" + errorString + " extraData：" + extraData);
                        if (errorCode == Definition.ERROR_IN_DESTINATION) {
                            Log.d(mName, "Elevator navigation successful, is in destination, checking gate passing for final destination");
                            onElevatorNavigationSuccess();
                        } else {
                            processChargeStartResult(errorCode);
                        }
                    }

                    @Override
                    public void onStatusUpdate(int status, String data, String extraData) throws RemoteException {
                        Log.e(mName, "onStatusUpdate: " + status + " data：" + data + " extraData：" + extraData);
                        processChargeStartStatus(status);
                    }
                });
    }

    private void onElevatorNavigationSuccess() {
        checkChargePile();
    }

    private void checkChargePile() {
        mApi.getPlace(Definition.START_BACK_CHARGE_POSE, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(mName, "getPlace result:" + result + " message:" + message);
                if (result < 0 || TextUtils.isEmpty(message)) {
                    processChargeStartResult(ComponentError.ERROR_GET_CHARGE_POSE_FAILED);
                    return;
                }
                startAutoChargeAction();
            }
        });
    }

    private void startAutoChargeAction() {
        mApi.startNaviToAutoChargeAction(mChargeTimeout, mAvoidDistance, mAvoidTimeout, mMultiWaitTimeout,
                new ActionListener() {
                    @Override
                    public void onResult(int status, String responseString) {
                        Log.i(mName, "startNaviToAutoChargeAction onResult:" + status
                                + ", responseString:" + responseString);
                        switch (status) {
                            case Definition.RESULT_OK:
                                processChargeStartResult(ComponentResult.RESULT_SUCCESS);
                                break;
                            case Definition.RESULT_FAILURE:
                                boolean isCharging = mApi.getRobotChargingStatus();
                                if (isCharging) {
                                    processChargeStartResult(ComponentResult.RESULT_SUCCESS);
                                } else {
                                    processChargeStartError(responseString);
                                }
                                break;
                            default:
                                processChargeStartResult(status);
                                break;
                        }
                    }

                    @Override
                    public void onStatusUpdate(int status, String data) {
                        Log.i(mName, "startNaviToAutoChargeAction onStatusUpdate:" + status
                                + ", data:" + data);
                        switch (status) {
                            case Definition.STATUS_START_NAVIGATION:
                                processChargeStartStatus(ComponentStatus.STATUS_START_NAVIGATION);
                                break;
                            case Definition.STATUS_NAVI_OBSTACLES_AVOID:
                                processChargeStartStatus(ComponentStatus.STATUS_OBSTACLES_AVOID);
                                break;
                            case Definition.STATUS_GOAL_OCCLUDED:
                            case Definition.STATUS_NAVI_AVOID:
                                processChargeStartStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_START);
                                break;
                            case Definition.STATUS_ALREADY_AT_START_CHARGE_POINT:
                                processChargeStartStatus(ComponentStatus.STATUS_CHARGE_START_IN_PLACE);
                                break;
                            case Definition.STATUS_NAVI_OUT_MAP:
                                processChargeStartResult(ComponentError.ERROR_NAVIGATION_OUT_MAP);
                                break;
                            case Definition.STATUS_NAVI_GLOBAL_PATH_FAILED:
                                processChargeStartResult(ComponentError.ERROR_NAVIGATION_GLOBAL_PATH_FAILED);
                                break;
                            case Definition.STATUS_VISION_CHARGE_START_GOTO_CHARGING_PILE:
                                processChargeStartStatus(ComponentStatus.STATUS_VISION_CHARGE_START_GOTO_CHARGING_PILE);
                                break;
                            case Definition.STATUS_NAVI_MULTI_ROBOT_WAITING:
                                //processChargeStartResult(ComponentStatus.STATUS_NAVI_MULTI_ROBOT_WAITING);
                                break;
                            case Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END:
                                //processChargeStartResult(ComponentStatus.STATUS_NAVI_MULTI_ROBOT_WAITING_END);
                                break;
                            default:
                                processChargeStartStatus(status);
                                break;
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorString) {
                        Log.i(mName, "startNaviToAutoChargeAction onError:" + errorCode
                                + ", errorString:" + errorString);
                        processChargeStartResult(errorCode);
                    }
                });
    }

    private void processChargeStartError(String param) {
        try {
            JSONObject jsonObject = new JSONObject(param);
            int status = jsonObject.getInt(Definition.CHARGE_FAIL_STATUS);
            switch (status) {
                case Definition.CHARGE_FAIL_WHEN_NOT_ESTIMATE:
                    processChargeStartResult(ComponentError.ERROR_NOT_ESTIMATE);
                    break;
                case Definition.CHARGE_FAIL_WHEN_NAVIGATION:
                    processChargeStartResult(ComponentError.ERROR_CHARGE_POINT_CAN_NOT_ARRIVE);
                    break;
                case Definition.CHARGE_FAIL_WHEN_LARGE_MAP_NAVI_TIMEOUT:
                    processChargeStartResult(ComponentError.ERROR_CHARGE_START_NAVI_TIMEOUT);
                    break;
                case Definition.CHARGE_FAIL_WHEN_PARSE_IN_LOCATION:
                    processChargeStartResult(ComponentError.ERROR_CHARGE_START_PARSE_IN_LOCATION);
                    break;
                case Definition.CHARGE_FAIL_WHEN_NOT_MOVE_FOR_20S:
                    processChargeStartResult(ComponentError.ERROR_CHARGE_START_NOT_MOVE);
                    break;
                case Definition.CHARGE_FAIL_WHEN_PSB_CHARGE:
                    processChargeStartResult(ComponentError.ERROR_CHARGE_START_PSB_FAIL);
                    break;
                case Definition.CHARGE_FAIL_WHEN_PSB_NO_SIGNAL:
                    processChargeStartResult(ComponentError.ERROR_CHARGE_START_PBS_NO_SIGNAL);
                    break;
                case Definition.CHARGE_FAIL_WHEN_VISION_CHARGE_START:
                    processChargeStartResult(ComponentError.ERROR_VISION_CHARGE_START_FAIL);
                    break;
                case Definition.CHARGE_FAIL_WHEN_VISION_CHARGE_STOP:
                    processChargeStartResult(ComponentError.ERROR_VISION_CHARGE_STOP_FAIL);
                    break;
                case Definition.CHARGE_FAIL_VISION_CHARGE_TIMEOUT:
                    processChargeStartResult(ComponentError.ERROR_VISION_CHARGE_TIMEOUT);
                    break;
                case Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT:
                    processChargeStartResult(ComponentError.ERROR_MULTI_ROBOT_WAITING_TIMEOUT);
                    break;
                case Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH:
                    simulateCreateNavigationErrorEvent(NAVI_ERROR_MULTIPLE_MAP_NOT_MATCH,
                            "Multiple map not match!");
                    break;
                case Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT:
                    simulateCreateNavigationErrorEvent(NAVI_ERROR_MULTIPLE_LORA_DISCONNECT,
                            "Lora disconnect!");
                    break;
                case Definition.STATUS_NAVI_MULTI_VERSION_NOT_MATCH:
                    simulateCreateNavigationErrorEvent(NAVI_ERROR_MULTIPLE_VERSION_NOT_MATCH,
                            "Multiple version not match");
                    break;
                case Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL:
                    simulateCreateNavigationErrorEvent(NAVI_ERROR_MULTIPLE_LORA_CONFIG_FAIL,
                            "Multiple version not match");
                    break;
                default:
                    break;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void simulateCreateNavigationErrorEvent(int errorCode, String desMsg) {
        Log.d(mName, "simulateCreateNavigationErrorEvent:" + errorCode);
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("code", errorCode);
            jsonObject.put("message", desMsg);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        stop(STOP_TIMEOUT, ComponentError.ERROR_MULTIPLE_MODE_ERROR, jsonObject.toString());
    }

    private void processChargeStartResult(int reason) {
        stop(STOP_TIMEOUT, reason, "");
    }

    private void processChargeStartStatus(int status) {
        updateStatus(status, "");
    }

    @Override
    protected void onUpdate(String intent, String params) {

    }

    @Override
    protected void onStop(int reason) {
        mApi.stopAutoChargeAction(true);
    }
}
