package com.ainirobot.platform.component;/* */

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.light.LightManager;
import com.ainirobot.platform.light.impl.LightMultipleColor;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.List;

public class LightGroupComponent extends Component {

    private static final long STOP_TIMEOUT = 50;
    private static final int MSG_NEXT = 0x1;
    private static final int MSG_REMOVE = 0x2;
    private static final long DEFAULT_INTERVAL_TIME = 100;

    private List<LightMultipleColor> mBeanList;
    private long mIntervalTime;
    private boolean mIsLoop;
    private int mIndex;
    protected LightGroupHandler mHandler;
    private Gson mGson;

    public LightGroupComponent(String name) {
        this(name, Looper.myLooper());
    }

    public LightGroupComponent(String name, Looper looper) {
        super(name, looper);
        mHandler = new LightGroupHandler(mLooper);
        mGson = new Gson();
    }

    private static class LightGroupHandler extends Handler {
        private volatile LightGroupComponent mComponent;

        private LightGroupHandler(Looper looper) {
            super(looper);
        }

        public void setComponent(LightGroupComponent component) {
            mComponent = component;
        }

        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_NEXT:
                    if (mComponent == null) {
                        return;
                    }
                    if (!mComponent.isAlive()) {
                        return;
                    }
                    mComponent.mIndex++;
                    Log.d("LightGroupComponent", "handleMessage uid : " + mComponent.mUid);
                    if (mComponent.mIndex >= mComponent.mBeanList.size()) {
                        if (mComponent.mIsLoop) {
                            mComponent.mIndex = 0;
                        } else {
                            mComponent.processResult(ComponentResult.RESULT_LIGHT_GROUP_SUCCESS);
                            return;
                        }
                    }
                    mComponent.startPlayNext();
                    break;
                case MSG_REMOVE:
                    mComponent = null;
                    break;
                default:
                    break;
            }
        }
    }

    /**
     {"lightGroupBean":[
     {"rgbSet":[5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911]},
     {"rgbSet":[5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911,5542911]}]
     ,"intervalTime":300,"isLoop":false}
     **/
    @Override
    protected void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        if (mApi.isUseAutoEffectLed()) {
            Log.d(mName, "not support, isUseAutoEffectLed:" + mApi.isUseAutoEffectLed());
            return;
        }
        try {
            JSONObject json = new JSONObject(params);
            String lightGroupStr = json.optString(ComponentParams.LightGroup.PARAM_LIGHT_GROUP_BEAN, "");
//            int zcbLightEffect = json.optInt(ComponentParams.LightGroup.PARAM_LIGHT_GROUP_BEAN, Definition.LED_EFFECT_ALLOFF);

            if (!mApi.isUseAutoEffectLed()) {
                if (TextUtils.isEmpty(lightGroupStr)) {
                    processResult(ComponentError.ERROR_PARAMS_LIGHT_BEAN_GROUP_INVALID);
                    return;
                }
            }

            mBeanList = mGson.fromJson(lightGroupStr, new TypeToken<List<LightMultipleColor>>() {
            }.getType());
            if (mBeanList == null || mBeanList.size() <= 0) {
                if (!mApi.isUseAutoEffectLed()){
                    processResult(ComponentError.ERROR_PARAMS_LIGHT_BEAN_GROUP_INVALID);
                    return;
                }
            }
            mIntervalTime = json.optLong(ComponentParams.LightGroup.PARAM_INTERVAL_TIME,
                    DEFAULT_INTERVAL_TIME);
            if (mIntervalTime < DEFAULT_INTERVAL_TIME) {
                mIntervalTime = DEFAULT_INTERVAL_TIME;
            }
            mIsLoop = json.optBoolean(ComponentParams.LightGroup.PARAM_IS_LOOP, false);
            mIndex = 0;
            mHandler.setComponent(this);
//            if (mApi.isUseProZcbLed()) {
//                setProZcbLed(zcbLightEffect);
//            } else {
//            }
            startPlayNext();
        } catch (JSONException e) {
            processResult(ComponentError.ERROR_PARAMS_LIGHT_BEAN_GROUP_INVALID);
        }
    }

    private void startPlayNext() {
        if (!isAlive()) {
            return;
        }
        LightMultipleColor bean = mBeanList.get(mIndex);
        Log.d(mName, "startPlayNext bean: " + Arrays.toString(bean.getRgbSet())
                + ", mIndex: " + mIndex);
        mHandler.removeMessages(MSG_NEXT);
        mHandler.sendEmptyMessageDelayed(MSG_NEXT, mIntervalTime);
        LightManager.getInstance().playMultipleColor(bean);
    }

    private void processResult(int result) {
        Log.d(mName, "processResult result: " + result);
        mHandler.removeMessages(MSG_NEXT);
        mHandler.removeMessages(MSG_REMOVE);
        mHandler.removeCallbacksAndMessages(null);
        mHandler.sendEmptyMessage(MSG_REMOVE);
        stop(STOP_TIMEOUT, result, null);
        mIsLoop = false;
    }

    @Override
    protected void onUpdate(String intent, String params) {
    }

    @Override
    protected void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason+", removeMSG");
        mHandler.removeMessages(MSG_NEXT);
        mHandler.removeMessages(MSG_REMOVE);
        mHandler.removeCallbacksAndMessages(null);
        mHandler.sendEmptyMessage(MSG_REMOVE);
        mIsLoop = false;
    }
}
