/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.bean.Result;
import com.ainirobot.platform.bean.ReceptionRegisterBean;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentParams.ReceptionRegister;
import com.ainirobot.platform.utils.MessageUtils;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;

public class ReceptionRegisterComponent extends Component {

    private static final long STOP_TIMEOUT = 100;
    private String mLocalPhotoSrc;
    private String mGuestName;
    private String mTaskId;
    private int mHasImage;

    public ReceptionRegisterComponent(String name) {
        this(name, Looper.myLooper());
    }

    public ReceptionRegisterComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    protected void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            mHasImage = json.optInt(ReceptionRegister.PARAM_HAS_IMAGE, 1);
            Log.d(mName, "onStart params: mHasImage ——》" + mHasImage);
            mLocalPhotoSrc = json.optString(ReceptionRegister.PARAM_PHOTO_SRC, "");
            Log.d(mName, "onStart params: mLocalPhotoSrc ——》" + mLocalPhotoSrc);
            if (mHasImage==1 && !checkFileExist(mLocalPhotoSrc)) {
                Log.d(mName, "onStart params: mLocalPhotoSrc");
                processReceptionRegister(ComponentError.ERROR_PARAMS_PHOTO_PATH_INVALID, mLocalPhotoSrc);
                return;
            }
            mGuestName = json.optString(ReceptionRegister.PARAM_GUEST_NAME, "");
            Log.d(mName, "onStart params: mGuestName ——》" + mGuestName);
            if (TextUtils.isEmpty(mGuestName)) {
                processReceptionRegister(ComponentError.ERROR_PARAMS_GUEST_NAME_INVALID, mGuestName);
                return;
            }
            mTaskId = json.optString(ReceptionRegister.PARAM_TASK_ID, "");
            if (TextUtils.isEmpty(mTaskId)) {
                processReceptionRegister(ComponentError.ERROR_PARAMS_TASK_ID_INVALID, mTaskId);
                return;
            }
            startReceptionRegister();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private boolean checkFileExist(String src) {
        if (TextUtils.isEmpty(src)) {
            return false;
        }
        if (":".equals(src)){
            return false;
        }
        File file = new File(src);
        return  file.exists() && file.isFile();
    }

    private void startReceptionRegister() {
        mApi.startReceptionRegister(1, mLocalPhotoSrc, mGuestName, mTaskId, mHasImage,
                new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(mName, " startReceptionRegister onResult result: " + result
                                + ", message: " + message);
                        if (Definition.RESULT_OK == result && MessageUtils.isRemoteSuccess(message)) {
                            Gson gson = new Gson();
                            Result<LinkedTreeMap> jsonResult = gson.fromJson(message, Result.class);
                            if (jsonResult.isSucceed()) {
                                String jsonStr = jsonResult.getData().toString();
                                ReceptionRegisterBean beanModule = gson.fromJson(jsonStr,
                                        ReceptionRegisterBean.class);
                                processReceptionRegister(ComponentResult.RESULT_SUCCESS,
                                        new Gson().toJson(beanModule));
                            } else { //error code
                                processReceptionRegister(ComponentError.ERROR_REMOTE_RECEPTION_REGISTER_FAILED,
                                        Integer.toString(jsonResult.getCode()));
                            }
                        } else {
                            processReceptionRegister(ComponentError.ERROR_RECEPTION_REGISTER_TIMEOUT,
                                    null);
                        }
                    }
                });
    }

    @Override
    protected void onUpdate(String intent, String params) {
    }

    private void processReceptionRegister(int result, String message) {
        Log.d(mName, "processReceptionRegister result: " + result + ", message: " + message);
        stop(STOP_TIMEOUT, result, message);
    }

    @Override
    protected void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
    }
}
