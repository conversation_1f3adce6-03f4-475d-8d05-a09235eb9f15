package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.bean.BasicMotionBean;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by Orion on 2019/8/10.
 */
public class BasicMotionComponent extends Component {

    private static final long STOP_TIMEOUT = 200;

    private BasicMotionBean mBean;

    private BasicMotionBean.Mode mMode;
    private float mLinearSpeed;
    private float mDistance;
    private float mAngularSpeed;
    private float mAngle;
    private boolean mAvoidStop = false;

    /**
     * 最大旋转角度
     */
    private static final float MAX_ANGLE = 360;

    /**
     * 最大距离
     */
    private static final float MAX_DISTANCE = 5;

    /**
     * 线速度区间：0.1 - 1.2 m/s，默认0.3
     */
    private static final float MIN_LINEAR_SPEED = 0.1f;
    private static final float MAX_LINEAR_SPEED = 1.2f;

    /**
     * 角速度区间：10 - 100 角度/s （约 0.2 - 1.8 弧度/s），默认30
     */
    private static final float MIN_ANGULAR_SPEED = 10;
    private static final float MAX_ANGULAR_SPEED = 100;

    public BasicMotionComponent(String name) {
        this(name, Looper.myLooper());
    }

    public BasicMotionComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    protected void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            String motionBeanStr = json.optString(ComponentParams.BasicMotion.PARAM_BASIC_MOTION_BEAN,
                    "");
            if (TextUtils.isEmpty(motionBeanStr)) {
                processResult(ComponentError.ERROR_PARAMS_BASIC_MOTION_BEAN_INVALID
                        , "params string is empty");
                return;
            }

            Gson gson = new Gson();
            mBean = gson.fromJson(motionBeanStr, BasicMotionBean.class);
            if (mBean == null) {
                processResult(ComponentError.ERROR_PARAMS_BASIC_MOTION_BEAN_INVALID
                        , "parse motion bean null");
                return;
            }

            if (initParams()) {
                navigationMotion();
            }
        } catch (JSONException e) {
            processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR, "Json parse error");
        }
    }

    private boolean initParams() {
        mMode = mBean.getMode();
        mLinearSpeed = mBean.getLinearSpeed();
        mDistance = mBean.getDistance();
        mAngularSpeed = mBean.getAngularSpeed();
        mAngle = mBean.getAngle();
        mAvoidStop = mBean.isAvoidStop();

        if (mMode == BasicMotionBean.Mode.goForward && mDistance <= 0
                || mMode == BasicMotionBean.Mode.goBackward && mDistance <= 0
                || mMode == BasicMotionBean.Mode.turnLeft && mAngle <= 0
                || mMode == BasicMotionBean.Mode.turnRight && mAngle <= 0) {
            processResult(ComponentError.ERROR_PARAMS_BASIC_MOTION_BEAN_INVALID
                    , "motion offset invalid");
            return false;
        }

        if (mLinearSpeed < MIN_LINEAR_SPEED) {
            mLinearSpeed = MIN_LINEAR_SPEED;
        } else if (mLinearSpeed > MAX_LINEAR_SPEED) {
            mLinearSpeed = MAX_LINEAR_SPEED;
        }
        if (mDistance > MAX_DISTANCE) {
            mDistance = MAX_DISTANCE;
        }

        if (mAngularSpeed < MIN_ANGULAR_SPEED) {
            mAngularSpeed = MIN_ANGULAR_SPEED;
        } else if (mAngularSpeed > MAX_ANGULAR_SPEED) {
            mAngularSpeed = MAX_ANGULAR_SPEED;
        }
//        if (mAngle > MAX_ANGLE) {
//            mAngle = MAX_ANGLE;
//        }
        return true;
    }

    private void navigationMotion() {
        switch (mMode) {
            case goForward:
                mApi.goForward(mLinearSpeed, mDistance, mMotionCommandListener);
                break;
            case goBackward:
                mApi.goBackward(mLinearSpeed, mDistance, mMotionCommandListener);
                break;
            case turnLeft:
                mApi.turnLeft(mAngularSpeed, mAngle, mMotionCommandListener);
                break;
            case turnRight:
                mApi.turnRight(mAngularSpeed, mAngle, mMotionCommandListener);
                break;
            default:
                break;
        }
    }

    CommandListener mMotionCommandListener = new CommandListener() {
        @Override
        public void onResult(int result, String message) {
            Log.d(mName, "onResult : " + result + ", " + message);
            switch (result) {
                case Definition.RESULT_OK:
                    if (Definition.SUCCEED.equals(message)) {
                        processResult(ComponentResult.RESULT_SUCCESS, "motion success");
                    } else {
                        processResult(result, message);
                    }
                    break;
                case Definition.ACTION_RESPONSE_STOP_SUCCESS:
                    processResult(ComponentResult.RESULT_MOTION_STOP_SUCCESS, "stop success");
                    break;
                default:
                    processResult(result, message);
                    break;
            }
        }

        @Override
        public void onError(int errorCode, String errorString) {
            Log.d(mName, "onError : " + errorCode + ", " + errorString);
            switch (errorCode) {
                case Definition.ACTION_RESPONSE_REQUEST_RES_ERROR:
                    processResult(ComponentError.ERROR_REQUEST_RES_FAILED, "Res error");
                    break;
                default:
                    processResult(errorCode, errorString);
                    break;
            }
        }
    };

    @Override
    protected void onUpdate(String intent, String params) {

    }

    @Override
    protected void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        if (stopMoveWhenExit(reason)) {
            mApi.stopMove(null);
        }
    }

    private void processResult(int result, String message) {
        Log.d(mName, "processResult result: " + result);
        if (!isAlive()) {
            return;
        }
        stop(STOP_TIMEOUT, result, message);
    }

    private boolean stopMoveWhenExit(int reason) {
        if (reason == ComponentError.ERROR_REQUEST_RES_FAILED
                || reason == ComponentError.ERROR_PARAMS_BASIC_MOTION_BEAN_INVALID
                || reason == ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR) {
            return false;
        }
        return true;
    }

}
