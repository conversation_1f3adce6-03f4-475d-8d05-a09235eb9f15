/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *RecoverPositive
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.HeadTurnBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

public class NavigationBackComponent extends Component {

    /**
     * 支持水平 -270 角度的硬件版本
     */
    private static final String HEAD_TYPE_NEED_ROTATE = "H401";
    private static final double MAX_DIFF_ANGLE = 20;

    /**
     * 底盘最大速度，底盘原地旋转会把实际速度加倍，
     * 调用传参 40 ，底盘实际最大会加速到 80
     */
    private static final float BODY_SPEED = 40;
    /**
     * 导航任务默认优先级 值越大优先级越高，多机场景下使用
     */
    private static final int DEFAULT_NAVIGAITON_TASK_PRIORITY = 0;
    private String mDestination;
    private Pose mDestinationPose;
    private String mFinalDestination;
    private Pose mCurrentPose;
    private Gson mGson;
    private Component mHeadTurnComponent;
    private String mHardWareVersion;
    private double mDefaultLinearSpeed;
    private double mDefaultAngularSpeed;
    private double mLinearSpeed;
    private double mAngularSpeed;
    private int mTaskPriority;

    public NavigationBackComponent(String name) {
        this(name, Looper.myLooper());
    }

    public NavigationBackComponent(String name, Looper looper) {
        super(name, looper);
        mGson = new Gson();
        mHardWareVersion = "";
        mHeadTurnComponent = new HeadTurnComponent(mName, looper);

        mDefaultLinearSpeed = RobotSettingApi.getInstance().getRobotFloat(
                Definition.ROBOT_SETTING_NAV_LINEAR_SPEED);
        mDefaultAngularSpeed = RobotSettingApi.getInstance().getRobotFloat(
                Definition.ROBOT_SETTING_NAV_ANGULAR_SPEED);
    }

    @Override
    public void onStart(String params) {
        Log.i(mName, "onStart : " + params);
        try {
            JSONObject json = new JSONObject(params);
            mDestination = json.optString(ComponentParams.NavigationBack.PARAM_DESTINATION);
            mLinearSpeed = json.optDouble(ComponentParams.NavigationBack.PARAM_LINEAR_SPEED, mDefaultLinearSpeed);
            mAngularSpeed = json.optDouble(ComponentParams.NavigationBack.PARAM_ANGULAR_SPEED, mDefaultAngularSpeed);
            mTaskPriority = json.optInt(ComponentParams.NavigationBack.PARAM_PRIORITY, DEFAULT_NAVIGAITON_TASK_PRIORITY);
            if (TextUtils.isEmpty(mDestination)) {
                processResult(ComponentError.ERROR_PARAMS_PLACE_NAME_INVALID, null);
                return;
            }
            Log.d(mName, "onStart mLinearSpeed: " + mLinearSpeed + " mAngularSpeed:" + mAngularSpeed);
            isEstimate();
        } catch (JSONException e) {
            e.printStackTrace();
            processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR, "json parse exception");
        }
    }

    private void isEstimate() {
        mApi.isRobotEstimate(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                if (!isAlive()) {
                    Log.e(mName, "isEstimate onResult has not alive");
                    return;
                }
                Log.d(mName, "isEstimate result:" + result + " message:" + message);
                if (result == Definition.RESULT_OK && "true".equals(message)) {
                    startResetHead();
                } else {
                    processResult(ComponentError.ERROR_NOT_ESTIMATE, null);
                }
            }
        });
    }

    private void startResetHead() {
        mHeadTurnComponent.setComponentListener(new ComponentListener() {
            @Override
            public void onFinish(int result, String message, String extraData) {
                if (!NavigationBackComponent.this.isAlive()) {
                    Log.e(mName, "startResetHead onFinish has not alive");
                    return;
                }
                Log.d(mName, "startResetHead onFinish result: " + result + ", message: " + message);
                mHeadTurnComponent.setComponentListener(null);
                NavigationBackComponent.this.getDestinationPosition();
            }
        });
        mHeadTurnComponent.setStatusListener(null);
        HeadTurnBean bean = new HeadTurnBean();
        bean.setHorizontalAngle(0);
        bean.setHorizontalMode(HeadTurnBean.HeadTurnMode.absolute);
        bean.setVerticalAngle(70);
        bean.setVerticalMode(HeadTurnBean.HeadTurnMode.absolute);
        JSONObject json = new JSONObject();
        try {
            json.put(ComponentParams.HeadTurn.PARAM_HEAD_TURN_BEAN, mGson.toJson(bean));
            mHeadTurnComponent.start(json.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void getDestinationPosition() {
        mApi.getPlace(mDestination, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                if (!isAlive()) {
                    Log.e(mName, "getDestinationPosition onResult has not alive");
                    return;
                }
                Log.d(mName, "getDestinationPosition onResult result:" + result + " message:" + message);
                if (result < 0 || TextUtils.isEmpty(message)) {
                    processResult(ComponentError.ERROR_PLACE_NOT_EXIST, null);
                    return;
                }
                Pose pose = mGson.fromJson(message, Pose.class);
                if (pose == null) {
                    processResult(ComponentError.ERROR_PLACE_NOT_EXIST, null);
                    return;
                }
                mDestinationPose = pose;
                JSONObject object = new JSONObject();
                try {
                    object.put("x", mDestinationPose.getX());
                    object.put("y", mDestinationPose.getY());
                    object.put("theta", Math.PI + mDestinationPose.getTheta());
                    mFinalDestination = object.toString();
                } catch (JSONException e) {
                    e.printStackTrace();
                }

                mHardWareVersion = "";
                startNavigation();
            }
        });
    }

    private void startHeadBodyReverseMove(final boolean isFinal) {
        Log.d(mName, "startHeadBodyReverseMove isFinal: " + isFinal);
        if(isFinal && mHeadTurnComponent.isAlive()){
            mHeadTurnComponent.setComponentListener(null);
            Log.i(mName, "head turn compontent still alive, stop it");
            mHeadTurnComponent.stop(0);
        }
        mHeadTurnComponent.setComponentListener(new ComponentListener() {
            @Override
            public void onFinish(int result, String message, String extraData) {
                if (!NavigationBackComponent.this.isAlive()) {
                    Log.e(mName, "startHeadBodyReverseMove onFinish has not alive");
                    return;
                }
                Log.d(mName, "startHeadBodyReverseMove onFinish result: " + result
                        + ", message: " + message);
                mHeadTurnComponent.setComponentListener(null);
            }
        });
        mHeadTurnComponent.setStatusListener(new ComponentStatusListener() {
            @Override
            public void onStatusUpdate(int status, String data, String extraData) {
                if (!NavigationBackComponent.this.isAlive()) {
                    Log.e(mName, "startHeadBodyReverseMove onStatusUpdate has not alive");
                    return;
                }
                Log.d(mName, "startHeadBodyReverseMove onStatusUpdate status: " + status
                        + ", data: " + data);
                if (status == ComponentStatus.STATUS_HEAD_TURN_START) {
                    if (isFinal) {
                        NavigationBackComponent.this.resumeChassisTheta();
                    } else {
                        NavigationBackComponent.this.startTurnBack();
                    }
                }
            }
        });
        HeadTurnBean bean = new HeadTurnBean();
        bean.setHorizontalAngle(isFinal ? 0 : -180);
        bean.setHorizontalMode(HeadTurnBean.HeadTurnMode.absolute);
        bean.setHorizontalMaxSpeed(80);
        bean.setVerticalAngle(70);
        bean.setVerticalMode(HeadTurnBean.HeadTurnMode.absolute);
        JSONObject json = new JSONObject();
        try {
            json.put(ComponentParams.HeadTurn.PARAM_HEAD_TURN_BEAN, mGson.toJson(bean));
            mHeadTurnComponent.start(json.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 底盘向右转 180
     */
    private void startTurnBack() {
        Pose revertPose = mGson.fromJson(mFinalDestination, Pose.class);
        double angularSpeed = Math.toRadians(BODY_SPEED); //弧度值 1.5707963267948966
        mApi.turnToTargetDirection(revertPose, 0.0, angularSpeed, false, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(mName, "startTurnBack onResult: " + result + ", " + message);
                if (result == Definition.RESULT_OK && "true".equals(message)) {
                    startNavigation();
                } else {
                    processResult(ComponentError.ERROR_TURN_BACK_FAILED, null);
                }
            }

            @Override
            public void onError(int errorCode, String errorString) {
                Log.d(mName, "startTurnBack onError: " + errorCode + ", " + errorString);
                processResult(ComponentError.ERROR_TURN_BACK_FAILED, null);
            }

            @Override
            public void onStatusUpdate(int status, String data) {
                Log.d(mName, "startTurnBack onStatusUpdate: " + status + ", " + data);
            }
        });
    }

    private void startNavigation() {
        Log.d(mName, "startNavigation");
        String velocity = null;
        try {
            JSONObject vcObject = new JSONObject();
            vcObject.put(Definition.JSON_NAVI_LINEAR_SPEED, mLinearSpeed);
            vcObject.put(Definition.JSON_NAVI_ANGULAR_SPEED, mAngularSpeed);
            velocity = vcObject.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (!HEAD_TYPE_NEED_ROTATE.equals(mHardWareVersion)) {
            JSONObject object = new JSONObject();
            try {
                object.put("x", mDestinationPose.getX());
                object.put("y", mDestinationPose.getY());
                object.put("theta", mDestinationPose.getTheta());
                mFinalDestination = object.toString();
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        mApi.goPosition(mFinalDestination, velocity, false, 0.0D, mTaskPriority, new CommandListener() {
            @Override
            public void onResult(int status, String responseString) {
                Log.i(mName, "startNavigation result:" + status + ", msg:" + responseString);
                switch (status) {
                    case Definition.RESULT_OK: //仅仅表示api执行成功, 并不表示goPosition 结果正常,是否已经到达目的地.
                        if (Definition.NAVIGATION_OK.equals(responseString)){
                            if (HEAD_TYPE_NEED_ROTATE.equals(mHardWareVersion)) {
                                startHeadBodyReverseMove(true);
                            } else {
                                processResult(ComponentResult.RESULT_SUCCESS, null);
                            }
                        }else {
                            processResult(ComponentError.ERROR_NAVIGATION_BACK_FAILED, null);
                        }
                        break;
                    case Definition.RESULT_FAILURE:
                    default:
                        processResult(ComponentError.ERROR_NAVIGATION_BACK_FAILED, null);
                        break;
                }
            }

            @Override
            public void onError(int errorCode, String errorString){
                Log.i(mName, "startNavigation onError:" + errorCode + ", msg:" + errorString);
                processResult(ComponentError.ERROR_NAVIGATION_BACK_FAILED, null);
            }

        });
    }

    /**
     * 底盘向右转 180
     */
    private void resumeChassisTheta(){
        float linearSpeed = 0.7f;
        double angularSpeed = Math.toRadians(BODY_SPEED); //弧度值 1.5707963267948966
        String poseStr = "";
        try {
            JSONObject object = new JSONObject();
            object.put("px", mDestinationPose.getX());
            object.put("py", mDestinationPose.getY());
            object.put("theta", mDestinationPose.getTheta());
            poseStr = object.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Pose pose = mGson.fromJson(poseStr, Pose.class);
        mApi.turnToTargetDirection(pose, linearSpeed, angularSpeed, true, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(mName, "resumeChassisTheta onResult: " + result + ", " + message);
                if (result == Definition.RESULT_OK && "true".equals(message)) {
                    processResult(ComponentResult.RESULT_SUCCESS, null);
                } else {
                    processResult(ComponentError.ERROR_NAVIGATION_BACK_FAILED, null);
                }
            }

            @Override
            public void onError(int errorCode, String errorString) {
                Log.d(mName, "resumeChassisTheta onError: " + errorCode + ", " + errorString);
                processResult(ComponentError.ERROR_NAVIGATION_BACK_FAILED, null);
            }

            @Override
            public void onStatusUpdate(int status, String data) {
                Log.d(mName, "resumeChassisTheta onStatusUpdate: " + status + ", " + data);
            }
        });
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.i(mName, "onUpdate " + intent + ", " + params);
    }

    @Override
    public void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        mApi.stopMove(null);
        mApi.stopGoPosition();
        mApi.stopNavigationBack();
        mApi.stopTurnToTargetDirection();
        if (mHeadTurnComponent != null) {
            mHeadTurnComponent.setStatusListener(null);
            mHeadTurnComponent.setComponentListener(null);
            Log.d(mName, "Stop head component");
            mHeadTurnComponent.stop(300);
        }
    }

    private void processResult(int result, String message) {
        if (!isAlive())
            return;
        Log.d(mName, "processResult ," + result + ", " + message);
        stop(500, result, message);
    }

}
