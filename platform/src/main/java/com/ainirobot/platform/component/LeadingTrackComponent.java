/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.platform.component;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.person.PersonApi;
import com.ainirobot.coreservice.client.person.PersonListener;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.PlatformDef;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentParams.LeadingTrack;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.ainirobot.platform.speech.SpeechManager;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.concurrent.Future;

public class LeadingTrackComponent extends Component {

    /*default value*/
    private static final long DEFAULT_LOST_TIME = 2 * 1000;
    private static final double DEFAULT_FARAWAY_DISTANCE = 2.8d;
    private static final double FARAWAY_DISTANCE_DEVIATION = 0.2d;
    private static final long DEFAULT_DETECT_DELAY = 5 * 1000;
    private static final long PERSON_APPEAR_STOP_TIMEOUT = 100;
    private static final long DEFAULT_PERSON_APPEAR_MAX_DISTANCE = 3;
    private static final long DEFAULT_PERSON_APPEAR_MAX_FACE_ANGLE_X = 60;
    private static final long DEFAULT_PERSON_APPEAR_TIMEOUT = 1500;
    private static final boolean DEFAULT_NEED_TRACK = true;
    private final Object HEAD_LOCK = new Object();

    /**
     * 支持水平 -270 角度的硬件版本
     */
    private static final String HEAD_TYPE_NEED_ROTATE = "H401";

    /**
     * 绝对角度转动云台模式
     */
    private static final String HEAD_ABSOLUTE_MODE = "absolute";

    /**
     * 云台默认俯仰速度
     */
    private static final int DEFAULT_ANGULAR_SPEED_VERTICAL = 20;

    /**
     * 云台水平正方向角度
     */
    private static final int DEFAULT_FRONT_HORIZONTAL_ANGLE = 0;

    /**
     * 规避云台高速归正的偏差角度
     */
    private static final int FRONT_HORIZONTAL_ANGLE_OFFSET = -5;

    /**
     * 云台水平向后角度
     */
    private static final int DEFAULT_BACK_HORIZONTAL_ANGLE = 180;

    /**
     * 云台默认俯仰角度
     */
    private static final int DEFAULT_VERTICAL_ANGLE = 70;

    /**
     * 过程中转头速度
     */
    private static final int MIDDLE_ANGULAR_SPEED_HORIZONTAL = 80;

    /**
     * 任务退出，头部归正速度
     */
    private static final int RESET_ANGULAR_SPEED_HORIZONTAL = 90;

    /**
     * 非法距离视觉信息
     */
    private static final double ILLEGALITY_PERSON_DISTANCE = 8848;

    /**
     * 机器人正后方拾音角度
     */
    private static final String VOICE_CENTER_ANGLE_BACK = "180";

    /**
     * 不需要转头时，拾音角度设置到机器后方的延迟时间
     * 需求定义：等待导航转身动作完成
     */
    private static final long SET_VOICE_PICKUP_ANGLE_DELAY = 3500;

    /**
     * 开始转头默认延迟2s
     * 需求定义：避免和导航转身动作重合
     */
    private static final long TIME_HEAD_ROTATE_DELAY = 2 * 1000;

    /**
     * 默认超距等待没有超时
     */
    private static final long DEFAULT_WAIT_TIME = -1;

    private long mLostTime;
    private long mWaitTime;
    private long mDetectDelayTime;
    private double mFarawayDistance;
    private Future mLostTask;
    private Future mWaitTask;
    private Future mDetectDelayTask;
    private PersonAppearComponent mPersonAppearComponent;
    private double mPersonAppearMaxDistance;
    private double mPersonAppearMaxAngleX;
    private long mPersonAppearTimeout;
    private TrackState mTrackState = TrackState.IDLE;
    private boolean mIsNearDestination;

    /**
     * 前端自定义字段，是否需要检测目标人
     */
    private boolean mNeedFindPerson = DEFAULT_NEED_TRACK;

    /**
     * 过程中是否需要检测人丢和超距
     */
    private boolean mNeedCheckPersonInProcess = false;

    /**
     * 是否支持水平最大限角 -270 度，即头部转向朝后方向
     */
    private boolean mNeedRotateHeadBack = false;

    /**
     * 引领启动 Error
     */
    private boolean mHasLeadingError = false;


    private enum TrackState {
        IDLE, PREPARE, CHECKING, GUEST_LOST, GUEST_FARAWAY, GUEST_FARAWAY_TIMEOUT
    }

    public LeadingTrackComponent(String name) {
        this(name, Looper.myLooper());
    }

    public LeadingTrackComponent(String name, Looper looper) {
        super(name, looper);
        mPersonAppearComponent = new PersonAppearComponent(mName, mLooper);
    }

    @Override
    public void onStart(String params) {
        Log.i(mName, "onStart, params= " + params);
        try {
            JSONObject jsonObject = new JSONObject(params);
            mLostTime = jsonObject.optLong(LeadingTrack.PARAM_LOST_TIME, DEFAULT_LOST_TIME);
            mWaitTime = jsonObject.optLong(LeadingTrack.PARAM_WAIT_TIME, DEFAULT_WAIT_TIME);
            mFarawayDistance = jsonObject.optDouble(LeadingTrack.PARAM_FARAWAY_DISTANCE, DEFAULT_FARAWAY_DISTANCE);
            mDetectDelayTime = jsonObject.optLong(LeadingTrack.PARAM_DETECT_DELAY, DEFAULT_DETECT_DELAY);
            mPersonAppearMaxDistance = jsonObject.optDouble(LeadingTrack.PARAM_PERSON_APPEAR_MAX_DISTANCE,
                    DEFAULT_PERSON_APPEAR_MAX_DISTANCE);
            mPersonAppearMaxAngleX = jsonObject.optDouble(LeadingTrack.PARAM_PERSON_APPEAR_MAX_FACE_ANGLE_X,
                    DEFAULT_PERSON_APPEAR_MAX_FACE_ANGLE_X);
            mPersonAppearTimeout = jsonObject.optLong(LeadingTrack.PARAM_PERSON_APPEAR_TIMEOUT,
                    DEFAULT_PERSON_APPEAR_TIMEOUT);
            mNeedFindPerson = jsonObject.optBoolean(LeadingTrack.PARAM_TRACK_NEED_FIND_PERSON,
                    DEFAULT_NEED_TRACK);
            mIsNearDestination = false;

            checkIsNeedFindPerson();
        } catch (JSONException e) {
            e.printStackTrace();
            processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR, "json parse exception");
        }
    }

    private void checkIsNeedFindPerson() {
        mTrackState = TrackState.PREPARE;
        if (mNeedFindPerson) {
            startWaitPersonAppear(new IWaitPerson() {
                @Override
                public void personFond(Person person) {
                    Log.i(mName, "startWaitPersonAppear, personFond : " + person.toString());
                    processStatus(ComponentStatus.STATUS_LEAD_TRACK_FIND_PERSON,
                            "find person can be tracked, personId is " + person.getId());
                    mApi.setTrackTarget(null, person.getId(),
                            Definition.TrackMode.BODY_LEAD, mTrackListener);
                }

                @Override
                public void waitTimeOut() {
                    Log.i(mName, "startWaitPersonAppear, waitTimeOut");
                    headRotate(false);
                }
            });
        } else {
            headRotate(false);
        }
    }

    /**
     * 找人和 track 动作结束后开始转头
     */
    private void headRotate(final boolean trackSuccess) {
        Log.i(mName, "head rotate, trackSuccess= " + trackSuccess);
        if (!trackSuccess)
            processStatus(ComponentStatus.STATUS_TRACK_FAILED, "track failed");

        checkIsNeedRotateHead(new IHeadRotate() {
            @Override
            public void needRotate() {
                mNeedRotateHeadBack = true;
                moveHeadRotateImpl();

                if (trackSuccess)
                    startDetectPersonInProcess("track success and rotate head to back");
            }

            @Override
            public void noNeedRotate() {
                mNeedRotateHeadBack = false;
                mApi.resetHead(null);
                DelayTask.submit(new Runnable() {
                    @Override
                    public void run() {
                        if (mTrackState != TrackState.IDLE)
                            setVoicePickupAngle(VOICE_CENTER_ANGLE_BACK);
                    }
                }, SET_VOICE_PICKUP_ANGLE_DELAY);

                if (trackSuccess)
                    switchBackwardCamera();
            }
        });
    }

    private void switchBackwardCamera() {
        mApi.switchCamera(Definition.JSON_HEAD_BACKWARD, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(mName, "switch backward camera : " + result + ", " + message);
                if (Definition.RESULT_OK == result) {
                    startDetectPersonInProcess("track success and switch camera to backward");
                } else {
                    processStatus(ComponentStatus.STATUS_CAMERA_SWITCH_FAILED,
                            "camera switch failed");
                }
            }
        });
    }

    private void startDetectPersonInProcess(String message) {
        Log.i(mName, "need to detect person in process");
        mNeedCheckPersonInProcess = true;
        processStatus(ComponentStatus.STATUS_TRACK_SUCCESS, message);
        mTrackState = TrackState.CHECKING;
        if (!startDetectDelayTimer()) {
            startGetAllPersonInfo();
        }
    }

    /**
     * 云台归正
     */
    private void resetHeadBySpeed(int hSpeed, int hAngle) {
        Log.i(mName, "resetHeadBySpeed, hSpeed= " + hSpeed + " hAngle= " + hAngle);
        mApi.moveHead(HEAD_ABSOLUTE_MODE, HEAD_ABSOLUTE_MODE
                , hAngle, DEFAULT_VERTICAL_ANGLE
                , hSpeed, DEFAULT_ANGULAR_SPEED_VERTICAL
                , new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.i(mName, "resetHeadBySpeed onResult : result= "
                                + result + " message= " + message);
                    }
                });
    }

    /**
     * 云台水平转向后 -180 度
     */
    private void moveHeadRotateImpl() {
        DelayTask.submit(this, new Runnable() {
            @Override
            public void run() {
                if (mHasLeadingError || mIsNearDestination) return;

                Log.i(mName, "start rotate head, mTrackState= " + mTrackState);
                mApi.moveHead(HEAD_ABSOLUTE_MODE, HEAD_ABSOLUTE_MODE
                        , DEFAULT_BACK_HORIZONTAL_ANGLE, DEFAULT_VERTICAL_ANGLE
                        , MIDDLE_ANGULAR_SPEED_HORIZONTAL, DEFAULT_ANGULAR_SPEED_VERTICAL
                        , new CommandListener() {
                            @Override
                            public void onResult(int result, String message) {
                                Log.i(mName, "moveHeadRotateImpl onResult : result= "
                                        + result + " message= " + message);
                            }
                        });
            }
        }, TIME_HEAD_ROTATE_DELAY);
    }

    /**
     * 检测云台水平最大限角是否是 -270 度
     */
    private void checkIsNeedRotateHead(final IHeadRotate iHeadRotate) {
        if (mHasLeadingError) {
            Log.i(mName, "has leading error, do not need rotate head");
            iHeadRotate.noNeedRotate();
            return;
        }

        SharedPreferences sharedPreferences = BaseApplication.getContext()
                .getSharedPreferences(PlatformDef.DEVELOPER_CONFIGURATION_ITEM, Context.MODE_PRIVATE);
        boolean rotate = sharedPreferences.getBoolean(PlatformDef.OPEN_HEAD_ROTATE_ITEM, true);
        if (!rotate) {
            Log.i(mName, "head rotate item is not open");
            iHeadRotate.noNeedRotate();
            return;
        }

        mApi.getCanRotateSupport(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(mName, "getCanRotateSupport : " + result + ", " + message);
                if (TextUtils.isEmpty(message)) {
                    iHeadRotate.noNeedRotate();
                    return;
                }
                try {
                    JSONObject jsonObject = new JSONObject(message);
                    String hardWareVersion = jsonObject.optString(Definition.JSON_CAN_BOARD_APP_VERSION);
                    if (HEAD_TYPE_NEED_ROTATE.equals(hardWareVersion)) {
                        iHeadRotate.needRotate();
                    } else {
                        iHeadRotate.noNeedRotate();
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                    iHeadRotate.noNeedRotate();
                }
            }

            @Override
            public void onError(int errorCode, String errorString) {
                Log.i(mName, "getCanRotateSupport : " + errorCode + ", " + errorString);
                iHeadRotate.noNeedRotate();
            }
        });
    }

    interface IHeadRotate {
        void needRotate();

        void noNeedRotate();
    }

    /**
     * 开始走找人，默认 1.5s 超时，找不到人则过程中不判断丢人和超距
     */
    private void startWaitPersonAppear(final IWaitPerson iWaitPerson) {
        Log.d(mName, "Start wait person appear");
        mPersonAppearComponent.setComponentListener(new ComponentListener() {
            @Override
            public void onFinish(int result, String message, String extraData) {
                Log.i(mName, "Wait person component onFinish : " + result + ", " + message);
                switch (result) {
                    case ComponentResult.RESULT_SUCCESS:
                        Gson gson = new Gson();
                        Person person = gson.fromJson(message, Person.class);
                        if (person != null)
                            iWaitPerson.personFond(person);
                        break;

                    default:
                        iWaitPerson.waitTimeOut();
                        processStatus(result, message);
                        break;
                }
            }
        });
        try {
            JSONObject json = new JSONObject();
            json.put(ComponentParams.PersonAppear.PARAM_MAX_DISTANCE, mPersonAppearMaxDistance);
            json.put(ComponentParams.PersonAppear.PARAM_MAX_FACE_ANGLE_X, mPersonAppearMaxAngleX);
            json.put(ComponentParams.PersonAppear.PARAM_TIMEOUT, mPersonAppearTimeout);
            mPersonAppearComponent.start(json.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    interface IWaitPerson {
        void personFond(Person person);

        void waitTimeOut();
    }

    private void stopWaitPersonAppear() {
        Log.d(mName, "stopWaitPersonAppear");
        if (mPersonAppearComponent != null) {
            mPersonAppearComponent.setComponentListener(null);
            mPersonAppearComponent.stop(PERSON_APPEAR_STOP_TIMEOUT);
        }
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.d(mName, "onUpdate ：" + intent + ", " + params);

        switch (intent) {
            case Definition.REQ_SPEECH_WAKEUP:
                setVoicePickupAngle(params);
                break;
            case ComponentParams.LeadingTrack.INTENT_ARRIVED_DESTINATION:
            case ComponentParams.LeadingTrack.INTENT_NEAR_DESTINATION:
                Log.d(mName, "is need rotate : " + mNeedRotateHeadBack);
                mIsNearDestination = true;
                if (mNeedRotateHeadBack) {
                    setVoicePickupAngle(VOICE_CENTER_ANGLE_BACK);
                    resetHeadBySpeed(MIDDLE_ANGULAR_SPEED_HORIZONTAL, DEFAULT_FRONT_HORIZONTAL_ANGLE);
                }
                break;
            case ComponentParams.LeadingTrack.INTENT_LEADING_ERROR:
                mHasLeadingError = true;
                break;
            default:
                break;
        }
    }

    private CommandListener mTrackListener = new CommandListener() {
        @Override
        public void onResult(int result, String message) {
            try {
                Log.d(mName, "Set track target, result= " + result + ", message= " + message);
                JSONObject json = new JSONObject(message);
                String status = json.getString("status");
                switch (status) {
                    case Definition.RESPONSE_OK:
                        headRotate(true);
                        break;

                    default:
                        headRotate(false);
                        processStatus(result, message);
                        break;
                }
            } catch (JSONException e) {
                e.printStackTrace();
                headRotate(false);
                processStatus(result, "set track target message: "+message);
            }
        }
    };

    private PersonListener mPersonListener = new PersonListener() {
        @Override
        public void personChanged() {
            List<Person> personList = PersonApi.getInstance().getAllPersons();
            if (null == personList || personList.size() == 0) {
                return;
            }
            Person person = personList.get(0);
            double distance = person.getDistance();
            if (distance == ILLEGALITY_PERSON_DISTANCE) return;

            int id = person.getId();
            startLostTimer();
            Log.d(mName, "get person : " + distance + ", " + id + ", " + mTrackState);
            switch (mTrackState) {
                case CHECKING:
                    if (distance > mFarawayDistance) {
                        startWaitTimer();
                        mTrackState = TrackState.GUEST_FARAWAY;
                        processStatus(ComponentStatus.STATUS_GUEST_FARAWAY, "guest faraway");
                    }
                    break;

                case GUEST_LOST:
                case GUEST_FARAWAY_TIMEOUT:
                    mTrackState = TrackState.CHECKING;
                    processStatus(ComponentStatus.STATUS_LEAD_GUEST_APPEAR, "guest appear");
                    break;

                case GUEST_FARAWAY:
                    if (distance > 0 && distance < (mFarawayDistance - FARAWAY_DISTANCE_DEVIATION)) {
                        cancelWaitTimer();
                        mTrackState = TrackState.CHECKING;
                        processStatus(ComponentStatus.STATUS_GUEST_FARAWAY_END,
                                "guest faraway status end");
                    }
                    break;

                default:
                    break;
            }
        }
    };

    public void startGetAllPersonInfo() {
        Log.d(mName, "Start get all person info, mTrackState= " + mTrackState);
        PersonApi.getInstance().registerPersonListener(mPersonListener);
        startLostTimer();
    }

    private void startLostTimer() {
        if (mLostTime <= 0)
            return;
        cancelLostTimer();
        mLostTask = DelayTask.submit(new Runnable() {
            @Override
            public void run() {
                mTrackState = TrackState.GUEST_LOST;
                processStatus(ComponentStatus.STATUS_LEAD_GUEST_LOST, "guest lost");
            }
        }, mLostTime);
    }

    private void cancelLostTimer() {
        if (mLostTask != null && !mLostTask.isCancelled()) {
            mLostTask.cancel(true);
            mLostTask = null;
        }
    }

    private boolean startWaitTimer() {
        Log.d(mName, "Start wait timer out timer, mWaitTime= " + mWaitTime);
        if (mWaitTime <= 0) {
            return false;
        }
        cancelWaitTimer();
        mWaitTask = DelayTask.submit(new Runnable() {
            @Override
            public void run() {
                mTrackState = TrackState.GUEST_FARAWAY_TIMEOUT;
                processStatus(ComponentStatus.STATUS_LEAD_GUEST_FARAWAY_TIMEOUT, null);
            }
        }, mWaitTime);
        return true;
    }

    private void cancelWaitTimer() {
        if (mWaitTask != null && !mWaitTask.isCancelled()) {
            mWaitTask.cancel(true);
            mWaitTask = null;
        }
    }

    private boolean startDetectDelayTimer() {
        Log.d(mName, "Start detect delay, mDetectDelayTime= " + mDetectDelayTime);
        if (mDetectDelayTime <= 0) {
            return false;
        }
        cancelDetectDelayTimer();
        mDetectDelayTask = DelayTask.submit(new Runnable() {
            @Override
            public void run() {
                startGetAllPersonInfo();
            }
        }, mDetectDelayTime);
        return true;
    }

    private void cancelDetectDelayTimer() {
        if (mDetectDelayTask != null && !mDetectDelayTask.isCancelled()) {
            mDetectDelayTask.cancel(true);
            mDetectDelayTask = null;
        }
    }

    @Override
    public void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        mTrackState = TrackState.IDLE;
        DelayTask.cancel(this);
        stopWaitPersonAppear();
        PersonApi.getInstance().unregisterPersonListener(mPersonListener);
        mApi.stopTrack(null);
        mApi.switchCamera(Definition.JSON_HEAD_FORWARD, null);
        resetVoicePickupAngle();
        cancelLostTimer();
        cancelWaitTimer();
        cancelDetectDelayTimer();
        resetHead();
        Log.d(mName, "onStop really stopped");
    }

    /**
     * 如果是 -270 机器，等待机器头部归正后再退出组件
     */
    private void resetHead() {
        Log.d(mName, "reset head start");
        if (mNeedRotateHeadBack) {
            resetHeadBySpeed(RESET_ANGULAR_SPEED_HORIZONTAL, FRONT_HORIZONTAL_ANGLE_OFFSET);
            synchronized (HEAD_LOCK) {
                try {
                    HEAD_LOCK.wait(1500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        } else {
            mApi.resetHead(null);
        }
        Log.d(mName, "reset head end");
    }

    private void processStatus(int status, final String data) {
        if (!isAlive())
            return;
        updateStatus(status, data);
    }

    private void processResult(int result, String message) {
        if (!isAlive())
            return;
        stop(500, result, message);
    }

    private void setVoicePickupAngle(String params) {
        Log.i(mName, "set voice pickup angle : params" + params);
        if (!isAlive())
            return;

        try {
            float voiceAngle = Float.parseFloat(params);
            setVoicePickupAngle(voiceAngle);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
    }

    private void setVoicePickupAngle(float voiceAngle) {
        float defaultAngleRange = SpeechManager.getInstance().getDefautAngleRange();
        SpeechManager.getInstance().setAngleCenterRange(voiceAngle, defaultAngleRange);
    }

    private void resetVoicePickupAngle() {
        float angle_center = SpeechManager.getInstance().getDefautAngleCenter();
        float angle_range = SpeechManager.getInstance().getDefautAngleRange();
        SpeechManager.getInstance().setAngleCenterRange(angle_center, angle_range);
    }

}
