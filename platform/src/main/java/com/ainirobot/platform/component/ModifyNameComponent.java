/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;

import org.json.JSONException;
import org.json.JSONObject;

public class ModifyNameComponent extends Component {

    public static final String TAG = "ModifyNameComponent";

    private static final int STOP_TIMEOUT = 500;
    private String mRegisterName;
    private String mRegisterId;
    private String welcomeContent;

    public ModifyNameComponent(String name) {
        this(name, Looper.myLooper());
    }

    public ModifyNameComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    protected void onStart(String params) {
        try {
            JSONObject jsonObject = new JSONObject(params);
            mRegisterName = jsonObject.optString(ComponentParams.ModifyName.PARAM_PERSON_NAME, null);
            mRegisterId = jsonObject.optString(ComponentParams.ModifyName.PARAM_REGISTER_ID, null);
            welcomeContent = jsonObject.optString(ComponentParams.ModifyName.WELCOME_CONTENT, null);
            if (TextUtils.isEmpty(mRegisterName)) {
                processModifyNameStop(ComponentError.ERROR_PARAMS_GUEST_NAME_INVALID);
            } else if (TextUtils.isEmpty(mRegisterId)) {
                processModifyNameStop(ComponentError.ERROR_PARAMS_REGISTER_ID_INVALID);
            } else {
                startModifyName();
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

    }

    @Override
    protected void onUpdate(String intent, String params) {
    }

    private void startModifyName() {
        mApi.remoteModifyDetectName(mRegisterId, mRegisterName, welcomeContent, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(mName, "remoteModifyDetectName result:" + message);
                try {
                    JSONObject jsonObject = new JSONObject(message);
                    int code = jsonObject.optInt("code");
                    String data = jsonObject.optString("data");
                    if (code == 0 && !TextUtils.isEmpty(data)) {
                        processModifyNameStop(ComponentResult.RESULT_SUCCESS);
                    } else {
                        processModifyNameStop(ComponentError.ERROR_MODIFY_NAME_FAILED);
                    }
                } catch (JSONException e) {
                    processModifyNameStop(ComponentError.ERROR_MODIFY_NAME_FAILED);
                }
            }
        });
    }

    @Override
    protected void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
    }

    private void processModifyNameStop(int result) {
        Log.d(mName, "processModifyNameStop.result:" + result);
        stop(STOP_TIMEOUT, result, null);
    }
}
