/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.bean.Result;
import com.ainirobot.platform.bean.ReceptionBean;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams.ReservationCode;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.utils.MessageUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

public class ReservationCodeComponent extends Component {

    private static final int TYPE_PHONE = 0;
    private static final int TYPE_SMS = 1;

    private static final int CODE_NOT_EXIST = 404;
    private static final int CODE_USED = 406;
    private static final int CODE_LIMITED = 407;
    private static final int CODE_INVALID = 410;
    private static final int CODE_EXPIRED = 423;

    private static final long STOP_TIMEOUT = 100;
    private static final int CODE_LENGTH = 4;
    private Gson mGson;

    public ReservationCodeComponent(String name) {
        this(name, Looper.myLooper());
    }

    public ReservationCodeComponent(String name, Looper looper) {
        super(name, looper);
        mGson = new Gson();
    }

    @Override
    protected void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        try {
            JSONObject data = new JSONObject(params);
            int reservationType = data.optInt(ReservationCode.PARAM_RESERVATION_TYPE, TYPE_PHONE);
            if (reservationType != TYPE_PHONE && reservationType != TYPE_SMS){
                processResult(ComponentError.ERROR_PARAMS_RESERVATION_TYPE_INVALID,
                        "type is 0(phone) or 1(sms)");
                return;
            }
            String reservationCode = data.getString(ReservationCode.PARAM_RESERVATION_CODE);
            if (TextUtils.isEmpty(reservationCode) || reservationCode.length() < CODE_LENGTH){
                processResult(ComponentError.ERROR_PARAMS_RESERVATION_CODE_INVALID,
                        "code should 4 characters");
                return;
            }
            startCheckID(reservationType, reservationCode);

        } catch (JSONException e) {
            e.printStackTrace();
            processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR, "please input valid json");
        }
    }

    private void startCheckID(int type, String code) {

        switch (type){
            case TYPE_PHONE:
                mApi.remotePhoneCheckVerify(code, new CommandListener(){
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(mName, "Phone check result: " + result + " message: " + message);

                        if (Definition.RESULT_OK == result && MessageUtils.isRemoteSuccess(message)) {
                            Result<List<ReceptionBean>> jsonResult = mGson.fromJson(message,
                                    new TypeToken<Result<List<ReceptionBean>>>() {}.getType());

                            if (!jsonResult.isSucceed()) {
                                processResult(jsonResult.getCode(),null);
                                return;
                            }

                            if (jsonResult.getData() == null || jsonResult.getData().isEmpty()) {
                                processResult(ComponentError.ERROR_REMOTE_RESERVATION_SERVER_NO_DATA,
                                        "reception bean is empty");
                                return;
                            }

                            if (jsonResult.getData().size() > 1) {
                                processResult(ComponentError.ERROR_REMOTE_RESERVATION_NAME_REPEATED, mGson.toJson(
                                        jsonResult.getData()));

                            }else{
                                processResult(ComponentResult.RESULT_SUCCESS, mGson.toJson(jsonResult.getData().get(0)));
                            }

                        }else {
                            processResult(result, message);
                        }

                    }

                });
                break;
            case TYPE_SMS:
                mApi.remoteCheckVerify(code, new CommandListener(){
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(mName, "sms check result: " + result + " message: " + message);
                        if (Definition.RESULT_OK == result && MessageUtils.isRemoteSuccess(message)) {
                            Result<ReceptionBean> jsonResult = mGson.fromJson(message,
                                    new TypeToken<Result<ReceptionBean>>() {
                                    }.getType());

                            if (!jsonResult.isSucceed()) {
                                processResult(jsonResult.getCode(),null);
                                return;
                            }

                            if (jsonResult.getData() == null) {
                                processResult(ComponentError.ERROR_REMOTE_RESERVATION_SERVER_NO_DATA,
                                        "reception bean is empty");
                                return;
                            }

                            processResult(ComponentResult.RESULT_SUCCESS, mGson.toJson(jsonResult.getData()));

                        }else {
                            processResult(result, message);
                        }
                    }
                });
                break;
            default:
                break;

        }
    }

    private void processStatus(int status, String message){
        updateStatus(status, message);
    }

    private void processResult(int result, String message) {
        switch (result){
            case CODE_NOT_EXIST:
                updateResult(ComponentError.ERROR_REMOTE_RESERVATION_CODE_NOT_EXIST, message);
                break;
            case CODE_USED:
                updateResult(ComponentError.ERROR_REMOTE_RESERVATION_CODE_USED, message);
                break;
            case CODE_LIMITED:
                updateResult(ComponentError.ERROR_REMOTE_RESERVATION_CODE_LIMITED, message);
                break;
            case CODE_INVALID:
                updateResult(ComponentError.ERROR_REMOTE_RESERVATION_CODE_INVALID, message);
                break;
            case CODE_EXPIRED:
                updateResult(ComponentError.ERROR_REMOTE_RESERVATION_CODE_EXPIRED, message);
                break;

            default:
                updateResult(result, message);
                break;
        }
    }

    private void updateResult(int result, String message) {
        Log.d(mName, " result: " + result + ", message: " + message);
        stop(STOP_TIMEOUT, result, message);
    }

    @Override
    protected void onUpdate(String intent, String params) {
    }

    @Override
    protected void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
    }
}
