package com.ainirobot.platform.component;

import android.os.Looper;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * AI智能跟踪组件
 */
public class AIIntelligenceTrackComponent extends Component {

    private static final float DEFAULT_LOST_DISTANCE = 2;
    private static final long DEFAULT_LOST_TIMEOUT = 2000;
    private static final long STOP_TIMEOUT = 500;

    private double mLostDistance;
    private long mLostTimeout;

    public AIIntelligenceTrackComponent(String name) {
        this(name, Looper.myLooper());
    }

    public AIIntelligenceTrackComponent(String name, Looper looper) {
        super(name, looper);
    }


    @Override
    public void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            mLostDistance = json.optDouble(ComponentParams.FaceTrack.PARAM_LOST_DISTANCE,
                    DEFAULT_LOST_DISTANCE);
            mLostTimeout = json.optLong(ComponentParams.FaceTrack.PARAM_LOST_TIMEOUT,
                    DEFAULT_LOST_TIMEOUT);
            startTrackPerson();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.d(mName, "onUpdate params: " + params);
    }

    private void startTrackPerson() {
        mApi.startSmartFocusFollow(mLostTimeout, (float) mLostDistance, new ActionListener() {

            @Override
            public void onStatusUpdate(int status, String data) {
                Log.d(mName, "startTrackPerson onStatusUpdate " +
                        "status: " + status + ", data: " + data);
                switch (status) {
                    default:
                        processStatus(status, data);
                        break;
                }
            }

            @Override
            public void onError(int errorCode, String errorString) {
                Log.d(mName, "startTrackPerson onError" +
                        "errorCode: " + errorCode +
                        ", errorString: " + errorString);
                switch (errorCode) {
                    case Definition.ACTION_RESPONSE_ALREADY_RUN:
                        processTrackResult(ComponentError.ERROR_REQUEST_RES_BUSY);
                        break;
                    case Definition.ACTION_RESPONSE_REQUEST_RES_ERROR:
                        processTrackResult(ComponentError.ERROR_REQUEST_RES_FAILED);
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onResult(int status, String responseString) {
                Log.d(mName, "startTrackPerson onResult status: " + status +
                        ", responseString: " + responseString);
                switch (status) {
                    case Definition.ACTION_RESPONSE_STOP_SUCCESS:
                    default:
                        break;
                }
            }
        });
    }

    private void processStatus(int status, String result) {
        Log.d(mName, "processStatus status: " + status + ", result: " + result);
        updateStatus(status, result);
    }

    private void processTrackResult(int result) {
        Log.d(mName, "processTrackResult result: " + result);
        stop(STOP_TIMEOUT, result, null);
    }

    @Override
    public void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        mApi.stopSmartFocusFollow();
    }

}
