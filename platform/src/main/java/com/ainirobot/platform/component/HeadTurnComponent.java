package com.ainirobot.platform.component;/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.HeadTurnBean;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

public class HeadTurnComponent extends Component {

    private static final long STOP_TIMEOUT = 200;

    private HeadTurnBean mBean;

    public HeadTurnComponent(String name) {
        this(name, Looper.myLooper());
    }

    public HeadTurnComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    protected void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            String headTurnStr = json.optString(ComponentParams.HeadTurn.PARAM_HEAD_TURN_BEAN,
                    "");
            if (TextUtils.isEmpty(headTurnStr)) {
                processResult(ComponentError.ERROR_PARAMS_HEAD_TURN_BEAN_INVALID);
                return;
            }

            Gson gson = new Gson();
            mBean = gson.fromJson(headTurnStr, HeadTurnBean.class);
            if (mBean == null) {
                processResult(ComponentError.ERROR_PARAMS_HEAD_TURN_BEAN_INVALID);
                return;
            }
            startTurnHead();
        } catch (JSONException e) {
            processResult(ComponentError.ERROR_PARAMS_HEAD_TURN_BEAN_INVALID);
        }
    }

    private void startTurnHead() {
        mApi.turnHead(mBean, new CommandListener() {
            @Override
            public void onStatusUpdate(int status, String data) {
                if (isAlive()) {
                    Log.v(mName, "startTurnHead onStatusUpdate status: " + status
                            + ", data: " + data);
                    switch (status) {
                        case Definition.STATUS_TURN_HEAD_START:
                            processStatus(ComponentStatus.STATUS_HEAD_TURN_START, data);
                            break;
                        case Definition.STATUS_TURN_HEAD_MAX_UP_ANGLE:
                            processStatus(ComponentStatus.STATUS_TURN_HEAD_MAX_UP_ANGLE, data);
                            break;
                        case Definition.STATUS_TURN_HEAD_MAX_DOWN_ANGLE:
                            processStatus(ComponentStatus.STATUS_TURN_HEAD_MAX_DOWN_ANGLE, data);
                            break;
                        default:
                            break;
                    }
                }
            }

            @Override
            public void onResult(int result, String message) {
                if (isAlive()) {
                    Log.d(mName, "startTurnHead onResult result: " + result
                            + ", message: " + message);
                    switch (result) {
                        case Definition.RESULT_TURN_HEAD_SUCCESS:
                            processResult(ComponentResult.RESULT_HEAD_TURN_SUCCESS);
                            break;
                        case Definition.RESULT_TURN_HEAD_TIMEOUT:
                            processResult(ComponentError.ERROR_HEAD_TURN_TIMEOUT);
                            break;
                        case Definition.RESULT_TURN_HEAD_FAILED:
                            processResult(ComponentError.ERROR_HEAD_TURN_FAILED);
                            break;
                        case Definition.RESULT_TURN_HEAD_INTERRUPT:
                            processResult(ComponentError.ERROR_HEAD_TURN_INTERRUPT);
                            break;
                        case Definition.RESULT_TURN_HEAD_UN_SUPPORT:
                            processResult(ComponentError.ERROR_HEAD_TURN_NOT_SUPPORT);
                            break;
                        default:
                            processResult(result);
                            break;
                    }
                }
            }
        });
    }

    private void processStatus(int status, String data) {
        Log.d(mName, "processStatus status: " + status + ", data: " + data);
        updateStatus(status, data);
    }

    private void processResult(int result) {
        Log.d(mName, "processResult result: " + result);
        stop(STOP_TIMEOUT, result, null);
    }

    @Override
    protected void onUpdate(String intent, String params) {
    }

    @Override
    protected void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        mApi.stopTurnHead(null);
    }
}
