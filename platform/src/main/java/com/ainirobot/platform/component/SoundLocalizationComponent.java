/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.component;

import android.os.Looper;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.HeadTurnBean;
import com.ainirobot.coreservice.client.actionbean.HeadTurnBean.HeadTurnMode;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.react.server.control.RNServerManager;
import com.ainirobot.platform.react.server.impl.RNApiServer;
import com.ainirobot.platform.utils.StaticModeUtil;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

public class SoundLocalizationComponent extends Component {

    private static final int DEFAULT_ANGLE = Integer.MAX_VALUE;
    private static final int MAX_ANGLE = 360;
    private static final long STOP_TIMEOUT = 50;
    private static final int MAX_HEAD_ANGLE = 90;

    private boolean mIsNeedMoveBody;
    private Component mHeadTurnComponent;
    private Gson mGson = new Gson();
    private int mAngle;

    public SoundLocalizationComponent(String name) {
        this(name, Looper.myLooper());
    }

    public SoundLocalizationComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    public void onStart(String params) {
        Log.d(mName, "onStart params: " + params);

        if(StaticModeUtil.isInStaticMode()){
            //静止模式，不进行声源定位
            Log.d(mName, "in static mode, donot start sound Localization");
            return;
        }

        try {
            JSONObject json = new JSONObject(params);
            mIsNeedMoveBody = json.optBoolean(ComponentParams.SoundLocalization.PARAM_IS_NEED_MOVE_BODY,
                    true);
            if (RNServerManager.getInstance().getApiServer().isLara()) {
                mIsNeedMoveBody = false;
            }
            mAngle = json.optInt(ComponentParams.SoundLocalization.PARAM_ANGLE,
                    DEFAULT_ANGLE);
            if (Math.abs(mAngle) > MAX_ANGLE) {
                processSoundLocalizationResult(ComponentError.ERROR_PARAMS_SOUND_LOCALIZATION_ANGLE_INVALID);
                return;
            }

            if (mIsNeedMoveBody) {
                startSoundLocalization();
            } else {
                moveHead();
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.d(mName, "onUpdate params: " + params);
    }

    private void moveHead() {
        if (mHeadTurnComponent == null) {
            mHeadTurnComponent = new HeadTurnComponent(mName);
    }
        int moveAngle = (mAngle < 180) ? mAngle : (mAngle - 360);
        if (Math.abs(moveAngle) > MAX_HEAD_ANGLE) {
            moveAngle = moveAngle < 0 ? -MAX_HEAD_ANGLE : MAX_HEAD_ANGLE;
        }

        Log.d(mName, "Sound local move head : " + moveAngle);
        mHeadTurnComponent.setComponentListener(new ComponentListener() {
            @Override
            public void onFinish(int result, String message, String extraData) {
                if (!SoundLocalizationComponent.this.isAlive()) {
                    Log.e(mName, "startResetHead onFinish has not alive");
                    return;
                }
                Log.d(mName, "startResetHead onFinish result: " + result + ", message: " + message);
                processSoundLocalizationResult(ComponentResult.RESULT_SUCCESS);
            }
        });
        mHeadTurnComponent.setStatusListener(null);
        HeadTurnBean bean = new HeadTurnBean();
        bean.setHorizontalAngle(moveAngle);
        bean.setHorizontalMode(HeadTurnBean.HeadTurnMode.absolute);
        bean.setVerticalAngle(70);
        bean.setVerticalMode(HeadTurnMode.absolute);
        JSONObject json = new JSONObject();
        try {
            json.put(ComponentParams.HeadTurn.PARAM_HEAD_TURN_BEAN, mGson.toJson(bean));
            mHeadTurnComponent.start(json.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void startSoundLocalization() {
        mApi.wakeUp(mAngle, new ActionListener() {
            @Override
            public void onStatusUpdate(int status, String data) {
                Log.d(mName, "startSoundLocalization onStatusUpdate " +
                        "status: " + status + ", data: " + data);
                switch (status) {
                    default:
                        processStatus(status, data);
                        break;
                }
            }

            @Override
            public void onError(int errorCode, String errorString) {
                Log.d(mName, "startSoundLocalization onError" +
                        "errorCode: " + errorCode +
                        ", errorString: " + errorString);
                switch (errorCode) {
                    case Definition.ACTION_RESPONSE_ALREADY_RUN:
                        processSoundLocalizationResult(ComponentError.ERROR_REQUEST_RES_BUSY);
                        break;
                    case Definition.ACTION_RESPONSE_REQUEST_RES_ERROR:
                        processSoundLocalizationResult(ComponentError.ERROR_REQUEST_RES_FAILED);
                        break;
                    default:
                        processSoundLocalizationResult(ComponentError.ERROR_SOUND_LOCALIZATION_FAILED);
                        break;
                }
            }

            @Override
            public void onResult(int status, String responseString) {
                Log.d(mName, "startSoundLocalization onResult status: " + status +
                        ", responseString: " + responseString);
                switch (status) {
                    case Definition.ACTION_RESPONSE_STOP_SUCCESS:
                        processSoundLocalizationResult(ComponentResult.RESULT_SUCCESS);
                        break;
                    default:
                        processStatus(status, responseString);
                        processSoundLocalizationResult(ComponentResult.RESULT_SUCCESS);
                        break;
                }
            }
        });
    }

    private void processStatus(int status, String result) {
        Log.d(mName, "processStatus status: " + status + ", result: " + result);
        updateStatus(status, result);
    }

    private void processSoundLocalizationResult(int result) {
        Log.d(mName, "processSoundLocalizationResult result: " + result);
        stop(STOP_TIMEOUT, result, null);
    }

    @Override
    public void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        mApi.stopWakeUp();
        if (mHeadTurnComponent != null) {
            mHeadTurnComponent.setComponentListener(null);
            mHeadTurnComponent.stop(500);
            mHeadTurnComponent = null;
        }
    }
}
