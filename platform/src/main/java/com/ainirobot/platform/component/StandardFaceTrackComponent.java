/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.component;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.person.PersonApi;
import com.ainirobot.coreservice.client.person.PersonListener;
import com.ainirobot.coreservice.client.person.PersonUtils;
import com.ainirobot.platform.bi.wrapper.manager.at.AtReport;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentParams.StandardFaceTrack;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.List;

import com.ainirobot.platform.bi.wrapper.ReportControl;
import com.ainirobot.platform.data.FeatureConfig;
import com.ainirobot.platform.react.server.control.RNServerManager;
import com.ainirobot.platform.utils.StaticModeUtil;

public class StandardFaceTrackComponent extends Component {

    private static final String TAG = StandardFaceTrackComponent.class.getSimpleName();

    private static final int MSG_UPDATE_BEST_PERSON = 0x01;
    private static final int MSG_PERSON_APPEAR_TIMEOUT = 0x02;
    private static final int MSG_CONTINUE_SEARCH_FACE = 0x03;
    private static final int MSG_TRACK_STATUS = 0x04;
    private static final int MSG_TRACK_ERROR = 0x05;
    private static final int MSG_TRACK_RESULT = 0x06;
    private static final int MSG_SOUND_INPUT = 0x07;
    private static final int MSG_SOUND_LOCALIZATION_END = 0x08;
	private static final int MSG_UPDATE_INITIAL_PARAMS = 0x09;
    private static final double DEFAULT_MAX_DISTANCE = 1.5;
    private static final double DEFAULT_MAX_FACE_ANGLE_X = 60;
    private static final long DEFAULT_DISAPPEAR_TIMEOUT = 7000;
    private static final long DEFAULT_PERSON_LOST_TIMEOUT = 1000;
    private static final float DEFAULT_PERSON_LOST_DISTANCE = 2f;
    private static final double DEFAULT_MULTI_PERSON_NOT_TRACK_DISTANCE = 2;

    private StandardFaceTrackHandler mHandler;
    private double mMaxDistance;
    private double mMaxFaceAngleX;
    private boolean mIsNeedInCompleteFace;
    private Person mCurrentPerson;
    private long mDisappearTimeout;
    private boolean mIsMultiPersonNotTrack;
    private boolean isAllowMoveBody = true;
    private double mMultiPersonNotTrackDistance;
    private ProcessState mCurrentState;
    private SoundLocalizationComponent mSoundLocalizationComponent;
    private int mSoundAngle;

    private enum ProcessState {
        IDLE, SEARCH, TRACKING, DO_NOT_TRACKING, CAN_NOT_TRACKING, SOUND_LOCALIZATION
    }

    public StandardFaceTrackComponent(String name) {
        this(name, Looper.myLooper());
    }

    public StandardFaceTrackComponent(String name, Looper looper) {
        super(name, looper);
        mHandler = new StandardFaceTrackHandler(this, mLooper);
        mSoundLocalizationComponent = new SoundLocalizationComponent(mName);
    }

    private static class StandardFaceTrackHandler extends Handler {

        private WeakReference<StandardFaceTrackComponent> mComponent;

        private StandardFaceTrackHandler(StandardFaceTrackComponent component, Looper looper) {
            super(looper);
            mComponent = new WeakReference<>(component);
        }

        @Override
        public void handleMessage(Message msg) {
            StandardFaceTrackComponent component = mComponent.get();
            if (component == null || !component.isAlive()) {
                return;
            }
            Log.v(component.mName, "handleMessage what: " + msg.what
                    + ", currentState: " + component.mCurrentState);
            switch (msg.what) {
                case MSG_UPDATE_BEST_PERSON:
                    component.updateCurrentPerson((Person) msg.obj);
                    break;
                case MSG_PERSON_APPEAR_TIMEOUT:
                    component.processResult(ComponentResult.RESULT_TIMEOUT);
                    break;
                case MSG_CONTINUE_SEARCH_FACE:
                    component.continueSearchFace();
                    break;
                case MSG_TRACK_STATUS:
                    component.handleTrackStatus(msg.arg1, (String) msg.obj);
                    break;
                case MSG_TRACK_RESULT:
                    component.handleTrackResult(msg.arg1, (String) msg.obj);
                    break;
                case MSG_TRACK_ERROR:
                    component.handleTrackError(msg.arg1, (String) msg.obj);
                    break;
                case MSG_SOUND_INPUT:
                    component.handleSoundInput((String) msg.obj);
                    break;
                case MSG_SOUND_LOCALIZATION_END:
                    component.handleSoundLocalizationEnd();
                    break;
				case MSG_UPDATE_INITIAL_PARAMS:
                    component.handleInitialParamsUpdate((String) msg.obj);
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        int personId;
        try {
            JSONObject json = new JSONObject(params);
            personId = json.optInt(StandardFaceTrack.PARAM_PERSON_ID, -1);
            mIsNeedInCompleteFace = json.optBoolean(StandardFaceTrack.PARAM_IS_NEED_INCOMPLETE_FACE,
                    false);
            mMaxDistance = json.optDouble(StandardFaceTrack.PARAM_MAX_DISTANCE,
                    DEFAULT_MAX_DISTANCE);
            mMaxFaceAngleX = json.optDouble(StandardFaceTrack.PARAM_MAX_FACE_ANGLE_X,
                    DEFAULT_MAX_FACE_ANGLE_X);
            mDisappearTimeout = json.optLong(StandardFaceTrack.PARAM_DISAPPEAR_TIMEOUT,
                    DEFAULT_DISAPPEAR_TIMEOUT);
            mIsMultiPersonNotTrack = json.optBoolean(StandardFaceTrack.PARAM_IS_MULTI_PERSON_NOT_TRACK,
                    false);
            isAllowMoveBody = json.optBoolean(StandardFaceTrack.PARAM_IS_ALLOW_MOVE_BODY, true);
            if (RNServerManager.getInstance().getApiServer().isLara()) {
                isAllowMoveBody = false;
            }
            mMultiPersonNotTrackDistance = json.optDouble(StandardFaceTrack.PARAM_MULTI_PERSON_NOT_TRACK_DISTANCE,
                    DEFAULT_MULTI_PERSON_NOT_TRACK_DISTANCE);
            if (mDisappearTimeout <= 0) {
                mDisappearTimeout = DEFAULT_DISAPPEAR_TIMEOUT;
            }
        } catch (JSONException e) {
            personId = -1;
            mIsNeedInCompleteFace = false;
            mMaxDistance = DEFAULT_MAX_DISTANCE;
            mMaxFaceAngleX = DEFAULT_MAX_FACE_ANGLE_X;
            mDisappearTimeout = DEFAULT_DISAPPEAR_TIMEOUT;
            mIsMultiPersonNotTrack = false;
            mMultiPersonNotTrackDistance = DEFAULT_MULTI_PERSON_NOT_TRACK_DISTANCE;
        }
        if (personId >= 0) {
            mCurrentPerson = new Person();
            mCurrentPerson.setId(personId);
            List<Person> allFace = PersonApi.getInstance().getAllFaceList(mMultiPersonNotTrackDistance);
            if (allFace != null && allFace.size() > 1 && mIsMultiPersonNotTrack) {
                mCurrentState = ProcessState.DO_NOT_TRACKING;
                startAppearTimer();
            } else {
                mCurrentState = ProcessState.TRACKING;
                destroyAppearTimer();
                obstacleDetect();
            }
        } else {
            mCurrentState = ProcessState.SEARCH;
            mCurrentPerson = null;
            startAppearTimer();
        }

        if(!StaticModeUtil.isInStaticMode()){
            //非静止模式，注册识别到人的监听接口
            initPersonListener();
        }{
            Log.d(TAG,"inStaticMode donot track person");
        }
    }

    private void initPersonListener() {
        boolean result = PersonApi.getInstance().registerPersonListener(mPersonListener);
        if (!result) {
            processResult(ComponentError.ERROR_OPEN_PERSON_DETECT_FAILED);
        }
    }

    private PersonListener mPersonListener = new PersonListener() {
        @Override
        public void personChanged() {
            if (!isAlive()) {
                return;
            }
            Log.v(mName, "personChanged list: " + PersonApi.getInstance().getAllFaceList());
            List<Person> personList;
            if (mIsNeedInCompleteFace) {
                personList = PersonApi.getInstance().getAllFaceList(mMaxDistance);
            } else {
                personList = PersonApi.getInstance().getCompleteFaceList(mMaxDistance);
            }
            Person bestPerson = PersonUtils.getBestFace(personList, mMaxDistance, mMaxFaceAngleX);
            if (bestPerson != null) {
                mHandler.obtainMessage(MSG_UPDATE_BEST_PERSON, bestPerson).sendToTarget();
            }
        }
    };

    private synchronized void updateCurrentPerson(Person person) {
        Log.v(mName, "updateCurrentPerson currentState: "
                + mCurrentState + ", person: " + person);
        if (!isAlive()) {
            return;
        }
        switch (mCurrentState) {
            case SEARCH:
                mCurrentPerson = person;
                List<Person> allFace = PersonApi.getInstance().getAllFaceList(mMultiPersonNotTrackDistance);
                if (mCurrentPerson.getId() < 0) {
                    mCurrentState = ProcessState.CAN_NOT_TRACKING;
                    startAppearTimer();
                } else if (allFace != null && allFace.size() > 1 && mIsMultiPersonNotTrack) {
                    mCurrentState = ProcessState.DO_NOT_TRACKING;
                    startAppearTimer();
                } else {
                    mCurrentState = ProcessState.TRACKING;
                    destroyAppearTimer();
                    obstacleDetect();
                }
                break;
            case CAN_NOT_TRACKING:
                mCurrentPerson = person;
                if (mCurrentPerson.getId() < 0) {
                    startAppearTimer();
                } else {
                    mCurrentState = ProcessState.TRACKING;
                    destroyAppearTimer();
                    obstacleDetect();
                }
                break;
            case DO_NOT_TRACKING:
                mCurrentPerson = person;
                startAppearTimer();
                break;
            case TRACKING:
                mCurrentPerson = person;
                break;
            default:
                break;
        }
    }

    private void continueSearchFace() {
        switch (mCurrentState) {
            case SEARCH:
            case DO_NOT_TRACKING:
            case CAN_NOT_TRACKING:
                startAppearTimer();
                break;
            default:
                break;
        }
    }

    private void obstacleDetect() {
        mApi.hasObstacleInArea(-30, 30,
                0.1, 1, new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        if (!isAlive()) {
                            return;
                        }
                        Log.i(mName, "FaceTrack hasObstacleInArea result: " + result + " message= " + message);
                        if ("true".equals(message)) {
                            startTrack();
                        } else {
                            startAppearTimer();
                        }
                    }
                });
    }

    private void startTrack() {
        ReportControl.getInstance().reportMsg(new AtReport(AtReport.AtType.success, "开始焦点跟随"));
        mApi.startFocusFollow(mCurrentPerson.getId(), DEFAULT_PERSON_LOST_TIMEOUT,
                DEFAULT_PERSON_LOST_DISTANCE, isAllowMoveBody, new ActionListener() {

                    @Override
                    public void onStatusUpdate(int status, String data) {
                        Log.d(mName, "startTrackPerson onStatusUpdate " +
                                "status: " + status +
                                ", data: " + data);
                        mHandler.obtainMessage(MSG_TRACK_STATUS, status, 0, data).sendToTarget();
                    }

                    @Override
                    public void onError(int errorCode, String errorString) {
                        Log.d(mName, "startTrackPerson onError" +
                                "errorCode: " + errorCode +
                                ", errorString: " + errorString);
                        mHandler.obtainMessage(MSG_TRACK_ERROR, errorCode, 0, errorString).sendToTarget();
                    }

                    @Override
                    public void onResult(int status, String responseString) {
                        Log.d(mName, "startTrackPerson onResult status: " + status +
                                ", responseString: " + responseString);
                        mHandler.obtainMessage(MSG_TRACK_RESULT, status, 0, responseString).sendToTarget();
                    }
                });
    }

    private void handleTrackStatus(int status, String data) {
        switch (status) {
            case Definition.STATUS_TRACK_TARGET_SUCCEED:
                if (mCurrentState == ProcessState.TRACKING) {
                    processStatus(ComponentStatus.STATUS_TRACK_SUCCESS,
                            mCurrentPerson.toGson());
                }
                break;
            case Definition.STATUS_GUEST_LOST:
            case Definition.STATUS_GUEST_FARAWAY:
                if (mCurrentState == ProcessState.TRACKING) {
                    mApi.stopFocusFollow();
                }
                break;
            default:
                if (mCurrentState == ProcessState.TRACKING) {
                    processStatus(status, data);
                }
                break;
        }
    }

    private void handleTrackError(int errorCode, String errorString) {
        if (Definition.ACTION_RESPONSE_ALREADY_RUN != errorCode) {
            if (mCurrentState == ProcessState.TRACKING && isAlive()) {
                mCurrentState = ProcessState.SEARCH;
                ReportControl.getInstance().reportMsg(new AtReport(AtReport.AtType.success, "停止焦点跟随"));
                processStatus(ComponentStatus.STATUS_TRACK_END, errorString);
                mCurrentPerson = null;
                startAppearTimer();
            }
        }
    }

    private void handleTrackResult(int status, String data) {
        switch (status) {
            case Definition.ACTION_RESPONSE_STOP_SUCCESS:
                if (mCurrentState == ProcessState.TRACKING) {
                    mCurrentState = ProcessState.SEARCH;
                    ReportControl.getInstance().reportMsg(new AtReport(AtReport.AtType.success, "停止焦点跟随"));
                    processStatus(ComponentStatus.STATUS_TRACK_END, data);
                    mCurrentPerson = null;
                    startAppearTimer();
                } else if (mCurrentState == ProcessState.SOUND_LOCALIZATION) {
                    destroyAppearTimer();
                    soundLocalization(mSoundAngle);
                }
                break;
            default:
                processStatus(status, data);
                break;
        }
    }

    private void handleSoundInput(String params) {
        Log.d(mName, "handleSoundInput mCurrentState: " + ", params: " + params);
        if (TextUtils.isEmpty(params)) {
            return;
        }
        String sid;
        int soundAngle;
        try {
            JSONObject json = new JSONObject(params);
            soundAngle = json.optInt(StandardFaceTrack.UPDATE_PARAM_SOUND_ANGLE, Integer.MAX_VALUE);
            if (soundAngle > 360 || soundAngle < 0) {
                return;
            }
            sid = json.optString(StandardFaceTrack.UPDATE_PARAM_SOUND_SID, "");
            if (TextUtils.isEmpty(sid)) {
                return;
            }
        } catch (JSONException e) {
            e.printStackTrace();
            return;
        }
        mSoundAngle = soundAngle > 180 ? soundAngle - 360 : soundAngle;
        switch (mCurrentState) {
            case SEARCH:
                // sound localization range: -180 ~ 180
                mCurrentState = ProcessState.SOUND_LOCALIZATION;
                soundLocalization(mSoundAngle);
                destroyAppearTimer();
                break;
            case TRACKING:
                if (!isInSameDirectionWithCurrent(mSoundAngle,
                        (int) mCurrentPerson.getAngleInView())) {
                    if (isInVisualRange(mSoundAngle)) {
                        // switch track target range: 0 ~ 360
                        switchTrackTarget(soundAngle);
                    } else {
                        mCurrentState = ProcessState.SOUND_LOCALIZATION;
                        mApi.stopFocusFollow();
                    }
                }
                break;
            default:
                break;
        }
    }

    private void handleSoundLocalizationEnd() {
        if (mCurrentState == ProcessState.SOUND_LOCALIZATION) {
            mCurrentState = ProcessState.SEARCH;
            startAppearTimer();
        }
    }

    private void handleInitialParamsUpdate(String params) {
        if (TextUtils.isEmpty(params)) {
            return;
        }
        try {
            JSONObject json = new JSONObject(params);
            long disappearTimeout = json.optLong(StandardFaceTrack.PARAM_DISAPPEAR_TIMEOUT);
            if (disappearTimeout <= 0) {
                return;
            }
            mDisappearTimeout = disappearTimeout;
            switch (mCurrentState) {
                case SEARCH:
                case CAN_NOT_TRACKING:
                case DO_NOT_TRACKING:
                    destroyAppearTimer();
                    startAppearTimer();
                    break;
                default:
                    break;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void processStatus(int status, String result) {
        Log.d(mName, "processStatus status: " + status + ", result: " + result);
        updateStatus(status, result);
    }

    private synchronized void processResult(int result) {
        Log.d(mName, "processResult result: " + result);
        destroyAppearTimer();
        PersonApi.getInstance().unregisterPersonListener(mPersonListener);
        mHandler.removeMessages(MSG_UPDATE_BEST_PERSON);
        mCurrentState = ProcessState.IDLE;
        stop(300, result, null);
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.d(mName, "onUpdate intent: " + intent + ", params: " + params
                + ", currentState: " + mCurrentState);
        switch (intent) {
            case StandardFaceTrack.UPDATE_SOUND_INPUT:
                mHandler.obtainMessage(MSG_SOUND_INPUT, params).sendToTarget();
                break;
            case StandardFaceTrack.UPDATE_CONTINUE_SEARCH_FACE:
                mHandler.sendEmptyMessage(MSG_CONTINUE_SEARCH_FACE);
                break;
            case StandardFaceTrack.UPDATE_INITIAL_PARAMS:
                mHandler.obtainMessage(MSG_UPDATE_INITIAL_PARAMS, params).sendToTarget();
                break;
            default:
                break;
        }
    }

    private void switchTrackTarget(int soundAngle) {
        Log.d(mName, "switchTrackTarget soundAngle: " + soundAngle);
        mApi.switchTrackTarget(soundAngle, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(mName, "switchTrackTarget onResult result: " + result
                        + ", message: " + message);
            }
        });
    }

    private void soundLocalization(final int soundAngle) {
        mSoundLocalizationComponent.setComponentListener(new ComponentListener() {
            @Override
            public void onFinish(int result, String message, String extraData) {
                Log.d(mName, "soundLocalization onFinish soundAngle: " + soundAngle);
                mHandler.sendEmptyMessage(MSG_SOUND_LOCALIZATION_END);
            }
        });
        JSONObject json = new JSONObject();
        try {
            json.put(ComponentParams.SoundLocalization.PARAM_ANGLE, soundAngle);
            json.put(ComponentParams.SoundLocalization.PARAM_IS_NEED_MOVE_BODY, FeatureConfig.isNavigationEnable());
            mSoundLocalizationComponent.start(json.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void stopSoundLocalization() {
        if (mSoundLocalizationComponent != null) {
            mSoundLocalizationComponent.setComponentListener(null);
            mSoundLocalizationComponent.stop(300);
        }
    }

    private boolean isInSameDirectionWithCurrent(int queryAngle, int curAngle) {
        if (queryAngle <= 0 && curAngle >= 0
                || queryAngle >= 0 && curAngle <= 0) {
            int error = Math.abs(Math.abs(queryAngle) - Math.abs(curAngle));
            return error <= 10;
        }
        return Math.abs(queryAngle - curAngle) <= 10;
    }

    private boolean isInVisualRange(int angle) {
        return angle <= 45 && angle >= -45;
    }

    private void startAppearTimer() {
        Log.v(mName, "startAppearTimer");
        mHandler.removeMessages(MSG_PERSON_APPEAR_TIMEOUT);
        mHandler.sendEmptyMessageDelayed(MSG_PERSON_APPEAR_TIMEOUT, mDisappearTimeout);
    }


    private void destroyAppearTimer() {
        Log.d(mName, "destroyAppearTimer");
        mHandler.removeMessages(MSG_PERSON_APPEAR_TIMEOUT);
    }

    @Override
    public void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        PersonApi.getInstance().unregisterPersonListener(mPersonListener);
        mApi.stopFocusFollow();
        stopSoundLocalization();
        mHandler.removeMessages(MSG_PERSON_APPEAR_TIMEOUT);
        mHandler.removeMessages(MSG_UPDATE_BEST_PERSON);
        mHandler.removeMessages(MSG_SOUND_INPUT);
        mHandler.removeMessages(MSG_CONTINUE_SEARCH_FACE);
        mHandler.removeMessages(MSG_TRACK_STATUS);
        mHandler.removeMessages(MSG_TRACK_ERROR);
        mHandler.removeMessages(MSG_TRACK_RESULT);
        mHandler.removeMessages(MSG_SOUND_LOCALIZATION_END);
		mHandler.removeMessages(MSG_UPDATE_INITIAL_PARAMS);
    }
}
