/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.platform.component;

import android.annotation.SuppressLint;
import android.os.Looper;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.bean.RoverConfig;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.utils.GsonUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

@SuppressLint("LongLogTag")
public class SetNavigationConfigComponent extends Component {

    private static final String TAG = "SetNavigationConfigComponent";
    private static final int STOP_TIMEOUT = 500;
    private static final List<Float> mLegalMaxRangeList = new ArrayList<Float>() {
        {
            add((float) Definition.LOST_MAX_DISTANCE.RANGE_HIGH.getValue());
            add((float) Definition.LOST_MAX_DISTANCE.RANGE_MIDDLE.getValue());
            add((float) Definition.LOST_MAX_DISTANCE.RANGE_LOW.getValue());
            add((float) Definition.LOST_MAX_DISTANCE.RANGE_SUPER_LOW.getValue());
        }
    };

    private float mPositionLostMaxRange = Definition.LOST_MAX_DISTANCE.RANGE_HIGH.getValue();
    private RoverConfig mRoverConfig = null;

    public SetNavigationConfigComponent(String name) {
        this(name, Looper.myLooper());
    }

    public SetNavigationConfigComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    protected void onStart(String params) {
        Log.d(mName, "onStart Navigation config params: " + params);
        try {
            JSONObject jsonObject = new JSONObject(params);
            mPositionLostMaxRange = jsonObject.optInt(ComponentParams.NavigationConfig.PARAM_MAX_RANGE,
                    0);
            if (!isMaxRangeLegal(mPositionLostMaxRange)) {
                processSetConfigStop(ComponentError.ERROR_PARAMS_MAX_RANGE_INVALID);
            } else {
                getRoverConfig();
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

    }

    @Override
    protected void onUpdate(String intent, String params) {
    }

    @Override
    protected void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
    }

    private boolean isMaxRangeLegal(float distance) {
        return mLegalMaxRangeList.contains(Float.valueOf(distance));
    }

    private void getRoverConfig() {
        Log.d(mName, "getRoverConfig");
        mApi.getNavigationConfig(new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "getNavigationConfig onResult:" + result + " message:" + message);
                onGetNavigationConfig(message);
            }
        });
    }

    private void onGetNavigationConfig(String backMsg) {
        if (backMsg != null && backMsg.equals("timeout")) {
            processSetConfigStop(ComponentError.ERROR_GET_ROVER_CONFIG_TIMEOUT);
        } else {
            String message = (backMsg == null ? "" : backMsg);
            try {
                mRoverConfig = GsonUtil.fromJson(message, RoverConfig.class);
            } catch (Exception e) {
                mRoverConfig = null;
            }
            if (mRoverConfig == null) {
                processSetConfigStop(ComponentError.ERROR_GET_ROVER_CONFIG_FAILED);
                return;
            }
            setNavigationConfig();
        }
    }

    private void setNavigationConfig() {
        Log.d(mName, "setNavigationConfig");
        mRoverConfig.setPositionLostMaxRange(mPositionLostMaxRange);
        mApi.setNavigationConfig(GsonUtil.toJson(mRoverConfig), new CommandListener() {

            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "setNavigationConfig onResult:" + result + " message:" + message);
                if (result == 1 && "succeed".equals(message)) {
                    processSetConfigStop(ComponentResult.RESULT_SUCCESS);
                } else {
                    processSetConfigStop(ComponentError.ERROR_SET_ROVER_CONFIG_FAILED);
                }
            }
        });
    }

    private void processSetConfigStop(int result) {
        Log.d(mName, "processSetConfigStop.result:" + result);
        stop(STOP_TIMEOUT, result, null);
    }
}
