package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by Orion on 2019/9/19.
 */
public class BodyFollowComponent extends Component {
    private static final String TAG = BodyFollowComponent.class.getSimpleName();

    private static final long TIME_OUT_STOP_COMPONENT = 500;

    private Gson mGson = new Gson();
    private PersonAppearComponent mPersonAppearComponent;
    private long mFindPersonTimeOut;

    private static final long DEFAULT_FIND_PERSON_TIME_OUT = 1500;

    public BodyFollowComponent(String name) {
        this(name, Looper.myLooper());
    }

    public BodyFollowComponent(String name, Looper looper) {
        super(name, looper);
        mPersonAppearComponent = new PersonAppearComponent(mName);
    }

    @Override
    protected void onStart(String params) {
        if (TextUtils.isEmpty(params)) {
            mFindPersonTimeOut = DEFAULT_FIND_PERSON_TIME_OUT;
            startWaitPersonAppear();
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(params);
            mFindPersonTimeOut = jsonObject.optLong(ComponentParams.BodyFollow.PARAM_FIND_PERSON_TIMEOUT,
                    DEFAULT_FIND_PERSON_TIME_OUT);
            if (mFindPersonTimeOut < DEFAULT_FIND_PERSON_TIME_OUT) {
                mFindPersonTimeOut = DEFAULT_FIND_PERSON_TIME_OUT;
            }
            startWaitPersonAppear();
        } catch (JSONException e) {
            e.printStackTrace();
            processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR,
                    "json parse exception");
        }
    }

    private void startWaitPersonAppear() {
        Log.d(mName, "Start wait person appear");
        mPersonAppearComponent.setComponentListener(new Component.ComponentListener() {
            @Override
            public void onFinish(int result, String message, String extraData) {
                Log.i(mName, "Wait person component onFinish : " + result + ", " + message);
                switch (result) {
                    case ComponentResult.RESULT_SUCCESS:
                        startBodyFollowAction(message);
                        break;
                    case ComponentResult.RESULT_TIMEOUT:
                        processResult(ComponentError.ERROR_FIND_PERSON_TIMEOUT,
                                "Find person time out");
                        break;
                    default:
                        processResult(result, message);
                        break;
                }
            }
        });
        try {
            JSONObject json = new JSONObject();
            json.put(ComponentParams.PersonAppear.PARAM_MAX_DISTANCE, 10);
            json.put(ComponentParams.PersonAppear.PARAM_TIMEOUT, mFindPersonTimeOut);
            json.put(ComponentParams.PersonAppear.PARAM_IS_NEED_INCOMPLETE_FACE, true);
            mPersonAppearComponent.start(json.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void stopWaitPersonAppear() {
        Log.d(mName, "stopWaitPersonAppear");
        if (mPersonAppearComponent != null) {
            mPersonAppearComponent.setComponentListener(null);
            mPersonAppearComponent.stop(TIME_OUT_STOP_COMPONENT);
        }
    }

    private void startBodyFollowAction(String message) {
        Person person = mGson.fromJson(message, Person.class);
        Log.d(TAG, "BodyFollowAction follow person id : " + person.getId());
        mApi.startBodyFollowAction(person.getId(), new ActionListener() {
            @Override
            public void onError(int errorCode, String errorString) {
                Log.d(TAG, "startBodyFollowAction onError : " + errorCode + " " + errorString);
                switch (errorCode) {
                    case Definition.ERROR_SET_TRACK_FAILED:
                        processResult(ComponentError.ERROR_HEAD_TRACK_FAILED,
                                "Target track failed");
                        break;
                    case Definition.ERROR_TARGET_NOT_FOUND:
                        processResult(ComponentError.ERROR_TRACK_TARGET_NOT_FOUND,
                                "Target not found");
                        break;
                    case Definition.ERROR_FOLLOW_TIME_OUT:
                        processResult(ComponentError.ERROR_HEAD_GUEST_LOST, "Gust lost");
                        break;
                    default:
                        processResult(errorCode, errorString);
                        break;
                }
            }

            @Override
            public void onStatusUpdate(int status, String data) {
                Log.d(TAG, "startBodyFollowAction onStatusUpdate : " + status + " " + data);
                switch (status) {
                    case Definition.STATUS_TRACK_TARGET_SUCCEED:
                        processStatus(ComponentStatus.STATUS_TRACK_SUCCESS, "Track success");
                        break;
                    case Definition.STATUS_GUEST_NEAR:
                        processStatus(ComponentStatus.STATUS_GUEST_NEAR, "Guest near");
                        break;
                    case Definition.STATUS_NAVI_OBSTACLES_AVOID:
                        processStatus(ComponentStatus.STATUS_OBSTACLES_AVOID,
                                "Pause due to obstacles within 1 meter");
                        break;
                    case Definition.STATUS_NAVI_OBSTACLES_DISAPPEAR:
                        processStatus(ComponentStatus.STATUS_OBSTACLE_DISAPPEAR,
                                "Obstacle disappear");
                        break;
                    default:
                        processStatus(status, data);
                        break;
                }
            }

            @Override
            public void onResult(int status, String responseString) {
                Log.d(TAG, "startBodyFollowAction onResult : " + status + " " + responseString);
                processResult(status, responseString);
            }
        });
    }

    @Override
    protected void onUpdate(String intent, String params) {

    }

    @Override
    protected void onStop(int reason) {
        Log.i(TAG, "onStop");
        mApi.stopBodyFollowAction();
        stopWaitPersonAppear();
    }

    private void processStatus(int status, final String data) {
        if (!isAlive()) {
            return;
        }
        updateStatus(status, data);
    }

    private void processResult(int result, String message) {
        if (!isAlive()) {
            return;
        }
        stop(500, result, message);
    }

}
