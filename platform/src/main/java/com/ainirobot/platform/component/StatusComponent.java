package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.platform.component.definition.ComponentParams;

import org.json.JSONException;
import org.json.JSONObject;

public class StatusComponent extends Component {

    private String mStatusType;

    public StatusComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    protected void onStart(String params) {
        //获取状态名称
        Log.d(mName, "onStart params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            mStatusType = json.optString(ComponentParams.Status.PARAM_STATUS_TYPE);
            boolean needInit = json.optBoolean(ComponentParams.Status.PARAM_NEED_INIT);
            if (mStatusListener != null) {
                mApi.unregisterStatusListener(mStatusListener);
            }
            if (!TextUtils.isEmpty(mStatusType)) {
                mApi.registerStatusListener(mStatusType, mStatusListener);
                if (needInit) {
                    mApi.getRobotStatus(mStatusType, mStatusListener);
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private StatusListener mStatusListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
            if (!isAlive()) {
                return;
            }
            if (TextUtils.equals(type, mStatusType) && StatusComponent.this.isAlive()) {
                processStatus(0, data);
            }
        }
    };

    private void processStatus(int status, String data) {
        Log.d(mName, "processStatus status: " + status + ", data: " + data);
        updateStatus(status, data);
    }

    @Override
    protected void onUpdate(String intent, String params) {

    }

    @Override
    protected void onStop(int reason) {
        Log.i(mName, "onStop: " + mStatusType);
        if (!TextUtils.isEmpty(mStatusType)) {
            mApi.unregisterStatusListener(mStatusListener);
        }
    }
}
