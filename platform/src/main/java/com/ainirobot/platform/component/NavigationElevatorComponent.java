/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.platform.component;

import static com.ainirobot.platform.component.definition.ComponentResult.RESULT_NAVIGATION_ARRIVED;

import android.os.Looper;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentStatus;

import org.json.JSONException;
import org.json.JSONObject;

public class NavigationElevatorComponent extends Component {
    private String mNavPlace;
    private int mTargetFloor;
    private float mLinearSpeed;
    private float mAngularSpeed;
    private final String START_TAG = "delayStartTimer";

    public NavigationElevatorComponent(String name, Looper looper) {
        super(name, looper);
    }

    public NavigationElevatorComponent(String mName) {
        super(mName, Looper.myLooper());
    }

    @Override
    protected void onStart(String params) {
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(params);
            mNavPlace = jsonObject.optString(ComponentParams.Navigation.PARAM_DESTINATION);
            mTargetFloor = jsonObject.optInt(ComponentParams.Navigation.PARAM_TARGET_FLOOR);
            mLinearSpeed = (float) jsonObject.optDouble(ComponentParams.Navigation.PARAM_LINEAR_SPEED
                    , SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED);
            mLinearSpeed = rectifyLinearSpeed(mLinearSpeed);
            mAngularSpeed = (float) jsonObject.optDouble(ComponentParams.Navigation.PARAM_ANGULAR_SPEED
                    , SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED);
            mAngularSpeed = rectifyAngularSpeed(mAngularSpeed);

            startElevatorNavigation();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void startElevatorNavigation() {
        mApi.startElevatorNavigation(
                mNavPlace,
                mTargetFloor,
                false,
                mLinearSpeed,
                mAngularSpeed,
                false,
                new ActionListener() {
                    @Override
                    public void onResult(int status, String message, String extraData) throws RemoteException {
                        Log.e(mName, "onResult: " + status + " responseString：" + message + " extraData：" + extraData);
                        handlerResult(status, message, extraData);
                    }

                    @Override
                    public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                        Log.e(mName, "onError: " + errorCode + " errorString：" + errorString + " extraData：" + extraData);
                        handlerError(errorCode, errorString, extraData);
                    }

                    @Override
                    public void onStatusUpdate(int status, String data, String extraData) throws RemoteException {
                        Log.e(mName, "onStatusUpdate: " + status + " data：" + data + " extraData：" + extraData);
                        handlerStatusUpdate(status, data, extraData);
                    }
                });
    }

    private void handlerError(int errorCode, String message, String extraData) {
        switch (errorCode) {
            case Definition.ERROR_NOT_ESTIMATE:
                stop(ComponentError.ERROR_NOT_ESTIMATE,
                        "navigation not estimate", extraData);
                break;
            case Definition.ERROR_IN_DESTINATION:
                stop(ComponentError.ERROR_NAVIGATION_ALREADY_IN_DESTINATION,
                        "already in destination", extraData);
                break;
            case Definition.ERROR_DESTINATION_NOT_EXIST:
                stop(ComponentError.ERROR_DESTINATION_NOT_EXIST,
                        "destination not exist", extraData);
                break;
            case Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE:
                stop(ComponentError.ERROR_DESTINATION_CAN_NOT_ARRIVE,
                        "navigation moving time out", extraData);
                break;
            case Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT:
                stop(ComponentError.ERROR_MULTI_ROBOT_WAITING_TIMEOUT);
                break;
            case Definition.ERROR_NAVIGATION_AVOID_TIMEOUT:
                stop(ComponentError.ERROR_NAVIGATION_AVOID_TIMEOUT,
                        "navigation avoid time out", "");
            case Definition.ACTION_RESPONSE_ALREADY_RUN:
                stop(ComponentError.ERROR_REQUEST_RES_BUSY);
                break;
            case Definition.ACTION_RESPONSE_REQUEST_RES_ERROR:
                stop(ComponentError.ERROR_REQUEST_RES_FAILED);
                break;
            case Definition.ERROR_WHEEL_OVER_CURRENT_RUN_OUT:
                stop(ComponentError.ERROR_WHEEL_OVER_CURRENT_RUN_OUT,
                        "wheel over current retry count run out", extraData);
                break;
            case Definition.ACTION_RESPONSE_RES_UNAVAILBALE:
                stop(ComponentError.ERROR_OPEN_RADAR_FAILED,
                        "res unavailable: " + message, extraData);
                break;
            default:
                stop(errorCode, message, extraData);
                break;
        }
    }

    private void handlerStatusUpdate(int status, String data, String extraData) {
        switch (status) {
            case Definition.STATUS_GOAL_OCCLUDED:
            case Definition.STATUS_NAVI_AVOID:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_START,
                        "navigation avoid start", extraData);
                break;
            case Definition.STATUS_GOAL_OCCLUDED_END:
            case Definition.STATUS_NAVI_AVOID_END:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_END,
                        "navigation avoid end", extraData);
                break;
            case Definition.STATUS_NAVI_OBSTACLES_AVOID:
                updateStatus(ComponentStatus.STATUS_OBSTACLES_AVOID,
                        "pause to obstacles avoid", extraData);
                break;
            case Definition.STATUS_START_NAVIGATION:
                updateStatus(ComponentStatus.STATUS_START_NAVIGATION,
                        "start navigation");
                break;
            case Definition.STATUS_NAVI_MULTI_ROBOT_WAITING:
                updateStatus(ComponentStatus.STATUS_NAVI_MULTI_ROBOT_WAITING,
                        "navigation multi robot waiting");
                break;
            case Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END:
                updateStatus(ComponentStatus.STATUS_NAVI_MULTI_ROBOT_WAITING_END,
                        "navigation multi robot waiting end");
                break;
            case Definition.STATUS_NAVI_GO_STRAIGHT:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_GO_STRAIGHT, data,
                        extraData);
                break;
            case Definition.STATUS_NAVI_TURN_LEFT:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_TURN_LEFT, data,
                        extraData);
                break;
            case Definition.STATUS_NAVI_TURN_RIGHT:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_TURN_RIGHT, data,
                        extraData);
                break;
            case Definition.STATUS_NAVI_SET_PRIORITY_FAILED:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_SET_PRIORITY_FAILED, data,
                        extraData);
                break;
            default:
                updateStatus(status, data, extraData);
                break;
        }
    }

    private void handlerResult(int status, String message, String extraData) {
        switch (status) {
            case Definition.RESULT_OK:
                stop(RESULT_NAVIGATION_ARRIVED, message, extraData);
                break;
            case Definition.ACTION_RESPONSE_STOP_SUCCESS:
                stop(status, message + " 停止成功", extraData);
                break;
            case Definition.RESULT_FAILURE:
                stop(status, message + " 导航失败", extraData);
                break;
            case Definition.STATUS_NAVI_OUT_MAP:
                stop(ComponentError.ERROR_NAVIGATION_OUT_MAP, message, extraData);
                break;
            case Definition.STATUS_NAVI_GLOBAL_PATH_FAILED:
                stop(ComponentError.ERROR_NAVIGATION_GLOBAL_PATH_FAILED, message, extraData);
                break;
            case Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH:
            case Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT:
            case Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL:
            case Definition.STATUS_NAVI_MULTI_VERSION_NOT_MATCH:
                stop(ComponentError.ERROR_MULTIPLE_MODE_ERROR, message, extraData);
                break;
            case Definition.STATUS_NAVI_WHEEL_SLIP:
                stop(ComponentError.ERROR_NAVI_WHEEL_SLIP, message, extraData);
                break;
            default:
                stop(status, message, extraData);
                break;
        }
    }

    @Override
    protected void onUpdate(String intent, String params) {

    }

    @Override
    protected void onStop(int reason) {
        DelayTask.cancel(START_TAG);
        mApi.stopElevatorNavigation();
    }

    private void stop(int result, String message, String extraData) {
        stop(STOP_TIMEOUT, result, message, extraData);
    }
}
