/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.utils.ZipUtils;
import com.ainirobot.platform.bean.PoseItem;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

public class GetNearestPlaceComponent extends Component {

    private static final long STOP_TIMEOUT = 100;
    private List<PoseItem> mPoseList;
    private Pose mCurrentPose;

    public GetNearestPlaceComponent(String name) {
        this(name, Looper.myLooper());
    }

    public GetNearestPlaceComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    public void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        mCurrentPose = null;
        mPoseList = null;
        isEstimate();
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.d(mName, "onUpdate params: " + params);
    }

    private void isEstimate() {
        mApi.isRobotEstimate(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(mName, "isEstimate result:" + result + " message:" + message);

                if (result == Definition.RESULT_OK && "true".equals(message)) {
                    getPlaceList();
                } else {
                    processResult(ComponentError.ERROR_NOT_ESTIMATE,
                            null);
                }
            }
        });
    }

    private void getPlaceList() {
        mApi.getPlaceList(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                if (result < 0 || TextUtils.isEmpty(message)) {
                    processResult(ComponentError.ERROR_GET_PLACE_LIST_FAILED,
                            null);
                    return;
                }

                Gson gson = new Gson();
                message = ZipUtils.unzipMapData(gson, message);
                List<PoseItem> poses = gson.fromJson(message, new TypeToken<List<PoseItem>>() {
                }.getType());
                if (poses == null || poses.size() <= 0) {
                    processResult(ComponentError.ERROR_GET_PLACE_LIST_EMPTY,
                            null);
                } else {
                    mPoseList = poses;
                    getCurrentPosition();
                }
            }
        });
    }

    private void getCurrentPosition() {
        mApi.getPosition(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                if (result < 0 || TextUtils.isEmpty(message)) {
                    processResult(ComponentError.ERROR_GET_CURRENT_POSE_FAILED,
                            null);
                    return;
                }

                try {
                    JSONObject json = new JSONObject(message);
                    float x = (float) json.optDouble(Definition.JSON_NAVI_POSITION_X);
                    float y = (float) json.optDouble(Definition.JSON_NAVI_POSITION_Y);
                    float theta = (float) json.optDouble(Definition.JSON_NAVI_POSITION_THETA);
                    mCurrentPose = new Pose();
                    mCurrentPose.setX(x);
                    mCurrentPose.setY(y);
                    mCurrentPose.setTheta(theta);
                    calculateDistance();
                } catch (JSONException e) {
                    processResult(ComponentError.ERROR_GET_CURRENT_POSE_FAILED,
                            null);
                }
            }
        });
    }

    private void calculateDistance() {
        Log.d(mName, "calculateDistance currentPose: " + mCurrentPose
                + ", poseList: " + mPoseList);
        PoseItem poseItem = null;
        for (PoseItem newPose : mPoseList) {
            double newDistance = getDistance(mCurrentPose, newPose);
            if (poseItem == null) {
                poseItem = newPose;
            } else {
                double distance = getDistance(mCurrentPose, poseItem);
                if (distance > newDistance) {
                    poseItem = newPose;
                }
            }
        }
        Pose pose = new Pose();
        pose.setName(poseItem != null ? poseItem.getName() : "");
        pose.setDistance((float) getDistance(mCurrentPose, poseItem));
        Gson gson = new Gson();
        processResult(ComponentResult.RESULT_SUCCESS, gson.toJson(pose));
    }

    public double getDistance(Pose pose, PoseItem poseItem) {
        if (pose == null || poseItem == null) {
            return 1.7976931348623157E308D;
        } else {
            double destX = (double) pose.getX();
            double destY = (double) pose.getY();
            double x = (double) poseItem.x;
            double y = (double) poseItem.y;
            return Math.sqrt(Math.pow(x - destX, 2.0D) + Math.pow(y - destY, 2.0D));
        }
    }

    private void processResult(int result, String message) {
        Log.d(mName, "processResult result: " + result + ", message: " + message);
        stop(STOP_TIMEOUT, result, message);
    }

    @Override
    public void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
    }
}
