/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.component.definition;

public class ComponentParams {

    public static class PersonAppear {
        public static final String PARAM_PERSON_ID = "personId";
        public static final String PARAM_PERSON_NAME = "personName";
        public static final String PARAM_MAX_DISTANCE = "maxDistance";
        public static final String PARAM_MAX_FACE_ANGLE_X = "maxFaceAngleX";
        public static final String PARAM_IS_NEED_INCOMPLETE_FACE = "isNeedInCompleteFace";
        public static final String PARAM_INCOMPLETE_FACE_CACHE_TIME = "incompleteFaceCacheTimeout";
        public static final String PARAM_IS_NEED_BODY = "isNeedBody";
        public static final String PARAM_IS_NEED_RECOGNIZE = "isNeedRecognize";
        public static final String PARAM_RECOGNIZE_TIMEOUT = "recognizeTimeout";
        public static final String PARAM_IS_NEED_DELETE_PIC = "isNeedDeletePic";
        public static final String PARAM_TIMEOUT = "appearTimeout";

        public static final String UPDATE_CONTINUE_SEARCH_FACE = "continueSearchFace";
    }

    public static class PersonDisappear {
        public static final String PARAM_PERSON_ID = "personId";
        public static final String PARAM_PERSON_NAME = "personName";
        public static final String PARAM_MAX_DISTANCE = "maxDistance";
        public static final String PARAM_MAX_FACE_ANGLE_X = "maxFaceAngleX";
        public static final String PARAM_IS_NEED_INCOMPLETE_FACE = "isNeedInCompleteFace";

        public static final String PARAM_IS_NEED_BODY = "isNeedBody";
        public static final String PARAM_LOST_TIMEOUT = "lostTimeout";
        public static final String PARAM_DISAPPEAR_TIMEOUT = "disappearTimeout";

        public static final String UPDATE_CONTINUE_SEARCH_FACE = "continueSearchFace";
    }

    public static class WakeupAndPreWakeupStartCheck {
        public static final String PARAM_IS_NEED_PRE_WAKEUP = "isNeedPreWakeup";
        public static final String PARAM_MAX_DISTANCE = "maxDistance";
        public static final String PARAM_WAKEUP_FACE_DISTANCE = "wakeupFaceDistance";
        public static final String PARAM_WAKEUP_FACE_ANGLE_X = "wakeupFaceAngleX";
        public static final String PARAM_IS_NEED_INCOMPLETE_FACE = "isNeedInCompleteFace";
        public static final String PARAM_INCOMPLETE_FACE_CACHE_TIME = "incompleteFaceCacheTimeout";
        public static final String PARAM_IS_PRE_WAKEUP_NEED_INCOMPLETE_FACE = "isPreWakeupNeedInCompleteFace";
        public static final String PARAM_IS_PRE_WAKEUP_NEED_BODY = "isPreWakeupNeedBody";
        public static final String PARAM_PRE_WAKEUP_INTERVAL_TIME = "preWakeupIntervalTime";
        public static final String PARAM_RECOGNIZE_TIMEOUT = "recognizeTimeout";
        public static final String PARAM_IS_WAKEUP_NEED_RECOGNIZE = "isWakeupNeedRecognize";
    }

    public static class FaceTrack {
        public static final String PARAM_PERSON_ID = "personId";
        public static final String PARAM_LOST_DISTANCE = "lostDistance";
        public static final String PARAM_LOST_TIMEOUT = "lostTimeout";
        public static final String PARAM_IS_NEED_MOVE_BODY = "isNeedMoveBody";
    }

    public static class SoundLocalization {
        public static final String PARAM_ANGLE = "angle";
        public static final String PARAM_IS_NEED_MOVE_BODY = "isNeedMoveBody";
    }

    public static class Recognize {
        public static final String PARAM_PERSON_ID = "personId";
        public static final String PARAM_TIMEOUT = "recognizeTimeout";
        public static final String PARAM_IS_NEED_DELETE_PIC = "isNeedDeletePic";
    }

    public static class LeadingTrack {
        public static final String PARAM_LOST_TIME = "lostTime";
        public static final String PARAM_WAIT_TIME = "waitTime";
        public static final String PARAM_FARAWAY_DISTANCE = "farawayDistance";
        public static final String PARAM_DETECT_DELAY = "detectDelay";
        public static final String PARAM_PERSON_APPEAR_MAX_DISTANCE = "maxDistance";
        public static final String PARAM_PERSON_APPEAR_MAX_FACE_ANGLE_X = "maxFaceAngleX";
        public static final String PARAM_PERSON_APPEAR_TIMEOUT = "personAppearTimeout";
        public static final String PARAM_TRACK_NEED_FIND_PERSON = "param_track_need_find_person";

        public static final String INTENT_NEAR_DESTINATION = "intent_near_destination";
        public static final String INTENT_ARRIVED_DESTINATION = "intent_arrived_destination";
        public static final String INTENT_LEADING_ERROR = "intent_leading_error";
    }

    public static class Navigation {
        public static final String PARAM_NAVIGATION_TYPE = "navigation_type";
        public static final String PARAM_DESTINATION = "destination";
        public static final String PARAM_COORDINATE_DEVIATION = "coordinate_deviation";
        public static final String PARAM_MOVING_TIMEOUT_TIME = "moving_timeout_time";
        public static final String PARAM_AVOID_INTERVAL_TIME = "avoid_interval_time";
        public static final String PARAM_GET_DISTANCE_INTERVAL_TIME = "get_distance_interval_time";
        public static final String PARAM_MAX_AVOID_COUNT = "max_avoid_count";
        public static final String PARAM_AUTO_RESET_ESTIMATE = "auto_reset_estimate";
        public static final String PARAM_LINEAR_SPEED = "param_linear_speed";
        public static final String PARAM_ANGULAR_SPEED = "param_angular_speed";
        public static final String PARAM_IS_ADJUST_ANGLE = "param_is_adjust_angle";
        public static final String PARAM_MULTI_ROBOT_WAITING_TIMEOUT_TIME = "param_multi_waiting_timeout_time";
        public static final String PARAM_IS_NEED_AVOID_NOTIFY_IMMEDIATELY = "param_is_need_avoid_notify_immediately";
        public static final String PARAM_DESTINATION_RANGE = "param_destination_range";
        public static final String PARAM_WHEEL_OVER_CURRENT_RETRY_COUNT = "param_wheel_over_current_retry_count";
        public static final String PARAM_PRIORITY = "param_priority";
        public static final String PARAM_POSE_X = "pose_x";
        public static final String PARAM_POSE_Y = "pose_y";
        public static final String PARAM_POSE_THETA = "pose_theta";
        public static final String PARAM_LINEAR_ACCELERATION = "linear_acceleration";
        public static final String PARAM_ANGULAR_ACCELERATION = "angular_acceleration";
        public static final String PARAM_TARGET_FLOOR = "target_floor";
        public static final String PARAM_TARGET_MAP_NAME = "target_map_name";
        public static final String PARAM_TARGET_AVAILABLE_ELEVATOR = "target_available_elevator";
        public static final String PARAM_START_MODE_LEVEL = "start_mode_level";
        public static final String PARAM_BRAKE_MODE_LEVEL = "brake_mode_level";
        public static final String PARAM_OBS_DISTANCE = "obs_distance";
        public static final String PARAM_POS_TOLERANCE = "pos_tolerance";
        public static final String PARAM_ANGLE_TOLERANCE = "angle_tolerance";

        /**
         * 导航方式：按目的地导航；按点导航
         */
        public static final int TYPE_DESTINATION = 1;
        public static final int TYPE_POSE = 2;
    }

    public static class RecoverPositive {
        public static final String PARAM_PLACE_NAME = "placeName";
        public static final String PARAM_COORDINATE_DEVIATION = "coordinateDeviation";
    }

    public static class Forward {
        public static final String PARAMS_DISTANCE = "distance";
        public static final String PARAMS_LINEAR_SPEED = "linearSpeed";
        public static final String PARAMS_ANGULAR_SPEED = "angularSpeed";
        public static final String PARAMS_FORWARD_TIMEOUT = "forwardTimeout";
    }

    public static class StandardFaceTrack {
        public static final String PARAM_PERSON_ID = "personId";
        public static final String PARAM_MAX_DISTANCE = "maxDistance";
        public static final String PARAM_MAX_FACE_ANGLE_X = "maxFaceAngleX";
        public static final String PARAM_IS_NEED_INCOMPLETE_FACE = "isNeedInCompleteFace";
        public static final String PARAM_DISAPPEAR_TIMEOUT = "disappearTimeout";
        public static final String PARAM_IS_MULTI_PERSON_NOT_TRACK = "isMultiPersonNotTrack";
        public static final String PARAM_MULTI_PERSON_NOT_TRACK_DISTANCE = "multiPersonNotTrackDistance";
        public static final String PARAM_IS_ALLOW_MOVE_BODY = "isAllowMoveBody";

        public static final String UPDATE_SOUND_INPUT = "soundInput";
        public static final String UPDATE_PARAM_SOUND_ANGLE = "soundAngle";
        public static final String UPDATE_PARAM_SOUND_SID = "sid";
        public static final String UPDATE_CONTINUE_SEARCH_FACE = "continueSearchFace";
        public static final String UPDATE_INITIAL_PARAMS = "initialParams";
    }

    public static class GetDistanceWithPlace {
        public static final String PARAM_PLACES = "places";
    }

    public static class ReceptionRegister {
        public static final String PARAM_PHOTO_SRC = "photoSrc";
        public static final String PARAM_GUEST_NAME = "guestName";
        public static final String PARAM_TASK_ID = "taskId";
        public static final String PARAM_HAS_IMAGE = "hasImage";
    }

    public static class ReservationCode {
        public static final String PARAM_RESERVATION_TYPE = "reservationType";
        public static final String PARAM_RESERVATION_CODE = "reservationCode";

    }

    public static class ResetEstimate {
        public static final String PARAM_RESET_ESTIMATE_COUNT = "param_reset_estimate_count";
        public static final String PARAM_RESET_ESTIMATE_POSE = "param_reset_estimate_pose";
        public static final String PARAM_RESET_ESTIMATE_NAME = "param_reset_estimate_name";
        public static final String PARAM_RESET_ESTIMATE_TYPE = "param_reset_estimate_type";
        public static final String PARAM_RESET_ESTIMATE_INTERVAL = "param_reset_estimate_interval";
    }

    public static class Register {
        public static final String PARAM_PERSON_NAME = "personName";
        public static final String PARAM_PHOTO_PATH = "photoPath";
        public static final String PARAM_PERSON_APPEAR_TIMEOUT = "personAppearTimeOut";
        public static final String PARAM_RECOGNIZE_TIMEOUT = "recognizeTimeOut";
        public static final String WELCOME_CONTENT = "welcomeContent";
    }

    public static class ModifyName {
        public static final String PARAM_PERSON_NAME = "personName";
        public static final String PARAM_REGISTER_ID = "registerId";
        public static final String WELCOME_CONTENT = "welcomeContent";
    }

    public static class HeadTurn {
        public static final String PARAM_HEAD_TURN_BEAN = "headTurnBean";
    }

    public static class HeadTurnGroup {
        public static final String PARAM_HEAD_TURN_GROUP_BEAN = "headTurnGroupBean";
        public static final String PARAM_INTERVAL_TIME = "intervalTime";
    }

    public static class SetCruiseRoute {
        public static final String PARAM_CRUISE_ROUTE_LIST = "cruise_route_list";
        public static final String PARAM_CRUISE_ROUTE_NAME = "cruise_route_name";
    }

    public static class Cruise {
        public static final String PARAM_CRUISE_ROUTE_LIST = "cruise_route_list";
        public static final String PARAM_CRUISE_IS_NEED_DOCK = "cruise_is_need_dock";
        public static final String PARAM_CRUISE_POINT_AVOID_TIME_OUT = "cruise_point_avoid_time_out";
        public static final String PARAM_CRUISE_IS_USE_DEFAULT_ROUTE = "cruise_is_use_default_route";
        public static final String PARAM_CRUISE_IS_CHECK_POINTS_EXIT = "cruise_is_check_points_exit";
        public static final String PARAM_CRUISE_START_INDEX = "cruise_start_index";
        public static final String PARAM_CRUISE_LINEAR_SPEED = "cruise_linear_speed";
        public static final String PARAM_CRUISE_ANGULAR_SPEED = "cruise_angular_speed";
        public static final String PARAM_CRUISE_MULTI_WAITING_TIMEOUT = "cruise_multi_waiting_timeout";
    }

    public static class Charge {
        public static final String PARAM_TIMEOUT = "timeout";
        public static final String PARAM_AVOID_DISTANCE = "avoidDistance";
        public static final String PARAM_AVOID_TIMEOUT = "avoidTimeout";
    }

    public static class NavigationBack {
        public static final String PARAM_DESTINATION = "destination";
        public static final String PARAM_COORDINATE_DEVIATION = "coordinate_deviation";
        public static final String PARAM_MOVING_TIMEOUT_TIME = "moving_timeout_time";
        public static final String PARAM_AVOID_INTERVAL_TIME = "avoid_interval_time";
        public static final String PARAM_GET_DISTANCE_INTERVAL_TIME = "get_distance_interval_time";
        public static final String PARAM_MAX_AVOID_COUNT = "max_avoid_count";
        public static final String PARAM_AUTO_RESET_ESTIMATE = "auto_reset_estimate";
        public static final String PARAM_LINEAR_SPEED = "param_linear_speed";
        public static final String PARAM_ANGULAR_SPEED = "param_angular_speed";
        public static final String PARAM_PRIORITY = "param_priority";
    }

    public static class RobotStandby {
        public static final String PARAM_IS_NEED_CLOSE_HDMI = "isNeedCloseHdmi";
    }

    public static class BasicMotion {
        public static final String PARAM_BASIC_MOTION_BEAN = "basicMotionBean";
    }

    public static class ChargeStart {
        public static final String PARAM_CHARGE_TIMEOUT = "chargeTimeout";
        public static final String PARAM_AVOID_DISTANCE = "avoidDistance";
        public static final String PARAM_AVOID_TIMEOUT = "avoidTimeout";
        public static final String PARAM_MULTI_WAIT_TIMEOUT = "multiWaitTimeout";
    }

    public static class BodyFollow {
        public static final String PARAM_FIND_PERSON_TIMEOUT = "param_find_person_timeout";
    }

    public static class Status {
        public static final String PARAM_STATUS_TYPE = "statusType";
        public static final String PARAM_NEED_INIT = "needInit";
    }

    public static class LightGroup {
        public static final String PARAM_LIGHT_GROUP_BEAN = "lightGroupBean";
        public static final String PARAM_INTERVAL_TIME = "intervalTime";
        public static final String PARAM_IS_LOOP = "isLoop";
        public static final String PARAM_PRO_ZCB_LIGHT_EFFECT = "pro_zcb_light_effect";
    }

    public static class NavigationConfig {
        public static final String PARAM_MAX_RANGE = "maxRange";
    }

    public static class NavigationTransfer {
        /**
         * 目的地
         */
        public static final String PARAM_MAIN_DESTINATION = "destination";
        /**
         * 备用点位列表，按照顺序选取
         */
        public static final String PARAM_STANDBY_DESTINATION_LIST = "standby_destination_list";
        /**
         * 主目标点导航需要下发的参数信息
         */
        public static final String PARAM_MAIN_NAVGATION_PARAMS = "main_navigation_params";
        /**
         * 备用目标点导航需要下发的参数信息
         */
        public static final String PARAM_STANDBY_NAVGATION_PARAMS = "standby_navigation_params";
        /**
         * 最后一个目标点导航避障最大次数
         */
        public static final String PARAM_LAST_NAVIGATION_AVOID_MAX_COUNT = "last_navigation_avoid_max_count";
    }

    public static class NavigationCheck {
        /**
         * 目的地
         */
        public static final String PARAM_DESTINATION = "destination";
        /**
         * 当前点位
         */
        public static final String PARAM_CURRENT_PLACE = "current_place";
        /**
         * 目标点导航需要下发的参数信息
         */
        public static final String PARAM_NAVGATION_PARAMS = "navigation_params";
        /**
         * 备用点位列表，按照顺序选取
         */
        public static final String PARAM_STANDBY_DESTINATION_LIST = "standby_destination_list";
        /**
         * 补位调度策略:0 Lora优先级星形补位, 1 点位优先级星形补位, 2 点位优先级火车补位
         */
        public static final String PARAM_DYNAMIC = "dynamic";
        /**
         * 回程调度策略:0 按Lora优先级计算点位, 1 默认去最后的点位（只在火车调度生效）, 2 动态计算点位
         */
        public static final String PARAM_DYNAMIC_BACK = "dynamic_back";
        /**
         * 仅在导航时补位: false 机器推动离开位置也补位（默认）, true 机器导航离开才补位, 目前仅对火车生效
         */
        public static final String PARAM_FILLING_WHEN_DELIVERY = "fillingWhenDelivery";
        /**
         * 组件查询超时时间，单位ms，如果<= 0 视为无限长
         */
        public static final String PARAM_TIMEOUT = "timeout";
        /**
         * 目标点匹配半径,最小值为0.01m
         */
        public static final String PARAM_DESTINATION_RANGE = "destination_range";
    }

    public static class LeavePile {
        public static final String PARAM_LEAVE_PILE_DISTANCE = "param_leave_pile_distance";
        public static final String PARAM_LEAVE_PILE_SPEED = "param_leave_pile_speed";
    }

    public static class JudgeInChargingPile {
        public static final String PARAM_COORDINATE_DEVIATION = "param_coordinate_deviation";
    }

    public static class UvcCamera {
        /**
         * 识别类型,单次识别还是持续识别
         */
        public static final String PARAM_START_CLASSIFY_TYPE = "param_start_classify_type";
        public static final String PARAM_START_CLASSIFY_SINGLE = "single"; // 单次识别
        public static final String PARAM_START_CLASSIFY_CONTINUE = "continue";// 持续识别
    }

    public static class ProTrayLED{
        public static final String PARAM_TRAY_LED_FLOOR = "param_tray_led_floor"; //获取每一层指示灯的控制参数
        public static final String PARAM_TRAY_LED_ALL = "param_tray_led_all"; //获取统一控制所有层的指示灯的控制参数
    }

}
