package com.ainirobot.platform.component;

import android.os.Looper;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.react.reactnative.component.skillcomponent.ResouceType;

import org.json.JSONException;
import org.json.JSONObject;

public class JudgeInChargingPileComponent extends Component {
    private static final float DEFAULT_COORDINATE_DEVIATION=0.1f;
    private float mCoordinateDeviation;

    public JudgeInChargingPileComponent(String name) {
        this(name, Looper.myLooper());
    }

    public JudgeInChargingPileComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    protected void onStart(String params) {
        Log.i(mName, "onStart params:" + params);
        try {
            JSONObject json = new JSONObject(params);
            mCoordinateDeviation = (float) json.optDouble(ComponentParams.JudgeInChargingPile.PARAM_COORDINATE_DEVIATION,
                    DEFAULT_COORDINATE_DEVIATION);
        } catch (JSONException e) {
            mCoordinateDeviation=DEFAULT_COORDINATE_DEVIATION;
        }
        judgeInChargingPile();
    }

    private void judgeInChargingPile(){
       mApi.judgeInChargingPile(mCoordinateDeviation,new CommandListener(){
           @Override
           public void onResult(int result, String message) {
               Log.i(mName, "judgeChargingPile result:" + result + " message:" + message);
               switch (result){
                   case Definition.RESULT_OK_NO_ESTIMATE_CHARGING:
                       processResult(ComponentResult.RESULT_OK_NO_ESTIMATE_CHARGING,message);
                       break;
                   case Definition.RESULT_OK_ESTIMATE_WITHIN_RANGE:
                       processResult(ComponentResult.RESULT_OK_ESTIMATE_WITHIN_RANGE,message);
                       break;
                   case Definition.RESULT_NO_ESTIMATE_NO_CHARGING:
                       processResult(ComponentResult.RESULT_NO_ESTIMATE_NO_CHARGING,message);
                       break;
                   case Definition.RESULT_ESTIMATE_WITHOUT_RANGE:
                       processResult(ComponentResult.RESULT_ESTIMATE_WITHOUT_RANGE,message);
                       break;
                   case Definition.RESULT_OK_NO_ESTIMATE_CHARGING_WIRE:
                       processResult(ComponentResult.RESULT_OK_NO_ESTIMATE_CHARGING_WIRE,message);
                       break;
                   case Definition.RESULT_OK_ESTIMATE_WITHIN_RANGE_WIRE:
                       processResult(ComponentResult.RESULT_OK_ESTIMATE_WITHIN_RANGE_WIRE,message);
                       break;
                   default:
                       processResult(result,message);
                       break;
               }
           }
       });
    }
    /*
     * result：Definition类常量
     * RESULT_OK_NO_ESTIMATE_CHARGING：在桩未定位
     * RESULT_OK_ESTIMATE_WITHIN_RANGE：在桩在指定距离内
     *
     * RESULT_NO_ESTIMATE_NO_CHARGING：不在桩未定位
     * RESULT_ESTIMATE_WITHOUT_RANGE：不在桩未在指定距离内
     */
    private void processResult(int result,String message){
        stop(50, result, message);
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.i(mName, "onUpdate params:" + params);
    }

    @Override
    protected void onStop(int reason) {
        Log.i(mName, "onStop reason:" + reason);
    }
}
