package com.ainirobot.platform.component;/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Timer;
import java.util.TimerTask;

public class ForwardComponent extends Component {

    private static final double DEFAULT_FORWARD_DISTANCE = 0;
    private static final long STOP_TIMEOUT = 500;
    private static final long DEFAULT_FORWARD_TIMEOUT = 20000;
    private static final long MIN_FORWARD_TIMEOUT = 1000;

    private double mForwardDistance;
    private double mDefaultLinearSpeed;
    private double mDefaultAngularSpeed;
    private double mLinearSpeed;
    private double mAngularSpeed;
    private long mForwardTimeout;
    private Timer mForwardTimer;

    public ForwardComponent(String name) {
        this(name, Looper.myLooper());
    }

    public ForwardComponent(String name, Looper looper) {
        super(name, looper);
        mDefaultLinearSpeed = RobotSettingApi.getInstance().getRobotFloat(
                Definition.ROBOT_SETTING_GREET_LINEAR_SPEED);
        mDefaultAngularSpeed = RobotSettingApi.getInstance().getRobotFloat(
                Definition.ROBOT_SETTING_GREET_ANGULAR_SPEED);
    }

    @Override
    public void onStart(String params) {
        Log.d(mName, "onStart params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            mForwardDistance = json.optDouble(ComponentParams.Forward.PARAMS_DISTANCE,
                    DEFAULT_FORWARD_DISTANCE);
            if (mForwardDistance == DEFAULT_FORWARD_DISTANCE) {
                processForwardResult(ComponentError.ERROR_PARAMS_DISTANCE_INVALID);
                return;
            }
            mLinearSpeed = json.optDouble(ComponentParams.Forward.PARAMS_LINEAR_SPEED, mDefaultLinearSpeed);
            mAngularSpeed = json.optDouble(ComponentParams.Forward.PARAMS_ANGULAR_SPEED, mDefaultAngularSpeed);
            mForwardTimeout = json.optLong(ComponentParams.Forward.PARAMS_FORWARD_TIMEOUT, DEFAULT_FORWARD_TIMEOUT);
            if (mForwardTimeout < MIN_FORWARD_TIMEOUT) {
                mForwardTimeout = MIN_FORWARD_TIMEOUT;
            }

            Log.d(mName, "onStart mLinearSpeed: " + mLinearSpeed + " mAngularSpeed:" + mAngularSpeed);
            isEstimate();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onUpdate(String intent, String params) {
        Log.d(mName, "onUpdate params: " + params);
    }

    private void isEstimate() {
        mApi.isRobotEstimate(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                if (!isAlive()) {
                    Log.e(mName, "isEstimate onResult has not alive");
                    return;
                }
                Log.d(mName, "isEstimate result:" + result + " message:" + message);
                if (result == Definition.RESULT_OK && "true".equals(message)) {
                    getForwardPosition();
                } else {
                    processForwardResult(ComponentError.ERROR_NOT_ESTIMATE);
                }
            }
        });
    }

    private void getForwardPosition() {
        mApi.getPosition(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                if (!isAlive()) {
                    Log.e(mName, "getForwardPosition onResult has not alive");
                    return;
                }
                Log.d(mName, "getForwardPosition result: " + result +
                        ", message: " + message);
                if (result != Definition.RESULT_OK || TextUtils.isEmpty(message)) {
                    processForwardResult(ComponentError.ERROR_GET_CURRENT_POSE_FAILED);
                    return;
                }
                Pose pose = new Pose();
                try {
                    JSONObject object = new JSONObject(message);
                    pose.setX((float) object.optDouble(Definition.JSON_NAVI_POSITION_X));
                    pose.setY((float) object.optDouble(Definition.JSON_NAVI_POSITION_Y));
                    pose.setTheta((float) object.optDouble(Definition.JSON_NAVI_POSITION_THETA));
                } catch (JSONException e) {
                    e.printStackTrace();
                    return;
                }
                Pose newPose = new Pose();
                newPose.setX((float) (pose.getX() + mForwardDistance * Math.cos(pose.getTheta())));
                newPose.setY((float) (pose.getY() + mForwardDistance * Math.sin(pose.getTheta())));
                newPose.setTheta(pose.getTheta());
                goToForwardPosition(newPose);
            }
        });
    }

    private void goToForwardPosition(Pose pose) {
        Log.d(mName, "goToForwardPosition pose: " + pose);
        String poseStr = "";
        try {
            JSONObject object = new JSONObject();
            object.put(Definition.JSON_NAVI_POSITION_X, pose.getX());
            object.put(Definition.JSON_NAVI_POSITION_Y, pose.getY());
            object.put(Definition.JSON_NAVI_POSITION_THETA, pose.getTheta());
            poseStr = object.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }

        String velocity = "";
        try {
            JSONObject vcObject = new JSONObject();
            vcObject.put(Definition.JSON_NAVI_LINEAR_SPEED, mLinearSpeed);
            vcObject.put(Definition.JSON_NAVI_ANGULAR_SPEED, mAngularSpeed);
            velocity = vcObject.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }

        startForwardTimer();
        mApi.goPosition(poseStr, velocity, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                if (!isAlive()) {
                    Log.e(mName, "goToForwardPosition onResult has not alive");
                    return;
                }
                Log.d(mName, "goToForwardPosition onResult result: " + result +
                        ", message: " + message);
                switch (result) {
                    case Definition.RESULT_OK:  //Definition.RESULT_OK仅仅表示api执行成功, 并不表示goPosition 是否已经到达目的地.进一步判断message
                        if (Definition.NAVIGATION_OK.equals(message)){
                            processForwardResult(ComponentResult.RESULT_SUCCESS);
                        } else {
                            processForwardResult(ComponentError.ERROR_FORWARD_FAILED);
                        }
                        break;
                    case Definition.RESULT_FAILURE:
                    default:
                        processForwardResult(ComponentError.ERROR_FORWARD_FAILED);
                        break;
                }
            }

            @Override
            public void onStatusUpdate(int status, String data) {
                if (!isAlive()) {
                    Log.e(mName, "goToForwardPosition onStatusUpdate has not alive");
                    return;
                }
                Log.d(mName, "goToForwardPosition status: " + status +
                        ", data: " + data);
                if (data != null) {
                    switch (data) {
                        case Definition.NAVIGATION_OUT_MAP:
                            processForwardResult(ComponentError.ERROR_NAVIGATION_OUT_MAP);
                            break;

                        case Definition.NAVIGATION_GLOBAL_PATH_FAILED:
                            processForwardResult(ComponentError.ERROR_NAVIGATION_GLOBAL_PATH_FAILED);
                            break;

                        default:
                            processForwardStatus(status, data);
                            break;
                    }
                }
            }

            @Override
            public void onError(int errorCode, String errorString) {
                Log.d(mName, "goToForwardPosition onError errorCode: " + errorCode +
                        ", errorString: " + errorString);
                processForwardResult(errorCode);
            }
        });
    }

    private void startForwardTimer() {
        if (mForwardTimer != null) {
            mForwardTimer.cancel();
            mForwardTimer = null;
        }
        mForwardTimer = new Timer();
        mForwardTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                mApi.stopGoPosition();
                processForwardResult(ComponentError.ERROR_FORWARD_TIMEOUT);
            }
        }, mForwardTimeout);
    }

    private void cancelForwardTimer() {
        if (mForwardTimer != null) {
            mForwardTimer.cancel();
            mForwardTimer = null;
        }
    }

    private void processForwardStatus(int status, String data) {
        Log.d(mName, "processForwardStatus status: " + status + ", data: " + data);
        updateStatus(status, data);
    }

    private void processForwardResult(int result) {
        Log.d(mName, "processForwardResult result: " + result);
        cancelForwardTimer();
        stop(STOP_TIMEOUT, result, null);
    }

    @Override
    public void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        mApi.stopGoPosition();
        cancelForwardTimer();
    }
}
