package com.ainirobot.platform.component;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.actionbean.CruiseRouteBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.coreservice.utils.ZipUtils;
import com.ainirobot.platform.bean.PoseItem;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Created by Orion on 2019/5/13.
 */
public class CruiseComponent extends Component {

    private static final int STOP_TIMEOUT = 500;
    private static final Object AVOID_TASK_TAG = new Object();
    private static final Object ABORTED_TASK_TAG = new Object();
    private static final int NAN = -1;

    /**
     * 巡逻点堵死超时时间，超时后走下一个点
     */
    private static final long DEFAULT_POINT_AVOID_TIME_OUT = 5 * 1000;

    /**
     * 默认不停靠巡逻点，不在巡逻点校正位姿
     */
    private static final boolean DEFAULT_NEED_DOCK = false;

    /**
     * 开始巡逻点下标
     */
    private static final int DEFAULT_START_INDEX = 0;

    /**
     * 如果配置使用默认巡逻路线，则不需要传入路线参数
     */
    private static final boolean DEFAULT_USE_DEFAULT_ROUTE = false;

    /**
     * 重定位重试次数
     */
    private static final int DEFAULT_ESTIMATE_RETRY_COUNT = 5;

    /**
     * 默认不检测巡逻点是否删除，直接跳过缺失点
     */
    private static final boolean DEFAULT_CHECK_POINTS_EXIST = false;

    /**
     * 无位移超时时间
     */
    private static final long DEFAULT_ABORTED_TIME_OUT = 20 * 1000;

    /**
     * 多机避障等待默认超时时间，默认300s
     */
    private static final long DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT = 300 * 1000;

    /**
     * 无位移偏移量
     */
    private static final double MIN_DISTANCE = 0.1;

    /**
     * 无位移超时次数
     */
    private int mAbortedTimes;

    /**
     * 当前机器人坐标点
     */
    private AtomicReference<Pose> mCurrentPose = new AtomicReference<>();

    /**
     * 当前巡逻路线，过滤不存在的 Pose 点位
     */
    private List<Pose> mCurrPoseArray;

    /**
     * 停靠点
     */
    private List<Integer> mDockIndexArray;

    private boolean mNeedDock;
    private long mPointAvoidTimeOut;
    private boolean mIsUseDefaultRoute;
    private boolean mNeedCheckPointsExit;
    private int mStartIndex;
    private int mCurrTargetPoseIndex;
    private JSONObject mParams;
    private Gson mGson;
    private GetCruiseRouteComponent mGetCruiseRouteComponent;
    private Status mStatus = Status.IDLE;
    private float mLinearSpeed;
    private float mAngularSpeed;
    private StopCruiseListener mStopCruiseListener;
    private ResetEstimateComponent mResetEstimateComponent;
    private int mRetryCount;
    private Pose mFlagPose;
    /**是否是在多机避障状态，如果是则停止处理机器N秒不动退出导航的逻辑 **/
    private volatile boolean isMultipleWaitStatus = false;
    private long mMultiWaitTimeout;
    /**
     * 圈数
     */
    private int mNumberOfTurns = 0;

    private enum Status {
        /**
         * 空闲
         */
        IDLE,

        /**
         * 准备
         */
        PREPARE,

        /**
         * 巡逻
         */
        CRUISE,

        /**
         * 重定位
         */
        RELOCATION,

        /**
         * 堵死
         */
        AVOID,
    }

    public CruiseComponent(String name) {
        this(name, Looper.myLooper());
    }

    public CruiseComponent(String name, Looper looper) {
        super(name, looper);
        mGson = new Gson();
        mGetCruiseRouteComponent = new GetCruiseRouteComponent(mName, mLooper);
        mResetEstimateComponent = new ResetEstimateComponent(mName, mLooper);
    }

    @Override
    protected void onStart(String params) {
        Log.i(mName, "onStart:" + params);
        try {
            mParams = new JSONObject(params);
            mIsUseDefaultRoute = mParams.optBoolean(ComponentParams.Cruise.PARAM_CRUISE_IS_USE_DEFAULT_ROUTE
                    , DEFAULT_USE_DEFAULT_ROUTE);
            mNeedCheckPointsExit = mParams.optBoolean(ComponentParams.Cruise.PARAM_CRUISE_IS_CHECK_POINTS_EXIT
                    , DEFAULT_CHECK_POINTS_EXIST);
            mNeedDock = mParams.optBoolean(ComponentParams.Cruise.PARAM_CRUISE_IS_NEED_DOCK, DEFAULT_NEED_DOCK);
            mStartIndex = mParams.optInt(ComponentParams.Cruise.PARAM_CRUISE_START_INDEX, DEFAULT_START_INDEX);
            mLinearSpeed = (float) mParams.optDouble(ComponentParams.Cruise.PARAM_CRUISE_LINEAR_SPEED
                    , SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED);
            mLinearSpeed = rectifyLinearSpeed(mLinearSpeed);
            mAngularSpeed = (float) mParams.optDouble(ComponentParams.Cruise.PARAM_CRUISE_ANGULAR_SPEED
                    , SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED);
            mAngularSpeed = rectifyAngularSpeed(mAngularSpeed);
            Log.i(mName, "onStart, Speed : " + mLinearSpeed + ", " + mAngularSpeed);
            mPointAvoidTimeOut = mParams.optLong(ComponentParams.Cruise.PARAM_CRUISE_POINT_AVOID_TIME_OUT
                    , DEFAULT_POINT_AVOID_TIME_OUT);
            mPointAvoidTimeOut = (mPointAvoidTimeOut > DEFAULT_POINT_AVOID_TIME_OUT
                    ? mPointAvoidTimeOut : DEFAULT_POINT_AVOID_TIME_OUT);
            mRetryCount = mParams.optInt(ComponentParams.ResetEstimate.PARAM_RESET_ESTIMATE_COUNT
                    , DEFAULT_ESTIMATE_RETRY_COUNT);
            mMultiWaitTimeout = mParams.optLong(ComponentParams.Cruise.PARAM_CRUISE_MULTI_WAITING_TIMEOUT,
                    DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT);
            mStatus = Status.PREPARE;
            updateMultipleRobotWaitingStatus(false);
            checkRoute();
        } catch (JSONException e) {
            e.printStackTrace();
            processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR, "json parse exception");
        }
    }

    private void checkRoute() {
        if (mIsUseDefaultRoute) {
            Log.i(mName, "checkRoute, get default route");
            startGetRouteComponent();
        } else {
            Log.i(mName, "checkRoute, parse route list param");
            String routeList = mParams.optString(ComponentParams.Cruise.PARAM_CRUISE_ROUTE_LIST);
            parseRoute(routeList);
        }
    }

    private void parseRoute(String route) {
        Log.i(mName, "parse route, route= " + route);
        List<String> routeList = null;
        if (!TextUtils.isEmpty(route)) {
            routeList = mGson.fromJson(route, new TypeToken<List<String>>() {
            }.getType());
        }
        if (routeList == null || routeList.size() <= 1) {
            String message = routeList == null ? "route list invalid" : "points fewer than two";
            processResult(ComponentError.ERROR_PARAMS_CRUISE_ROUTE_INVALID, message);
            return;
        }
        if (mStartIndex > (routeList.size() - 1)) {
            processResult(ComponentError.ERROR_PARAMS_CRUISE_START_INDEX_INVALID
                    , "start index out of bounds");
            return;
        }
        Log.i(mName, "parse route, route list size : " + routeList.size());
        getPointList(routeList);
    }

    /**
     * 获取当前地图下所有地点
     */
    private void getPointList(final List<String> mRouteList) {
        mApi.getPlaceList(new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(mName, "get place list, : " + result + ", " + message);
                if (result < 0 || TextUtils.isEmpty(message)) {
                    processResult(ComponentError.ERROR_GET_PLACE_LIST_FAILED,
                            "get place list failed");
                    return;
                }
                message = ZipUtils.unzipMapData(mGson, message);
                List<PoseItem> allPoseList = mGson.fromJson(message
                        , new TypeToken<List<PoseItem>>() {
                        }.getType());
                if (allPoseList == null || allPoseList.size() <= 0) {
                    processResult(ComponentError.ERROR_GET_PLACE_LIST_EMPTY,
                            "place list empty");
                    return;
                }
                HashMap<String, PoseItem> allPoseItem = new HashMap<>();
                for (PoseItem poseItem : allPoseList) {
                    allPoseItem.put(poseItem.getName(), poseItem);
                }

                //巡逻 pose 点
                mCurrPoseArray = new ArrayList<>();
                for (String point : mRouteList) {
                    if (TextUtils.isEmpty(point) || !allPoseItem.containsKey(point)) {
                        if (mNeedCheckPointsExit) {
                            processResult(ComponentError.ERROR_CRUISE_POINT_NOT_EXIT,
                                    String.format("%s point is not exit", point));
                            return;
                        } else {
                            Log.i(mName, String.format("%s point is not exit", point));
                            continue;
                        }
                    }
                    PoseItem poseItem = allPoseItem.get(point);
                    Pose pose = new Pose();
                    pose.setX(poseItem.x);
                    pose.setY(poseItem.y);
                    pose.setTheta(poseItem.theta);
                    pose.setName(poseItem.name);
                    mCurrPoseArray.add(pose);
                }

                if (mCurrPoseArray.size() <= 1) {
                    processResult(ComponentError.ERROR_PARAMS_CRUISE_ROUTE_INVALID,
                            "available points fewer than two");
                    return;
                }

                mDockIndexArray = new ArrayList<>();
                if (mNeedDock) {
                    for (int i = 0; i < mCurrPoseArray.size(); i++) {
                        mDockIndexArray.add(i);
                    }
                }

                Log.i(mName, "pose array size : " + mCurrPoseArray.size());
                startCruiseAction(mStartIndex);
                registerEventStatusListener();
                mApi.registerStatusListener(Definition.STATUS_POSE, mPoseListener);

            }
        });
    }

    /**
     * 开始巡逻 Action
     *
     * @param index 起始点下标
     */
    private void startCruiseAction(int index) {
        Log.i(mName, "start cruise action : " + index + ", "
                + mCurrPoseArray.get(index) + ", " + mStatus);

        mCurrTargetPoseIndex = index;
        updateMultipleRobotWaitingStatus(false);
        mApi.startCruise(mCurrPoseArray, index, mDockIndexArray, mLinearSpeed, mAngularSpeed,
                mMultiWaitTimeout, new ActionListener() {
            @Override
            public void onResult(int status, String responseString) {
                Log.i(mName, "startCruise onResult : " + status + " || " + responseString);
                switch (status) {
                    case Definition.RESULT_OK:
                        processResult(ComponentResult.RESULT_SUCCESS, "cruise finished");
                        break;
                    case Definition.ACTION_RESPONSE_STOP_SUCCESS:
                        if (null != mStopCruiseListener) {
                            mStopCruiseListener.actionResponseStopSuccess();
                        }
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onStatusUpdate(int status, String data) {
                Log.i(mName, "startCruise onStatusUpdate status:" + status
                        + " data:" + data + " CruiseState:" + mStatus);
                switch (status) {
                    case Definition.STATUS_START_CRUISE:
                        if (mStatus == Status.PREPARE) {
                            processStatus(ComponentStatus.STATUS_START_CRUISE
                                    , "start cruise success");
                        }
                        mStatus = Status.CRUISE;
                        break;
                    case Definition.STATUS_NAVI_OUT_MAP:
                        processResult(ComponentError.ERROR_NAVIGATION_OUT_MAP
                                , "out of map");
                        break;
                    case Definition.STATUS_NAVI_GLOBAL_PATH_FAILED:
                        processResult(ComponentError.ERROR_NAVIGATION_GLOBAL_PATH_FAILED
                                , "global path search failed");
                        break;
                    case Definition.STATUS_CRUISE_REACH_POINT:
                        updateMultipleRobotWaitingStatus(false);
                        processStatus(ComponentStatus.STATUS_CRUISE_REACH_POINT, data);
                        int index = Integer.parseInt(data);
                        mCurrTargetPoseIndex = index + 1;
                        break;
                    case Definition.STATUS_GOAL_OCCLUDED:
                    case Definition.STATUS_NAVI_AVOID:
                        processStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_START
                                , "cruise avoid start");
                        mStatus = Status.AVOID;
                        startAvoidDelayTask();
                        break;
                    case Definition.STATUS_GOAL_OCCLUDED_END:
                    case Definition.STATUS_NAVI_AVOID_END:
                        processStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_END
                                , "cruise avoid end");
                        mStatus = Status.CRUISE;
                        cancelAvoidDelayTask();
                        break;
                    case Definition.STATUS_NAVI_OBSTACLES_AVOID:
                        processStatus(ComponentStatus.STATUS_OBSTACLES_AVOID
                                , "pause to obstacles avoid");
                        break;
                    case Definition.STATUS_START_NAVIGATION:
                        updateMultipleRobotWaitingStatus(false);
                        processStatus(ComponentStatus.STATUS_START_NAVIGATION,
                                "start navigation");
                        startCheckAbortedTask();
                        break;
                    case Definition.STATUS_NAVI_MULTI_ROBOT_WAITING:
                        updateMultipleRobotWaitingStatus(true);
                        break;
                    case Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END:
                        updateMultipleRobotWaitingStatus(false);
                        break;
                    default:
                        processStatus(status, data);
                        break;
                }
            }

            @Override
            public void onError(int errorCode, String errorString) {
                Log.i(mName, "startCruise onError : " + errorCode + " || " + errorString);
                switch (errorCode) {
                    case Definition.ERROR_NOT_ESTIMATE:
                        processResult(ComponentError.ERROR_NOT_ESTIMATE, "not estimate");
                        break;
                    case Definition.ERROR_NAVIGATION_FAILED:
                        processResult(ComponentError.ERROR_CRUISE_GO_POSITION_FAILED
                                , "cruise go position failed");
                        break;
                    case Definition.ACTION_RESPONSE_ALREADY_RUN:
                        processResult(ComponentError.ERROR_REQUEST_RES_BUSY, "already run");
                        break;
                    case Definition.ACTION_RESPONSE_REQUEST_RES_ERROR:
                        processResult(ComponentError.ERROR_REQUEST_RES_FAILED
                                , "request resource error");
                        break;
                    case Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT:
                        processResult(ComponentError.ERROR_MULTI_ROBOT_WAITING_TIMEOUT,
                                "multiple robot waiting timeout");
                        break;
                    default:
                        processResult(errorCode, errorString);
                        break;
                }
            }
        });
    }

    private StatusListener mPoseListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
//            Log.w(mName, "update pose=== " + data);
            Pose pose = mGson.fromJson(data, Pose.class);
            mCurrentPose.set(pose);
        }
    };

    /**
     * 检测 20s 无位移状态
     * 在底盘无堵死状态上报情况下，作为兜底策略
     * 所有点都无法到达则退出巡逻
     */
    private void startCheckAbortedTask() {
        cancelCheckAbortedTask();
        mFlagPose = null;
        DelayTask.submit(ABORTED_TASK_TAG, new Runnable() {
            @Override
            public void run() {
                if (mStatus != Status.CRUISE) {
                    return;
                }
                Pose pose = mCurrentPose.get();
                if (pose != null && isAborted(pose)) {
                    Log.i(mName, "aborted to go next point, times " + mAbortedTimes);
                    if (mAbortedTimes++ >= mCurrPoseArray.size()) {
                        processResult(ComponentError.ERROR_CRUISE_PERSISTENT_IMMOBILITY
                                , "Persistent immobility");
                        return;
                    }
                    goNextPose();
                }
            }
        }, 1000, 1000);
    }

    private boolean isAborted(Pose pose) {
        double distance = pose.getDistance(mFlagPose);
        if (!isMultipleWaitStatus && Double.compare(distance, MIN_DISTANCE) < 0) {
            long movingTime = pose.getTime() - mFlagPose.getTime();
            if (movingTime > DEFAULT_ABORTED_TIME_OUT) {
                return true;
            }
        } else {
            mFlagPose = pose;
        }
        return false;
    }

    /**
     * 更新多机避障状态，如果是多机避障状态，则机器人超时等待策略需忽略
     * @param isWaiting 是否正在多机避障等待状态
     */
    private void updateMultipleRobotWaitingStatus(boolean isWaiting){
        isMultipleWaitStatus = isWaiting;
    }

    private void cancelCheckAbortedTask() {
        DelayTask.cancel(ABORTED_TASK_TAG);
    }

    /**
     * 堵死状态计时
     */
    private void startAvoidDelayTask() {
        DelayTask.submit(AVOID_TASK_TAG, new Runnable() {
            @Override
            public void run() {
                Log.i(mName, "avoid delay task run : " + mStatus);
                if (mStatus == Status.AVOID) {
                    goNextPose();
                }
            }
        }, mPointAvoidTimeOut);
    }

    private void goNextPose() {
        stopCruiseAsynchronous(new StopCruiseListener() {
            @Override
            void actionResponseStopSuccess() {
                super.actionResponseStopSuccess();
                processStatus(ComponentStatus.STATUS_CRUISE_AVOID_GO_NEXT_POINT,
                        "go next point after avoid time out");
                goNextCruisePose();
            }
        });
    }

    private void goNextCruisePose() {
        Log.i(mName, "avoid to go next pose, mCurrTargetPoseIndex= " + mCurrTargetPoseIndex);
        int nextIndex = getNextPoint(mCurrTargetPoseIndex);
        if (nextIndex == NAN) {
            Log.i(mName, "cruise finished");
            processResult(ComponentResult.RESULT_SUCCESS, "cruise finished");
            return;
        }

        Log.i(mName, "avoid to go next pose, nextIndex= " + nextIndex);
        processStatus(ComponentStatus.STATUS_CRUISE_AVOID_POINT, String.valueOf(mCurrTargetPoseIndex));
        startCruiseAction(nextIndex);
    }

    private int getNextPoint(int index) {
        if(mCurrPoseArray == null){
            return NAN;
        }
        int size = mCurrPoseArray.size();
        if (++index >= size) {
            return NAN;
        }
        return index;
    }

    private void cancelAvoidDelayTask() {
        Log.i(mName, "cancel avoid delay task : " + mStatus);
        DelayTask.cancel(AVOID_TASK_TAG);
    }

    /**
     * 获取默认路线
     */
    private void startGetRouteComponent() {
        mGetCruiseRouteComponent.setComponentListener(new ComponentListener() {
            @Override
            public void onFinish(int result, String message, String extraData) {
                Log.i(mName, "onFinish : " + result + ", " + message);
                switch (result) {
                    case ComponentResult.RESULT_SUCCESS:
                        CruiseRouteBean route = mGson.fromJson(message, CruiseRouteBean.class);
                        parseRoute(route.getCoordinate());
                        break;

                    case ComponentError.ERROR_NAVIGATION_NO_MAP:
                    case ComponentError.ERROR_NO_CRUISE_ROUTE:
                    default:
                        processResult(result, message);
                        break;
                }
            }
        });
        mGetCruiseRouteComponent.start(null);
    }

    private void stopGetRouteComponent() {
        mGetCruiseRouteComponent.setComponentListener(null);
        mGetCruiseRouteComponent.stop(STOP_TIMEOUT);
    }

    /**
     * 注册事件监听，处理定位丢失状态
     */
    private void registerEventStatusListener() {
        mApi.registerStatusListener(Definition.STATUS_EVENT, mStatusListener);
    }

    StatusListener mStatusListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
            if (!isAlive()) {
                return;
            }
            try {
                JSONObject json = new JSONObject(data);
                String estimateLost = json.optString(Definition.HW_NAVI_ESTIMATE_LOST);
                if (!TextUtils.isEmpty(estimateLost)) {
                    handleEstimateLost(type, data);
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    };

    private void handleEstimateLost(String type, final String data) {
        Log.i(mName, "handleEstimateLost, mStatus= " + mStatus);
        if (mStatus == Status.CRUISE) {
            mStatus = Status.RELOCATION;
            stopCruiseAsynchronous(new StopCruiseListener() {
                @Override
                void actionResponseStopSuccess() {
                    super.actionResponseStopSuccess();
                    if (mStatus == Status.RELOCATION) {
                        startResetEstimate();
                    }
                }
            });
        }
    }

    /**
     * 开始重定位
     */
    private void startResetEstimate() {
        Log.i(mName, "startResetEstimate");
        try {
            mResetEstimateComponent.setComponentListener(mResetEstimateComponentListener);
            mResetEstimateComponent.setStatusListener(mResetEstimateComponentStatus);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(ComponentParams.ResetEstimate.PARAM_RESET_ESTIMATE_COUNT, mRetryCount);
            mResetEstimateComponent.start(jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void stopResetEstimate() {
        Log.i(mName, "stopResetEstimate");
        if (mResetEstimateComponent != null) {
            mResetEstimateComponent.setComponentListener(null);
            mResetEstimateComponent.setStatusListener(null);
            mResetEstimateComponent.stop(100);
        }
    }

    private ComponentListener mResetEstimateComponentListener = new ComponentListener() {
        @Override
        public void onFinish(int result, String message, String extraData) {
            Log.i(mName, "estimate onFinish : " + result + ", " + message + ", " + mStatus);
            switch (result) {
                case ComponentResult.RESULT_SUCCESS:
                    if (mStatus == Status.RELOCATION) {
                        startCruiseAction(mCurrTargetPoseIndex);
                        processStatus(ComponentStatus.STATUS_NAVIGATION_RESET_ESTIMATE_SUCCESS
                                , "reset estimate success");
                    }
                    break;
                case ComponentResult.RESULT_RESET_ESTIMATE_FAIL:
                    if (mStatus == Status.RELOCATION) {
                        processResult(ComponentError.ERROR_NAVIGATION_RESET_ESTIMATE_FAIL
                                , "reset estimate failed");
                    }
                    break;
                default:
                    processStatus(result, message);
                    break;
            }
        }
    };

    private ComponentStatusListener mResetEstimateComponentStatus = new ComponentStatusListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            Log.i(mName, "estimate onStatusUpdate status: " + status + ", data: " + data);
            processStatus(status, data);
        }
    };

    @Override
    protected void onUpdate(String intent, String params) {
        Log.i(mName, "onUpdate");
    }

    @Override
    protected void onStop(int reason) {
        Log.i(mName, "onStop, status= " + mStatus + ", mNumberOfTurns= " + mNumberOfTurns
                + ", reason: " + reason);
        updateMultipleRobotWaitingStatus(false);
        mStatus = Status.IDLE;
        mStopCruiseListener = null;
        mNumberOfTurns = 0;
        mAbortedTimes = 0;
        cancelAvoidDelayTask();
        cancelCheckAbortedTask();
        DelayTask.cancel(this);
        stopResetEstimate();
        stopGetRouteComponent();
        mApi.unregisterStatusListener(mStatusListener);
        mApi.unregisterStatusListener(mPoseListener);
        mApi.stopCruise();
        mApi.resetHead(null);
    }

    private void processStatus(int status, final String data) {
        if (!isAlive()) {
            return;
        }
        updateStatus(status, data);
    }

    private void processResult(int result, String message) {
        if (!isAlive()) {
            return;
        }
        stop(STOP_TIMEOUT, result, message);
    }

    private void stopCruiseAsynchronous(StopCruiseListener listener) {
        mStopCruiseListener = listener;
        mApi.stopCruise();
    }

    private abstract class StopCruiseListener {
        void actionResponseStopSuccess() {
            mStopCruiseListener = null;
        }
    }

}
