package com.ainirobot.platform.component;/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

import android.os.Looper;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.RobotStandbyBean;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.platform.speech.SpeechManager;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;

import org.json.JSONException;
import org.json.JSONObject;

public class RobotStandbyComponent extends Component {

    private static final long STOP_TIMEOUT = 300;

    private boolean mIsNeedCloseHdmi;

    public RobotStandbyComponent(String name) {
        this(name, Looper.myLooper());
    }

    public RobotStandbyComponent(String name, Looper looper) {
        super(name, looper);
    }

    @Override
    protected void onStart(String params) {
        Log.d(mName, "onStart params: " + params);

        try {
            JSONObject json = new JSONObject(params);
            mIsNeedCloseHdmi = json.optBoolean(ComponentParams.RobotStandby.PARAM_IS_NEED_CLOSE_HDMI, false);
        } catch (JSONException e) {
            mIsNeedCloseHdmi = false;
        }
        startStandby();
    }

    private void startStandby() {
        SpeechManager.getInstance().closeSpeechAsrRecognize();
        SpeechManager.getInstance().closeRobotRecognize();
        RobotStandbyBean bean = new RobotStandbyBean();
        bean.setDisconnectHdmi(mIsNeedCloseHdmi);
        mApi.robotStandby(bean, new ActionListener() {
            @Override
            public void onResult(int status, String responseString) {
                if (!isAlive()) {
                    return;
                }
                if (Definition.RESULT_STANDBY_END_SUCCESS == status) {
                    processResult(ComponentResult.RESULT_STANDBY_END_SUCCESS, responseString);
                } else {
                    processResult(status, responseString);
                }
            }

            @Override
            public void onError(int errorCode, String errorString) {
                processResult(errorCode, errorString);
            }

            @Override
            public void onStatusUpdate(int status, String data) {
                if (Definition.STATUS_STANDBY_SUCCESS == status) {
                    processStatus(ComponentStatus.STATUS_STANDBY_SUCCESS);
                } else {
                    processStatus(status);
                }
            }
        });
    }

    @Override
    protected void onUpdate(String intent, String params) {

    }

    private void processStatus(int status) {
        if (!isAlive()) {
            return;
        }
        updateStatus(status, "");
    }

    private void processResult(int result, String message) {
        if (!isAlive()) {
            return;
        }
        stop(STOP_TIMEOUT, result, message);
    }

    @Override
    protected void onStop(int reason) {
        Log.d(mName, "onStop reason: " + reason);
        mApi.robotStandbyEnd();
        SpeechManager.getInstance().openSpeechAsrRecognize();
    }
}
