/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.platform.component;

import android.os.Looper;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.platform.component.definition.ComponentError;
import com.ainirobot.platform.component.definition.ComponentParams;
import com.ainirobot.platform.component.definition.ComponentResult;
import com.ainirobot.platform.component.definition.ComponentStatus;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class NavigationTransferComponent extends Component {

    private final ResetEstimateComponent mResetEstimateComponent;
    /**
     * 目的地
     */
    private String mDestination;
    /**
     * 备用目的地列表
     */
    private List<String> mStandbyDesList;
    /**
     * 导航组件需要透传的参数信息
     */
    private JSONObject mNavigationParams;
    /**
     * 导航时缓存队列里面去最后一个点位执行的最大避障超时次数，每次5s
     */
    private int mLastAvoidMaxCnt = 0;
    /**
     * 在有多个备用点可以进行导航时执行的最大避障次数
     */
    private int mAvoidMaxCnt = DEFAULT_MAX_AVOID_COUNT;
    private static final int DEFAULT_MAX_AVOID_COUNT = 5;
    private static final long STOP_TIMEOUT = 500;
    private static final int DEFAULT_ESTIMATE_RETRY_COUNT = 5;
    /**
     * 补位动态调度策略:0 Lora优先级星形补位, 1 点位优先级星形补位, 2 点位优先级火车补位
     */
    private int mQueuingStrategy = 0;
    /**
     * 回程调度策略:0 按Lora优先级计算点位, 1 默认去最后的点位（只在火车调度生效）, 2 动态计算点位
     */
    private int mBackStrategy = 0;
    /**
     * 导航策略
     */
    private Definition.AdvNaviStrategy mAdvNaviStrategy = Definition.AdvNaviStrategy.DEFAULT;

    /**
     * 重定位重试次数
     */
    private int mRetryCount;

    /**
     * 是否自动重定位
     */
    private boolean mIsAutoEstimate;

    private static final int BASE_NAVIGATION_ERROR = -20010000;
    /**
     * Lora配置异常，此状态在NavigationService内定义
     */
    private static final int NAVI_ERROR_MULTIPLE_LORA_CONFIG_FAIL = BASE_NAVIGATION_ERROR - 1;
    /**
     * 多机地图不匹配，此状态在NavigationService内定义
     */
    private static final int NAVI_ERROR_MULTIPLE_MAP_NOT_MATCH = BASE_NAVIGATION_ERROR - 2;
    /**
     * 多机Lora断连，此状态在NavigationService内定义
     */
    private static final int NAVI_ERROR_MULTIPLE_LORA_DISCONNECT = BASE_NAVIGATION_ERROR - 3;

    private static final int NAVI_ERROR_MULTIPLE_VERSION_NOT_MATCH = BASE_NAVIGATION_ERROR - 4;

    public NavigationTransferComponent(String name) {
        this(name, Looper.myLooper());
    }

    public NavigationTransferComponent(String name, Looper looper) {
        super(name, looper);
        mResetEstimateComponent = new ResetEstimateComponent(mName, mLooper);
    }

    @Override
    public void onStart(String params) {
        Log.i(mName, "onStart : " + params);
        try {
            JSONObject mParams = new JSONObject(params);
            mDestination = mParams.optString(ComponentParams.NavigationTransfer.PARAM_MAIN_DESTINATION);
            JSONArray standbyList = mParams.optJSONArray(ComponentParams.NavigationTransfer.PARAM_STANDBY_DESTINATION_LIST);
            mStandbyDesList = new ArrayList<String>();
            if (standbyList != null) {
                for (int i = 0; i < standbyList.length(); i++) {
                    String standbyData = standbyList.getString(i);
                    mStandbyDesList.add(standbyData);
                }
            }
            mQueuingStrategy = mParams.optInt(ComponentParams.NavigationCheck.PARAM_DYNAMIC, 0);
            mBackStrategy = mParams.optInt(ComponentParams.NavigationCheck.PARAM_DYNAMIC_BACK, 0);
            mNavigationParams = mParams.optJSONObject(ComponentParams.NavigationTransfer.PARAM_MAIN_NAVGATION_PARAMS);
            getNavigationParams();

            //去备用点时需要传入的导航参数
            JSONObject standbyNavParams = mParams.optJSONObject(ComponentParams.NavigationTransfer.PARAM_STANDBY_NAVGATION_PARAMS);

            mLastAvoidMaxCnt = mParams.optInt(ComponentParams.NavigationTransfer.PARAM_LAST_NAVIGATION_AVOID_MAX_COUNT, 0);
            if (mLastAvoidMaxCnt <= 0) {
                mLastAvoidMaxCnt = DEFAULT_MAX_AVOID_COUNT;
            }
            if (standbyNavParams != null) {
                mAvoidMaxCnt = standbyNavParams.optInt(ComponentParams.Navigation.PARAM_MAX_AVOID_COUNT, DEFAULT_MAX_AVOID_COUNT);
            }
            if (TextUtils.isEmpty(mDestination)) {
                processResult(ComponentError.ERROR_PARAMS_PLACE_NAME_INVALID);
            } else {
                prepareMultiRobotNavi();
            }
        } catch (Exception e) {
            e.printStackTrace();
            processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR);
        }
    }

    @Override
    protected void onUpdate(String intent, String params) {

    }

    @Override
    protected void onStop(int reason) {
        mApi.stopMultiRobotNavigation();
        stopResetEstimate();
    }

    private void prepareMultiRobotNavi(){
        mAdvNaviStrategy = getNaviStrategy();
        Log.i(mName, "getNaviStrategy: " + mAdvNaviStrategy.name());
        startMultiRobotNavi(mAdvNaviStrategy);
    }

    private void startMultiRobotNavi(Definition.AdvNaviStrategy naviStrategy) {
        initNaviParams();
        mApi.startMultiRobotNavigation(
                mDestination,
                naviStrategy,
                mStandbyDesList,
                mNavigationParams.toString(),
                new ActionListener() {
                    @Override
                    public void onResult(int status, String message, String extraData) throws RemoteException {
                        Log.i(mName, "onResult: " + status + " responseString：" + message + " extraData：" + extraData);
                        handlerResult(status, message, extraData);
                    }

                    @Override
                    public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                        Log.e(mName, "onError: " + errorCode + " errorString：" + errorString + " extraData：" + extraData);
                        handlerError(errorCode, errorString, extraData);
                    }

                    @Override
                    public void onStatusUpdate(int status, String data, String extraData) throws RemoteException {
                        Log.i(mName, "onStatusUpdate: " + status + " data：" + data + " extraData：" + extraData);
                        handlerStatusUpdate(status, data, extraData);
                    }
                });
    }

    private void initNaviParams() {
        try {
            mNavigationParams.put(ComponentParams.NavigationTransfer.PARAM_LAST_NAVIGATION_AVOID_MAX_COUNT,
                    mLastAvoidMaxCnt);//最后一个点避障次数
            mNavigationParams.put(ComponentParams.Navigation.PARAM_MAX_AVOID_COUNT, mAvoidMaxCnt);

            //去最后点位且补位策略为火车补位时，到达补位区不需要回正方向
            if (isLastPointAndTrainStrategy()) {
                //该任务会执行两次导航，当导航策略为去最后点位时，不需要回正方向
                mNavigationParams.put(ComponentParams.Navigation.PARAM_IS_ADJUST_ANGLE,
                        mAdvNaviStrategy == Definition.AdvNaviStrategy.TO_LAST_POINT);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void getNavigationParams() {
        try {
            mRetryCount = mNavigationParams.optInt(ComponentParams.ResetEstimate.PARAM_RESET_ESTIMATE_COUNT
                    , DEFAULT_ESTIMATE_RETRY_COUNT);
            mIsAutoEstimate = mNavigationParams.optBoolean(ComponentParams.Navigation.PARAM_AUTO_RESET_ESTIMATE,
                    true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据业务区分导航策略
     */
    private Definition.AdvNaviStrategy getNaviStrategy() {
        switch (mBackStrategy) {
            //根据lora优先级导航
            case 0:
                return Definition.AdvNaviStrategy.LORA_PRIORITY;
            //去补位区最后点位
            case 1:
                //只在补位使用火车补位策略时，才生效
                if (mQueuingStrategy == 2) {
                    return Definition.AdvNaviStrategy.TO_LAST_POINT;
                }
                //否则继续使用lora优先级策略
                return Definition.AdvNaviStrategy.LORA_PRIORITY;
            //动态调度
            case 2:
                return Definition.AdvNaviStrategy.DYNAMIC_POINT;
        }
        return Definition.AdvNaviStrategy.DEFAULT;
    }

    private boolean isLastPointAndTrainStrategy() {
        return mBackStrategy == 1 && mQueuingStrategy == 2;
    }

    private void handlerStatusUpdate(int status, String data, String extraData) {
        switch (status) {
            //第一次导航开始
            case Definition.STATUS_START_NAVIGATION:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_TRANSFER_START_NAVIGATION, data, extraData);
                break;
            //导航已开始，替换目的地
            case Definition.STATUS_NAVI_REPLACE_DESTINATION:
            case Definition.STATUS_NAVI_ARRIVED_REPLACE_DESTINATION:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_TRANSFER_REPLACE_DESTINATION, data, extraData);
                break;
            case Definition.STATUS_DISTANCE_WITH_DESTINATION:
                updateStatus(ComponentStatus.STATUS_DISTANCE_WITH_DESTINATION, data, extraData);
                break;
            case Definition.STATUS_NAVI_AVOID_IMMEDIATELY:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_IMMEDIATELY, data, extraData);
                break;
            default:
                navigationStatusUpdate(status, data, extraData);
                break;
        }

    }

    private void handlerError(int errorCode, String errorString, String extraData) {
        switch (errorCode) {
            case Definition.ERROR_LISTENER_INVALID:
                processResult(ComponentError.ERROR_QUERY_POSE_LIST_FAILED);
                break;
            case Definition.ERROR_TARGET_NOT_FOUND:
                processResult(ComponentError.ERROR_DESTINATION_LIST_NOT_EXIST);
                break;
            //导航事件已结束。业务自己处理定位丢失问题，决定是否重定位、重新导航
            case Definition.ERROR_ESTIMATE_ERROR:
                startResetEstimate(errorString, extraData);
                break;
            case Definition.ERROR_PARAMS_JSON_PARSER_ERROR:
                processResult(ComponentError.ERROR_PARAMS_JSON_PARSER_ERROR, errorString, extraData);
                break;
            case Definition.ERROR_NO_AVAILABLE_DESTINATION:
                processResult(ComponentError.ERROR_NO_AVAILABLE_DESTINATION, errorString, extraData);
                break;
            default:
                navigationResult(errorCode, errorString, extraData);
                break;

        }

    }

    private void handlerResult(int status, String message, String extraData) {
        switch (status) {
            //导航到达
            case Definition.RESULT_OK:
            case Definition.RESULT_NAVIGATION_ARRIVED:
                processArrivedStrategy(message, extraData);
                break;
            //地点可用
            case Definition.RESULT_DESTINATION_AVAILABLE:
                processResult(ComponentResult.RESULT_NAVIGATION_DESTINATION_AVAILABLE, message, extraData);
                break;
            //到达点位附近
            case Definition.RESULT_DESTINATION_IN_RANGE:
                processResult(ComponentResult.RESULT_NAVIGATION_IN_DESTINATION_RANGE, message, extraData);
                break;
            default:
                navigationResult(status, message, extraData);
                break;
        }
    }

    private void processArrivedStrategy(String message, String extraData) {
        //去最后一个点，且补位模式为火车补位的任务
        //且刚刚导航策略是去最后一个点策略，则需要再次执行一次动态火车补位策略
        if (isLastPointAndTrainStrategy()
                && mAdvNaviStrategy == Definition.AdvNaviStrategy.TO_LAST_POINT) {
            Log.i(mName, "processArrivedStrategy 3-2 mode next: DYNAMIC_TRAIN_BY_POINT");
            mAdvNaviStrategy = Definition.AdvNaviStrategy.DYNAMIC_TRAIN_BY_POINT;
            startMultiRobotNavi(mAdvNaviStrategy);
            return;
        }

        //结束任务
        Log.i(mName, "processArrivedStrategy arrived finish");
        processResult(ComponentResult.RESULT_NAVIGATION_TRANSFER_ARRIVED, message, extraData);
    }

    private void startResetEstimate(String data, String extraData) {
        Log.i(mName, "startResetEstimate " + mIsAutoEstimate);
        if (!mIsAutoEstimate) {
            updateStatus(ComponentStatus.STATUS_ESTIMATE_LOST, data, extraData);
            return;
        }

        try {
            mResetEstimateComponent.setComponentListener(mResetEstimateComponentListener);
            mResetEstimateComponent.setStatusListener(mResetEstimateComponentStatus);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(ComponentParams.ResetEstimate.PARAM_RESET_ESTIMATE_COUNT, mRetryCount);
            mResetEstimateComponent.start(jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void stopResetEstimate() {
        Log.i(mName, "stopResetEstimate");
        mResetEstimateComponent.setComponentListener(null);
        mResetEstimateComponent.setStatusListener(null);
        mResetEstimateComponent.stop(100);
    }

    private final ComponentListener mResetEstimateComponentListener = new ComponentListener() {
        @Override
        public void onFinish(int result, String message, String extraData) {
            Log.i(mName, "estimate onFinish result: " + result + ", message: " + message);
            switch (result) {
                case ComponentResult.RESULT_SUCCESS:
                    if (isAlive()) {
                        startMultiRobotNavi(mAdvNaviStrategy);
                        updateStatus(ComponentStatus.STATUS_NAVIGATION_RESET_ESTIMATE_SUCCESS
                                , "navigation reset estimate success", extraData);
                    }
                    break;
                case ComponentResult.RESULT_RESET_ESTIMATE_FAIL:
                    processResult(ComponentError.ERROR_NAVIGATION_RESET_ESTIMATE_FAIL, "", extraData);
                    break;
                default:
                    updateStatus(result, message, extraData);
                    break;
            }
        }
    };

    private final ComponentStatusListener mResetEstimateComponentStatus = new ComponentStatusListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            Log.i(mName, "estimate onStatusUpdate status: " + status + ", data: " + data);
            updateStatus(status, data, extraData);
        }
    };

    private void simulateCreateNavigationErrorEvent(int errorCode, String desMsg) {
        Log.d(mName, "simulateCreateNavigationErrorEvent:" + errorCode);
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("code", errorCode);
            jsonObject.put("message", desMsg);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        processResult(ComponentError.ERROR_MULTIPLE_MODE_ERROR, "", jsonObject.toString());
    }

    private void navigationResult(int status, String message, String extraData) {
        switch (status) {
            //----------  RESULT  ----------//
            case Definition.ACTION_RESPONSE_STOP_SUCCESS:
                processResult(status, message + " 停止成功", extraData);
                break;
            case Definition.RESULT_FAILURE:
                processResult(status, message + " 导航失败", extraData);
                break;
            case Definition.STATUS_NAVI_OUT_MAP:
                processResult(ComponentError.ERROR_NAVIGATION_OUT_MAP, message, extraData);
                break;
            case Definition.STATUS_NAVI_GLOBAL_PATH_FAILED:
                processResult(ComponentError.ERROR_NAVIGATION_GLOBAL_PATH_FAILED, message, extraData);
                break;
            case Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH:
                simulateCreateNavigationErrorEvent(NAVI_ERROR_MULTIPLE_MAP_NOT_MATCH,
                        "Multiple map not match!");
                break;
            case Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT:
                simulateCreateNavigationErrorEvent(NAVI_ERROR_MULTIPLE_LORA_DISCONNECT,
                        "Lora disconnect!");
                break;
            case Definition.STATUS_NAVI_MULTI_VERSION_NOT_MATCH:
                simulateCreateNavigationErrorEvent(NAVI_ERROR_MULTIPLE_VERSION_NOT_MATCH,
                        "Multiple version not match");
                break;
            case Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL:
                simulateCreateNavigationErrorEvent(NAVI_ERROR_MULTIPLE_LORA_CONFIG_FAIL,
                        "Multiple version not match");
                break;
            case Definition.STATUS_NAVI_WHEEL_SLIP:
                processResult(ComponentError.ERROR_NAVI_WHEEL_SLIP, message, extraData);
                break;

            //----------  ERROR  ----------//
            case Definition.ERROR_NOT_ESTIMATE:
                processResult(ComponentError.ERROR_NOT_ESTIMATE,
                        "navigation not estimate", extraData);
                break;
            case Definition.ERROR_IN_DESTINATION:
                processResult(ComponentError.ERROR_NAVIGATION_ALREADY_IN_DESTINATION,
                        "already in destination", extraData);
                break;
            case Definition.ERROR_DESTINATION_NOT_EXIST:
                processResult(ComponentError.ERROR_DESTINATION_NOT_EXIST,
                        "destination not exist", extraData);
                break;
            case Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE:
                processResult(ComponentError.ERROR_DESTINATION_CAN_NOT_ARRIVE,
                        "navigation moving time out", extraData);
                break;
            case Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT:
                processResult(ComponentError.ERROR_MULTI_ROBOT_WAITING_TIMEOUT);
                break;
            case Definition.ERROR_NAVIGATION_AVOID_TIMEOUT:
                processResult(ComponentError.ERROR_NAVIGATION_AVOID_TIMEOUT,
                        "navigation avoid time out", "");
            case Definition.ACTION_RESPONSE_ALREADY_RUN:
                processResult(ComponentError.ERROR_REQUEST_RES_BUSY);
                break;
            case Definition.ACTION_RESPONSE_REQUEST_RES_ERROR:
                processResult(ComponentError.ERROR_REQUEST_RES_FAILED);
                break;
            case Definition.ERROR_WHEEL_OVER_CURRENT_RUN_OUT:
                processResult(ComponentError.ERROR_WHEEL_OVER_CURRENT_RUN_OUT,
                        "wheel over current retry count run out", extraData);
                break;
            case Definition.ACTION_RESPONSE_RES_UNAVAILBALE:
                processResult(ComponentError.ERROR_OPEN_RADAR_FAILED,
                        "res unavailable: " + message, extraData);
                break;
            default:
                processResult(status, message, extraData);
                break;
        }
    }


    private void navigationStatusUpdate(int status, String data, String extraData) {
        switch (status) {
            case Definition.STATUS_GOAL_OCCLUDED:
            case Definition.STATUS_NAVI_AVOID:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_START,
                        "navigation avoid start", extraData);
                break;
            case Definition.STATUS_GOAL_OCCLUDED_END:
            case Definition.STATUS_NAVI_AVOID_END:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_AVOID_END,
                        "navigation avoid end", extraData);
                break;
            case Definition.STATUS_NAVI_OBSTACLES_AVOID:
                updateStatus(ComponentStatus.STATUS_OBSTACLES_AVOID,
                        "pause to obstacles avoid", extraData);
                break;
            case Definition.STATUS_NAVI_MULTI_ROBOT_WAITING:
                updateStatus(ComponentStatus.STATUS_NAVI_MULTI_ROBOT_WAITING,
                        "navigation multi robot waiting");
                break;
            case Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END:
                updateStatus(ComponentStatus.STATUS_NAVI_MULTI_ROBOT_WAITING_END,
                        "navigation multi robot waiting end");
                break;
            case Definition.STATUS_NAVI_GO_STRAIGHT:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_GO_STRAIGHT, data,
                        extraData);
                break;
            case Definition.STATUS_NAVI_TURN_LEFT:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_TURN_LEFT, data,
                        extraData);
                break;
            case Definition.STATUS_NAVI_TURN_RIGHT:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_TURN_RIGHT, data,
                        extraData);
                break;
            case Definition.STATUS_NAVI_SET_PRIORITY_FAILED:
                updateStatus(ComponentStatus.STATUS_NAVIGATION_SET_PRIORITY_FAILED, data,
                        extraData);
                break;
            default:
                updateStatus(status, data, extraData);
                break;
        }
    }

    private void processResult(int result) {
        processResult(result, "", "");
    }

    private void processResult(int result, String message, String extraData) {
        if (!isAlive()) {
            return;
        }
        stop(STOP_TIMEOUT, result, message, extraData);
    }
}
