package com.ainirobot.platform.speech.decorator.impl;

import android.util.Log;

import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.platform.speech.decorator.BaseSpeechDecorator;
import com.ainirobot.platform.speech.decorator.IComponentPlayText;

public class StateDecorator extends BaseSpeechDecorator {

    public enum TalkState {
        IDLE, TALKING
    }

    private static final String TAG = "StateDecorator";
    private TalkState mTalkState = TalkState.IDLE;

    private IComponentPlayText iComponentPlayText;

    @Override
    public void setIComponentPlayText(IComponentPlayText componentPlayText) {
        iComponentPlayText = componentPlayText;
    }

    @Override
    public void speechPlayText(String text, final TextListener listener) {
        iComponentPlayText.speechPlayText(text, new TextListener() {
            @Override
            public void onStart() {
                Log.d(TAG, "speechPlayText onStart()");
                mTalkState = TalkState.TALKING;
                listener.onStart();
            }

            @Override
            public void onStop() {
                Log.d(TAG, "speechPlayText onStop()");
                mTalkState = TalkState.IDLE;
                listener.onStop();
            }

            @Override
            public void onError() {
                Log.d(TAG, "speechPlayText onError()");
                mTalkState = TalkState.IDLE;
                listener.onError();
            }

            @Override
            public void onComplete() {
                Log.d(TAG, "speechPlayText onComplete()");
                mTalkState = TalkState.IDLE;
                listener.onComplete();
            }
        });
    }

    public boolean isTalking() {
        return mTalkState == TalkState.TALKING;
    }
}
