package com.ainirobot.platform.speech.decorator.impl;

import android.util.Log;

import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.platform.speech.SpeechApiCost;
import com.ainirobot.platform.speech.SpeechRegister;
import com.ainirobot.platform.speech.decorator.BaseSpeechDecorator;
import com.ainirobot.platform.speech.decorator.IComponentPlayText;

public class CallBackDecorator extends BaseSpeechDecorator {

    private static final String TAG = "CallBackDecorator";

    private IComponentPlayText iComponentPlayText;

    @Override
    public void setIComponentPlayText(IComponentPlayText componentPlayText) {
        iComponentPlayText = componentPlayText;
    }

    @Override
    public void speechPlayText(String text,final TextListener listener) {
        iComponentPlayText.speechPlayText(text, new TextListener() {
            @Override
            public void onStart() {
                Log.d(TAG, "speechPlayText onStart()");
                SpeechRegister.getInstance().handleListeners(SpeechApiCost.MSG_SPEECH_START, 0, null);
                listener.onStart();
            }

            @Override
            public void onStop() {
                Log.d(TAG, "speechPlayText onStop()");
                SpeechRegister.getInstance().handleListeners(SpeechApiCost.MSG_SPEECH_STOP, 0, null);
                listener.onStop();
            }

            @Override
            public void onError() {
                Log.d(TAG, "speechPlayText onError()");
                listener.onError();
            }

            @Override
            public void onComplete() {
                Log.d(TAG, "speechPlayText onComplete()");
                SpeechRegister.getInstance().handleListeners(SpeechApiCost.MSG_SPEECH_STOP, 1, null);
                SpeechRegister.getInstance().handleListeners(SpeechApiCost.MSG_TTS_SPEECH_COMPLETE, 1, null);
                listener.onComplete();
            }
        });

    }
}
