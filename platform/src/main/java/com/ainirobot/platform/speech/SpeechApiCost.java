package com.ainirobot.platform.speech;


public class SpeechApiCost {

    private static final int MSG_BASE = 0;
    public static final int MSG_PAR_RESULT = MSG_BASE + 1;
    public static final int MSG_RECOGNITION_START = MSG_BASE + 2;
    public static final int MSG_RECOGNITION_STOP = MSG_BASE + 3;
    public static final int MSG_VOLUME_CHANGE = MSG_BASE + 4;
    public static final int MSG_QUERY_ENDED = MSG_BASE + 5;
    public static final int MSG_QUERY_ASR_RESULT = MSG_BASE + 6;

    public static final int MSG_SPEECH_START = MSG_BASE + 7;
    public static final int MSG_SPEECH_STOP = MSG_BASE + 8;
    public static final int MSG_TTS_SPEECH_COMPLETE = MSG_BASE + 9;

    public static final int MSG_RECOGNITION_ERROR = MSG_BASE + 10;
    public static final int MSG_SPEECH_STREAM_DATA = MSG_BASE + 11; // 语音NLP长数据的流式逐包结果

}
