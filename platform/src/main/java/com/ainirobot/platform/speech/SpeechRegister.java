package com.ainirobot.platform.speech;


import android.util.Log;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

public class SpeechRegister {

    private static final String TAG = "SpeechRegister";
    private static Map<Integer, List> mRegisterMap = new HashMap<>();
    private static SpeechRegister mInstance;
    private ArrayList<VolumeChangeListener> mVolumeChangeListener;
    private ArrayList<SpeechRecognitionListener> mRecognitionListeners;
    private CopyOnWriteArrayList<SpeechStatusListener> mCompleteListeners;
    private CopyOnWriteArrayList<TTSSpeechStatueListener> mTTSSpeechStatueListeners;
    private CopyOnWriteArrayList<SpeechStreamListener> mSpeechStreamListeners;

    public static SpeechRegister getInstance() {
        if (mInstance == null) {
            synchronized (SpeechRegister.class) {
                if (mInstance == null) {
                    mInstance = new SpeechRegister();
                }
            }
        }
        return mInstance;
    }

    private SpeechRegister() {

        mCompleteListeners = new CopyOnWriteArrayList<>();
        mTTSSpeechStatueListeners = new CopyOnWriteArrayList<>();
        mSpeechStreamListeners = new CopyOnWriteArrayList<>();
        mRecognitionListeners = new ArrayList<>();
        mVolumeChangeListener = new ArrayList<>();

        mRegisterMap.put(SpeechApiCost.MSG_VOLUME_CHANGE, mVolumeChangeListener);

        mRegisterMap.put(SpeechApiCost.MSG_RECOGNITION_START, mRecognitionListeners);
        mRegisterMap.put(SpeechApiCost.MSG_RECOGNITION_STOP, mRecognitionListeners);

        mRegisterMap.put(SpeechApiCost.MSG_PAR_RESULT, mCompleteListeners);
        mRegisterMap.put(SpeechApiCost.MSG_QUERY_ENDED, mCompleteListeners);
        mRegisterMap.put(SpeechApiCost.MSG_QUERY_ASR_RESULT, mCompleteListeners);

        mRegisterMap.put(SpeechApiCost.MSG_SPEECH_START, mTTSSpeechStatueListeners);
        mRegisterMap.put(SpeechApiCost.MSG_SPEECH_STOP, mTTSSpeechStatueListeners);
        mRegisterMap.put(SpeechApiCost.MSG_TTS_SPEECH_COMPLETE, mTTSSpeechStatueListeners);

        mRegisterMap.put(SpeechApiCost.MSG_SPEECH_STREAM_DATA, mSpeechStreamListeners);

    }

    private List getRegisterListeners(int index) {
        if (mRegisterMap.containsKey(index)) {
            return mRegisterMap.get(index);
        }
        return new ArrayList();
    }

    public void handleListeners(int what, int arg1, Object result) {
        List bindListeners = getRegisterListeners(what);

        if (bindListeners == null || bindListeners.size() <= 0) {
            Log.i(TAG, String.format("get bind listener is null for key :%d", what));
            return;
        }

        instanceofType(bindListeners, what, arg1, (String) result);
    }


    private void instanceofType(List bindListeners, int what, int arg1, String result) {
        for (Object item : bindListeners) {

            if (item instanceof SpeechStreamListener) {
                if (what == SpeechApiCost.MSG_SPEECH_STREAM_DATA) {
                    continue;
                }
            }

            if (item instanceof VolumeChangeListener) {
                ((VolumeChangeListener) item).onVolumeChange(arg1);
                continue;
            }

            if (item instanceof SpeechStatusListener) {
                if (what == SpeechApiCost.MSG_PAR_RESULT) {
                    ((SpeechStatusListener) item).onSpeechParResult(result);
                    continue;
                }

                if (what == SpeechApiCost.MSG_QUERY_ASR_RESULT) {
                    ((SpeechStatusListener) item).onAsrResult(result);
                    continue;
                }

                if (what == SpeechApiCost.MSG_QUERY_ENDED) {
                    ((SpeechStatusListener) item).onQueryEnded(arg1);
                    continue;
                }
            }

            if (item instanceof SpeechRecognitionListener) {
                if (what == SpeechApiCost.MSG_RECOGNITION_START) {
                    ((SpeechRecognitionListener) item).onSpeechRecognitionStart();
                    continue;
                }
                if (what == SpeechApiCost.MSG_RECOGNITION_STOP) {
                    ((SpeechRecognitionListener) item).onSpeechRecognitionStop();
                    continue;
                }
            }

            if (item instanceof TTSSpeechStatueListener) {
                if (what == SpeechApiCost.MSG_SPEECH_START) {
                    ((TTSSpeechStatueListener) item).onStartTTS();
                    continue;
                }

                if (what == SpeechApiCost.MSG_SPEECH_STOP) {
                    ((TTSSpeechStatueListener) item).onStopTTS(arg1 == 1);
                    continue;
                }

                if (what == SpeechApiCost.MSG_TTS_SPEECH_COMPLETE) {
                    ((TTSSpeechStatueListener) item).onSpeechCompleteTTS(arg1 == 1);
                }
            }
        }
    }


    public String onGetMultipleModeInfos(int index) {
        Log.d(TAG, "onGetMultipleModeInfos index = " + index);
        return null;
    }

    public void registerVolumeChangeListener(VolumeChangeListener listener) {
        if (null == listener) {
            Log.e(TAG, "VolumeChangeListener attempt to regist a null listener!");
            return;
        }

        if (mVolumeChangeListener.contains(listener)) {
            Log.w(TAG, "VolumeChangeListener attempt to regist same listener!");
            return;
        }
        mVolumeChangeListener.add(listener);
    }

    public void unRegisterVolumeChangeListener(VolumeChangeListener listener) {
        if (null == listener) {
            Log.e(TAG, "VolumeChangeListener attempt to unregist a null listener!");
            return;
        }

        if (mVolumeChangeListener.contains(listener)) {
            mVolumeChangeListener.remove(listener);
        }
    }

    public void registerRecognitionListener(SpeechRecognitionListener listener) {
        if (null == listener) {
            Log.e(TAG, "SpeechRecognitionListener attempt to regist a null listener!");
            return;
        }

        if (mRecognitionListeners.contains(listener)) {
            Log.w(TAG, "SpeechRecognitionListener attempt to regist same listener!");
            return;
        }

        mRecognitionListeners.add(listener);
    }

    public void unRegisterRecognitionListener(SpeechRecognitionListener listener) {
        if (null == listener) {
            Log.e(TAG, "SpeechRecognitionListener attempt to unregist a null listener!");
            return;
        }

        if (mRecognitionListeners.contains(listener)) {
            mRecognitionListeners.remove(listener);
        }
    }

    public void registerStatusListener(SpeechStatusListener listener) {
        if (null == listener) {
            Log.e(TAG, "SpeechStatusListener attempt to regist a null listener!");
            return;
        }

        if (mCompleteListeners.contains(listener)) {
            Log.w(TAG, "SpeechStatusListener attempt to regist same listener!");
            return;
        }

        mCompleteListeners.add(listener);
    }

    public void unRegisterStatusListener(SpeechStatusListener listener) {
        if (null == listener) {
            Log.e(TAG, "SpeechStatusListener attempt to unregist a null listener!");
            return;
        }

        if (mCompleteListeners.contains(listener)) {
            mCompleteListeners.remove(listener);
        }
    }

    public void registerTTSStatusListener(TTSSpeechStatueListener listener) {
        if (null == listener) {
            Log.e(TAG, "attempt to regist a null listener!");
            return;
        }

        if (mTTSSpeechStatueListeners.contains(listener)) {
            Log.w(TAG, "TTSSpeechStatueListener attempt to regist same listener!");
            return;
        }

        mTTSSpeechStatueListeners.add(listener);
    }

    public void unRegisterTTSStatusListener(TTSSpeechStatueListener listener) {
        if (null == listener) {
            Log.e(TAG, "TTSSpeechStatueListener attempt to unregist a null listener!");
            return;
        }

        if (mTTSSpeechStatueListeners.contains(listener)) {
            mTTSSpeechStatueListeners.remove(listener);
        }
    }

    public interface SpeechRecognitionListener {
        void onSpeechRecognitionStart();

        void onSpeechRecognitionStop();
    }

    public interface VolumeChangeListener {
        void onVolumeChange(int volume);
    }

    public interface SpeechStatusListener {

        void onSpeechParResult(String result);

        void onQueryEnded(int type);

        void onAsrResult(String asrResult);
    }

    public interface TTSSpeechStatueListener {

        void onStartTTS();

        void onStopTTS(boolean isComplete);

        void onSpeechCompleteTTS(boolean isComplete);
    }

    public interface SpeechStreamListener{
        void onSpeechStreamData(String result);
    }

}
