package com.ainirobot.platform.speech.decorator;

import android.text.TextUtils;

import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.platform.speech.OnSpeakListener;
import com.ainirobot.platform.speech.PlayerListener;
import com.ainirobot.platform.speech.decorator.impl.CallBackDecorator;
import com.ainirobot.platform.speech.decorator.impl.EmojiDecorator;
import com.ainirobot.platform.speech.decorator.impl.StateDecorator;
import com.ainirobot.platform.speech.decorator.impl.UnifyResultDecorator;

public class SpeechPlayTextImpl implements ISpeechPlayText {

    private StateDecorator mStateSpeechDecorator;
    private final SpeechPlayText mSpeechPlayText;
    private final CallBackDecorator mCallBackDecorator;
    private final StateDecorator mStateDecorator;
    private final EmojiDecorator mEmojiDecorator;
    private final UnifyResultDecorator mUnifyResultDecorator;
    private String mTag;

    public SpeechPlayTextImpl() {
        mSpeechPlayText = new SpeechPlayText();
        mStateDecorator = new StateDecorator();
        mEmojiDecorator = new EmojiDecorator();
        mCallBackDecorator = new CallBackDecorator();
        mUnifyResultDecorator = new UnifyResultDecorator();
    }

    @Override
    public void speechPlayText(PlayTextBean bean) {
        if (!TextUtils.isEmpty(bean.text)) {
            if (bean.playerListener != null) {
                dealPlayListener(bean);
            } else if (bean.textListener != null) {
                dealTextListener(bean);
            } else {
                dealNoListener(bean);
            }
        } else {
            dealError(bean);
        }
        mTag = bean.tag;
        bean.recycle();
    }

    private void dealNoListener(PlayTextBean bean) {
        if (bean.isTogetherEmoji) {
            speechPlayTextBasic(speechPlayTextEmoji(mSpeechPlayText)).speechPlayText(bean.text, new TextListener());
        } else {
            speechPlayTextBasic(bean.text, null);
        }
    }

    private void dealTextListener(PlayTextBean bean) {
        if (bean.isTogetherEmoji) {
            speechPlayTextBasic(speechPlayTextEmoji(mSpeechPlayText)).speechPlayText(bean.text, bean.textListener);
        } else {
            speechPlayTextBasic(bean.text, bean.textListener);
        }
    }

    private void dealPlayListener(PlayTextBean bean) {
        if (bean.isTogetherEmoji) {
            UnifyResultDecorator decorator = (UnifyResultDecorator) speechPlayTextUnify(speechPlayTextBasic(speechPlayTextEmoji(mSpeechPlayText)));
            decorator.speechPlayText(bean.text, bean.playerListener);
        } else {
            speechPlayWithPlayListener(bean.text, bean.playerListener);
        }
    }

    private void dealError(PlayTextBean bean) {
        if (bean.textListener != null) {
            bean.textListener.onError();
        }
        if (bean.playerListener != null) {
            bean.playerListener.onResult(0);
        }
    }

    private void speechPlayWithPlayListener(String text, PlayerListener listener) {
        UnifyResultDecorator decorator = (UnifyResultDecorator) speechPlayTextUnify(speechPlayTextBasic(mSpeechPlayText));
        decorator.speechPlayText(text, listener);
    }

    @Override
    public boolean isTalking() {
        return mStateSpeechDecorator != null && mStateSpeechDecorator.isTalking();
    }

    @Override
    public String getTag() {
        return mTag;
    }

    @Override
    public void clearTag() {
        mTag = null;
    }

    @Override
    public void setOnSpeakListener(OnSpeakListener listener) {
        mEmojiDecorator.setOnSpeakListener(listener);
    }

    private void speechPlayTextBasic(String text, TextListener listener) {
        mStateSpeechDecorator = (StateDecorator) speechPlayTextState(speechPlayTextCallBack(mSpeechPlayText));
        mStateSpeechDecorator.speechPlayText(text, listener == null ? new TextListener() : listener);
    }

    private IComponentPlayText speechPlayTextBasic(IComponentPlayText componentPlayText) {
        return speechPlayTextState(speechPlayTextCallBack(componentPlayText));
    }

    private IComponentPlayText speechPlayTextEmoji(IComponentPlayText componentPlayText) {
        mEmojiDecorator.setIComponentPlayText(componentPlayText);
        return mEmojiDecorator;
    }

    private IComponentPlayText speechPlayTextCallBack(IComponentPlayText componentPlayText) {
        mCallBackDecorator.setIComponentPlayText(componentPlayText);
        return mCallBackDecorator;
    }

    private IComponentPlayText speechPlayTextState(IComponentPlayText componentPlayText) {
        mStateDecorator.setIComponentPlayText(componentPlayText);
        return mStateDecorator;
    }

    private IComponentPlayText speechPlayTextUnify(IComponentPlayText componentPlayText) {
        mUnifyResultDecorator.setIComponentPlayText(componentPlayText);
        return mUnifyResultDecorator;
    }
}
