package com.ainirobot.platform.speech.decorator;


import android.util.Log;

import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.platform.speech.SpeechManager;

public class SpeechPlayText implements IComponentPlayText {

    private static final String TAG = "SpeechPlayText";

    @Override
    public void speechPlayText(String text, TextListener listener) {
        Log.i(TAG, "speechPlayText = " + text);
//        StorageInteractionUtils.getInstance().saveRobotSpeechInteraction(text);
        SpeechManager.getInstance().playText(text, listener);
    }
}
