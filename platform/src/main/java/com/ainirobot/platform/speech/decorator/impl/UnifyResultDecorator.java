package com.ainirobot.platform.speech.decorator.impl;

import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.platform.speech.PlayerListener;
import com.ainirobot.platform.speech.decorator.BaseSpeechDecorator;
import com.ainirobot.platform.speech.decorator.IComponentPlayText;

public class UnifyResultDecorator extends BaseSpeechDecorator {

    private static final String TAG = "UnifyResultDecorator";

    private IComponentPlayText iComponentPlayText;

    @Override
    public void setIComponentPlayText(IComponentPlayText componentPlayText) {
        iComponentPlayText = componentPlayText;
    }

    public void speechPlayText(String text, final PlayerListener listener){
        iComponentPlayText.speechPlayText(text,new TextListener(){
            @Override
            public void onStart() {
                super.onStart();
            }

            @Override
            public void onStop() {
                super.onStop();
                if (listener != null) {
                    listener.onResult(0);
                }
            }

            @Override
            public void onError() {
                super.onError();

                if (listener != null) {
                    listener.onResult(0);
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                if (listener != null) {
                    listener.onResult(0);
                }
            }
        });
    }

    @Override
    public void speechPlayText(String text, TextListener listener) {
        iComponentPlayText.speechPlayText(text, listener);
    }
}
