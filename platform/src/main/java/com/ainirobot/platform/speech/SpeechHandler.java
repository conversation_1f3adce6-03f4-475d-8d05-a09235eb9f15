package com.ainirobot.platform.speech;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;

public class SpeechHandler extends HandlerThread {

    private static final String TAG = "SpeechHandler";
    private SpeechInternalHandler mHandler;

    public SpeechHandler(String name) {
        super(name);
        startThread();
    }

    private void startThread() {
        this.start();
        mHandler = new SpeechInternalHandler(this.getLooper());
    }

    public Handler getHandler() {
        return mHandler;
    }

    static class SpeechInternalHandler extends Handler {
        public SpeechInternalHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);

            SpeechRegister.getInstance().handleListeners(msg.what, msg.arg1, msg.obj);
        }
    }
}
