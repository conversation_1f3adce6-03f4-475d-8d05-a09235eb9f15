package com.ainirobot.platform.speech;

import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.client.listener.ToneListener;
import com.ainirobot.coreservice.client.speech.entity.ASRParams;
import com.ainirobot.coreservice.client.speech.entity.TTSParams;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


public interface ISpeechApi {

    void playText(String text, TextListener listener);

    void playText(String text, HashMap<String, String> ttsParams, TextListener listener);

    void setTTSParams(@TTSParams String ttsType, int value);

    void playTone(String type, ToneListener listener);

    void playRawTone(int rawResourceId, ToneListener listener);

    void stopTTS();

    void stopTone();

    void cancelAudioOperation();

    void setRecognizeMode(boolean isContinue);

    void setRecognizeModeForce(boolean isContinue);

    void setRecognizeModeNew(boolean isContinue, boolean isCloseStreamData);

    void setASREnabled(boolean enable);

    void setRecognizable(boolean enable);

    void queryByText(String text);

    void getActiveAsk(String properType, String robotProperJson);

    void setAngleCenterRange(float angle_center, float angle_range);

    void setMultipleModeEnable(boolean enale);

    int setCustomizeWakeUpWord(String wakeUpWordChinese, String wakeUpWordPinYin, String separator);

    int closeCustomizeWakeUpWord();

    int getPinYinScore(String pinyin, String separator);

    String queryPinYinFromChinese(String chineseWord);

    String queryPinYinMappingTable(String pinyin);

    String queryUserSetWakeUpWord();

    void playToneByLocalPath(String path, ToneListener listener);

    void setLangRec(String autoLangJson);

    void setASRParams(@ASRParams String asrType, String value);

    boolean setAsrExtendProperty(String propertyJson);

    void startApp(String appId);

    void moveToForeground(String appId);

    void moveToBack(String appId);

    void destroyApp(String appId);

    void setAppPath(String appId, String path);

    void sendAgentMessage(String type,int code,String message);

    void setAppVersion(String appId, String version);

    void setSyncCustomNlpData(Map map);

    String setAsyncCustomNlpData(String opt, String data);

    void resetNlpState();

    void setServerApp(List<String> appList);

    void setNLPDebug(boolean value);

    void downloadTtsAudio(String ttsEntitiesJson);

    String getSpokemanListByLanguage(String lang);

    void closeStreamDataReceived(String paramJson);

    boolean isRecognizeContinue();

    boolean isRecognizable();

    void playStreamText(String streamSid, String textSid, String text, TextListener listener);

    void onAgentActionFinish(String action, int code, String message);

    void onAgentActionState(String action, int state, String data);

    void queryByTextWithThinking(String text, boolean isShowThinking);
}
