package com.ainirobot.platform.speech;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.client.listener.ToneListener;
import com.ainirobot.coreservice.client.speech.entity.ASRParams;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.PlatformDef;
import com.ainirobot.platform.speech.decorator.ISpeechPlayText;
import com.ainirobot.platform.speech.decorator.PlayTextBean;
import com.ainirobot.platform.speech.decorator.SpeechPlayTextImpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SpeechManager implements ISpeechApi {

    private static final String TAG = "SpeechManager";
    private volatile boolean isPlayTTS = true;
    private boolean isInRemoteControl = false;
    private final ISpeechPlayText mSpeechPlayText;

    private SpeechManager() {
        mSpeechPlayText = new SpeechPlayTextImpl();
    }

    private static class SpeechManagerInner {
        public static final SpeechManager INSTANCE = new SpeechManager();
    }

    public static SpeechManager getInstance() {
        return SpeechManagerInner.INSTANCE;
    }

    public void enableTTS() {
        Log.d(TAG, "enableTTS");
        isPlayTTS = true;
    }

    public void disableTTS() {
        Log.d(TAG, "disableTTS");
        isPlayTTS = false;
    }

    public void disableSkill() {
        Log.d(TAG, "in remotecontrol, disable skill");
        isInRemoteControl = true;
        this.setRecognizable(false);
    }

    public void enableSkill() {
        Log.d(TAG, "not in remotecontrol, enable skill");
        isInRemoteControl = false;
        this.setRecognizable(true);
    }

    public void openSpeechAsrRecognize() {
        if (isInRemoteControl) {
            Log.d(TAG, "in remotecontrol, openSpeechAsrRecognize is not working");
            return;
        }
        this.setRecognizable(true);
    }

    public void closeSpeechAsrRecognize() {
        this.setRecognizable(false);
    }

    public void openRobotRecognize() {
        Log.d(TAG, "set recognize mode true");
        this.setRecognizeMode(true);
    }

    public void closeRobotRecognize() {
        Log.d(TAG, "set recognize mode false");
        this.setRecognizeMode(false);
    }

    public void speechPlayText(String text) {
        if (!isPlayTTS) {
            Log.e(TAG, "tts is enable:" + isPlayTTS);
            return;
        }
        mSpeechPlayText.speechPlayText(PlayTextBean.obtain(text));
    }

    public void speechPlayText(String text, TextListener listener) {
        mSpeechPlayText.speechPlayText(PlayTextBean.obtain(text, listener));
    }

    public void speechPlayText(String text, PlayerListener listener) {
        mSpeechPlayText.speechPlayText(PlayTextBean.obtain(text, listener));
    }

    public void speechPlayTextEmoji(String text, TextListener listener) {
        mSpeechPlayText.speechPlayText(PlayTextBean.obtain(text, true, listener));
    }

    public void speechPlayTextEmoji(String text, PlayerListener listener) {
        mSpeechPlayText.speechPlayText(PlayTextBean.obtain(text, true, listener));
    }

    public void speechPlayText(String text, String tag, TextListener listener) {
        PlayTextBean obtain = PlayTextBean.obtain(text, listener);
        obtain.tag = tag;
        mSpeechPlayText.speechPlayText(obtain);
    }

    public void speechPlayText(String text, String tag, PlayerListener listener) {
        PlayTextBean obtain = PlayTextBean.obtain(text, listener);
        obtain.tag = tag;
        mSpeechPlayText.speechPlayText(obtain);
    }

    public void speechPlayTextEmoji(String text, String tag, TextListener listener) {
        PlayTextBean obtain = PlayTextBean.obtain(text, true, listener);
        obtain.tag = tag;
        mSpeechPlayText.speechPlayText(obtain);
    }

    public void setOnSpeakListener(OnSpeakListener listener) {
        mSpeechPlayText.setOnSpeakListener(listener);
    }


    public boolean isTalking() {
        return mSpeechPlayText.isTalking();
    }

    public void switchRecognizeMode() {
        Log.i(TAG, "robot speech recognize mode switch false");
        SpeechManager.getInstance().setRecognizeMode(false);
        SpeechManager.getInstance().cancelAudioOperation();
    }

    public void resetAngleCenterRange() {
        float defautAngleCenter = getDefautAngleCenter();
        float defautAngleRange = getDefautAngleRange();
        this.setAngleCenterRange(defautAngleCenter, defautAngleRange);
        Log.d(TAG, "defautAngleCenter defautAngleRange :" + defautAngleCenter + " " + defautAngleRange);
    }

    public float getDefautAngleCenter() {
        Float angleCenter = 0f;
        SharedPreferences sp = BaseApplication.getContext().getSharedPreferences(PlatformDef.DEVELOPER_CONFIGURATION_ITEM, Context.MODE_PRIVATE);
        angleCenter = sp.getFloat(PlatformDef.ANGLE_CENTER_ITEM, 0f);
        Log.d(TAG, "getDefautAngleCenter form sp angleCenter : " + angleCenter.toString());
        return angleCenter;
    }

    public float getDefautAngleRange() {
        Float angle_range = 120f;
        SharedPreferences sp = BaseApplication.getContext().getSharedPreferences(PlatformDef.DEVELOPER_CONFIGURATION_ITEM, Context.MODE_PRIVATE);
        angle_range = sp.getFloat(PlatformDef.ANGLE_RANGE_ITEM, 120f);
        Log.d(TAG, "getDefautAngleRange form sp angle_range : " + angle_range.toString());
        return angle_range;
    }

    private boolean getSpeechBeamFormingFlag() {
        SharedPreferences sp = BaseApplication.getContext().getSharedPreferences(PlatformDef.DEVELOPER_CONFIGURATION_ITEM, Context.MODE_PRIVATE);
        boolean beamFormingSwitch = sp.getBoolean(PlatformDef.BEAMFOMING_ITEM, true);
        Log.d(TAG, "getSpeechBeamFormingFlag beamFormingSwitch : " + beamFormingSwitch);
        return beamFormingSwitch;
    }

    @Override
    public void setAngleCenterRange(float centerAngle, float rangeAngle) {
        //switch is closeFragment return
        if (!getSpeechBeamFormingFlag()) {
            return;
        }
        if (centerAngle >= 0 && rangeAngle >= 0) {
            SpeechApi.getInstance().setAngleCenterRange(centerAngle, rangeAngle);
        }
    }

    @Override
    public void playText(String text, TextListener listener) {
        SpeechApi.getInstance().playText(text, listener);
    }

    @Override
    public void playText(String text, HashMap<String, String> ttsParams, TextListener listener) {
        SpeechApi.getInstance().playText(text, ttsParams, listener);
    }

    @Override
    public void setTTSParams(String ttsType, int value) {
        SpeechApi.getInstance().setTTSParams(ttsType, value);
    }

    @Override
    public void playRawTone(int rawResourceId, ToneListener listener) {
        SpeechApi.getInstance().playRawTone(rawResourceId, listener);
    }

    @Override
    public void playTone(String type, ToneListener listener) {
        SpeechApi.getInstance().playTone(type, listener);
    }

    public void stopTtsWithCallBack() {
        SpeechRegister.getInstance().handleListeners(SpeechApiCost.MSG_TTS_SPEECH_COMPLETE, 0, null);
        if (SpeechManager.getInstance().isTalking()) {
            this.stopTTS();
        }
    }

    @Override
    public void stopTTS() {
        if (isTalking()) {
            SpeechApi.getInstance().stopTTS();
            mSpeechPlayText.clearTag();
        }
    }

    public void stopTTS(String tag) {
        String historyTag = mSpeechPlayText.getTag();
        if (!TextUtils.isEmpty(historyTag) && TextUtils.equals(tag, historyTag)) {
            stopTTS();
        }
    }

    @Override
    public void stopTone() {
        SpeechApi.getInstance().stopTone();
    }

    @Override
    public void cancelAudioOperation() {
        SpeechApi.getInstance().cancelAudioOperation();
        mSpeechPlayText.clearTag();
    }

    public void cancleAudioOperationWithCallBack() {
        SpeechRegister.getInstance().handleListeners(SpeechApiCost.MSG_TTS_SPEECH_COMPLETE, 0, null);
        this.cancelAudioOperation();
    }

    @Override
    public void setRecognizeMode(boolean isContinue) {
        SpeechApi.getInstance().setRecognizeMode(isContinue);
    }

    @Override
    public void setRecognizeModeForce(boolean isContinue) {
        SpeechApi.getInstance().setRecognizeModeForce(isContinue);
    }

    @Override
    public void setRecognizeModeNew(boolean isContinue, boolean isCloseStreamData) {
        SpeechApi.getInstance().setRecognizeModeNew(isContinue, isCloseStreamData);
    }

    @Override
    public void setASREnabled(boolean enable) {
        SpeechApi.getInstance().setASREnabled(enable);
    }

    @Override
    public void setRecognizable(boolean enable) {
        SpeechApi.getInstance().setRecognizable(enable);
    }

    @Override
    public void queryByText(String text) {
        SpeechApi.getInstance().queryByText(text);
    }

    @Override
    public void getActiveAsk(String properType, String robotProperJson) {
        SpeechApi.getInstance().getActiveAsk(properType, robotProperJson);
    }

    @Override
    public void setMultipleModeEnable(boolean enale) {
        SpeechApi.getInstance().setMultipleModeEnable(enale);
    }

    @Override
    public int setCustomizeWakeUpWord(String wakeUpWordChinese, String wakeUpWordPinYin, String separator) {
        return SpeechApi.getInstance().setCustomizeWakeUpWord(wakeUpWordChinese, wakeUpWordPinYin, separator);
    }

    @Override
    public int closeCustomizeWakeUpWord() {
        return SpeechApi.getInstance().closeCustomizeWakeUpWord();
    }

    @Override
    public int getPinYinScore(String pinyin, String separator) {
        return SpeechApi.getInstance().getPinYinScore(pinyin, separator);
    }

    @Override
    public String queryPinYinFromChinese(String chineseWord) {
        return SpeechApi.getInstance().queryPinYinFromChinese(chineseWord);
    }

    @Override
    public String queryPinYinMappingTable(String pinyin) {
        return SpeechApi.getInstance().queryPinYinMappingTable(pinyin);
    }

    @Override
    public String queryUserSetWakeUpWord() {
        return SpeechApi.getInstance().queryUserSetWakeUpWord();
    }

    @Override
    public void playToneByLocalPath(String path, ToneListener listener) {
        SpeechApi.getInstance().playToneByLocalPath(path, listener);
    }

    @Override
    public void setLangRec(String autoLangJson) {
        SpeechApi.getInstance().setLangRec(autoLangJson);
    }

    @Override
    public void setASRParams(@ASRParams String asrType, String value) {
        SpeechApi.getInstance().setASRParams(asrType, value);
    }

    @Override
    public boolean setAsrExtendProperty(String propertyJson) {
        return SpeechApi.getInstance().setAsrExtendProperty(propertyJson);
    }


    @Override
    public void startApp(String appId) {
        SpeechApi.getInstance().startApp(appId);
    }

    @Override
    public void moveToForeground(String appId) {
        SpeechApi.getInstance().moveToForeground(appId);
    }

    @Override
    public void moveToBack(String appId) {
        SpeechApi.getInstance().moveToBack(appId);
    }

    @Override
    public void destroyApp(String appId) {
        SpeechApi.getInstance().destroyApp(appId);
    }

    @Override
    public void setAppPath(String appId, String path) {
        SpeechApi.getInstance().setAppPath(appId, path);
    }

    @Override
    public void sendAgentMessage(String type, int code, String message) {
        SpeechApi.getInstance().sendAgentMessage(type, code, message);
    }

    @Override
    public void setAppVersion(String appId, String version) {
        SpeechApi.getInstance().setAppVersion(appId, version);
    }

    @Override
    public void setSyncCustomNlpData(Map map) {
        SpeechApi.getInstance().setSyncCustomNlpData(map);
    }

    @Override
    public String setAsyncCustomNlpData(String opt, String data) {
        return SpeechApi.getInstance().setAsyncCustomNlpData(opt, data);
    }

    @Override
    public void resetNlpState() {
        SpeechApi.getInstance().resetNlpState();
    }

    @Override
    public void setServerApp(List<String> appList) {
        SpeechApi.getInstance().setServerApp(appList);
    }

    @Override
    public void setNLPDebug(boolean value) {
        SpeechApi.getInstance().setNLPDebug(value);
    }

    @Override
    public void downloadTtsAudio(String ttsEntitiesJson) {
        SpeechApi.getInstance().downloadTtsAudio(ttsEntitiesJson);
    }

    @Override
    public String getSpokemanListByLanguage(String lang) {
        return SpeechApi.getInstance().getSpokemanListByLanguage(lang);
    }

    @Override
    public void closeStreamDataReceived(String paramJson) {
        SpeechApi.getInstance().closeStreamDataReceived(paramJson);
    }

    @Override
    public boolean isRecognizeContinue() {
        return SpeechApi.getInstance().isRecognizeContinue();
    }

    @Override
    public boolean isRecognizable() {
        return SpeechApi.getInstance().isRecognizable();
    }

    @Override
    public void playStreamText(String streamSid, String textSid, String text, TextListener listener) {
        SpeechApi.getInstance().playStreamText(streamSid, textSid, text, listener);
    }

    @Override
    public void onAgentActionFinish(String action, int code, String message) {
        SpeechApi.getInstance().onAgentActionFinish(action, code, message);
    }

    @Override
    public void onAgentActionState(String action, int state, String data) {
        SpeechApi.getInstance().onAgentActionState(action, state, data);
    }

    @Override
    public void queryByTextWithThinking(String text, boolean isShowThinking) {
        SpeechApi.getInstance().queryByTextWithThinking(text, isShowThinking);
    }
}
