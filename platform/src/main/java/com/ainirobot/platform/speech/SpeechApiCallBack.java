package com.ainirobot.platform.speech;


import android.os.Handler;
import android.os.Message;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.speech.SkillCallback;

public final class SpeechApiCallBack extends SkillCallback {

    private static final String TAG = "SpeechApiCallBack";
    private Handler mHandler;

    private static final class SpeechApiProxyInner {
        static SpeechApiCallBack mInstance = new SpeechApiCallBack();
    }

    public static SpeechApiCallBack getInstance() {
        return SpeechApiProxyInner.mInstance;
    }

    private SpeechApiCallBack() {
        this.mHandler = new SpeechHandler("speech native thread").getHandler();
    }

    @Override
    public void onSpeechParResult(String s) throws RemoteException {
        Log.v(TAG, "onSpeechParResult :" + s);
        mHandler.sendMessage(Message.obtain(mHandler, SpeechApiCost.MSG_PAR_RESULT, s));
    }

    @Override
    public void onStart() throws RemoteException {
        Log.d(TAG, "onSpeechRecognitionStart ");
        mHandler.sendMessage(Message.obtain(mHand<PERSON>, SpeechApiCost.MSG_RECOGNITION_START, null));
    }

    @Override
    public void onStop() throws RemoteException {
        Log.d(TAG, "onSpeechRecognitionStop ");
        mHandler.sendMessage(Message.obtain(mHandler, SpeechApiCost.MSG_RECOGNITION_STOP, null));
    }

    @Override
    public void onVolumeChange(int i) throws RemoteException {
        Log.v(TAG, "onVolumeChange :" + i);
        mHandler.sendMessage(Message.obtain(mHandler, SpeechApiCost.MSG_VOLUME_CHANGE, i, 0));
    }

    @Override
    public void onQueryEnded(int i) throws RemoteException {
        Log.d(TAG, "onQueryEnded()");
        mHandler.sendMessage(Message.obtain(mHandler, SpeechApiCost.MSG_QUERY_ENDED, i, 0));
    }

    @Override
    public void onQueryAsrResult(String asrResult) throws RemoteException {
        super.onQueryAsrResult(asrResult);
        Log.d(TAG, "asrResult = " + asrResult);
        mHandler.sendMessage(Message.obtain(mHandler, SpeechApiCost.MSG_QUERY_ASR_RESULT, asrResult));
    }

    @Override
    public String onGetMultipleModeInfos(int i) throws RemoteException {
        Log.d(TAG, "onGetMultipleModeInfos");
        return SpeechRegister.getInstance().onGetMultipleModeInfos(i);
    }

    @Override
    public void onSpeechStreamData(String data) {

    }
}
