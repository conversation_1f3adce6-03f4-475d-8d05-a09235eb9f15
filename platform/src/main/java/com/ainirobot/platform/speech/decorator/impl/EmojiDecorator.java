package com.ainirobot.platform.speech.decorator.impl;

import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.platform.speech.OnSpeakListener;
import com.ainirobot.platform.speech.decorator.BaseSpeechDecorator;
import com.ainirobot.platform.speech.decorator.IComponentPlayText;

public class EmojiDecorator extends BaseSpeechDecorator {

    private static final String TAG = "EmojiDecorator";

    private IComponentPlayText iComponentPlayText;
    private OnSpeakListener emojiListener;

    public void setOnSpeakListener(OnSpeakListener listener){
        this.emojiListener = listener;
    }

    @Override
    public void setIComponentPlayText(IComponentPlayText componentPlayText) {
        iComponentPlayText = componentPlayText;
    }

    @Override
    public void speechPlayText(String text, final TextListener listener) {
        iComponentPlayText.speechPlayText(text, new TextListener() {
            @Override
            public void onStart() {
                super.onStart();
                playMouthSpeakEmoji();
                listener.onStart();
            }

            @Override
            public void onStop() {
                super.onStop();
                playMouthSpeakEndEmoji();
                listener.onStop();
            }

            @Override
            public void onError() {
                super.onError();
                playMouthSpeakEndEmoji();
                listener.onError();
            }

            @Override
            public void onComplete() {
                super.onComplete();
                playMouthSpeakEndEmoji();
                listener.onComplete();
            }
        });
    }

    public void playMouthSpeakEndEmoji() {
        if(emojiListener != null){
            emojiListener.mouthSpeakEnd();
        }
    }

    public void playMouthSpeakEmoji() {
        if(emojiListener != null){
            emojiListener.mouthSpeakStart();
        }
    }
}
