package com.ainirobot.platform.speech;


import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.client.listener.ToneListener;
import com.ainirobot.coreservice.client.speech.SkillApi;
import com.ainirobot.coreservice.client.speech.SkillCallback;
import com.ainirobot.coreservice.client.speech.SkillServerCheckListener;
import com.ainirobot.coreservice.client.speech.entity.ASRParams;
import com.ainirobot.coreservice.client.speech.entity.LangJson;
import com.ainirobot.coreservice.client.speech.entity.TTSEntity;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.nlp.state.AppLifeCycle;
import com.ainirobot.platform.nlp.state.NlpStateManager;
import com.ainirobot.platform.utils.AudioManagerProxy;
import com.ainirobot.platform.utils.GsonUtil;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SpeechApi implements ISpeechApi {

    private static final String TAG = "SpeechApi";
    private SkillApi mSpeechApi;
    private CallbackProxy mCallbackProxy;
    private SkillServerCheckCallback mServerCheckCallback;
    private SkillServerCheckListener mOutServerCheckCallback;
    private String mForegroundApp;

    public void setSpeechApi(SkillApi speechApi) {
        this.mSpeechApi = speechApi;
        registerCallBackProxy();
        init();
    }

    private void registerCallBackProxy() {
        if (checkNotNull()) {
            mSpeechApi.registerCallBack(mCallbackProxy);
            mSpeechApi.registerServerCheck(mServerCheckCallback);
        }
    }

    public void setSkillServerCheckCallback(SkillServerCheckListener skillServerCheckCallback) {
        this.mOutServerCheckCallback = skillServerCheckCallback;
        registerCallBackProxy();
    }

    public void unRegisterServerCheck() {
        if (this.mOutServerCheckCallback != null) {
            this.mOutServerCheckCallback = null;
        }
    }

    public void init() {
        if (checkNotNull()) {
            boolean isAlreadyOpenAsr = BaseApplication.getIsAlreadyOpenAsr();
            Log.d(TAG, "handleApiConnected asr open state:" + isAlreadyOpenAsr + "  foregroundApp : " + mForegroundApp);
            if (!isAlreadyOpenAsr) {
                mSpeechApi.setRecognizable(false);
                SpeechManager.getInstance().resetAngleCenterRange();
            }

            if (!TextUtils.isEmpty(mForegroundApp)) {
                startApp(mForegroundApp);
                moveToForeground(mForegroundApp);
            }
        }
    }

    private static class SpeechManagerNativeInner {
        static final SpeechApi INSTANCE = new SpeechApi();
    }

    public static SpeechApi getInstance() {
        return SpeechManagerNativeInner.INSTANCE;
    }

    private SpeechApi() {
        mCallbackProxy = new CallbackProxy();
        mServerCheckCallback = new SkillServerCheckCallback();
    }

    public void setCallBack(SkillCallback callback) {
        mCallbackProxy.setCallBack(callback);
    }

    private boolean isAudioAlive() {
        return AudioManagerProxy.INSTANCE.isAudioAlive();
    }

    private boolean checkNotNull() {
        boolean flag = mSpeechApi != null;
        Log.i(TAG, "check not null result:" + flag);
        return flag;
    }

    public boolean checkConnectSpeechService() {
        return checkNotNull();
    }

    @Override
    public void playText(String text, TextListener listener) {
        if (checkNotNull() && isAudioAlive()) {
            mSpeechApi.playText(new TTSEntity(text), listener);
        } else {
            if (listener != null) {
                listener.onError();
            }
        }
    }

    @Override
    public void playText(String text, HashMap<String, String> ttsParams, TextListener listener) {
        if (checkNotNull() && isAudioAlive()) {
            mSpeechApi.playText(new TTSEntity(null, text, ttsParams), listener);
        } else {
            if (listener != null) {
                listener.onError();
            }
        }
    }

    @Override
    public void setTTSParams(String ttsType, int value) {
        if (checkNotNull()) {
            mSpeechApi.setTTSParams(ttsType, value);
        }
    }

    @Override
    public void playTone(String type, ToneListener listener) {
        if (checkNotNull() && isAudioAlive()) {
            mSpeechApi.playTone(type, listener);
        } else {
            if (listener != null) {
                listener.onError();
            }
        }
    }

    @Override
    public void playRawTone(int rawResourceId, ToneListener listener) {
        String path = "android.resource://" + BaseApplication.getContext().getPackageName()
                + File.separator + rawResourceId;
        if (checkNotNull() && isAudioAlive()) {
            mSpeechApi.playToneByLocalPath(path, listener);
        } else {
            if (listener != null) {
                listener.onError();
            }
        }
    }

    @Override
    public void playToneByLocalPath(String path, ToneListener listener) {
        if (checkNotNull() && isAudioAlive()) {
            mSpeechApi.playToneByLocalPath(path, listener);
        } else {
            if (listener != null) {
                listener.onError();
            }
        }
    }

    @Override
    public void setLangRec(String autoLangJson) {
        if (checkNotNull()) {
            mSpeechApi.setLangRec(GsonUtil.fromJson(autoLangJson, LangJson.class));
        }
    }

    @Override
    public void setASRParams(@ASRParams String asrType, String value) {
        if (checkNotNull()) {
            mSpeechApi.setASRParams(asrType, value);
        }
    }

    @Override
    public void stopTTS() {
        if (checkNotNull()) {
//            Log.d(TAG, Log.getStackTraceString(new Throwable()));
            mSpeechApi.stopTTS();
        }
    }

    @Override
    public void stopTone() {
        if (checkNotNull()) {
            mSpeechApi.stopTone();
        }
    }

    @Override
    public void cancelAudioOperation() {
        if (checkNotNull()) {
            mSpeechApi.cancleAudioOperation();
        }
    }

    @Override
    public void setRecognizeMode(boolean isContinue) {
        if (checkNotNull()) {
            mSpeechApi.setRecognizeMode(isContinue);
        }
    }

    @Override
    public void setRecognizeModeForce(boolean isContinue) {
        if (checkNotNull()) {
            mSpeechApi.setRecognizeModeForce(isContinue);
        }
    }

    @Override
    public void setRecognizeModeNew(boolean isContinue, boolean isCloseStreamData) {
        if (checkNotNull()) {
            mSpeechApi.setRecognizeModeNew(isContinue, isCloseStreamData);
        }
    }

    @Override
    public void setASREnabled(boolean enable) {
        if (checkNotNull()) {
            mSpeechApi.setASREnabled(enable);
        }
    }

    @Override
    public void setRecognizable(boolean enable) {
        if (checkNotNull()) {
            mSpeechApi.setRecognizable(enable);
        }
    }

    @Override
    public void queryByText(String text) {
        if (checkNotNull()) {
            mSpeechApi.queryByText(text);
        }
    }

    @Override
    public void getActiveAsk(String properType, String robotProperJson) {
        if (checkNotNull()) {
            mSpeechApi.getActiveAsk(properType, robotProperJson);
        }
    }

    @Override
    public void setAngleCenterRange(float angle_center, float angle_range) {
        if (checkNotNull()) {
            mSpeechApi.setAngleCenterRange(angle_center, angle_range);
        }
    }

    @Override
    public void setMultipleModeEnable(boolean enale) {
        if (checkNotNull()) {
            mSpeechApi.setMultipleModeEnable(enale);
        }
    }

    @Override
    public int setCustomizeWakeUpWord(String wakeUpWordChinese, String wakeUpWordPinYin, String separator) {
        if (checkNotNull()) {
            return mSpeechApi.setCustomizeWakeUpWord(wakeUpWordChinese, wakeUpWordPinYin, separator);
        }
        return -1;
    }

    @Override
    public int closeCustomizeWakeUpWord() {
        if (checkNotNull()) {
            return mSpeechApi.closeCustomizeWakeUpWord();
        }
        return -1;
    }

    @Override
    public int getPinYinScore(String pinyin, String separator) {
        if (checkNotNull()) {
            return mSpeechApi.getPinYinScore(pinyin, separator);
        }
        return -1;
    }

    @Override
    public String queryPinYinFromChinese(String chineseWord) {
        if (checkNotNull()) {
            return mSpeechApi.queryPinYinFromChinese(chineseWord);
        }
        return null;
    }

    @Override
    public String queryPinYinMappingTable(String pinyin) {
        if (checkNotNull()) {
            return mSpeechApi.queryPinYinMappingTable(pinyin);
        }
        return null;
    }

    @Override
    public String queryUserSetWakeUpWord() {
        if (checkNotNull()) {
            return mSpeechApi.queryUserSetWakeUpWord();
        }
        return null;
    }

    @Override
    public boolean setAsrExtendProperty(String propertyJson) {
        if (checkNotNull()) {
            return mSpeechApi.setAsrExtendProperty(propertyJson);
        }
        return false;
    }

    /**
     * 预下载mp3文件
     *
     * @param ttsEntitiesJson {@link TTSEntity}
     */
    @Override
    public void downloadTtsAudio(String ttsEntitiesJson) {
        if (checkNotNull()) {
            mSpeechApi.downloadTtsAudio(ttsEntitiesJson);
        }
    }

    private static class CallbackProxy extends SkillCallback {

        private SkillCallback mCallBack = SpeechApiCallBack.getInstance();

        private void setCallBack(SkillCallback callback) {
            this.mCallBack = callback;
        }

        private boolean checkCallBack() {
            return mCallBack != null;
        }

        @Override
        public void onSpeechParResult(String s) throws RemoteException {
            if (checkCallBack()) {
                mCallBack.onSpeechParResult(s);
            }
        }

        @Override
        public void onStart() throws RemoteException {
            if (checkCallBack()) {
                mCallBack.onStart();
            }
        }

        @Override
        public void onStop() throws RemoteException {
            if (checkCallBack()) {
                mCallBack.onStop();
            }
        }

        @Override
        public void onVolumeChange(int i) throws RemoteException {
            if (checkCallBack()) {
                mCallBack.onVolumeChange(i);
            }
        }

        @Override
        public void onQueryEnded(int i) throws RemoteException {
            if (checkCallBack()) {
                mCallBack.onQueryEnded(i);
            }
        }

        @Override
        public String onGetMultipleModeInfos(int index) throws RemoteException {
            if (checkCallBack()) {
                return mCallBack.onGetMultipleModeInfos(index);
            }
            return null;
        }

        @Override
        public void onQueryAsrResult(String asrResult) throws RemoteException {
            if (checkCallBack()) {
                mCallBack.onQueryAsrResult(asrResult);
            }
        }

        @Override
        public void onSpeechStreamData(String data) throws RemoteException {
            if (checkCallBack()) {
                try {
                    mCallBack.onSpeechStreamData(data);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private class SkillServerCheckCallback extends SkillServerCheckListener {
        @Override
        public void onDelay(int timeMilliSeconds) {
            Log.d(TAG, "onDelay: timeMilliSeconds = " + timeMilliSeconds);
            if (mOutServerCheckCallback != null) {
                mOutServerCheckCallback.onDelay(timeMilliSeconds);
            }
        }

        @Override
        public void onError(String message) {
            Log.d(TAG, "onError: message = " + message);
            if (mOutServerCheckCallback != null) {
                mOutServerCheckCallback.onError(message);
            }
        }
    }

    @Override
    public void startApp(String appId) {
        Log.d(TAG, "Start app: " + appId);
        if (checkNotNull()) {
            mSpeechApi.onCreate(appId);
            NlpStateManager.Companion.get().setAppLifeCycle(appId, AppLifeCycle.CREATE);
        }
    }

    @Override
    public void moveToForeground(String appId) {
        Log.d(TAG, "Move to foreground : " + appId);
        mForegroundApp = appId;
        if (checkNotNull()) {
            mSpeechApi.onForeground(appId);
            NlpStateManager.Companion.get().setAppLifeCycle(appId, AppLifeCycle.FOREGROUND);
        }
    }

    @Override
    public void moveToBack(String appId) {
        Log.d(TAG, "Move to back : " + appId);
        if (checkNotNull()) {
            mSpeechApi.onBackground(appId);
            NlpStateManager.Companion.get().setAppLifeCycle(appId, AppLifeCycle.BACKGROUND);
            if (mForegroundApp != null && mForegroundApp.equals(appId)) {
                mForegroundApp = null;
            }
        }
    }

    @Override
    public void destroyApp(String appId) {
        Log.d(TAG, "Destroy app : " + appId);
        if (checkNotNull()) {
            mSpeechApi.onDestroy(appId);
            NlpStateManager.Companion.get().setAppLifeCycle(appId, AppLifeCycle.DESTROY);
            if (mForegroundApp != null && mForegroundApp.equals(appId)) {
                mForegroundApp = null;
            }
        }
    }

    @Override
    public void setAppPath(String appId, String path) {
        if (TextUtils.isEmpty(appId)
                || TextUtils.isEmpty(path)) {
            return;
        }
        Log.d(TAG, "Set app path  : " + appId + "  " + path);
        if (checkNotNull()) {
            mSpeechApi.setPath(appId, path);
            NlpStateManager.Companion.get().setAppPath(appId, path);
        }
    }

    @Override
    public void sendAgentMessage(String type, int code, String message) {
        Log.d(TAG, "sendAgentMessage: type = " + type+"==message=="+message);
        if (checkNotNull()) {
            mSpeechApi.sendAgentMessage(type, code, message);
        }
    }

    @Override
    public void setAppVersion(String appId, String version) {
        if (checkNotNull()) {
            mSpeechApi.setVersion(appId, version);
            NlpStateManager.Companion.get().setAppVersion(appId, version);
        }
    }

    @Override
    public void setSyncCustomNlpData(Map map) {
        if (checkNotNull()) {
            mSpeechApi.setSyncCustomNlpData(map);
            NlpStateManager.Companion.get().setSyncCustomNlpData(map);
        }
    }

    @Override
    public String setAsyncCustomNlpData(String opt, String data) {
        if (checkNotNull()) {
            NlpStateManager.Companion.get().setAsyncCustomNlpData(opt, data);
            return mSpeechApi.setAsyncCustomNlpData(opt, data);
        }
        return "setAsyncCustomNlpData null";
    }

    @Override
    public void resetNlpState() {
        if (checkNotNull()) {
            mSpeechApi.resetNlpState();
        }
    }

    @Override
    public void setServerApp(List<String> appList) {
        if (checkNotNull()) {
            mSpeechApi.setServerApp(appList);
            NlpStateManager.Companion.get().setServerApp(appList);
        }
    }

    @Override
    public void setNLPDebug(boolean value) {
        if (checkNotNull()) {
            mSpeechApi.setDebug(value);
            NlpStateManager.Companion.get().setNLPDebug(value);
        }
    }

    @Override
    public String getSpokemanListByLanguage(String lang) {
        if (checkNotNull()) {
            return mSpeechApi.getSpokemanListByLanguage(lang);
        }
        return null;
    }

    public String getForegroundApp() {
        return mForegroundApp;
    }

    @Override
    public void closeStreamDataReceived(String paramJson){
        if (checkNotNull()) {
            mSpeechApi.closeStreamDataReceived(paramJson);
        }
    }

    @Override
    public boolean isRecognizeContinue() {
        if (checkNotNull()) {
            return mSpeechApi.isRecognizeContinue();
        }
        return true;
    }

    @Override
    public boolean isRecognizable() {
        if (checkNotNull()) {
            return mSpeechApi.isRecognizable();
        }
        return true;
    }

    @Override
    public void playStreamText(String streamSid, String textSid, String text, TextListener listener) {
        if (checkNotNull() && isAudioAlive()) {
            mSpeechApi.playText(new TTSEntity(textSid, streamSid, text), listener);
        } else {
            if (listener != null) {
                listener.onError();
            }
        }
    }

    @Override
    public void onAgentActionFinish(String action, int code, String message) {
        Log.d(TAG, "On agent action finish  : " + action + "  " + code + "  " + message);
        if (checkNotNull()) {
            mSpeechApi.onAgentActionFinish(action, code, message);
        }
    }

    @Override
    public void onAgentActionState(String action, int state, String data) {
        Log.d(TAG, "On agent action state  : " + action + "  " + state + "  " + data);
        if (checkNotNull()) {
            mSpeechApi.onAgentActionState(action, state, data);
        }
    }

    @Override
    public void queryByTextWithThinking(String text, boolean isShowThinking) {
        if (checkNotNull()) {
            mSpeechApi.queryByTextWithThinking(text, isShowThinking);
        }
    }
}
