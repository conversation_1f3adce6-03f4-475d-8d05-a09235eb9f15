package com.ainirobot.platform.speech.decorator;

import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.platform.speech.PlayerListener;

import java.util.ArrayDeque;
import java.util.concurrent.locks.ReentrantLock;

public class PlayTextBean {

    private static ArrayDeque<PlayTextBean> sMessagePool = new ArrayDeque<>();
    private static ReentrantLock sLock = new ReentrantLock();

    public String tag;
    public String text;
    public TextListener textListener;
    public PlayerListener playerListener;
    public boolean isTogetherEmoji = false;

    protected PlayTextBean() {
    }

    public static PlayTextBean obtain(String text) {
        return obtain(text, null, null);
    }

    public static PlayTextBean obtain(String text, TextListener textListener) {
        return obtain(text, textListener, null);
    }

    public static PlayTextBean obtain(String text, PlayerListener playerListener) {
        return obtain(text, null, playerListener);
    }

    public static PlayTextBean obtain(String text, boolean isTogetherEmoji, TextListener textListener) {
        return obtain(text, isTogetherEmoji, textListener, null);
    }

    public static PlayTextBean obtain(String text, boolean isTogetherEmoji, PlayerListener playerListener) {
        return obtain(text, isTogetherEmoji, null, playerListener);
    }

    public static PlayTextBean obtain(String text, TextListener textListener, PlayerListener playerListener) {
        return obtain(text, false, textListener, playerListener);
    }

    public static PlayTextBean obtain(String text, boolean isTogetherEmoji, TextListener textListener, PlayerListener playerListener) {
        PlayTextBean bean;
        sLock.lock();
        try {
            bean = sMessagePool.poll();
        } finally {
            sLock.unlock();
        }
        if (bean == null) {
            bean = new PlayTextBean();
        }
        bean.text = text;
        bean.textListener = textListener;
        bean.playerListener = playerListener;
        bean.isTogetherEmoji = isTogetherEmoji;
        return bean;
    }

    public void recycle() {
        text = null;
        tag = null;
        textListener = null;
        playerListener = null;
        isTogetherEmoji = false;
        sLock.lock();
        try {
            sMessagePool.offer(this);
        } finally {
            sLock.unlock();
        }
    }
}
