package com.ainirobot.platform;

import android.app.Application;
import android.content.Context;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.WindowManager;

import com.AlexanderZaytsev.RNI18n.RNI18nPackage;
import com.RNRSA.RNRSAPackage;
import com.ainirobot.base.OrionBase;
import com.ainirobot.base.listener.ICpuWarnListener;
import com.ainirobot.base.listener.IMemoryWarnListener;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.upload.bi.BiReport;
import com.ainirobot.emoji.SwitchEmojiManager;
import com.ainirobot.emoji.config.EmojiConfig;
import com.ainirobot.platform.appmanager.AppPackageManager;
import com.ainirobot.platform.bi.WakeUpIdWrapper;
import com.ainirobot.platform.bi.wrapper.ReportControl;
import com.ainirobot.platform.bi.wrapper.manager.bi.impl.OpkPerformanceMonitorPoint;
import com.ainirobot.platform.character.CharacterManager;
import com.ainirobot.platform.control.AutoCruiseManager;
import com.ainirobot.platform.control.ControlManager;
import com.ainirobot.platform.data.FeatureConfig;
import com.ainirobot.platform.data.ModuleDataMgr;
import com.ainirobot.platform.react.AppBeanV2;
import com.ainirobot.platform.react.AppManger;
import com.ainirobot.platform.react.CharacterReceiver;
import com.ainirobot.platform.react.CountdownAlarmReceiver;
import com.ainirobot.platform.react.InstallCharacterReceiver;
import com.ainirobot.platform.react.InstallPluginReceiver;
import com.ainirobot.platform.react.OPKBeanV3;
import com.ainirobot.platform.react.RomOpkUpdateReceiver;
import com.ainirobot.platform.react.SceneReceiver;
import com.ainirobot.platform.react.client.RNClientManager;
import com.ainirobot.platform.react.network.NetworkManger;
import com.ainirobot.platform.react.reactnative.RNDebugReceiver;
import com.ainirobot.platform.react.reactnative.character.ReactCharacter;
import com.ainirobot.platform.react.reactnative.component.CommonBridgeModuleReactPackage;
import com.ainirobot.platform.react.reactnative.component.ComponentBridgeModuleReactPackage;
import com.ainirobot.platform.react.reactnative.component.battery.BatteryPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.BaiduMapPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.baidumap.BaiduMapSDKInitializer;
import com.ainirobot.platform.react.reactnative.component.uicomponent.bigimage.BigImagePackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.bigimagefit.BigImageFitPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.blur.SajjadBlurOverlayPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.bubbleview.BubbleViewPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.camera.CameraViewPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.camerafilter.CameraFilterViewPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.clipimage.ClipIconImageViewPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.dropShadow.DropShadowPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.emojiplayer.RNEmojiPlayerViewPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.external.ExternalDisplayPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.external.view.ExternalDisplay;
import com.ainirobot.platform.react.reactnative.component.uicomponent.faceparticleview.RNFaceParticleViewPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.gradient.LinearGradientPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.imageframe.ImageFramePackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.justifytext.JustifyTextViewPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.mute.MuteViewPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.ninepatch.NinePatchViewPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.recognitionview.RecognitionViewPackage;
import com.ainirobot.platform.react.reactnative.component.uicomponent.slideverifyimageview.SlideVerifyImageViewPackage;
import com.ainirobot.platform.react.reactnative.crashhandler.OSNativeModuleCallExceptionHandler;
import com.ainirobot.platform.react.reactnative.crashhandler.bridge.RNCrashHandlerPackage;
import com.ainirobot.platform.utils.AudioManagerProxy;
import com.ainirobot.platform.utils.BackgroundThread;
import com.ainirobot.platform.utils.DimenUtils;
import com.ainirobot.platform.utils.FileDownloadManager;
import com.ainirobot.platform.utils.GsonUtil;
import com.ainirobot.platform.utils.ReactPackageInDexUtil;
import com.ainirobot.platform.utils.SettingDataHelper;
import com.ainirobot.platform.utils.SystemUtils;
import com.airbnb.android.react.lottie.LottiePackage;
import com.asterinet.react.tcpsocket.TcpSocketPackage;
import com.brentvatne.react.ReactVideoPackage;
import com.chinaztt.encapsulation.EncryptionReactPackager;
import com.devstepbcn.wifi.AndroidWifiPackage;
import com.facebook.cache.disk.DiskCacheConfig;
import com.facebook.common.internal.Supplier;
import com.facebook.common.memory.MemoryTrimType;
import com.facebook.common.memory.MemoryTrimmable;
import com.facebook.common.memory.NoOpMemoryTrimmableRegistry;
import com.facebook.common.util.ByteConstants;
import com.facebook.imagepipeline.cache.MemoryCacheParams;
import com.facebook.imagepipeline.core.ImagePipelineConfig;
import com.facebook.imagepipeline.core.ImagePipelineFactory;
import com.facebook.infer.annotation.Assertions;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactInstanceManager;
import com.facebook.react.ReactInstanceManagerBuilder;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.ReactMarker;
import com.facebook.react.bridge.ReactMarkerConstants;
import com.facebook.react.common.LifecycleState;
import com.facebook.react.shell.MainPackageConfig;
import com.facebook.react.shell.MainReactPackage;
import com.facebook.soloader.DirectorySoSource;
import com.facebook.soloader.SoLoader;
import com.facebook.soloader.SoSource;
import com.horcrux.svg.SvgPackage;
import com.pilloxa.backgroundjob.BackgroundJobPackage;
import com.poberwong.launcher.IntentLauncherPackage;
import com.polidea.reactnativeble.BlePackage;
import com.reactnativecommunity.rctaudiotoolkit.AudioPackage;
import com.reactnativecommunity.slider.ReactSliderPackage;
import com.reactnativecommunity.webview.RNCWebViewPackage;
import com.rnfs.RNFSPackage;
import com.sha256lib.Sha256Package;
import com.rnziparchive.RNZipArchivePackage;
import com.tradle.react.UdpSocketsModule;

import org.reactnative.camera.RNCameraPackage;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class BaseApplication extends Application implements ReactApplication {

    private static final String TAG = BaseApplication.class.getSimpleName();

    private static final int MAX_MEMORY_CACHE_SIZE = 125 * ByteConstants.MB;
    private static final long MAX_DISK_CACHE_SIZE = 80L * ByteConstants.MB;

    private static volatile BaseApplication sBase;
    private static Application sContext;
    private static boolean mIsAlreadyOpenAsr = false;
    // 蜂鸟云SDK是否初始化成功
    private ReactNativeHost mReactNativeHost;
    private OSNativeModuleCallExceptionHandler mRNCrashHandler;
    private ReactInstanceManager mReactInstanceManager;
    protected boolean isFirstBoot;
    private String mAssertEmojiPath;

    private CountdownAlarmReceiver mCountdownAlarmReceiver;
    private RomOpkUpdateReceiver mRomOpkUpdateReceiver;
    private InstallPluginReceiver mInstallPluginReceiver;
    private InstallCharacterReceiver mInstallCharacterReceiver;
    private CharacterReceiver mCharacterReceiver;
    private SceneReceiver mSceneReceiver;

    @Override
    public void onCreate() {
        super.onCreate();
        SettingDataHelper.getInstance().setContext(this);
        AudioManagerProxy.INSTANCE.init(this);

        // 获取当前的 WindowManager
        WindowManager windowManager = (WindowManager) getSystemService(WINDOW_SERVICE);
        DisplayMetrics displayMetrics = new DisplayMetrics();

        // 获取当前显示指标
        windowManager.getDefaultDisplay().getMetrics(displayMetrics);

        Log.d(TAG, "onCreate displayMetrics density: " + displayMetrics.density
                + ", densityDpi:" + displayMetrics.densityDpi
                + ", scaledDensity:" + displayMetrics.scaledDensity);

        if (displayMetrics.densityDpi != 560) {
            // 设置新的屏幕密度
            int newDensityDpi = 560;
            float newDensity = newDensityDpi / 160f;
            displayMetrics.density = newDensity;
            displayMetrics.densityDpi = newDensityDpi;
            displayMetrics.scaledDensity = newDensity;

            // 更新资源配置
            android.content.res.Configuration configuration = getResources().getConfiguration();
            configuration.densityDpi = newDensityDpi;
            getResources().updateConfiguration(configuration, displayMetrics);
        }

        sContext = this;
        sBase = this;
        if (SystemUtils.isMainProcess(sContext)) {
            initMainProcess();
        } else if (SystemUtils.isRNProcess(sContext)) {
            initRNProcess();
        } else {
            Log.e(TAG, "onCreate not main or sandbox");
        }

        BiReport.init();
        String zone = SettingDataHelper.getInstance().getCloudServerZone();
        Log.d(TAG,"zone: " + zone);
        OrionBase.setDomainEnv(ProductInfo.isOverSea(), zone,RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_SYSTEM_ENV) == 1);
        OrionBase.start(sContext, null);
        DimenUtils.setContext(sContext);

        isDebug();
        registerOpkReceiver();
    }

    private void registerOpkReceiver() {
        mCountdownAlarmReceiver = new CountdownAlarmReceiver();
        IntentFilter filterCountdownAlarm = new IntentFilter();
        filterCountdownAlarm.addAction("trigger_count_down_timer");
        sContext.registerReceiver(mCountdownAlarmReceiver, filterCountdownAlarm);

        mRomOpkUpdateReceiver = new RomOpkUpdateReceiver();
        IntentFilter filterRomOpk = new IntentFilter();
        filterRomOpk.addAction("com.ainirobot.moduleapp.rom_opk_update");
        sContext.registerReceiver(mRomOpkUpdateReceiver, filterRomOpk);

        mInstallPluginReceiver = new InstallPluginReceiver();
        IntentFilter filterInstallPlugin = new IntentFilter();
        filterInstallPlugin.addAction("com.ainirobot.install.plugin");
        filterInstallPlugin.addAction("com.ainirobot.install.host");
        filterInstallPlugin.addAction("com.ainirobot.uninstall.plugin");
        filterInstallPlugin.addAction("com.ainirobot.set.portal");
        sContext.registerReceiver(mInstallPluginReceiver, filterInstallPlugin);

        mInstallCharacterReceiver = new InstallCharacterReceiver();
        IntentFilter filterInstallCharacter = new IntentFilter();
        filterInstallCharacter.addAction("com.ainirobot.remotecontrolservice.rninstallfinish");
        sContext.registerReceiver(mInstallCharacterReceiver, filterInstallCharacter);

        mCharacterReceiver = new CharacterReceiver();
        IntentFilter filterSwitchCharacter = new IntentFilter();
        filterSwitchCharacter.addAction("com.ainirobot.moduleapp.SWITCH_CHARACTER");
        sContext.registerReceiver(mCharacterReceiver, filterSwitchCharacter);

        mSceneReceiver = new SceneReceiver();
        IntentFilter filterScene = new IntentFilter();
        filterScene.addAction("com.ainirobot.moduleapp.SWITCH_SCENE");
        sContext.registerReceiver(mSceneReceiver, filterScene);
    }

    private void initMainProcess() {
        initPlatform();
        initWakeupId();
        isFirstBoot = FeatureConfig.isInitBoot();
        FileDownloadManager.init(this);

        AppPackageManager.init(this);

//        RomOpkCheck.Companion.getInstance().checkOpkVersion(this);
        ModuleDataMgr.getInstance().setContext(sContext);
        if (isDebug() || RNDebugReceiver.Companion.getDEBUG()) {
            registerReactNativeCharacter("react");
        } else {
            parseAndRegister();
        }

        ExternalDisplay.INSTANCE.init(this, null);
        initPerformanceMonitor();

        AutoCruiseManager.newInstance().setup();
        onMainProcessInit();
    }

    public void initPlatform() {
        PlatformInit.getInstance().init();
    }

    public void onActivityCreate() {
        Log.d(TAG, "onActivityCreate");
//        RNServerManager.getInstance().stopRNProcess();
        ControlManager.handleApiConnection();
    }

    private void initRNProcess() {
        if (RNClientManager.Companion.getInstance() != null) {
            RNClientManager.Companion.getInstance().bindModuleService(sContext);
        }

        loadNativeLibrary();

        initWakeupId();
        initRNEmoji();
        if (!ProductInfo.isOverSea()) {
            initBaidu();
        }
        ModuleDataMgr.getInstance().setContext(sContext);
        getReactNativeHost();
        NetworkManger.Companion.getInstance().initDomain();
        if (!mReactNativeHost.getUseDeveloperSupport()) {
            mRNCrashHandler = new OSNativeModuleCallExceptionHandler(true);
        }
        onRNProcessInit();
    }

    public void initBaidu() {
        BaiduMapSDKInitializer.initialize(this);
    }

    private void initRNEmoji() {
        EmojiConfig.init(this, true, false);
        EmojiConfig.setShowDefault(false);
        EmojiConfig.setEnableLog(true);
        EmojiConfig.setAssetsEmojiPath(getAssertEmojiPath());
        EmojiConfig.setDefaultSmallBackgroundRes(R.drawable.skill_default_bg);
        EmojiConfig.setDefaultBigBackgroundRes(R.color.black);
        Log.d(TAG, "Set default backgournd res : " + R.color.black);

        BackgroundThread.Companion.post(new Runnable() {
            @Override
            public void run() {
                SwitchEmojiManager.getInstance().init(BaseApplication.this);
            }
        });
    }

    public static Context getContext() {
        return sContext;
    }

    public static BaseApplication getApplication() {
        return sBase;
    }

    public static void setIsAlreadyOpenAsr(boolean openState) {
        mIsAlreadyOpenAsr = openState;
    }

    public static boolean getIsAlreadyOpenAsr() {
        return mIsAlreadyOpenAsr;
    }

    /**
     * 初始化wakeupId,用于埋点上报
     */
    private void initWakeupId() {
        if (TextUtils.isEmpty(WakeUpIdWrapper.getInstance().getWakeUpId())) {
            WakeUpIdWrapper.getInstance().generateWakeUpId();
            Log.i(TAG, "module app create wakeupId: "
                    + WakeUpIdWrapper.getInstance().getWakeUpId());
        }
    }

    private void loadNativeLibrary() {
        Log.i(TAG, "loadNativeLibrary");
        try {
            SoLoader.init(sContext, SoLoader.SOLOADER_ALLOW_ASYNC_INIT);
            Log.i(TAG, "nativeLibraryDir:" + sContext.getApplicationInfo().nativeLibraryDir);
            File soLib = new File(sContext.getApplicationInfo().nativeLibraryDir);
            DirectorySoSource soSource = new DirectorySoSource(soLib,
                    SoSource.LOAD_FLAG_ALLOW_IMPLICIT_PROVISION);
            SoLoader.prependSoSource(soSource);

            if (BuildConfig.HERMES) {
                System.loadLibrary("PlatformJSI");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void initPerformanceMonitor() {
        OrionBase.setProcessName("com.ainirobot.moduleapp:sandbox");
        int[] cpuInts = new int[]{60, 90};
        int[] memoryInts = new int[]{600 * 1024, 900 * 1024, 1200 * 1024};
        OrionBase.setCpuWarnThreshold(cpuInts);
        OrionBase.setMemoryWarnThreshold(memoryInts);
        OrionBase.setWarnSampleCount(4);
        OrionBase.setWarnSampleRate(30000);
        OrionBase.setMemoryWarnListener(new IMemoryWarnListener() {
            @Override
            public void memoryWarn(int threshold, int memory, double cpu) {
                Log.d(TAG, "memoryWarn threshold: $threshold memory: " + memory
                        + ", cpu: " + cpu);
                reportMonitor(threshold, cpu, memory, 1);
            }
        });
        OrionBase.setCpuWarnListener(new ICpuWarnListener() {
            @Override
            public void cpuWarn(int threshold, double cpu, int memory) {
                Log.d(TAG, "cpuWarn threshold: $threshold cpu: " + cpu
                        + ", memory: " + memory);
                reportMonitor(threshold, cpu, memory, 2);
            }
        });

        String zone = SettingDataHelper.getInstance().getCloudServerZone();
        Log.d(TAG, "zone: " + zone);
        OrionBase.setDomainEnv(ProductInfo.isOverSea(), zone, RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_SYSTEM_ENV) == 1);
        OrionBase.start(sContext, null);
    }

    private void reportMonitor(int threshold, double cpu, int memory, int type) {
        String opkVersion = "";
        String appid = "";
        OPKBeanV3 opkBeanV3 = AppManger.INSTANCE
                .getRPKByCharacter(ReactCharacter.Companion.getCurrentCharacter());

        if (opkBeanV3 != null) {
            opkVersion = opkBeanV3.getVersionName();
            appid = opkBeanV3.getAppid();
        }

        ReportControl.getInstance().reportMsg(new OpkPerformanceMonitorPoint(opkVersion,
                appid, type, threshold, memory, cpu));
    }

    public final boolean registerReactNativeCharacter(String appId) {
        if (TextUtils.isEmpty(appId)) {
            Log.d(TAG, "registerCharacter appid is empty");
            return false;

        }
        Log.i(TAG, "registerCharacter");
        ReactCharacter character = new ReactCharacter(appId);
        return CharacterManager.getInstance().registerCharacterFromRN(character.getName(),
                character);
    }

    private void parseAndRegister() {
        SharedPreferences sp = sContext.getSharedPreferences(ReactCharacter.sharedPreferences,
                Context.MODE_PRIVATE);
        String rpkIndexPath = sp.getString(ReactCharacter.defaultRpkPath, null);
        String opkBizPath = sp.getString(ReactCharacter.defaultOpkBizPath, null);
        String opkPlatformPath = sp.getString(ReactCharacter.defaultOpkPlatformPath, null);
        String opkJson = sp.getString(ReactCharacter.defaultRpk, null);
        if (opkJson != null) {
            if (rpkIndexPath != null || (opkBizPath != null && opkPlatformPath != null)) {
                OPKBeanV3 opkBean = GsonUtil.fromJson(opkJson, OPKBeanV3.class);
                Log.d(TAG, "appid: " + opkBean.getAppid());
                Log.d(TAG, "index: " + rpkIndexPath);
                Log.d(TAG, "biz: " + opkBizPath);
                Log.d(TAG, "platform: " + opkPlatformPath);
                ReactCharacter.Companion.setCurrentCharacter(opkBean.getAppid());
                AppManger.INSTANCE.addAppIfNotExists(new AppBeanV2(rpkIndexPath, opkBizPath,
                        opkPlatformPath, opkBean));

                //Plugin类型的OPK不进行注册
//                if (!("plugin").equalsIgnoreCase(opkBean.getType())) {
//
//                }
                registerReactNativeCharacter(opkBean.getAppid());
                // 4.12 兼容
//                registerReactNativeCharacter("react");
            }
        }

        Map<String, AppBeanV2> installList = AppManger.INSTANCE.getAllInstallList();

        if (installList != null) {
            for (String appId : installList.keySet()) {
                Log.d(TAG, "appid: " + appId);
                AppBeanV2 appBean = installList.get(appId);
//                if (appBean != null && ("plugin").equalsIgnoreCase(appBean.getRpkBean().getType())) {
//                    continue;
//                }
                if (null == ReactCharacter.Companion.getCurrentCharacter()
                        || !ReactCharacter.Companion.getCurrentCharacter().equals(appId)) {
                    registerReactNativeCharacter(appId);
                }
            }
        }
    }

    private void createReactNativeHost() {
        mReactNativeHost = new PlatformReactNativeHost(BaseApplication.sContext);
    }

    @Override
    public synchronized final ReactNativeHost getReactNativeHost() {
        if (mReactNativeHost == null) {
            createReactNativeHost();
        }
        return mReactNativeHost;
    }

    public final class PlatformReactNativeHost extends ReactNativeHost {

        PlatformReactNativeHost(Application application) {
            super(application);
        }

        @Override
        public boolean getUseDeveloperSupport() {
            return isDebug() || RNDebugReceiver.Companion.getDEBUG();
        }


        @Override
        public ReactInstanceManager getReactInstanceManager() {
            synchronized (this) {
                return super.getReactInstanceManager();
            }
        }

        @Override
        public List<ReactPackage> getPackages() {
            Log.i(TAG, "getPackages : " + MAX_MEMORY_CACHE_SIZE);
            NoOpMemoryTrimmableRegistry memoryTrimmableRegistry = NoOpMemoryTrimmableRegistry.getInstance();
            memoryTrimmableRegistry.registerMemoryTrimmable(new MemoryTrimmable() {
                @Override
                public void trim(MemoryTrimType trimType) {
                    double suggestedTrimRatio = trimType.getSuggestedTrimRatio();
                    if (MemoryTrimType.OnCloseToDalvikHeapLimit.getSuggestedTrimRatio() == suggestedTrimRatio
                            || MemoryTrimType.OnSystemLowMemoryWhileAppInBackground.getSuggestedTrimRatio() == suggestedTrimRatio
                            || MemoryTrimType.OnSystemLowMemoryWhileAppInForeground.getSuggestedTrimRatio() == suggestedTrimRatio) {
                        ImagePipelineFactory.getInstance().getImagePipeline().clearMemoryCaches();
                    }
                }
            });
            ImagePipelineConfig pipelineConfig = ImagePipelineConfig.newBuilder(getContext())
                    .setBitmapsConfig(Bitmap.Config.RGB_565)
                    .setBitmapMemoryCacheParamsSupplier(new Supplier<MemoryCacheParams>() {
                        @Override
                        public MemoryCacheParams get() {
                            return new MemoryCacheParams(
                                    MAX_MEMORY_CACHE_SIZE,
                                    Integer.MAX_VALUE,
                                    MAX_MEMORY_CACHE_SIZE,
                                    Integer.MAX_VALUE,
                                    Integer.MAX_VALUE);
                        }
                    })
                    .setMemoryTrimmableRegistry(memoryTrimmableRegistry)
                    .setMainDiskCacheConfig(DiskCacheConfig.newBuilder(getContext())
                            .setBaseDirectoryPath(sContext.getCacheDir())
                            .setBaseDirectoryName("stuff")
                            .setMaxCacheSize(MAX_DISK_CACHE_SIZE)
                            .build())
                    .setDownsampleEnabled(true)
                    .setResizeAndRotateEnabledForNetwork(true)
                    .build();

            List<ReactPackage> packageList = new ArrayList<>();
            packageList.add(new MainReactPackage(new MainPackageConfig.Builder().setFrescoConfig(pipelineConfig).build()));
            packageList.add(new CommonBridgeModuleReactPackage());
            packageList.add(new ComponentBridgeModuleReactPackage());
            packageList.add(new CameraViewPackage());
            packageList.add(new RNCWebViewPackage());
//            packageList.add(new RNGestureHandlerPackage());
            packageList.add(new RecognitionViewPackage());
            packageList.add(new RNFaceParticleViewPackage());
            packageList.add(new ReactVideoPackage());
            packageList.add(new RNCrashHandlerPackage());
//            packageList.add(new RNCViewPagerPackage());
            packageList.add(new RNEmojiPlayerViewPackage());
            packageList.add(new EncryptionReactPackager());
            packageList.add(new AudioPackage());
            packageList.add(new AndroidWifiPackage());
            packageList.add(new BlePackage());
            packageList.add(new IntentLauncherPackage());
//            packageList.add(new RNLocalizePackage());
            packageList.add(new RNRSAPackage());
            packageList.add(new Sha256Package());
            packageList.add(new RNCameraPackage());
            packageList.add(new ImageFramePackage());
            packageList.add(new BackgroundJobPackage());
            packageList.add(new LottiePackage());
            packageList.add(new JustifyTextViewPackage());
            packageList.add(new NinePatchViewPackage());
            packageList.add(new ClipIconImageViewPackage());
            packageList.add(new SajjadBlurOverlayPackage());
            packageList.add(new LinearGradientPackage());
            packageList.add(new com.BV.LinearGradient.LinearGradientPackage());
            packageList.add(new SvgPackage());
            packageList.add(new BigImagePackage());
            packageList.add(new BigImageFitPackage());
            packageList.add(new ExternalDisplayPackage());
            packageList.add(new RNFSPackage());
            packageList.add(new MuteViewPackage());
            packageList.add(new RNI18nPackage());
            if (!ProductInfo.isOverSea()) {
                packageList.add(new BaiduMapPackage());
            }
            packageList.addAll(ReactPackageInDexUtil.Companion.getPackageList(getContext()));
            packageList.add(new BubbleViewPackage());
            packageList.add(new ReactSliderPackage());
            packageList.add(new CameraFilterViewPackage());
            packageList.add(new RNZipArchivePackage());
            packageList.add(new SlideVerifyImageViewPackage());
            packageList.add(new DropShadowPackage());
            packageList.add(new UdpSocketsModule());
            packageList.add(new TcpSocketPackage());
            packageList.add(new BatteryPackage());

            List<String> diffPackages = getDiffPackageList();
            for (String className : diffPackages) {
                try {
                    Class<?> clazz = Class.forName(className);
                    packageList.add((ReactPackage) clazz.newInstance());
                } catch (ClassNotFoundException | IllegalAccessException | InstantiationException e) {
                    e.printStackTrace();
                }
            }

            if (getCustomReactNativePackage() != null) {
                for (ReactPackage reactPackage : getCustomReactNativePackage()) {
                    if (reactPackage != null) {
                        packageList.add(reactPackage);
                    }
                }
            }
            return packageList;
        }

        @Override
        public String getJSMainModuleName() {
            return "index";
        }

        @Override
        public String getJSBundleFile() {
            if (getUseDeveloperSupport()) {
                return null;
            } else {
                //v1 : 需要返回整体的bundle文件
                //v2 : 返回null
                if (RNClientManager.Companion.getInstance() == null
                        || RNClientManager.Companion.getInstance().getDataManager() == null) {
                    return null;
                }
                String name;
                try {
                    name = RNClientManager.Companion.getInstance().getDataManager()
                            .getCurrentCharcater();
                    String path = AppManger.INSTANCE.getAppIndexPathByCharacter(name);
                    if (path == null) {
                        path = AppManger.INSTANCE.getAppPlatformPathByCharacter(name);
                    }
                    return path;
                } catch (RemoteException e) {
                    return null;
                }
            }
        }

        @Override
        public ReactInstanceManager createReactInstanceManager() {
            Log.i(TAG, "createReactInstanceManager:");
            ReactMarker.logMarker(ReactMarkerConstants.BUILD_REACT_INSTANCE_MANAGER_START);
            ReactInstanceManagerBuilder builder = ReactInstanceManager.builder()
                    .setApplication(BaseApplication.sContext)
                    .setJSMainModulePath(getJSMainModuleName())
                    .setUseDeveloperSupport(getUseDeveloperSupport())
                    .setRedBoxHandler(getRedBoxHandler())
                    .setJavaScriptExecutorFactory(getJavaScriptExecutorFactory())
                    .setUIImplementationProvider(getUIImplementationProvider())
                    .setJSIModulesPackage(getJSIModulePackage())
                    .setInitialLifecycleState(LifecycleState.BEFORE_CREATE)
                    .setNativeModuleCallExceptionHandler(mRNCrashHandler);

            for (ReactPackage reactPackage : getPackages()) {
                builder.addPackage(reactPackage);
            }

            String jsBundleFile = getJSBundleFile();
            Log.d(TAG, "getJSBundleFile: " + getJSBundleFile());

            if (jsBundleFile != null) {
                builder.setJSBundleFile(jsBundleFile);
            } else {
                if (isDebug() || RNDebugReceiver.Companion.getDEBUG()) {
                    builder.setBundleAssetName(Assertions.assertNotNull(getBundleAssetName()));
                } else {
                    if (RNClientManager.Companion.getInstance() == null
                            || RNClientManager.Companion.getInstance().getDataManager() == null) {
                        return null;
                    }
                    String name;
                    try {
                        name = RNClientManager.Companion.getInstance().getDataManager()
                                .getCurrentCharcater();
                    } catch (RemoteException e) {
                        return null;
                    }
                    //如果当前为V1模式加载bundle 为null需要return
                    OPKBeanV3 opkBeanV3 = AppManger.INSTANCE.getRPKByCharacter(name);
                    if (opkBeanV3 == null) {
                        return null;
                    }
                    String opkLoad = opkBeanV3.getOpkLoad();
                    if (!TextUtils.isEmpty(opkLoad)
                            && (("v2").equalsIgnoreCase(opkLoad) || ("v3").equalsIgnoreCase(opkLoad))) {
                        builder.setBundleAssetName(Assertions.assertNotNull(getBundleAssetName()));
                    } else {
                        return null;
                    }
                }
            }

            Log.e(TAG, "createReactInstanceManager: Success!");
            mReactInstanceManager = builder.build();
            ReactMarker.logMarker(ReactMarkerConstants.BUILD_REACT_INSTANCE_MANAGER_END);
            return mReactInstanceManager;
        }
    }

    /**
     * 获取RN版本间有差异的Package
     */
    public final List<String> getDiffPackageList() {
        if (BuildConfig.RN_VERSION.equals("069")) {
            return new ArrayList<String>() {{
                add("com.zoontek.rnlocalize.RNLocalizePackage");
                add("com.swmansion.gesturehandler.RNGestureHandlerPackage");
                add("com.reactnativepagerview.PagerViewPackage");
                add("com.reactnativecommunity.art.ARTPackage");
                add("com.reactnativecommunity.netinfo.NetInfoPackage");
                add("org.linusu.RNGetRandomValuesPackage");
            }};
        } else {
            return new ArrayList<String>() {{
                add("com.reactcommunity.rnlocalize.RNLocalizePackage");
                add("com.swmansion.gesturehandler.react.RNGestureHandlerPackage");
                add("com.reactnativecommunity.viewpager.RNCViewPagerPackage");
            }};
        }
    }

    public final ReactContext getReactContext() {
        return getReactNativeHost().getReactInstanceManager().getCurrentReactContext();
    }

    public final boolean isDebug() {
        String packageName = sContext.getPackageName();
        try {
            Class buildConfig = Class.forName(packageName + ".BuildConfig");
            Field debug = buildConfig.getField("DEBUG");
            debug.setAccessible(true);
            Log.d(TAG, "isDebug: " + debug.getBoolean(null));
            return debug.getBoolean(null);
        } catch (IllegalAccessException e) {
            Log.d(TAG, "isDebug: IllegalAccessException");
        } catch (NoSuchFieldException e) {
            Log.d(TAG, "isDebug: NoSuchFieldException");
        } catch (ClassNotFoundException e) {
            Log.d(TAG, "isDebug: ClassNotFoundException");
        }
        return false;
    }

    public String getDefaultReactNativeAppId() {
        String systemOSType = RobotSettings.getGlobalSettings(BaseApplication.getContext(), Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE, "");
        Log.d(TAG, "getDefaultReactNativeAppId systemOSType=" + systemOSType);
        if (ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()) {
            if (TextUtils.equals(systemOSType, Definition.OSType.AGENTOS.getValue())) {
                return "system_336f7c6446c0575d42b793a2e30492f2";
            } else {
                return "system_a111fbbbb02ec018d4ad79d1d63c7222";
            }
        } else if (ProductInfo.isMeissaPlus()) {
            return "system_f46e7debff0c847f57474351787a6dfa";
        } else {
            return "baoxiaomi_91d5a88c4eafda508216fb516dad4a80";
        }

    }

    public boolean getDefaultReactNativeAutoSwitch() {
        return false;
    }

    public boolean isForceSwitchRomOpk() {
        return true;
    }

    public String getAssertEmojiPath() {
        return null;
    }

    public List<ReactPackage> getCustomReactNativePackage() {
        return null;
    }

    public boolean isSupportBigScreen() {
        return false;
    }

    protected void onMainProcessInit() {
    }

    protected void onRNProcessInit() {
    }

    public Map<String, Boolean> getCustomReactInitParam() {
        return null;
    }

    public boolean isConfigUpdateNotify() {
        return true;
    }

}
