package com.ainirobot.platform.utils;

import android.content.Context;
import android.media.MediaMetadataRetriever;
import androidx.viewpager.widget.ViewPager;
import android.util.Log;
import android.view.animation.AccelerateDecelerateInterpolator;
import com.ainirobot.platform.bean.FixedSpeedScroller;

import java.io.File;
import java.lang.reflect.Field;

public class CommonUtils {
    private static FixedSpeedScroller mScroller = null;
    /**
     * 设置ViewPager的滑动时间
     * @param context
     * @param viewpager ViewPager控件
     * @param DurationSwitch 滑动延时
     */
    public static void controlViewPagerSpeed(Context context, ViewPager viewpager, int DurationSwitch) {
        try {
            Field mField;

            mField = ViewPager.class.getDeclaredField("mScroller");
            mField.setAccessible(true);

            mScroller = new FixedSpeedScroller(context,
                    new AccelerateDecelerateInterpolator());
            mScroller.setmDuration(DurationSwitch);
            mField.set(viewpager, mScroller);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // [解决通过MediaMetadataRetriever获取音频文件时长不准确的问题]参看 http://www.voidcn.com/article/p-rohmyanz-e.html
    public static long getDuration(String videoPath) {
        long duration = 0;
        MediaMetadataRetriever mm = new MediaMetadataRetriever();
        try {
            mm.setDataSource(videoPath);  //recordingFilePath（）为音频文件的路径
            long bitRate = Long.parseLong(mm.extractMetadata(MediaMetadataRetriever.METADATA_KEY_BITRATE)) / 1000;
            long fileSize = new File(videoPath).length();
            duration = (fileSize * 8) / (bitRate);// 单位:ms
        } catch (Exception e) {
            Log.w("CommonUtils", Log.getStackTraceString(e));
        } finally {
            mm.release();
        }
        Log.d("CommonUtils", "duration is " + duration);
        return duration;
    }
}
