/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.platform.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

/**
 * Created by Orion on 2018/6/19.
 */
public class LocalUtils {

    private static final String TAG = LocalUtils.class.getSimpleName();
    private static final String WRITE_SETTINGS = "android.permission.WRITE_SETTINGS";
    private static final String WRITE_SECURE_SETTINGS = "android.permission.WRITE_SECURE_SETTINGS";

    private static final String DEFAULT_VALUE = "";
    public static final String SHARED_PREFERENCES_NAME = "PLATFORM_SHARED_PREFERENCES_NAME";

    public static void storage2SystemSettings(Context ctx, final String key, final String value) {
        if (TextUtils.isEmpty(key) || null == ctx) {
            Log.e(TAG, "can't put system.settings a empty key or null context!");
            return;
        }

        // 检测是否有 写系统settings权限
        int writePerm = ctx.checkCallingOrSelfPermission(WRITE_SETTINGS);
        int writeSecurePerm = ctx.checkCallingOrSelfPermission(WRITE_SECURE_SETTINGS);
        if (PackageManager.PERMISSION_GRANTED != writePerm || PackageManager.PERMISSION_GRANTED != writeSecurePerm) {
            Log.e(TAG, "This app have no WRITE_SETTINGS or WRITE_SECURE_SETTINGS permission!");
            return;
        }
        // put to system.settings
        if (!Settings.Global.putString(ctx.getContentResolver(), key, value)) {
            Log.w(TAG, "Settings.Global put error! key: " + key + ", value: " + value);
        }
    }

    public static String getGlobalSettings(Context ctx, final String key, final String defValue) {
        String global = defValue;

        if (TextUtils.isEmpty(key) || null == ctx) {
            Log.w(TAG, "can't get system.settings a empty key or null context!");
            return global;
        }

        global = Settings.Global.getString(ctx.getContentResolver(), key);
        if (TextUtils.isEmpty(global)) {
            global = defValue;
        }

        return global;
    }

    public static int getGlobalSettings(Context ctx, final String key, final int defValue) {
        int global = defValue;

        if (TextUtils.isEmpty(key) || null == ctx) {
            Log.w(TAG, "can't get system.settings a empty key or null context!");
            return global;
        }

        try {
            global = Settings.Global.getInt(ctx.getContentResolver(), key);
        } catch (Settings.SettingNotFoundException e) {
            Log.w(TAG, "SettingNotFoundException key === " + key);
            global = defValue;
        }

        return global;
    }

    public static void setGlobalSettings(Context ctx, final String key, final int value) {

        if (TextUtils.isEmpty(key) || null == ctx) {
            Log.w(TAG, "can't get system.settings a empty key or null context!");
            return;
        }
        Settings.Global.putInt(ctx.getContentResolver(), key, value);
    }


    public static boolean putStringSettings(Context ctx, final String key, final String value) {

        if (TextUtils.isEmpty(key) || null == ctx) {
            Log.w(TAG, "putStringSettings: context=" + ctx + " key=" + key);
            return false;
        }
        return Settings.Global.putString(ctx.getContentResolver(), key, value);
    }


    public static void set(Context context, String key, String value) {
        SharedPreferences.Editor editor = getPreferences(context).edit();
        editor.putString(key, value);
        editor.commit();
    }

    public static String get(Context context, String key) {
        return getPreferences(context).getString(key, DEFAULT_VALUE);
    }

    private static SharedPreferences getPreferences(Context ctx) {
        if (null == ctx) {
            Log.e(TAG, "ctx is null!");
            return null;
        }
        return ctx.getSharedPreferences(SHARED_PREFERENCES_NAME, Context.MODE_PRIVATE);
    }

}
