package com.ainirobot.platform.utils;

import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;

public class StaticModeUtil {

    private static final String TAG = "StaticModeUtil";

    public static boolean isInStaticMode(){

        boolean result = false;

        String modeSwitch = SettingDataHelper.getInstance().getStaticModeSwitch();

        Log.d(TAG,"modeSwitch: " + modeSwitch);

        if(modeSwitch.equals("1") && isModelMatch()){
            result = true;
        }
        Log.d(TAG,"isInStaticMode: " + result);
        return result;
    }

    public static boolean isModelMatch(){

        boolean result = false;

        String systemModel  = SystemUtils.getSystemModel();
        Log.d(TAG,"SystemModel: " + systemModel);

        //mini 或者 mini海外版
        if(systemModel.equals("OS-R-SD01B") || systemModel.equals("OS-R-SD03") ){
            result = true;
        }
        //小秘1.0 或者 小秘1.1
        if (systemModel.equals("CM-GB01N") || systemModel.equals("CM-GB01C")){
           result = true;
        }
        //小秘2.0
        if(ProductInfo.isMeissa2()){
            result = true;
        }
        return result;
    }

}
