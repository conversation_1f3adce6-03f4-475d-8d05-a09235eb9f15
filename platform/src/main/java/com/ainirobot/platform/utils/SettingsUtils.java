package com.ainirobot.platform.utils;

import android.content.Context;
import android.text.TextUtils;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.PlatformDef;
import com.ainirobot.platform.bean.AutoCruiseTaskInfo;

public class SettingsUtils {

    public static boolean isPreWakeOpenInSettings() {
        return LocalUtils.getGlobalSettings(BaseApplication.getContext(),
                "need_say_hello", "0").equals("1");
    }

    public static boolean isFaceWList() {
        return TextUtils.equals(RobotSettingApi.getInstance().getRobotString
                (Definition.COMPANY_IN_FACE_WHITE_LIST), "1");
    }

    public static boolean isGongFuOpenInSettings() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SMALL_ACTION)
                == Definition.ROBOT_SETTING_ENABLE;
    }

    public static boolean isMultiPersonNotTrackOpenInSettings() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_FOCUS_FOLLOW)
                != Definition.ROBOT_SETTING_ENABLE;
    }

    public static boolean isQueryLocationFindWaitPerson() {
        String strRlt = SettingsUtil.getString(BaseApplication.getContext(), "guide_wait");
        return TextUtils.equals(strRlt, "1");
    }

    public static boolean getIfNeedToSwitchFocus() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.SETTINGS_GLOBAL_SWITCH_FOLLOW_STYLE)
                == PlatformDef.FOLLOW_WHO_SPEAK;
    }

    public static boolean isAllowGuideChat() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.SWITCH_ALLOW_CHAT_WHEN_INTERPRET)
                == Definition.ROBOT_SETTING_ENABLE;
    }

    /**
     * @return false: 静默答复
     */
    public static boolean isReply() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_CHAT_REPLY)
                == Definition.ROBOT_SETTING_ENABLE;
    }

    /**
     * 获取是否开启CM bluetooth 门禁,默认值是允许.
     */
    public static boolean getIsAllowOpenCmDoor() {
        Context ctx = BaseApplication.getContext();
        if (null != ctx) {
            int allow = LocalUtils.getGlobalSettings(ctx, PlatformDef.SWITCH_ALLOW_OPEN_CM_BLUETOOTH_DOOR, PlatformDef.ALLOW_OPEN_CM_DOOR);
            return allow == PlatformDef.ALLOW_OPEN_CM_DOOR;
        } else {
            return false;
        }
    }

    /**
     * 是否开启CM bluetooth 门禁
     *
     * @param enable true note open ble door ; false note close this feature.
     */
    public static void setIsAllowOpenCmDoor(boolean enable) {
        Context ctx = BaseApplication.getContext();
        if (null != ctx) {
            LocalUtils.setGlobalSettings(ctx, PlatformDef.SWITCH_ALLOW_OPEN_CM_BLUETOOTH_DOOR,
                    (enable ? PlatformDef.ALLOW_OPEN_CM_DOOR : PlatformDef.BAN_OPEN_CM_DOOR));
        }
    }

    public static boolean getIsAllowLiteHome() {
        Context ctx = BaseApplication.getContext();
        if (null != ctx) {
            int allow = LocalUtils.getGlobalSettings(ctx, PlatformDef.SWITCH_LITE_HOME, 0);
            return allow == 1;
        } else {
            return false;
        }
    }


    public static void setIsAllowLiteHome(boolean enable) {
        Context ctx = BaseApplication.getContext();
        if (null != ctx) {
            LocalUtils.setGlobalSettings(ctx, PlatformDef.SWITCH_LITE_HOME, (enable ? 1 : 0));
        }
    }

    public static AutoCruiseTaskInfo getAutoCruiseInfo() {
        Context ctx = BaseApplication.getContext();
        if (null != ctx) {
            String patrolInfoJson =
                    LocalUtils.getGlobalSettings(ctx, PlatformDef.GLOBAL_REGULAR_PATROL, "");
            return GsonUtil.fromJson(patrolInfoJson, AutoCruiseTaskInfo.class);
        }

        return null;
    }
}