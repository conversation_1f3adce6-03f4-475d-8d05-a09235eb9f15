package com.ainirobot.platform.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.text.TextUtils
import android.util.AndroidRuntimeException
import android.util.Log
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.react.AppManger
import com.ainirobot.platform.react.client.RNClientManager
import com.facebook.react.ReactPackage
import dalvik.system.DexClassLoader
import org.json.JSONObject
import java.io.File
import java.io.IOException

//动态加载的 dex 和 so（目前由于系统现在还没解决） 要遵循下面的目录规则，要放在 opk 的 extraResource/libs/ 下
//```markdown
//extraResource
//└── libs
//├── amap
//│   ├── 3dmap-6.6.0.jar
//│   ├── config.json
//│   ├── dexlib
//│   │   ├── amap.dex
//│   │   └── amaprnclasses.dex
//│   └── jnilib
//│       └── libAMapSDK_MAP_v6_6_0.so
//└── test
//├── config.json
//├── dexlib
//│   ├── classes1.dex
//│   ├── classes2.dex
//│   ├── core-3.3.0.dex
//│   └── retrofit.dex
//└── jnilib
//├── libhello-jni.so
//└── libplus.so
//```
//安装完成之后在 sdcard 上的路径如下
//```markdown
//sdcard
//└── debug or appid
//└── extra
//└── libs
//└── test
//├── config.json
//├── dexlib
//│   ├── classes1.dex
//│   ├── classes2.dex
//│   ├── core-3.3.0.dex
//│   └── retrofit.dex
//└── jnilib
//├── libhello-jni.so
//└── libplus.so
//```
//jnilib 目录中存放 .so 文件
//dexlib 目录中存放 dex 文件
//config.json 中配置 ReactPackge 子类的 classname，如下
//```json
//{
//    "packageClassNames": [
//    "com.ainirobot.testsdk.HelloModuleReactPackage",
//    "com.ainirobot.testsdk2.Test2ReactPackage"
//    ]
//}
//```
class ReactPackageInDexUtil {
    companion object {

        val TAG = "ReactPackageInDexUtil"
        const val SO_SUFFIX = ".so"
        const val DEX_SUFFIX = ".dex"


        private val classNameList by lazy { ArrayList<String>() }
        private val dexPathList by lazy { ArrayList<String>() }
        private val soPathList by lazy { ArrayList<String>() }

        @SuppressLint("UnsafeDynamicallyLoadedCode")
        fun getPackageList(context: Context): List<ReactPackage> {
            parseClassNameAndSo()

            val list = ArrayList<ReactPackage>()
            if (classNameList.isNullOrEmpty() || dexPathList.isNullOrEmpty()) {
                return list
            }

            var sodir: File? = null
            // TODO so 加载暂时还有问题，先屏蔽掉，还有 AssertManager 要 hook 处理？
            if (!soPathList.isNullOrEmpty()) {
                sodir = context.getDir("libs", Activity.MODE_PRIVATE)
//                sodir = File(context.applicationInfo.nativeLibraryDir)
                soPathList.forEach { soPath ->
                    val soName = soPath.substring(soPath.lastIndexOf("/"))
                    if (soName.endsWith(SO_SUFFIX)) {
                        val soDestFile = File("${sodir.absolutePath}${File.separator}$soName")
                        Log.i(TAG, "soDestFile ${soDestFile.absolutePath}")

                        try {
                            if (soDestFile.exists()) {
                                soDestFile.delete()
                            }
                            soDestFile.createNewFile()
                            FileHelper.copyFile(File(soPath), File(soDestFile.absolutePath))
                            Log.i(TAG, "soDestFile exists ${File(soDestFile.absolutePath).exists()}")

                        } catch (e: IOException) {
                            e.printStackTrace()
                            Log.i(TAG, "printStackTrace")
                        }
                    }
                }
            }

            try {
                val dexPathList = dexPathList.map {
                    File(it)
                }
                val dexClassLoader = createLoader(context, FileHelper.getCacheDir(context), sodir?.absolutePath, dexPathList)

                // with classNameList
                classNameList.forEach {
                    try {
                        val packageClass = dexClassLoader.loadClass(it)
                        Log.i(TAG, "packageClass $packageClass")
                        val instance = packageClass.newInstance()
                        if (instance != null && instance is ReactPackage) {
                            list.add(instance)
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

                // without classNameList
//                val reactPackageClazz = ReactPackage::class.java
//                val reactPackageClassInDexFiles = getAllReactPackageClassInDexFiles(dexPathList,reactPackageClazz, dexClassLoader)
//                if (reactPackageClassInDexFiles.isNotEmpty()) {
//                    reactPackageClassInDexFiles.forEach { itClazz ->
//                        try {
//                            val instance = itClazz.newInstance()
//                            if (instance != null && instance is ReactPackage) {
//                                list.add(instance)
//                            }
//                        } catch (e: Exception) {
//                            e.printStackTrace()
//                        }
//                    }
//                }

//                if(list.isNotEmpty()){ // TODO 有 package，需要把 opk 中asset 的目录设置给 AssetManager
//                    SystemClock.sleep(5000)
//                    val opkPath = getOpkPath()
//                    opkPath?.apply {
//                        val addAssetPathMethod = AssetManager::class.java.getDeclaredMethod("addAssetPath", String::class.java)
//                        Log.i(TAG,"addAssetPathMethod $addAssetPathMethod")
//                        val invoke = addAssetPathMethod.invoke(context.assets, "$opkPath/assets/")
//                        Log.i(TAG,"addAssetPathMethod invoke:$invoke")
//                    }
//                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

            classNameList.clear()
            dexPathList.clear()
            soPathList.clear()
            return list
        }

        private fun createLoader(context: Context, optimizedDir: File, sodir: String?, dexFiles: List<File>): DexClassLoader {
            var path = dexFiles[0].absolutePath
            for (i in 1 until dexFiles.size) {
                path += File.pathSeparator + dexFiles[i].absolutePath
            }
            return DexClassLoader(path, optimizedDir.absolutePath, sodir, context.classLoader)
        }

        // 使用 dexFile 找到所有的 reactPackageClazz，但时间太长
//        private fun getAllReactPackageClassInDexFiles(dexFiles: List<File>, reactPackageClazz: Class<*>, classLoader: ClassLoader): List<Class<*>> {
//            val factorySubClassList = ArrayList<Class<*>>()
//            if (!dexFiles.isNullOrEmpty()) {
//                dexFiles.forEach {
//                    val dexFile = DexFile(it)
//                    val entries = dexFile.entries()
//                    while (entries.hasMoreElements()) {
//                        val loadClass = dexFile.loadClass(entries.nextElement(), classLoader)
//                        if (loadClass != null) {
//                            if (reactPackageClazz in loadClass.interfaces) {
//                                factorySubClassList.add(loadClass)
//                            }
//                        }
//                    }
//                }
//            }
//            return factorySubClassList
//        }

        private fun getOpkPath(): List<String?> {
            if (BaseApplication.getApplication().isDebug) {
                return listOf("/sdcard/debug/extra")
            }

            val appId: String? = RNClientManager.instance?.dataManager?.currentCharcater
                    ?: return listOf()
            val appInfo = AppManger.getRPKByCharacter(appId)
            when (appInfo?.opkLoad) {
                "v3" -> {
                    val list = mutableListOf(AppManger.getAppBizPathByCharacter(appId))
                    if (appInfo.type == "host") {
                        AppManger.getAllPluginInfo().forEach {
                            if (AppManger.isAppValid(it.appid)) {
                                list.add(AppManger.getAppBizPathByCharacter(it.appid))
                            }
                        }
                    }
                    return list
                }
                "v2" -> {
                    return listOf(AppManger.getAppBizPathByCharacter(appId))
                }
                "v1", null -> {
                    return listOf(AppManger.getAppIndexPathByCharacter(appId))
                }
            }

            return listOf()
        }

        private fun parseClassNameAndSo() {
            val opkPathList = getOpkPath()
            opkPathList.forEach {
                Log.i(TAG, "parseClassNameAndSo opkPath $it")
                it?.apply {
                    val pathArray = it.split("/")
                    val extraLibsPath = "${it.replace(pathArray.last(), "extra")}/libs/"
                    Log.i(TAG, "parseClassNameAndSo OpkExtraPath $extraLibsPath")
                    val libsFile = File(extraLibsPath)
                    if (libsFile.exists()) {
                        val listFiles = libsFile.listFiles()
                        listFiles.forEach { libPath ->
                            if (libPath.isDirectory) {
                                val configFile = File(libPath, "config.json")
                                if (configFile.exists()) {
                                    val confgJsonStr = FileHelper.loadJsonFromFile(configFile.absolutePath)
                                    val config = JSONObject(confgJsonStr)
                                    val packageClassNames = config.optJSONArray("packageClassNames")
                                    if (packageClassNames != null) {
                                        for (i in 0 until packageClassNames.length()) {
                                            val className = packageClassNames.optString(i)
                                            if (!TextUtils.isEmpty(className)) {
                                                if (classNameList.contains(className)) {
                                                    throw AndroidRuntimeException("Duplicate package className$className in config.json")
                                                }
                                                classNameList.add(className)
                                            }
                                        }
                                    }
                                    if (classNameList.isNotEmpty()) {
                                        val dexlib = File(libPath, "dexlib")
                                        if (dexlib.exists()) {
                                            dexPathList.addAll(dexlib.listFiles().filter {
                                                it.absolutePath.endsWith(ReactPackageInDexUtil.DEX_SUFFIX)
                                            }.map {
                                                it.absolutePath
                                            })
                                        }
                                        val jnilib = File(libPath, "jnilib")
                                        if (jnilib.exists()) {
//                                        soPathList.add(jnilib.absolutePath)
                                            soPathList.addAll(jnilib.listFiles().filter {
                                                it.absolutePath.endsWith(ReactPackageInDexUtil.SO_SUFFIX)
                                            }.map {
                                                it.absolutePath
                                            })
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 去重
            dexPathList.distinct()
            soPathList.distinct()
        }

    }
}

