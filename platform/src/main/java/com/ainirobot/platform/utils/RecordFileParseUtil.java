package com.ainirobot.platform.utils;

import android.util.Log;

import java.io.File;

/**
 * 文件名称: RecordFileParseUtil
 * 功能描述: 录音文件解析工具
 *
 * @author: <PERSON><PERSON><PERSON>
 * 创建时间: 2019/4/29 14:08
 * 责任人:
 * 备份责任人:
 * 修改人:
 * 修改内容:
 * 修改时间: 2019/4/29 14:08
 * Copyright (C) 2019 CMCM
 */
public class RecordFileParseUtil {

    /**
     * 分隔符
     */
    public final static String SEPARATOR = "#";
    /**
     * 文件后缀
     */
    public final static String SUFFIX = ".wav";

    private final static String TAG = "RecordFileParseUtil";
    private final static String PEOPLE_WAV_PATH = "/sdcard/feedback/";
    private final static int SPLIT_NUM = 3;


    /**
     * 获取录音文件目录
     *
     * @return
     */
    public static String getRecordDirectory() {
        Log.d(TAG, "getRecordDirectory: ");
        File outputPath = new File(PEOPLE_WAV_PATH);
        if (!outputPath.exists()) {
            outputPath.mkdirs();
        }
        return outputPath.getPath();
    }

    /**
     * 删除录音文件目录
     */
    public static void deleteRecordDirectory() {
        Log.d(TAG, "getRecordDirectory: ");
        File dirFile = new File(PEOPLE_WAV_PATH);
        try {
            File[] files = dirFile.listFiles();
            for (int i = 0; i < files.length; i++) {
                if (files[i].isFile()) {
                    files[i].delete();
                }
            }
        }catch (Exception e){

        }
    }
}
