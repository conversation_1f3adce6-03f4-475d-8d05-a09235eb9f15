/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.platform.utils;

import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import com.ainirobot.platform.R;
import com.ainirobot.platform.react.view.SoundProgressView;


public class ToastUtil {

    private static ToastUtil mInstance = null;
    private Toast mToast;
    private TextView mTextView;

    public void init(Context context) {
        mToast = new Toast(context);
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View toastRoot = inflater.inflate(R.layout.platform_layout_toast, null);
        mTextView = (TextView)toastRoot.findViewById(R.id.toast_text);
        mToast.setView(toastRoot);
        mToast.setGravity(Gravity.CENTER, 0, 395);
        setDuration(Toast.LENGTH_LONG);
        setText("");
    }

    public static ToastUtil getInstance() {
        if (mInstance == null) {
            mInstance = new ToastUtil();
        }
        return mInstance;
    }

    public ToastUtil setDuration(int duration) {
        if (mToast != null) {
            mToast.setDuration(duration);
        }
        return this;
    }

    /**
     * show Toast need to setText
     * @param text
     * @return
     */
    public ToastUtil setText(CharSequence text) {
        if (mTextView != null) {
            mTextView.setText(text);
        }
        return this;
    }

    public void show() {
        if (mToast != null) {
            mToast.show();
        }
    }


    private static Toast soundToast = null;
    private static View layout = null;
    private static SoundProgressView sView = null;
    public static void show(Context context, int progress) {
        if (soundToast == null) {
            soundToast = new Toast(context);
            soundToast.setGravity(Gravity.CENTER,0,0);
            layout = LayoutInflater.from(context).inflate(R.layout.platform_toast_view, null);
            sView = (SoundProgressView) layout.findViewById(R.id.sView);
            soundToast.setView(layout);
            soundToast.setDuration(Toast.LENGTH_SHORT);
        }
        sView.setProgress(progress);
        soundToast.show();
    }

    public void cancelSoundToast() {
        if (soundToast != null) {
            soundToast.cancel();
            soundToast = null;
            layout = null;
            sView = null;
        }
    }
}
