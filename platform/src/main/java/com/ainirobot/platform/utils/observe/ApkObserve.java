package com.ainirobot.platform.utils.observe;

import android.app.ActivityManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import androidx.core.content.FileProvider;
import android.util.Log;

import com.ainirobot.base.ApplicationWrapper;
import com.ainirobot.coreservice.client.permission.PermissionApi;
import com.ainirobot.coreservice.client.permission.PermissionListener;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.react.server.impl.ActivityProcessObserver;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.util.List;

import static android.content.Context.ACTIVITY_SERVICE;

final public class ApkObserve implements ISystemObserve {

    public static Handler mHandler;

    public ApkObserve() {
        mHandler = new Handler(Looper.getMainLooper());
    }

    public static ApkObserve instance = new ApkObserve();

    public static String TAG = "ApkObserve";
    private ActivityManager activityManager = (ActivityManager) BaseApplication.getContext().getSystemService(ACTIVITY_SERVICE);

    private MyPermissionListener permissionListener;

    private boolean hasInit = false;
    private InstallReceiver installReceiver;

    @Override
    public void initInvoke() {
        if (hasInit) {
            return;
        }
        hasInit = true;
        if (permissionListener == null) {
            permissionListener = new MyPermissionListener();
        }
        PermissionApi.getInstance().registerPermissionListener(permissionListener);

        IntentFilter filterInstall = new IntentFilter();
        filterInstall.addAction(InstallReceiver.ACTION_INSTALL_RESULT);
        installReceiver = new InstallReceiver();
        ApplicationWrapper.getApplicationContext().registerReceiver(installReceiver, filterInstall);
    }

    private static class InstallReceiver extends BroadcastReceiver {
        private final static String ACTION_INSTALL_RESULT = "ainirobot.intent.action.INSTALL_RESULT";

        @Override
        public void onReceive(Context context, Intent intent) {
            if (ACTION_INSTALL_RESULT.equals(intent.getAction())) {
                boolean installResult = intent.getBooleanExtra("result", false);
                String errMsg = intent.getStringExtra("error_msg");
                String taskId = intent.getStringExtra("task_id");
                String pkgName = intent.getStringExtra("package_name");
                Log.d(TAG, "install done. result:" + installResult + " errMsg:" + errMsg
                        + " taskId:" + taskId + " pkgName:" + pkgName);
                ActivityProcessObserver.INSTANCE.apkInstallResult(pkgName, installResult, errMsg, taskId);
            }
        }
    }

    private class MyPermissionListener extends PermissionListener {

        @Override
        public void activityStarting(Intent intent, String pkg) {
            //试图打开一个activity
            Log.d(TAG, "ApkObserve activityStarting intent:" + intent + ",pkg:" + pkg);
            updateRunningApps();//每次有activity打开都获取一次全部进程
            ActivityProcessObserver.INSTANCE.activityStarting(intent, pkg);
        }

        @Override
        public void activityResuming(String pkg) {
            Log.d(TAG, "ApkObserve activityResuming :" + pkg);
            updateRunningApps();
            ActivityProcessObserver.INSTANCE.activityResuming(pkg);
        }

        @Override
        public void appCrashed(String processName, int pid, String shortMsg, String longMsg, long timeMillis, String stackTrace) {
            ActivityProcessObserver.INSTANCE.appCrashed(processName, pid, shortMsg, longMsg, timeMillis, stackTrace);

        }

        @Override
        public void appEarlyNotResponding(String processName, int pid, String annotation) {
            Log.d(TAG, "ApkObserve appEarlyNotResponding :" + processName + ",pid:" + pid + ",annotation:" + annotation);
            ActivityProcessObserver.INSTANCE.appEarlyNotResponding(processName, pid, annotation);

        }

        @Override
        public void appNotResponding(String processName, int pid, String processStats) {
            Log.d(TAG, "ApkObserve appNotResponding :" + processName + ",pid:" + pid + ",processStats:" + processStats);
            ActivityProcessObserver.INSTANCE.appNotResponding(processName, pid, processStats);

        }

        @Override
        public void systemNotResponding(String msg) {
            Log.d(TAG, "ApkObserve systemNotResponding :" + msg);
            ActivityProcessObserver.INSTANCE.systemNotResponding(msg);

        }

        @Override
        public void onForegroundActivitiesChanged(int pid, int uid, boolean foregroundActivities) {
            ApkObserve.this.onForegroundActivitiesChanged(pid, uid, foregroundActivities);
        }

        @Override
        public void onProcessDied(int pid, int uid) {
            ApkObserve.this.onProcessDied(pid, uid);
        }

        public void onProcessStateChanged(int pid, int uid, int procState) {
            Log.d(TAG, "onProcessStateChanged :" + pid + ",uid:" + uid + ",procState:" + procState);
            ApkObserve.this.onProcessStateChanged(pid, uid, procState);
        }

    }

    @Override
    public void unregister() {
        //取消注册
        hasInit = false;
        PermissionApi.getInstance().unregisterPermissionListener(permissionListener);
        ApplicationWrapper.getApplicationContext().unregisterReceiver(installReceiver);
    }

    /**
     * 前台activity 改变
     *
     * @param pid
     * @param uid
     * @param foregroundActivities
     */
    private void onForegroundActivitiesChanged(int pid, int uid, boolean foregroundActivities) {
        Log.i(TAG, "onForegroundActivitiesChanged :" + pid + ",uid:" + uid + ",foregroundActivities:" + foregroundActivities);
        ActivityProcessObserver.INSTANCE.onForegroundActivitiesChanged(pid, uid, foregroundActivities);
    }

    /**
     * 进程状态改变
     *
     * @param pid
     * @param uid
     * @param procState
     */
    private void onProcessStateChanged(int pid, int uid, int procState) {
        Log.d(TAG, "onProcessStateChanged :" + pid + ",uid:" + uid + ",procState:" + procState);
        ActivityProcessObserver.INSTANCE.onProcessStateChanged(pid, uid, procState);
    }

    private void onProcessDied(int pid, int uid) {
        //进程死亡都会走到这里
        ActivityProcessObserver.INSTANCE.onProcessDied(pid, uid);
        String name = getProcessName(pid);
        Log.i(TAG, "onProcessDied :" + pid + ",uid:" + uid + " name:" + name);
    }

    private volatile List<ActivityManager.RunningAppProcessInfo> runningApps;
    private Runnable updateRunningAppsTask = new Runnable() {
        @Override
        public void run() {
            runningApps = activityManager.getRunningAppProcesses();
        }
    };

    public void updateRunningApps() {
        mHandler.postDelayed(updateRunningAppsTask, 60);
    }

    public String getProcessName(int pid) {
        if (runningApps != null && runningApps.size() > 0) {
            for (ActivityManager.RunningAppProcessInfo app : runningApps) {
                if (app.pid == pid) {
                    return app.processName;
                }
            }
        }
        return "";
    }

    public ActivityManager.RunningAppProcessInfo getAppProcessInfo(int pid) {
        if (runningApps != null && runningApps.size() > 0) {
            for (ActivityManager.RunningAppProcessInfo app : runningApps) {
                if (app.pid == pid) {
                    return app;
                }
            }
        }
        return null;
    }

    private String getAppPkg(int pid) {
        String processName = "";
        List<ActivityManager.RunningAppProcessInfo> list = activityManager.getRunningAppProcesses();
        for (ActivityManager.RunningAppProcessInfo info : list) {
            if (info.pid == pid) {
                processName = info.processName;
                break;
            }
        }
        return processName;
    }


//    安装apk，filePath为apk文件路径，如/mnt/sdcard/ApiDemos.apk
//    execCommand("pm","install","-f",filePath);
//    卸载apk，packageName为包名，如com.example.android.apis
//	  execCommand("pm","uninstall", packageName);

    /**
     * @param command
     * @return
     */
    public static String execCommand(String... command) {
        Process process = null;
        InputStream errIs = null;
        InputStream inIs = null;
        String result = "";

        try {
            process = new ProcessBuilder().command(command).start();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            int read = -1;
            errIs = process.getErrorStream();
            while ((read = errIs.read()) != -1) {
                baos.write(read);
            }

            inIs = process.getInputStream();
            while ((read = inIs.read()) != -1) {
                baos.write(read);
            }
            result = new String(baos.toByteArray());

            if (inIs != null) {
                inIs.close();
            }
            if (errIs != null) {
                errIs.close();
            }
            process.destroy();
        } catch (IOException e) {

            result = e.getMessage();
        }

        return result;
    }


    /**
     * 安装应用
     *
     * @param path
     */
    public static void installApk(String path) {
        File file = new File(path);
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        if (Build.VERSION.SDK_INT >= 24) { //Android 7.0及以上
            Uri apkUri = FileProvider.getUriForFile(BaseApplication.getContext(), "com.ainirobot.platform.fileprovider", file);
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
        } else {
            intent.setDataAndType(Uri.fromFile(file), "application/vnd.android.package-archive");
        }
        BaseApplication.getContext().startActivity(intent);

    }

    /**
     * 卸载应用
     *
     * @param packageName
     */
    void uninstall(String packageName) {

        Uri uri = Uri.fromParts("package", packageName, null);
        Intent intent = new Intent(Intent.ACTION_DELETE, uri);
        BaseApplication.getContext().startActivity(intent);
    }

    Method forceStopPackageMethod = null;

    /**
     * @param packageName
     */
    public void forceStopPackage(String packageName) {

        try {
            if (forceStopPackageMethod == null) {
                forceStopPackageMethod = Class.forName("android.app.ActivityManager").getMethod("forceStopPackage", String.class);
            }
            forceStopPackageMethod.invoke(activityManager, packageName);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}

