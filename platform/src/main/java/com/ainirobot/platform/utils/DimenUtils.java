package com.ainirobot.platform.utils;

import android.content.Context;
import android.util.DisplayMetrics;
import android.view.WindowManager;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/15.
 */

public class DimenUtils {

    private static DisplayMetrics mMetrics = null;
    private static int mStatusBarHeight = 0;
    private static Context mContext;


    public static void setContext(Context context) {
        mContext = context;
    }


    public static DisplayMetrics getDisplayMetrics() {
        if (mMetrics != null) {
            return mMetrics;
        }
        if (mContext != null && mContext.getResources() != null) {
            mMetrics = mContext.getResources().getDisplayMetrics();
        }
        return mMetrics;
    }

    public static int getStatusBarHeight() {
        if (mStatusBarHeight != 0) {
            return mStatusBarHeight;
        }
        Class<?> localClass;
        try {
            localClass = Class.forName("com.android.internal.R$dimen");
            Object localObject = localClass.newInstance();
            int resId = Integer.parseInt(localClass.getField("status_bar_height").get(localObject).toString());
            mStatusBarHeight = mContext.getResources().getDimensionPixelSize(resId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return mStatusBarHeight;
    }

    public static int getScreenWidthPixels() {
        if (mMetrics == null) {
            getDisplayMetrics();
        }
        return mMetrics.widthPixels;
    }

    public static int getScreenHeightPixels() {
        if (mMetrics == null) {
            getDisplayMetrics();
        }
        WindowManager windowMgr = (WindowManager)mContext.getSystemService(Context.WINDOW_SERVICE);
        windowMgr.getDefaultDisplay().getRealMetrics(mMetrics);
        return mMetrics.heightPixels;
    }

    public static int dp2Px(float dp) {
        if (mMetrics == null) {
            getDisplayMetrics();
        }
        return (int) (dp * mMetrics.density + 0.5f);
    }

    public static int sp2Px(int sp) {
        if (mMetrics == null) {
            getDisplayMetrics();
        }
        return (int) (sp * mMetrics.scaledDensity + 0.5f);
    }

    public static int px2Dp(int px) {
        if (mMetrics == null) {
            getDisplayMetrics();
        }
        return (int)(px / mMetrics.density + 0.5f);
    }

    public static int px2Sp(int sp) {
        if (mMetrics == null) {
            getDisplayMetrics();
        }
        return (int)(sp / mMetrics.scaledDensity + 0.5f);
    }
}
