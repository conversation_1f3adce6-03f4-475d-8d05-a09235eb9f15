package com.ainirobot.platform.utils

import android.util.Log
import com.facebook.react.ReactInstanceManager
import com.facebook.react.bridge.JSBundleLoader
import com.facebook.react.bridge.ReactContext
import java.lang.reflect.Field

/***
 ** <AUTHOR>
 ** @email <EMAIL>
 ** @date 2019-06-14
 **/

object ReactHelper {

    const val TAG: String = "ReactHelper";
    /**
     * 预加载ReactNative环境， 第一次
     * */
    fun loadReactJSOnCreate(reactInstanceManager: ReactInstanceManager, bundleFilePath: String, listener: ((context: ReactContext?) -> Unit)) {
        loadReactJS(reactInstanceManager, bundleFilePath, true, listener)
    }

    /**
     * 预加载ReactNative环境， 再次
     * */
    fun loadReactJSOnRecreate(reactInstanceManager: ReactInstanceManager, bundleFilePath: String, listener: ((context: ReactContext?) -> Unit)) {
        loadReactJS(reactInstanceManager, bundleFilePath, false, listener)
    }

    /**
     * 整包的reload
     * */
    fun reloadReactJS(reactInstanceManager: ReactInstanceManager) {
        if (reactInstanceManager.hasStartedCreatingInitialContext()) {
            reactInstanceManager.recreateReactContextInBackground()
        } else {
            reactInstanceManager.createReactContextInBackground()
        }
    }

    /**
     * 预加载ReactNative环境
     * */
    fun loadReactJS(reactInstanceManager: ReactInstanceManager, bundleFilePath: String, isCreate: Boolean, listener: ((context: ReactContext?) -> Unit)) {
        Log.i(TAG, "loadReactJS")
        val jsBundleField: Field?
        try {
            val RIManagerClazz = reactInstanceManager.javaClass
            jsBundleField = RIManagerClazz.getDeclaredField("mBundleLoader")
            jsBundleField.isAccessible = true
            jsBundleField.set(reactInstanceManager, JSBundleLoader.createFileLoader(bundleFilePath))
            if (!reactInstanceManager.hasStartedCreatingInitialContext()) {
                reactInstanceManager.createReactContextInBackground()
            } else {
                reactInstanceManager.recreateReactContextInBackground()
            }
            reactInstanceManager.addReactInstanceEventListener(object : ReactInstanceManager.ReactInstanceEventListener {
                override fun onReactContextInitialized(context: ReactContext?) {
                    reactInstanceManager.removeReactInstanceEventListener(this)
                    listener.invoke(context)
                }
            })
        } catch (e: NoSuchFieldException) {
            e.printStackTrace()
        }
    }
}