package com.ainirobot.platform.utils

import android.content.Context
import android.media.AudioManager
import android.media.IAudioService
import android.os.ServiceManager
import android.util.Log
import kotlinx.coroutines.*
import java.lang.reflect.Method
import java.lang.reflect.Proxy
import kotlin.reflect.KMutableProperty
import kotlin.reflect.full.memberFunctions
import kotlin.reflect.full.staticProperties
import kotlin.reflect.jvm.isAccessible

object AudioManagerProxy {

    private const val TAG = "AudioManagerProxy"

    /**
     * 单次调用超时时间2s
     */
    private const val TIMEOUT = 2 * 1000L

    @OptIn(DelicateCoroutinesApi::class)
    private val thread = newSingleThreadContext("AudioManagerWrapper")

    private var audioImpl: IAudioService? = null
    private var audioProxy: IAudioService? = null

    fun init(context: Context) {
        GlobalScope.launch(thread) {
            if (audioProxy == null) {
                audioProxy = createAudioProxy()
                hookAudioManager(context)
            }
        }

        Log.d(TAG, "Audio is alive : ${isAudioAlive()}")
    }

    fun isAudioAlive(): Boolean {
        val binder = ServiceManager.getService("media.audio_flinger")
        return binder?.pingBinder() ?: false
    }

    private fun hookAudioManager(context: Context) {
        val field =
            AudioManager::class.staticProperties.find { it.name == "sService" } as KMutableProperty<IAudioService>?
        field?.let {
            it.isAccessible = true
            it.setter.call(audioProxy)
        }
    }

    private fun createAudioProxy(): IAudioService {
        return Proxy.newProxyInstance(
            AudioManagerProxy.javaClass.classLoader,
            arrayOf(IAudioService::class.java)
        ) { proxy, method, args ->
            invokeWithTimeout(proxy, method, args)
        } as IAudioService
    }

    private fun invokeWithTimeout(proxy: Any, method: Method, args: Array<*>?): Any? {
        var result: Any? = null
        Log.d(TAG, "AudioManager[${method.name}] invoke start : ${args?.contentToString()}")
        runBlocking {
            result = withTimeoutOrNull(TIMEOUT) {
                var returnResult: Any? = null
                GlobalScope.launch(thread) {
                    returnResult = invoke(proxy, method, args)
                }.join()
                returnResult
            } ?: getTypeDefault(method.returnType)
        }
        Log.d(TAG, "AudioManager[${method.name}] invoke end : $result")
        return result
    }

    private fun invoke(proxy: Any, method: Method, args: Array<*>?): Any? {
        val audioService = getService() ?: return null
        Log.d(TAG, "AudioManager[${method.name}] invoke real : ${args?.contentToString()}")
        val func = audioService::class.memberFunctions.find { it.name == method.name }
        return if (args == null) {
            func?.call(audioService)
        } else {
            func?.call(audioService, *args)
        }
    }

    private fun getService(): IAudioService? {
        if (audioImpl == null) {
            val binder = ServiceManager.getService(Context.AUDIO_SERVICE)
            audioImpl = IAudioService.Stub.asInterface(binder)
        } else {
            audioImpl
        }
        return audioImpl
    }

    private fun getTypeDefault(type: Class<*>): Any? {
        if (!type.isPrimitive) {
            return null
        }
        return when (type.name) {
            "string" -> "timeout"
            "boolean" -> false
            "void" -> "timeout"
            else -> 0
        }
    }
}