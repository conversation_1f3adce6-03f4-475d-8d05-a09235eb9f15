package com.ainirobot.platform.utils;

import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.platform.bi.annotation.Mode;
import com.ainirobot.platform.bi.annotation.SpeechResponse;
import com.ainirobot.platform.bi.wrapper.ReportControl;
import com.ainirobot.platform.bi.wrapper.manager.bi.impl.SpeechResponsePoint;
import com.ainirobot.platform.bi.wrapper.utils.ParamLegalCheckUtils;
import com.ainirobot.platform.bi.wrapper.utils.ShareDataUtils;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * check request type
 *
 * @version V1.0.0
 * @date 2019/5/20 21:44
 */
public class SpeechUtils {

    private static final String TAG = "SpeechUtils";

    /**
     * report speech response result
     *
     * @param params   speech nlp result
     * @param response speech response result
     */
    public static void checkSpeechResponse(String params, @SpeechResponse int response) {
        if (isSpeechRequest(params)) {
            String sid = parseSid(params);
            if (TextUtils.isEmpty(sid)) {
                Log.i(TAG, "checkSpeechParam: not si speech request,sid don't exit");
                return;
            }
            ReportControl.getInstance().reportMsg(new SpeechResponsePoint(sid, response,
                    ShareDataUtils.getPackageName()));
        }
    }

    /**
     * parse nlp result to obtain sid
     *
     * @param params parse speech nlp params sid
     * @return speech sid
     */
    public static String parseSid(String params) {
        Log.i(TAG, "parseSid: params:" + params);
        if (TextUtils.isEmpty(params)) {
            return null;
        }
        try {
            JSONObject jsonObject = new JSONObject(params);
            return jsonObject.getString("sid");
        } catch (JSONException e) {
            return null;
        }
    }

    /**
     * check sid weather is empty
     *
     * @param params sid
     * @return sid is empty return true otherwise return false
     */
    public static boolean isSpeechRequest(String params) {
        return !TextUtils.isEmpty(parseSid(params));
    }

    /**
     * resolver semantics trigger way
     *
     * @param params query location module semantics params
     * @return semantics queryType 1-voice 2-query by text
     */
    public static int parseTriggerMode(@NonNull String params) {
        ParamLegalCheckUtils.isNull(params);
        int queryType = Mode.MODE_BY_OTHER;
        try {
            JSONObject jsonObject = new JSONObject(params);
            queryType = jsonObject.optInt("queryType");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return queryType;
    }
}
