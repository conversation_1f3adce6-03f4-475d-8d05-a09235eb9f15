
package com.ainirobot.platform.utils;

import com.orionstar.eve.rpkfile.engine.RpkVerifier;

import java.io.File;
import java.security.cert.X509Certificate;
import java.util.List;


public class OpkFile {
    /*
        RPK Sign Verify

        return 0 : succeed
        rertun -1: failed
    * */
    public static int verify(File rpkpath) {
        return verifyInternal(rpkpath);
    }

    private static int verifyInternal(File inFile){
        try {
            RpkVerifier.Result result = new RpkVerifier.Builder(inFile)
                    .build()
                    .verify();

            if (result.isVerified()) {
                System.out.println("Verify succeed!!!");

                List<X509Certificate> certificateList = result.getSignerCertificates();
                for (X509Certificate certificate : certificateList) {
                    System.out.println("\t" + certificate.getIssuerX500Principal().getName());
                }

                return 0;

            } else {
                System.out.println("Verify failed !!!");
                return -1;
            }

        } catch ( Exception e) {
            System.out.println("Verify failed !!! engine exception. ");

            e.printStackTrace();

            return -2;
        }
    }
}
