/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.platform.utils;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.listener.Person;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class VisionUtils {

    private static final String TAG = VisionUtils.class.getSimpleName();
    private static final String MESSAGE_TIMEOUT = "timeout";
    private static final String MESSAGE_NO_REGISTERED = "HWService not registered";
    private static final int CODE_OK = 0;

    public static boolean isRemoteDetectSuccess(String jsonMsg) {
        if (jsonMsg == null || MESSAGE_TIMEOUT.equals(jsonMsg)
                || MESSAGE_NO_REGISTERED.equals(jsonMsg)) {
            return false;
        }

        try {
            JSONObject json = new JSONObject(jsonMsg);
            int code = json.optInt("code", -1);
            if (CODE_OK != code) {
                return false;
            }
        } catch (JSONException e) {
            return false;
        }
        return true;
    }

    public static Person updatePersonData(Person person, String jsonMsg, boolean updateAction) {
        if (person == null) {
            return null;
        }
        try {
            JSONObject json = new JSONObject(jsonMsg);
            String remoteReqId = json.optString("reqId");
            String remoteWakeupId = json.optString("wakeupId");
            JSONObject obj = new JSONObject(json.optString("data"));
            String people = obj.optString("people", "");
            if(!TextUtils.isEmpty(people)){
                JSONObject personObj = new JSONObject(people);
                String name = personObj.optString("name");
                int age = personObj.optInt("age", 0);
                String role = personObj.optString("role");
                String gender = personObj.optString("gender");
                String userId = personObj.optString("user_id");
                int roleId = personObj.optInt("role_id", 0);
                String avatarUrl = personObj.optString("avatar_url");
                String staffMobile = personObj.optString("staff_mobile");
                String staffJob = personObj.optString("staff_job");
                String staffDept = personObj.optString("staff_dept");
                boolean isStaff = personObj.optInt("is_staff") == 1;
                String remoteFaceId = personObj.optString("face_id");
                String registerTime = personObj.optString("user_register_time");
                boolean isNewUser = personObj.optBoolean("user_is_new");
                String quality = personObj.optString("quality");
                person.setUserId(userId);
                person.setQuality(quality);
                person.setName(name);
                person.setStaff(isStaff);
                person.setAge(age);
                person.setRole(role);
                person.setGender(gender);
                person.setRoleId(roleId);
                person.setAvatarUrl(avatarUrl);
                person.setStaffMobile(staffMobile);
                person.setStaffJob(staffJob);
                person.setStaffDept(staffDept);
                person.setRemoteReqId(remoteReqId);
                person.setRemoteFaceId(remoteFaceId);
                person.setUserRegisterTime(registerTime);
                person.setRemoteWakeupId(remoteWakeupId);
                person.setNewUser(isNewUser);
            }
            JSONArray actions = obj.optJSONArray("actions");
            if (updateAction && actions != null && actions.length() > 0) {
                ArrayList<Person.WelcomeAction> list = new ArrayList<>();
                for (int i = 0; i < actions.length(); i++) {
                    try {
                        JSONObject object = actions.getJSONObject(i);
                        Person.WelcomeAction action = person.obtainNewWelcomeAction();
                        if ("play_tts".equals(object.optString("action"))) {
                            action.setAction("play_tts");
                            JSONObject object1 = new JSONObject(object.optString("tts"));
                            action.setValue(object1.optString("value"));
                            action.setRecommend(object.optString("recommend"));
                        } else if("query_by_text".equals(object.optString("action"))){
                            action.setAction("query_by_text");
                            action.setValue(object.optString("param"));
                            action.setConfirmTTS(object.optString("confirm_tts"));
                            action.setHelloTTS(object.optString("hello_tts"));
                            action.setIsConfirm(object.optInt("enable_confirm"));
                        }
                        list.add(action);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
                person.setWelcomeActions(list);
            }

            JSONArray configs = obj.optJSONArray("configs");
            if (configs != null && configs.length() > 0) {
                ArrayList<Person.Config> configList = new ArrayList<>();
                for (int i = 0; i < configs.length(); i++) {
                    try {
                        JSONObject configObj = configs.getJSONObject(i);
                        Person.Config config = person.obtainNewConfig();
                        config.setType(configObj.optString("type"));

                        JSONObject configDetails = configObj.optJSONObject("config");
                        if (configDetails != null) {
                            Person.ConfigDetail configDetail = new Person.ConfigDetail();
                            configDetail.setConfigId(configDetails.optString("config_id"));
                            configDetail.setConfigVersion(configDetails.optString("config_version"));
                            configDetail.setLang(configDetails.optString("lang"));
                            configDetail.setRangeType(configDetails.optString("range_type"));

                            config.setConfig(configDetail);
                        }
                        configList.add(config);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
                person.setConfigs(configList);
            }


            Log.d(TAG, "updatePersonData name:" + person.getName() + " quality " + person.getQuality());
        } catch (JSONException e) {
            e.printStackTrace();
        } finally {
            return person;
        }
    }

    public static void deletePictures(final List<String> fileNames) {
        if (fileNames == null || fileNames.size() <= 0) {
            Log.i(TAG, "file name length error, return!!");
            return;
        }
        for (String filename : fileNames) {
            File file = new File(filename);
            if(file.exists() && file.isFile()){
                Log.i(TAG, "delete file:"+filename);
                file.delete();
            }else {
                Log.i(TAG, "no exits, file name:"+fileNames);
            }
        }
    }

    /*
     * 过滤掉拼音 eg:(chao2)
     * @param tempText
     * @return
     */
    public static String filterPinyin(String tempText) {
        String regex1 = "(\\([a-z]+[1-5]\\))";
        String regex2 = "(\\(#[1-3]\\))";
        String filterText = tempText.replaceAll(regex1, "");
        filterText = filterText.replaceAll(regex2, "");
        return filterText;
    }
}
