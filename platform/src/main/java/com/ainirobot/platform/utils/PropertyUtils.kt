/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.platform.utils

import android.os.Environment
import android.text.TextUtils
import com.ainirobot.platform.react.network.NetworkConstant
import com.ainirobot.remotecontrolservice.utils.IOUtils
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.*

object PropertyUtils {
    private val TAG = PropertyUtils::class.java.simpleName
    private val CONFIG_FILE = "/robot/config/remote.properties"

    val config: String?
        get() = getProperty("config")

    val domain: String
        get() {
            val domain = getProperty("domain")
            return if (TextUtils.isEmpty(domain)) NetworkConstant.DOMAIN else domain!!
        }

    val orionBaseDomain: String
        get() {
            val domain = getProperty("OrionBaseDomain")
            return if (TextUtils.isEmpty(domain)) NetworkConstant.ORION_BASE_DOMAIN else domain!!
        }

    fun getProperty(key: String, path: String): String? {
        if (TextUtils.isEmpty(key)) {
            return null
        }

        val root = Environment.getExternalStorageDirectory()
        val file = File(root, path)
        val props = loadProperty(file.path)
        return props?.getProperty(key)
    }

    private fun getProperty(key: String): String? {
        return getProperty(key, CONFIG_FILE)
    }

    fun setProperty(key: String, value: String, path: String): Boolean {
        if (TextUtils.isEmpty(key)) {
            return false
        }

        val root = Environment.getExternalStorageDirectory()
        val file = File(root, path)
        val props = loadProperty(file.path)
        if (props != null) {
            props[key] = value
            return saveProperty(file.path, props)
        }
        return false
    }

    private fun loadProperty(path: String): Properties? {
        var fileInputStream: FileInputStream? = null

        val file = File(path)
        if (!file.exists()) {
            return null
        }

        val properties = Properties()
        try {
            fileInputStream = FileInputStream(path)
            properties.load(fileInputStream)
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        } finally {
            IOUtils.close(fileInputStream)
        }
        return properties
    }

    private fun saveProperty(path: String, properties: Properties): Boolean {
        var out: FileOutputStream? = null
        try {
            val file = File(path)
            if (!file.exists()) {
                file.createNewFile()
            }
            out = FileOutputStream(file)
            properties.store(out, null)
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        } finally {
            IOUtils.close(out)
        }
        return true
    }
}
