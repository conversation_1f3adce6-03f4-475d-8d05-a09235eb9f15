package com.ainirobot.platform.utils;

import android.graphics.Bitmap;
import android.os.Environment;
import androidx.annotation.NonNull;
import android.text.TextUtils;

import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc 文件操作工具类
 * @date 2018/5/8
 */
public class ReceptionFileUtils {

    private static final String CACH_DIR = "robot/cache";
    public static final String DIR_DEFAULT = "photo821";

    private static File getRoot(){
        return Environment.getExternalStorageDirectory();
    }

    private static File getCacheDir() {
        File root = getRoot();
        File dir = new File(root, CACH_DIR);
        if (dir.exists()) {
            dir.mkdirs();
        }
        return dir;
    }

    public static File getFile() {
        return getFile(null);
    }

    public static File getFile(String fileName) {
        return getFile(DIR_DEFAULT, fileName);
    }

    public static File getFile(@NonNull String dirName, String fileName) {
        File logsDir = getCacheDir();
        File dir = new File(logsDir, dirName);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        if (fileName == null) {
            fileName = "photo_" + getTime() + ".jpg";
        }
        return new File(dir, fileName);
    }

    private static String getTime() {
        return new SimpleDateFormat("yyyyMMdd_HHmmss_SSS").format(new Date(System.currentTimeMillis()));
    }

    //保存照片
    public static String saveBitmap(Bitmap b) {
        File saveFile = getFile();
        try {
            FileOutputStream fout = new FileOutputStream(saveFile);
            BufferedOutputStream bos = new BufferedOutputStream(fout);
            b.compress(Bitmap.CompressFormat.JPEG, 100, bos);
            bos.flush();
            bos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return saveFile.getAbsolutePath();
    }

    /**
     * 从SD卡中读取文件内容
     * @param filePath 文件路径
     * @return
     */
    public static String getFileContent(String filePath){
        File file = new File(filePath);
        String str = "";
        StringBuffer content = new StringBuffer();
        InputStream is = null;
        InputStreamReader input = null;
        BufferedReader reader = null;
        try {
            is = new FileInputStream(file);
            input = new InputStreamReader(is, "UTF-8");
            reader = new BufferedReader(input);
            while ((str = reader.readLine()) != null) {
                content.append(str);
            }

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            JFileHelper.closeIOStream(is, input, reader);
           return content.toString();
        }

    }

    public static boolean checkFileExist(String filePath) {
        boolean isExist = false;
        if (!TextUtils.isEmpty(filePath)) {
            File file = new File(filePath);
            return file.exists();
        }
        return isExist;
    }
}
