package com.ainirobot.platform.utils

import android.os.Handler
import android.os.HandlerThread

class BackgroundThread : HandlerThread("BackgroundThread", android.os.Process.THREAD_PRIORITY_DEFAULT) {
    companion object {

        private var sInstance: BackgroundThread? = null
        private var sHandler: Handler? = null

        private fun ensureThreadLocked() {
            if (sInstance == null) {
                sInstance = BackgroundThread()
                sInstance!!.start()
                sHandler = Handler(sInstance!!.looper)
            }
        }

        fun get(): BackgroundThread? {
            synchronized(BackgroundThread::class.java) {
                ensureThreadLocked()
                return sInstance
            }
        }

        val handler: Handler?
            get() = synchronized(BackgroundThread::class.java) {
                ensureThreadLocked()
                return sHandler
            }

        fun post(runnable: Runnable) {
            synchronized(BackgroundThread::class.java) {
                ensureThreadLocked()
                sHandler!!.post(runnable)
            }
        }

        fun postDelayed(runnable: Runnable, nDelay: Long) {
            synchronized(BackgroundThread::class.java) {
                ensureThreadLocked()
                sHandler!!.postDelayed(runnable, nDelay)
            }
        }

        fun removeTask(runnable: Runnable) {
            synchronized(BackgroundThread::class.java) {
                ensureThreadLocked()
                sHandler!!.removeCallbacks(runnable)
            }
        }
    }
}
