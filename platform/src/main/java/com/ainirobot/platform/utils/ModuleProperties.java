/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.utils;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.platform.BaseApplication;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Properties;

/**
 * 保存ModuleApp属性数据
 */
public class ModuleProperties {
    private static final String TAG = "ModuleProperties";

    private static final String PROPERTIES_NAME = "ModuleApp";
    private static final String DEFAULT_CHARACTER = "default_character";

    public static int getInt(String key, int defaultValue) {
        String value = getProperties(key);
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            e.printStackTrace();
            return defaultValue;
        }
    }

    public static boolean setInt(String key, int value) {
        return setProperties(key, String.valueOf(value));
    }

    public static boolean setProperties(String key, String value) {
        Log.i(TAG, "setProperties: key=" + key + " value=" + value);
        Properties properties = getProperties();
        if (properties != null) {
            properties.setProperty(key, value);
            return saveProperties(properties);
        }
        return false;
    }

    public static String getProperties(String key) {
        Log.i(TAG, "getProperties: key=" + key);
        Properties properties = getProperties();
        return properties.getProperty(key);
    }

    private static boolean saveProperties(Properties properties) {
        Context context = BaseApplication.getContext();
        FileOutputStream fos = null;
        try {
            fos = context.openFileOutput(PROPERTIES_NAME, Context.MODE_PRIVATE);
            properties.store(fos, "config");
        } catch (IOException e) {
            e.printStackTrace();
            Log.e(TAG, "save default properties fail! ");
            return false;
        } finally {
            IOUtils.close(fos);
        }
        return true;
    }

    private static Properties getProperties() {
        Properties properties = new Properties();
        FileInputStream fis = null;
        try {
            fis = BaseApplication.getContext().openFileInput(PROPERTIES_NAME);
            properties.load(fis);
        } catch (IOException e) {
            e.printStackTrace();
            Log.e(TAG, "load default properties fail! ");
            return properties;
        } finally {
            IOUtils.close(fis);
        }
        return properties;
    }

    /**
     * save default character name
     *
     * @param characterName default character name
     */
    public static void saveDefaultCharacter(String characterName) {
        if (TextUtils.isEmpty(characterName)) {
            Log.e(TAG, "saveDefaultCharacter: character is empty");
            return;
        }
        boolean result = updateDefaultCharacter(DEFAULT_CHARACTER, characterName);
        Log.i(TAG, "saveDefaultCharacter: settings: characterName=" + characterName + " result=" + result);
        boolean isSuccess = setProperties(DEFAULT_CHARACTER, characterName);
        Log.i(TAG, "saveDefaultCharacter: property: isSuccess=" + isSuccess);
    }


    /**
     * load default character
     *
     * @return default character name
     */
    public static String loadDefaultCharacter() {
        String defaultCharacter = loadDefaultCharacter(DEFAULT_CHARACTER);
        Log.i(TAG, "loadDefaultCharacter: settings: defaultCharacter=" + defaultCharacter);
        if (TextUtils.isEmpty(defaultCharacter)) {
            defaultCharacter = getProperties(DEFAULT_CHARACTER);
            Log.i(TAG, "loadDefaultCharacter: property: defaultCharacter" + defaultCharacter);
        }
        return defaultCharacter;
    }

    public static boolean updateDefaultCharacter(String key, String value) {
        Context ctx = BaseApplication.getContext();
        if (null != ctx) {
            return LocalUtils.putStringSettings(ctx, key, value);
        }
        Log.i(TAG, "updateDefaultCharacter: context is empty");
        return false;
    }

    public static String loadDefaultCharacter(String key) {
        Context ctx = BaseApplication.getContext();
        if (null != ctx) {
            return LocalUtils.getGlobalSettings(ctx, key, null);
        }
        Log.i(TAG, "loadDefaultCharacter: context is empty");
        return null;
    }

    public static boolean updatePlatform(String platform) {
        Context ctx = BaseApplication.getContext();
        if (null != ctx) {
            return LocalUtils.putStringSettings(ctx, SettingsUtil.ROBOT_SETTING_PLATFORM_NAME, platform);
        }
        Log.i(TAG, "updatePlatform: context is empty");
        return false;
    }
}
