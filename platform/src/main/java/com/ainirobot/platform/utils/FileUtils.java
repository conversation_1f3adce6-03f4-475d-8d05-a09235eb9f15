package com.ainirobot.platform.utils;

import android.text.TextUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayDeque;

/**
 * <AUTHOR>
 * @desc 文件操作工具类
 * @date 2018/5/8
 */
public class FileUtils {
    /**
     * 从SD卡中读取文件内容
     * @param filePath 文件路径
     * @return
     */
    public static String getFileContent(String filePath){
        File file = new File(filePath);
        String str = "";
        StringBuffer content = new StringBuffer();
        try {
            InputStream is = new FileInputStream(file);
            InputStreamReader input = new InputStreamReader(is, "UTF-8");
            BufferedReader reader = new BufferedReader(input);
            while ((str = reader.readLine()) != null) {
                content.append(str);
            }

        } catch (FileNotFoundException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } finally {
           return content.toString();
        }

    }

    public static boolean checkFileExist(String fileName) {
        boolean isExist = false;
        if (!TextUtils.isEmpty(fileName)) {
            File file = new File(fileName);
            return file.exists() && file.length() > 0;
        }
        return isExist;
    }

    public static boolean checkDirExist(String dirName) {
        if (!TextUtils.isEmpty(dirName)) {
            File file = new File(dirName);
            return file.exists() && file.isDirectory();
        }
        return false;
    }

    public static void deleteFile(String fileName) {
//        try {
//            Runtime.getRuntime().exec("rm -rf " + fileName);
//        } catch (IOException e) {
            File file = new File(fileName);
            if (file.isFile()) {
                file.delete();
            } else {
                ArrayDeque<File> files = new ArrayDeque<>();
                files.add(file);
                do {
                    file = files.poll();
                    if (file.isFile()) {
                        file.delete();
                    } else {
                        File[] fileList = file.listFiles();
                        if (fileList == null || fileList.length < 1) {
                            file.delete();
                        } else {
                            files.addFirst(file);
                            for (File item : fileList) {
                                if (item.isFile()) {
                                    item.delete();
                                } else {
                                    files.addFirst(item);
                                }
                            }
                        }
                    }
                } while (!files.isEmpty());
            }
//        }
    }

    public static boolean rename(String name, String desName) {
        try {
            Runtime.getRuntime().exec("vm " + name + " " + desName);
            return true;
        } catch (IOException e) {
            File file = new File(name);
            File desFile = new File(desName);
            return file.renameTo(desFile);
        }
    }

    public static String getFileSuffix(String fileName) {
        int index = fileName.lastIndexOf('.');
        String suffix = null;
        if (index > 0) {
            suffix = fileName.substring(index);
        }
        return suffix;
    }

    public static boolean find(String dirName, String fileName, boolean nest) {
        boolean found = false;
        if (!TextUtils.isEmpty(dirName) && !TextUtils.isEmpty(fileName)) {
            File file = new File(dirName);
            if (file.isFile()) {
                return false;
            } else {
                boolean addRoot = true;
                ArrayDeque<File> files = new ArrayDeque<>();
                files.add(file);
                do {
                    file = files.poll();
                    if (file.getName().equals(fileName)) {
                        found = true;
                        break;
                    } else if (nest && file.isDirectory() || addRoot) {
                        addRoot = false;
                        File[] fileList = file.listFiles();
                        if (fileList != null && fileList.length > 0) {
                            for (File item : fileList) {
                                if (item.getName().equals(fileName)) {
                                    return true;
                                } else if (item.isDirectory()) {
                                    files.addFirst(item);
                                }
                            }
                        }
                    }
                } while (!files.isEmpty());
            }
        }
        return found;
    }


    /**
     * 复制单个文件
     *
     * @param oldPath String 原文件路径+文件名 如：data/user/0/com.test/files/abc.txt
     * @param newPath String 复制后路径+文件名 如：data/user/0/com.test/cache/abc.txt
     * @return <code>true</code> if and only if the file was copied;
     * <code>false</code> otherwise
     */
    public static boolean copyFile(String oldPath, String newPath) {
        FileInputStream fileInputStream = null;
        FileOutputStream fileOutputStream = null;

        try {

            File oldFile = new File(oldPath);

            if (!oldFile.exists() || !oldFile.isFile() || !oldFile.canRead()) {
                return false;
            }

            if (TextUtils.isEmpty(newPath)) {
                return false;
            }

            File parentPathFile = new File(newPath.substring(0, newPath.lastIndexOf('/')));
            if (!parentPathFile.exists()) {
                parentPathFile.mkdirs();
            }

            //读入原文件
            fileInputStream = new FileInputStream(oldPath);
            fileOutputStream = new FileOutputStream(newPath);
            byte[] buffer = new byte[1024];

            int byteRead;
            while ((byteRead = fileInputStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, byteRead);
            }

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {

            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            if (fileOutputStream != null) {
                try {
                    fileOutputStream.flush();
                    fileOutputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
