/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.utils;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import androidx.annotation.StringRes;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.R;
import com.ainirobot.platform.bean.OpenAppBean;
import com.ainirobot.platform.react.view.FloatDialogManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

public class Utils {
    private final static String TAG = Utils.class.getSimpleName();
    private final static String SKILL_DATA = "skillData";
    private final static String OPEN_APP = "open_app";

    private static Context mApplicationContext = null;

    public static String getString(@StringRes int resId, Object... formatArgs) {
        Context ctx = BaseApplication.getContext();
        return ctx.getString(resId, formatArgs);
    }

    public static boolean isInDebugModule(Context context) {

        boolean bRet = false;
        try {
            ApplicationInfo info = context.getApplicationInfo();
            bRet = (info.flags & ApplicationInfo.FLAG_DEBUGGABLE) != 0;
        } catch (Exception e) {
        }

        return bRet;
    }

    public static boolean isInDebugModule() {
        if (true) {
            return true;
        }
        boolean bRet = false;
        try {
            bRet = isInDebugModule(mApplicationContext);
        } catch (Exception e) {
        }

        return bRet;
    }


    public static Context getRobotApplicationContext() {
        return mApplicationContext;
    }

    public static void setApplicationContext(Context context) {
        mApplicationContext = context;
    }

    public static void openDiagnosticApp(Context context) {
        openAnotherApp(context, "com.ainirobot.inspection");
    }

    public static void openMaptoolApp(Context context) {
        openAnotherApp(context, "com.ainirobot.maptool");
    }

    public static void openAnotherApp(Context context, String packageName) {
        PackageManager packageManager = context.getPackageManager();
        if (checkPackageInfo(context, packageName)) {
            Intent intent = packageManager.getLaunchIntentForPackage(packageName);
            context.startActivity(intent);
        } else {
            Toast.makeText(context, "No installation " + packageName, Toast.LENGTH_SHORT).show();
        }
    }

    private static boolean checkPackageInfo(Context context, String packageName) {
        PackageInfo packageInfo = null;
        try {
            packageInfo = context.getPackageManager().getPackageInfo(packageName, 0);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return packageInfo != null;
    }

    public static boolean openThirdPartyApp(final String param) {
        Context context = BaseApplication.getContext();
        String params;
        try {
            JSONObject object = new JSONObject(param);
            JSONObject skillData = new JSONObject(object.optString(SKILL_DATA));
            params = skillData.optString(OPEN_APP);
            Log.d(TAG, "openThirdPartyApp params = " + params);
        } catch (JSONException e) {
            Log.d(TAG, "parse param = " + param);
            e.printStackTrace();
            return false;
        }
        PackageManager packageManager = context.getPackageManager();
        String packageName = parsePackName(params);
        String activityName = parseActivityName(params);
        ArrayList<OpenAppBean.KeyVal> keyVals = parseKeyValue(params);
        if (checkPackageInfo(context, packageName)) {
            Intent intent;
            if (!TextUtils.isEmpty(activityName)) {
                intent = new Intent();
                intent.setComponent(new ComponentName(packageName, activityName));

                if (intent.resolveActivityInfo(packageManager, PackageManager.MATCH_DEFAULT_ONLY)
                        == null) {
                    //if Activity not exist， use Application info
//                    Toast.makeText(context, "No Activity " + activityName, Toast.LENGTH_SHORT).show();
                    intent = packageManager.getLaunchIntentForPackage(packageName);
                    FloatDialogManager.getInstance().showFloatDialog(FloatDialogManager
                            .FloatDialogType.TYPE_RESPONSE_LITE, BaseApplication.getContext()
                            .getString(R.string.response_no_Activity));
                }
            } else {
                intent = packageManager.getLaunchIntentForPackage(packageName);
            }

            if (intent == null) {
                Log.d(TAG, "can not start package = " + packageName);
                return false;
            }
            if (keyVals!= null && !keyVals.isEmpty()) {
                for (OpenAppBean.KeyVal entry : keyVals) {
                    Log.d(TAG, "openThirdPartyApp key = " + entry.getKey()
                            + ", value = " + entry.getValue());
                    intent.putExtra(entry.getKey(), entry.getValue());
                }
            }
//            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            //add category not to implicit start, but to identity is ModuleApp start to add whitelist
            intent.addCategory(Definition.CATEGORY_OPEN_APP);
            context.startActivity(intent);
            return true;
        } else {
//            Toast.makeText(context, "No installation " + packageName, Toast.LENGTH_SHORT).show();
            FloatDialogManager.getInstance().showFloatDialog(FloatDialogManager
                    .FloatDialogType.TYPE_RESPONSE_LITE, BaseApplication.getContext()
                    .getString(R.string.response_no_app));
            return false;
        }
    }



    private static String parsePackName(final String params) {
        String packageName = "";
        try {
            OpenAppBean openAppBean = GsonUtil.fromJson(params, OpenAppBean.class);
            packageName = openAppBean.getPkg_name();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return packageName;
    }

    private static String parseActivityName(final String params) {
        String activityName = "";
        try {
            OpenAppBean openAppBean = GsonUtil.fromJson(params, OpenAppBean.class);
            activityName = openAppBean.getClass_name();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return activityName;
    }

    private static ArrayList<OpenAppBean.KeyVal> parseKeyValue(final String params) {
        ArrayList<OpenAppBean.KeyVal> keyVals = null;
        try {
            OpenAppBean openAppBean = GsonUtil.fromJson(params, OpenAppBean.class);
            keyVals = openAppBean.getParams();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return keyVals;
    }

    public static void startMaptoolCruisePage(Context context){
        PackageManager packageManager = context.getPackageManager();
        if (checkPackageInfo(context, "com.ainirobot.maptool")) {
            Intent intent = packageManager.getLaunchIntentForPackage("com.ainirobot.maptool");
            intent.putExtra("data", "project_route");
            context.startActivity(intent);
        } else {
            Toast.makeText(context, "No installation " + "com.ainirobot.maptool", Toast.LENGTH_SHORT).show();
        }
    }

    public static boolean isValidContextForGlide(final Context context) {
        if (context == null) {
            return false;
        }
        if (context instanceof Activity) {
            final Activity activity = (Activity) context;
            if (activity.isDestroyed() || activity.isFinishing()) {
                return false;
            }
        }
        return true;
    }

}