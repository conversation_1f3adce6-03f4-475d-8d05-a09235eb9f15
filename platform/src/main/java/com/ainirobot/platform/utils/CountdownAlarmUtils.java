package com.ainirobot.platform.utils;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.ainirobot.platform.BaseApplication;

import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

public final class CountdownAlarmUtils {

    public static final String TAG = CountdownAlarmUtils.class.getSimpleName();
    private AlarmManager mAlarmManager;
    private PendingIntent mShutdownIntent;

    private static final class CountdownAlarmUtilsHolder{
        static CountdownAlarmUtils instance = new CountdownAlarmUtils();
    }

    @NotNull
    public static CountdownAlarmUtils getInstance() {
        return CountdownAlarmUtilsHolder.instance;
    }

    private CountdownAlarmUtils() {
        initAlarmManager();
    }

    private void initAlarmManager() {
        mAlarmManager = (AlarmManager) BaseApplication.getContext().getSystemService(Context.ALARM_SERVICE);
        try {
            //解决Alarm时间延迟问题
            Field field = AlarmManager.class.getDeclaredField("mAlwaysExact");
            field.setAccessible(true);
            field.set(mAlarmManager, true);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    public void setCountdownAlarm(String time) {
        if (mShutdownIntent != null) {
            mAlarmManager.cancel(mShutdownIntent);
        }

        String[] parts = time.split(":");

        int hour = Integer.parseInt(parts[0]);
        int minute = Integer.parseInt(parts[1]);

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, 0);

        if (calendar.getTimeInMillis() > System.currentTimeMillis()) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss", Locale.getDefault());
            Log.d(TAG, "Start Cruise at : " + format.format(calendar.getTimeInMillis()));
            Intent intent = new Intent();
            intent.putExtra("hour", calendar.get(Calendar.HOUR_OF_DAY));
            intent.putExtra("minute", calendar.get(Calendar.MINUTE));
            intent.setAction("trigger_count_down_timer");
            mShutdownIntent = PendingIntent.getBroadcast(BaseApplication.getContext(), 0, intent, PendingIntent.FLAG_CANCEL_CURRENT);
            mAlarmManager.set(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), mShutdownIntent);
        }
    }
}
