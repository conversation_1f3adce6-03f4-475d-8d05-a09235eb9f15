package com.ainirobot.platform.utils;

import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.media.audiofx.AcousticEchoCanceler;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.concurrent.locks.ReentrantLock;

public class FilmRecorderTool {

    public static final String TAG = FilmRecorderTool.class.getSimpleName();

    //音频相关成员
    private AudioRecord mAudioRecorder = null;
    private final ReentrantLock mRecLocker = new ReentrantLock();
    private AcousticEchoCanceler mAecer = null;
    private boolean mThreadRun = false;
    private Thread mProducerThread;
    private int mBufferSize;
    private int mOneSecondBytes;//1秒钟的数据量
    private boolean mIsDoubleChannel = true;


    private static File mSampleFile = null;
    public static final String FILE_NAME_PREFIX = "fileRecording";
    public static final String DATA_FORMAT = ".pcm";
    public static final String STORAGE_DIRECTORY = "/mnt/sdcard/filmRecorder/";

    private SaveAudioFinishCallBack mSaveAudioFinishCallBack;
    public interface SaveAudioFinishCallBack{

        public void onSaveAudioFinish(boolean isFinish);
    }

    private AudioRecordCallback mCallback;

    public interface AudioRecordCallback {
        public void onFrameDataIn(byte[] data, boolean isMute);
    }

    public int startRecord(int sampleRate, int bufferSize, AudioRecordCallback callback) {
        return startRecord(sampleRate, bufferSize, true, callback);
    }

    private int startRecord(int sampleRate, int bufferSize, boolean needStartThread, AudioRecordCallback callback) {

        deleteDirectory(STORAGE_DIRECTORY);
        Log.i(TAG, "Init Recording");

        File sampleDir = getFile(DATA_FORMAT);
        Log.d(TAG, TAG + " path sampleDir= " + sampleDir);

        Log.d(TAG, TAG + " path= " + STORAGE_DIRECTORY);

        int channelConfig = AudioFormat.CHANNEL_IN_MONO;
        if (mIsDoubleChannel) {
            //channelConfig = AudioFormat.CHANNEL_IN_FRONT | AudioFormat.CHANNEL_IN_BACK;
            channelConfig = AudioFormat.CHANNEL_IN_STEREO;
        }
        mCallback = callback;
        mBufferSize = bufferSize;
        mOneSecondBytes = sampleRate * 16 * 1 / 8;
        Log.d(TAG, TAG + " mOneSecondBytes= " + mOneSecondBytes);
        int audioSource = MediaRecorder.AudioSource.MIC;
        //audioSource = MediaRecorder.AudioSource.REMOTE_SUBMIX;
        // get the minimum buffer size that can be used
        int minRecBufSize = AudioRecord.getMinBufferSize(
                sampleRate, channelConfig,
                AudioFormat.ENCODING_PCM_16BIT);

        // double size to be more safe
        int recBufSize = minRecBufSize * 3;

        Log.i(TAG, TAG + " min buffer size:" + minRecBufSize);

        try {
            if (mAecer != null) {
                mAecer.release();
                mAecer = null;
            }
        } catch (Exception ex) {
            Log.d(TAG, TAG + " audio aecer excep:" + ex.getMessage());
        }

        // release the object
        if (mAudioRecorder != null) {
            mAudioRecorder.release();
            mAudioRecorder = null;
        }

        try {
            audioSource = MediaRecorder.AudioSource.MIC;
            //audioSource = MediaRecorder.AudioSource.VOICE_COMMUNICATION;
            mAudioRecorder = new AudioRecord(
                    audioSource,
                    sampleRate,
                    channelConfig,
                    AudioFormat.ENCODING_PCM_16BIT,
                    recBufSize);
        } catch (Exception ex) {
            Log.d(TAG, TAG + " open audio recore failed:" + ex.getMessage());
            return -1;
        }

        try {
            mAudioRecorder.startRecording();
        } catch (Exception ex) {
            Log.d(TAG, TAG + " start recore failed:" + ex.getMessage());
            return -1;
        }

        Log.i(TAG, "start recore status:" + mAudioRecorder.getState() + "," + mAudioRecorder.getRecordingState());
        if (mAudioRecorder.getRecordingState() != AudioRecord.RECORDSTATE_RECORDING) {
            Log.d(TAG, TAG + " start recore status err");
            return -1;
        }

        if (needStartThread) {
            mThreadRun = true;
            mProducerThread = new Thread(mRunnableRecorder, "AudioProducerThread");
            mProducerThread.start();
        }

        return 0;
    }

    public int stopRecord() {
        Log.i(TAG, "Stop Record");
        mThreadRun = false;
        mRecLocker.lock();
        try {
            if (mAudioRecorder != null && mAudioRecorder.getRecordingState() == AudioRecord.RECORDSTATE_RECORDING) {
                mAudioRecorder.stop();
            }

            if (mAudioRecorder != null) {
                mAudioRecorder.release();
                mAudioRecorder = null;
            }

            if (mProducerThread != null) {
                mProducerThread.join(50);
            }
            mProducerThread = null;

            if (mAecer != null) {
                mAecer.release();
                mAecer = null;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            mRecLocker.unlock();
        }

        Log.i(TAG, "Stop Record end");
        return 0;
    }


    private void saveData(byte[] bytes, int len) {
        try {
            OutputStream os = new FileOutputStream(mSampleFile, true);
            os.write(bytes, 0, len);
            os.flush();
            os.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private Runnable mRunnableRecorder = new Runnable() {
        @Override
        public void run() {
            Log.d(TAG, TAG + " ===== Audio Recorder Start ===== ");

            byte[] tempBufRec = new byte[mBufferSize];
            byte[] muteBufRec = new byte[mBufferSize];
            byte[] bfOutLeft = null;
            byte[] bfOutRight = null;
            android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_URGENT_AUDIO);

            boolean bNeedEmptyPkg = true;
            long tmLastRealSend = System.currentTimeMillis();
            long tmLastMuteSend = System.currentTimeMillis();
            long peroid = (long) (mOneSecondBytes / mBufferSize);
            while (mThreadRun && mAudioRecorder != null) {
                try {
                    Thread.sleep(1);

                    int readBytes = mAudioRecorder.read(tempBufRec, 0, mBufferSize);
                    if (readBytes == mBufferSize) {
//                        saveData(tempBufRec, mBufferSize);
                        if (mCallback != null) {
                            tmLastRealSend = System.currentTimeMillis();
                            bNeedEmptyPkg = false;
                            //mCallback.onFrameDataIn(tempBufRec, false);
                            if (bfOutLeft == null) {
                                bfOutLeft = new byte[mIsDoubleChannel ? mBufferSize / 2 : mBufferSize];
                                bfOutRight = new byte[mIsDoubleChannel ? mBufferSize / 2 : mBufferSize];
                            }
                            if (mIsDoubleChannel) {
                                deinterleaveData(tempBufRec, bfOutLeft, bfOutRight, mBufferSize);
                            } else {
                                System.arraycopy(tempBufRec, 0, bfOutLeft, 0, mBufferSize);
                            }
                            mCallback.onFrameDataIn(bfOutLeft, false);
                            saveData(bfOutLeft, bfOutLeft.length);
                        //    Log.d(TAG, TAG + " onFrameDataIn");
                        }
                    } else {
                        long currTime = System.currentTimeMillis();
                        if (bNeedEmptyPkg && currTime - tmLastRealSend > 1 * 1000) //超过1秒都没有采集到数据
                        {
                            if ((currTime - tmLastMuteSend) > peroid) {
                                tmLastMuteSend = currTime;
                                if (mCallback != null) {
                                    //MLog.i("will send empty data");
                                    mCallback.onFrameDataIn(muteBufRec, true);
                                    Log.d(TAG, TAG + " 超过1SonFrameDataIn");
                                }
                            }
                        }
                    }

                } catch (Exception e) {
                    Log.d(TAG, TAG + " Record Audio try failed: " + e.getMessage());
                }
            }

            getAllFile();
           // deleteDirectory(STORAGE_DIRECTORY);
            if (null != mSaveAudioFinishCallBack){
                mSaveAudioFinishCallBack.onSaveAudioFinish(true);
                Log.i(TAG, "run: isFinish = true");
            }
            Log.i(TAG, "===== Audio Recorder Stop ===== ");
        }
    };

    private void deinterleaveData(byte[] src, byte[] leftdest, byte[] rightdest, int len) {
        int rIndex = 0;
        int lIndex = 0;
        for (int i = 0; i < len; ) {
            leftdest[rIndex] = src[i];
            leftdest[rIndex + 1] = src[i + 1];
            rIndex = rIndex + 2;

            rightdest[lIndex] = src[i + 2];
            rightdest[lIndex + 1] = src[i + 3];
            lIndex = lIndex + 2;

            i = i + 4;
        }
    }

    private File getFile(String extension) {


        File sampleDir = new File(STORAGE_DIRECTORY);
        Log.d(TAG, "startRecording ,savepath: " + sampleDir.getAbsolutePath());
        try {
            if (!sampleDir.exists()) {
                sampleDir.mkdirs();
                Log.d(TAG, "startRecording ,mkdirs!");
            }
        } catch (SecurityException se) {
            Log.d(TAG, "startRecording, mkdirs error = " + se);

        }

        if (!sampleDir.canWrite()) {
            Log.d(TAG, "startRecording, sampleDir can not write !");
            sampleDir = new File("/sdcard/SD_CARD");
            Log.d(TAG, "startRecording, new sdcard");
        }

        try {
            mSampleFile = File.createTempFile(FILE_NAME_PREFIX, extension, sampleDir);
            mSampleFile.setReadable(true, false);
            Log.d(TAG, "startRecording, sampleFile create! " + mSampleFile.getAbsolutePath());
        } catch (IOException e) {
           // SkillManager.getInstance().setASREnabled(true);
            Log.d(TAG, "startRecording, error = " + e.toString());
            Log.d(TAG, "startRecording, sampleFile create IOException!");

        }

        return mSampleFile;
    }


    private void getAllFile() {
        Log.i(TAG, "deleteFile: ");
        File file = new File(STORAGE_DIRECTORY);
        File[] fileList = file.listFiles();
        for (int i = 0; i < fileList.length; i++) {
            String fileName = fileList[i].getName();
            Log.d(TAG, TAG + " file name：" + fileName + "file length：" + fileName.length());
        }
    }

    public  boolean deleteDirectory(String dir) {
        // 如果dir不以文件分隔符结尾，自动添加文件分隔符
        if (!dir.endsWith(File.separator))
            dir = dir + File.separator;
        File dirFile = new File(dir);
        // 如果dir对应的文件不存在，或者不是一个目录，则退出
        if ((!dirFile.exists()) || (!dirFile.isDirectory())) {
            Log.i(TAG, "deleteDirectory: "+"删除目录失败：" + dir + "不存在！");
            return false;
        }
        boolean flag = true;
        // 删除文件夹中的所有文件包括子目录
        File[] files = dirFile.listFiles();
        for (int i = 0; i < files.length; i++) {
            // 删除子文件
            if (files[i].isFile()) {
                flag = deleteFile(files[i].getAbsolutePath());
                if (!flag)
                    break;
            }
            // 删除子目录
            else if (files[i].isDirectory()) {
                flag = deleteDirectory(files[i]
                        .getAbsolutePath());
                if (!flag)
                    break;
            }
        }
        if (!flag) {
            Log.i(TAG, "deleteDirectory: 删除目录失败！");
            return false;
        }
        // 删除当前目录
        if (dirFile.delete()) {
            Log.i(TAG, "deleteDirectory: 删除目录 "+ dir +" 成功");
            return true;
        } else {
            return false;
        }
    }

    public  boolean delete(String fileName) {
        File file = new File(fileName);
        if (!file.exists()) {
            Log.i(TAG, "delete: 删除文件失败:" + fileName + "不存在！");
            return false;
        } else {
            if (file.isFile())
                return deleteFile(fileName);
            else
                return deleteDirectory(fileName);
        }
    }

    /**
     * 删除单个文件
     *
     * @param fileName
     *            要删除的文件的文件名
     * @return 单个文件删除成功返回true，否则返回false
     */
    public  boolean deleteFile(String fileName) {
        File file = new File(fileName);
        // 如果文件路径所对应的文件存在，并且是一个文件，则直接删除
        if (file.exists() && file.isFile()) {
            if (file.delete()) {
                Log.i(TAG, "deleteFile: 删除单个文件" + fileName + "成功！");
                return true;
            } else {
                Log.i(TAG, "deleteFile:删除单个文件" + fileName + "失败！");
                return false;
            }
        } else {
            Log.i(TAG, "deleteFile: 删除单个文件失败：" + fileName + "不存在！");
            return false;
        }
    }

    public String getAudioPath(){
        File file = new File(STORAGE_DIRECTORY);
        File[] fileList = file.listFiles();
        String fileName = fileList[0].getName();
        return fileName;
    }
}
