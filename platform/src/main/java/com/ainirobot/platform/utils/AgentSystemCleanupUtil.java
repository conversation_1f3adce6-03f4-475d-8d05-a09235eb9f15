package com.ainirobot.platform.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.platform.bean.CharacterInfo;
import com.ainirobot.platform.character.CharacterManager;
import com.ainirobot.platform.data.provider.AppInfoHelper;
import com.ainirobot.platform.react.AppBeanV2;
import com.ainirobot.platform.react.AppManger;
import com.ainirobot.platform.react.OPKBeanV3;
import com.ainirobot.platform.react.reactnative.character.Constant;
import com.ainirobot.platform.react.reactnative.character.ReactCharacter;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class AgentSystemCleanupUtil {
    private static final String TAG = "AgentSystemCleanupUtil";

    public static void cleanAllOpkData(Context context) {
        Log.i(TAG, "Cleaning all OPK data...");

        // First, uninstall all installed OPKs
        uninstallAllInstalledOpks(context);

        // Delete _react_character_ SharedPreferences
        deleteReactCharacterPreferences(context);

        // Clear default character from ModuleProperties
        clearDefaultCharacterFromModuleProperties(context);

        // Clear opk database
        clearOpkDatabase(context);

        // Clear RPK install list file
        clearRpkInstallListFile(context);

        // Reload AppManger to reflect the cleanup
        AppManger.INSTANCE.reload();

        Log.i(TAG, "All OPK data cleanup completed");
    }

    /**
     * Uninstall all installed OPKs
     *
     * @param context Application context
     */
    private static void uninstallAllInstalledOpks(Context context) {
        try {
            Log.d(TAG, "Uninstalling all installed OPKs...");

            // Get all installed OPKs
            Map<String, AppBeanV2> installedOpks =
                    AppManger.INSTANCE.getAllInstallList();

            if (installedOpks != null && !installedOpks.isEmpty()) {
                Log.d(TAG, "Found " + installedOpks.size() + " OPKs to uninstall");

                // First collect all appIds to avoid ConcurrentModificationException
                List<String> appIdsToUninstall = new ArrayList<>();
                for (String appId : installedOpks.keySet()) {
                    if (appId != null) {
                        appIdsToUninstall.add(appId);
                    }
                }

                // Now uninstall each OPK
                for (String appId : appIdsToUninstall) {
                    Log.d(TAG, "Uninstalling OPK: " + appId);

                    // Unregister from CharacterManager
                    CharacterManager.getInstance()
                            .unRegisterCharacter(appId, CharacterInfo.PLATFORM_RN);

                    // Remove from AppManger (this also deletes files)
                    boolean removed = AppManger.INSTANCE.removeAppId(appId);
                    Log.d(TAG, "Remove OPK " + appId + " result: " + removed);
                }

                Log.i(TAG, "Successfully uninstalled all OPKs");
            } else {
                Log.d(TAG, "No OPKs found to uninstall");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to uninstall all OPKs", e);
        }
    }


    /**
     * Clear default character from ModuleProperties
     *
     * @param context Application context
     */
    private static void clearDefaultCharacterFromModuleProperties(Context context) {
        try {
            Log.d(TAG, "Clearing default character from ModuleProperties...");

            // Clear from Settings.Global
            boolean result = ModuleProperties.updateDefaultCharacter("default_character", "");
            Log.d(TAG, "Clear default character from Settings.Global: " + result);

            // Clear from ModuleProperties file
            ModuleProperties.setProperties("default_character", "");

            // Also clear the current character setting
            SettingsUtil.putString(context, SettingsUtil.ROBOT_SETTING_CURRENT_CHARACTER, "");

            Log.i(TAG, "Successfully cleared default character from ModuleProperties");
        } catch (Exception e) {
            Log.e(TAG, "Failed to clear default character from ModuleProperties", e);
        }
    }

    /**
     * Delete _react_character_ SharedPreferences
     *
     * @param context Application context
     */
    private static void deleteReactCharacterPreferences(Context context) {
        try {
            Log.d(TAG, "Deleting _react_character_ SharedPreferences...");

            SharedPreferences sp = context.getSharedPreferences(
                    ReactCharacter.sharedPreferences, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = sp.edit();

            // Clear all data in the SharedPreferences
            editor.clear();
            editor.apply();

            Log.i(TAG, "Successfully deleted _react_character_ SharedPreferences");
        } catch (Exception e) {
            Log.e(TAG, "Failed to delete _react_character_ SharedPreferences", e);
        }
    }

    /**
     * Clear opk database
     *
     * @param context Application context
     */
    private static void clearOpkDatabase(Context context) {
        try {
            Log.d(TAG, "Clearing opk database...");

            AppInfoHelper appInfoHelper = new AppInfoHelper(context);

            // Get all opk info first
            List<OPKBeanV3> allOpkInfo =
                    appInfoHelper.getAllOpkInfo();

            if (allOpkInfo != null && !allOpkInfo.isEmpty()) {
                Log.d(TAG, "Found " + allOpkInfo.size() + " opk entries to delete");

                // Delete each opk entry
                for (OPKBeanV3 opkInfo : allOpkInfo) {
                    if (opkInfo != null && opkInfo.getAppid() != null) {
                        boolean deleted = appInfoHelper.delete(opkInfo.getAppid());
                        Log.d(TAG, "Delete opk " + opkInfo.getAppid() + ": " + deleted);
                    }
                }

                Log.i(TAG, "Successfully cleared opk database");
            } else {
                Log.d(TAG, "No opk entries found in database");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to clear opk database", e);
        }
    }

    /**
     * Clear RPK install list file
     *
     * @param context Application context
     */
    private static void clearRpkInstallListFile(Context context) {
        try {
            Log.d(TAG, "Clearing RPK install list file...");

            String rpkInstallPath = context.getFilesDir().getAbsolutePath() +
                    "/" + Constant.RPK_INSTALL_DIR + "/" + Constant.RPK_INSTALL_NAME;

            File rpkInstallFile = new File(rpkInstallPath);
            if (rpkInstallFile.exists()) {
                boolean deleted = rpkInstallFile.delete();
                Log.i(TAG, "Delete RPK install list file: " + deleted);
            } else {
                Log.d(TAG, "RPK install list file does not exist");
            }

            // Also try to delete the directory if empty
            File rpkDir = new File(context.getFilesDir().getAbsolutePath() +
                    "/" + Constant.RPK_INSTALL_DIR);
            if (rpkDir.exists() && rpkDir.isDirectory()) {
                String[] files = rpkDir.list();
                if (files == null || files.length == 0) {
                    boolean dirDeleted = rpkDir.delete();
                    Log.d(TAG, "Delete empty RPK directory: " + dirDeleted);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to clear RPK install list file", e);
        }
    }
} 