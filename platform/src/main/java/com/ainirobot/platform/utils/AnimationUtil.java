/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.utils;

import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.util.Log;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;

public class AnimationUtil {

    public static final String TAG = AnimationUtil.class.getSimpleName();
    public static final int DEFAULT_DURATION_TIME = 500;
    private static final int DEFAULT_DISTANCE = 70;

    public static AnimatorSet enterAnimator(View mView) {
        Log.d(TAG,"enterAnimator");
        AnimatorSet enterSet = new AnimatorSet();
        ObjectAnimator translationYAnimator = ObjectAnimator.ofFloat(mView, "translationY", DEFAULT_DISTANCE, 0);
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(mView, "alpha", 0.0f, 1.0f);
        enterSet.setInterpolator(new AccelerateDecelerateInterpolator());
        enterSet.play(translationYAnimator).with(alphaAnimator);
        enterSet.setDuration(DEFAULT_DURATION_TIME);
        enterSet.start();
        return enterSet;
    }

    public static AnimatorSet shiftOutAnimator(View mView,AnimatorListenerAdapter listenerAdapter) {
        Log.d(TAG,"shiftOutAnimator");
        AnimatorSet shiftOutSet = new AnimatorSet();
        ObjectAnimator translationYAnimator = ObjectAnimator.ofFloat(mView, "translationY", 0, -DEFAULT_DISTANCE);
        translationYAnimator.addListener(listenerAdapter);
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(mView, "alpha", 1.0f, 0.0f);
        shiftOutSet.setInterpolator(new AccelerateDecelerateInterpolator());
        shiftOutSet.play(translationYAnimator).with(alphaAnimator);
        shiftOutSet.setDuration(DEFAULT_DURATION_TIME);
        shiftOutSet.start();
        return shiftOutSet;
    }

}
