/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.utils;

import android.app.ActivityManager;
import android.app.ActivityManager.RunningServiceInfo;
import android.app.ActivityManager.RunningTaskInfo;
import android.content.ComponentName;
import android.content.Context;
import android.content.pm.PackageManager;
import android.content.res.Resources;
import android.os.Build;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.platform.BaseApplication;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.lang.reflect.Method;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Enumeration;
import java.util.List;
import java.util.Properties;

public class SystemUtils {
    private static final String TAG = SystemUtils.class.getSimpleName();
    private static final String CONFIG_FILE = "/robot/config/remote.properties";
    private static final String MAP_CONFIG_FILE = "/robot/config/navigation.properties";

    //1豹花瓶 2豹大屏
    private static final char ROBOT_HUA_PING_SN = '1';

    private static ActivityManager activityManager = null;

    private static Method forceStopPackageMethod = null;

    private static final String BUILD_TYPE_USER = "user";


    public static Date stringToDate(String strTime, String formatType)
            throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat(formatType);
        return formatter.parse(strTime);
    }

    public static long dateToLong(Date date) {
        return date.getTime();
    }

    public static long stringToLong(String strTime, String formatType)
            throws ParseException {
        Date date = stringToDate(strTime, formatType);
        if (date == null) {
            return 0;
        } else {
            long currentTime = dateToLong(date);
            return currentTime;
        }
    }

    public static String getIp() {
        try {
            Enumeration<NetworkInterface> nets = NetworkInterface.getNetworkInterfaces();
            while (nets.hasMoreElements()) {
                NetworkInterface net = nets.nextElement();
                Enumeration<InetAddress> addresses = net.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress inetA = addresses.nextElement();
                    if (inetA instanceof Inet4Address && !inetA.isLoopbackAddress()) {
                        return inetA.getHostAddress();
                    }
                }
            }
            return null;
        } catch (SocketException e) {
            return null;
        }
    }

    public static long getCurrentTime() {
        return System.currentTimeMillis() / 1000;
    }

    public static long getTimeMillis() {
        return System.currentTimeMillis();
    }

    /**
     * 包名判断是否为ReactNative进程
     *
     * @param context
     * @return
     */
    public static boolean isRNProcess(Context context, String rnPackageName) {
        return rnPackageName.equals(getProcessName(context));
    }

    /**
     * 包名判断是否为主进程
     *
     * @param context
     * @return
     */
    public static boolean isMainProcess(Context context) {
        return context.getPackageName().equals(getProcessName(context));
    }

    public static boolean isRNProcess(Context context) {
        return context.getPackageName().concat(":sandbox").equals(getProcessName(context));
    }

    public static boolean isWebProcess(Context context) {
        return context.getPackageName().concat(":remote").equals(getProcessName(context));
    }

    /**
     * 包名判断是否为WebView进程
     *
     * @param context
     * @return
     */
    public static boolean isWebProcess(Context context, String webPackageName) {
        return webPackageName.equals(getProcessName(context));
    }

    /**
     * 获取进程名称
     *
     * @param context
     * @return
     */
    public static String getProcessName(Context context) {
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> runningApps = am.getRunningAppProcesses();
        if (runningApps == null) {
            return null;
        }
        for (ActivityManager.RunningAppProcessInfo proInfo : runningApps) {
            if (proInfo.pid == android.os.Process.myPid()) {
                if (proInfo.processName != null) {
                    return proInfo.processName;
                }
            }
        }
        return null;
    }

    public static ActivityManager getActivityManager() {
        if (activityManager == null) {
            activityManager = (ActivityManager) BaseApplication.getContext().getSystemService(Context.ACTIVITY_SERVICE);
        }
        return activityManager;
    }

    public static void forceStopPackage(String packageName) {
        Log.d(TAG, "forceStopPackage packageName:" + packageName);
        try {
            if (forceStopPackageMethod == null) {
                forceStopPackageMethod = Class.forName("android.app.ActivityManager").getMethod("forceStopPackage", String.class);
            }
            forceStopPackageMethod.invoke(getActivityManager(), packageName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String getSystemSerialNo() {
        return RobotSettings.getSystemSn();
    }

    public static boolean isBigShowEnabled() {
        Log.i(TAG, "isBigShowEnabled mSerialNo BIG_SHOW_SN :" + getSystemSerialNo());
        if (TextUtils.isEmpty(getSystemSerialNo()) || getSystemSerialNo().length() < 2) {
            return false;
        }
        if (getSystemSerialNo().charAt(1) != ROBOT_HUA_PING_SN) {
            return true;
        }
        return false;
    }

    public static String getSystemModel() {
        return RobotSettings.getProductModel();
    }

    public static String getSystemProperties(String key) {
        String ret;
        try {
            Method systemProperties_get = Class.forName("android.os.SystemProperties").getMethod("get", String.class);
            if ((ret = (String) systemProperties_get.invoke(null, key)) != null)
                return ret;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }

    public static String getTestParamName() {
        return getProperty("name");
    }

    public static String getTestParamId() {
        return getProperty("id");
    }

    public static String getTestParamPic() {
        return getProperty("pic");
    }

    public static String getTestParamCode() {
        return getProperty("code");
    }

    public static String getTestParamType() {
        return getProperty("type");
    }

    public static String getTestParamTaskId() {
        return getProperty("taskid");
    }

    public static String getTestParamStatus() {
        return getProperty("status");
    }

    public boolean setDomain(String key, String value) {
        return setProperty(key, value);
    }

    private static String getProperty(String key) {
        return getProperty(key, CONFIG_FILE);
    }

    private static String getProperty(String key, String configPath) {
        if (TextUtils.isEmpty(key)) {
            return null;
        }

        File root = Environment.getExternalStorageDirectory();
        File file = new File(root, configPath);
        if (!file.exists()) {
            return null;
        }
        Properties props = loadProperty(file.getPath());
        if (props != null) {
            return props.getProperty(key);
        }
        return null;
    }

    private boolean setProperty(String key, String value) {
        if (TextUtils.isEmpty(key)) {
            return false;
        }

        File root = Environment.getExternalStorageDirectory();
        File file = new File(root, CONFIG_FILE);
        Properties props = loadProperty(file.getPath());
        if (props != null) {
            props.put(key, value);
            return saveProperty(file.getPath(), props);
        }
        return false;
    }

    private static Properties loadProperty(String path) {
        FileInputStream in = null;

        Properties properties = new Properties();
        try {
            in = new FileInputStream(path);
            properties.load(in);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            IOUtils.close(in);
        }
        return properties;
    }

    private boolean saveProperty(String path, Properties properties) {
        FileOutputStream out = null;
        try {
            File file = new File(path);
            if (!file.exists()) {
                file.createNewFile();
            }
            out = new FileOutputStream(file);
            properties.store(out, null);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            IOUtils.close(out);
        }
        return true;
    }

    public static boolean isActivityTop(Class cls, Context context) {
        if (cls == null || TextUtils.isEmpty(cls.getName())) {
            return false;
        }

        ActivityManager manager = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        try {
            List<RunningTaskInfo> list = manager
                    .getRunningTasks(1);
            if (list != null && list.size() > 0) {
                return cls.getName().equals(list.get(0).topActivity.getClassName());
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }
        return false;
    }

    public static boolean isServiceRunning(Class cls, Context context) {
        if (cls == null || TextUtils.isEmpty(cls.getName()) || cls.getPackage() == null) {
            return false;
        }

        ActivityManager manager = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        try {
            List<RunningServiceInfo> list = manager
                    .getRunningServices(50);
            if (list != null && list.size() > 0) {
                for (RunningServiceInfo info : list) {
                    if (cls.getName().equals(info.service.getClassName())
                            && cls.getPackage().getName().equals(info.service.getPackageName())) {
                        return true;
                    }
                }
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }
        return false;
    }

    public static String getCurMapName() {
        String mapName = getProperty("mapName", MAP_CONFIG_FILE);
        return TextUtils.isEmpty(mapName) ? null : mapName;
    }

    /**
     * 是否存在NavigationBar
     *
     * @param context
     * @return
     */
    public static boolean checkDeviceHasNavigationBar(Context context) {
        boolean hasNavigationBar = false;
        Resources rs = context.getResources();
        int id = rs.getIdentifier("config_showNavigationBar", "bool", "android");
        if (id > 0) {
            hasNavigationBar = rs.getBoolean(id);
        }
        try {
            Class systemPropertiesClass = Class.forName("android.os.SystemProperties");
            Method m = systemPropertiesClass.getMethod("get", String.class);
            String navBarOverride = (String) m.invoke(systemPropertiesClass, "qemu.hw.mainkeys");
            if ("1".equals(navBarOverride)) {
                hasNavigationBar = false;
            } else if ("0".equals(navBarOverride)) {
                hasNavigationBar = true;
            }
        } catch (Exception e) {

        }
        return hasNavigationBar;
    }

    /**
     * 是否正在进行首次配置
     *
     * @return
     */
    public static boolean isFirstConfig() {
        if (BaseApplication.getContext() != null) {
            try {
                PackageManager pm = BaseApplication.getContext().getPackageManager();
                ComponentName componentName = new ComponentName(Definition.FIRST_CONFIG_PACKAGE,
                        Definition.FIRST_CONFIG_ACTIVITY);

                int flag = pm.getComponentEnabledSetting(componentName);
                Log.d(TAG, "componentName:" + componentName + " enable status:" + flag);
                if (flag == PackageManager.COMPONENT_ENABLED_STATE_DEFAULT
                        || flag == PackageManager.COMPONENT_ENABLED_STATE_ENABLED) {
                    return true;
                }
            } catch (IllegalArgumentException | NullPointerException e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    public static boolean isUser() {
        return BUILD_TYPE_USER.equals(Build.TYPE);
    }

    /**
     * 设置Application 状态
     * @param packageName 包名
     * @param isEnable  true 可用
     *                  false 不可用
     */
    public static void setApplicationState(String packageName,boolean isEnable){
        int state=1;
        if(!isEnable){
            state= PackageManager.COMPONENT_ENABLED_STATE_DISABLED;
        }
        PackageManager packageManager= BaseApplication.getApplication().getPackageManager();
        packageManager.setApplicationEnabledSetting(packageName,state,0 );
    }
}
