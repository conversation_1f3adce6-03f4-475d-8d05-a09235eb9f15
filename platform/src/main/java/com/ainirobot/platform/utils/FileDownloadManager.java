package com.ainirobot.platform.utils;

import android.app.DownloadManager;
import android.app.DownloadManager.Query;
import android.app.DownloadManager.Request;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.Cursor;
import android.net.Uri;
import android.util.Log;
import android.util.LongSparseArray;

import java.io.File;
import java.net.URI;
import java.net.URISyntaxException;

public class FileDownloadManager {

    private static String TAG = "FileDownloadManager";
    private static DownloadManager sDownloadManager;
    private static LongSparseArray<DownloadListener> mListeners = new LongSparseArray<>();

    public static void init(Context context) {
        sDownloadManager = (DownloadManager) context.getSystemService(Context.DOWNLOAD_SERVICE);
        DownloadReceiver downloadReceiver = new DownloadReceiver();
        context.registerReceiver(downloadReceiver,
                new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE));
    }

    public static long download(String url, File file, DownloadListener listener) {
        if (sDownloadManager == null) {
            throw new RuntimeException("FileDownloadManager is not initialized");
        }
        boolean res = false;
        if (file.exists()) {
            res = file.delete();
        }
        Log.d(TAG, "path:" + file.getAbsolutePath() + ", res:" + res);
        Request request = new Request(Uri.parse(url));
        request.setDestinationUri(Uri.fromFile(file));
        request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_HIDDEN);
        request.setVisibleInDownloadsUi(false);
        long downloadId = sDownloadManager.enqueue(request);
        mListeners.put(downloadId, listener);
        return downloadId;
    }

    public static void remove(long downloadId) {
        sDownloadManager.remove(downloadId);
    }

    /**
     * 下载结果监听
     */
    private static class DownloadReceiver extends BroadcastReceiver {


        @Override
        public void onReceive(Context context, Intent intent) {
            if (!DownloadManager.ACTION_DOWNLOAD_COMPLETE.equals(intent.getAction())) {
                return;
            }

            long downloadId = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1);
            DownloadListener listener = mListeners.get(downloadId);
            if (listener != null) {
                Query query = new Query();
                query.setFilterById(downloadId);
                Cursor cursor = null;
                try {
                    cursor = sDownloadManager.query(query);
                    cursor.moveToFirst();
                    int status = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS));
                    String uri = cursor.getString(cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI));
                    Log.d(TAG, "Download complete : " + status + "  " + uri);

                    switch (status) {
                        case DownloadManager.STATUS_SUCCESSFUL:
                            String path = new File(new URI(uri)).getAbsolutePath();
                            Log.d(TAG, "Download succeed : " + path);
                            listener.onSucceed(path);
                            break;

                        case DownloadManager.STATUS_FAILED:
                            Log.d(TAG, "Download failed : " + uri);
                            listener.onFailed(status);
                            break;

                        default:
                            break;
                    }
                } catch (URISyntaxException e) {
                    e.printStackTrace();
                } finally {
                    IOUtils.close(cursor);
                }
            }
        }
    }

    public interface DownloadListener {
        /**
         * 下载成功
         */
        void onSucceed(String uri);

        /**
         * 下载失败
         */
        void onFailed(int errorCode);
    }

}


