package com.ainirobot.platform.utils;

import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * @date: 2019-06-27
 * @author: lumeng
 * @email: <EMAIL>
 * @desc: 录音转码 for rn
 */
public final class RNAudioRecorderUtils {

    public static final String TAG = RNAudioRecorderUtils.class.getSimpleName();

    private static final int AUDIO_RATE = 16000;
    private static final int AUDIO_BUF_LEN = 640;
    private FilmRecorderTool mRecorderUtils = null;
    private PcmToWavUtil mPcmToWavUtil = null;
    private String mOutFilePath = null;
    private RNAudioRecorderUtils(){}

    private void initData(){
        if(mRecorderUtils == null){
            mRecorderUtils = new FilmRecorderTool();
        }
        if(mPcmToWavUtil == null){
            mPcmToWavUtil = new PcmToWavUtil();
        }
    }
    private static final class RNAudioRecorderUtilsHolder{
        static RNAudioRecorderUtils instance = new RNAudioRecorderUtils();
    }
    private FilmRecorderTool.AudioRecordCallback mAudioRecordCallback = new FilmRecorderTool.AudioRecordCallback() {
        @Override
        public void onFrameDataIn(byte[] data, boolean isMute) {
        }
    };
    public int startRecord(int sampleRate, int bufferSize) {
        initData();

        RecordFileParseUtil.deleteRecordDirectory();
        mRecorderUtils.startRecord(sampleRate, bufferSize, mAudioRecordCallback);
        return 0;
    }

    public void stopRecord(RecordFinishCallback callback) {
        mRecorderUtils.stopRecord();
        mOutFilePath = RecordFileParseUtil.getRecordDirectory() + "/"
                + System.currentTimeMillis() / 1000 + RecordFileParseUtil.SUFFIX;
        mPcmToWavUtil.pcmToWav(FilmRecorderTool.STORAGE_DIRECTORY+mRecorderUtils.getAudioPath(),mOutFilePath);
        if(callback!=null){
            callback.onRecordFinish(mOutFilePath);
        }
    }

    @NotNull
    public static RNAudioRecorderUtils  getInstance() {
        return RNAudioRecorderUtilsHolder.instance;
    }

    public interface RecordFinishCallback{
        void onRecordFinish(String filePath);
    }

    public boolean copyFile(String sourceFilePath, String destFilePath) {
        File sourceFile = new File(sourceFilePath);
        if (!sourceFile.exists()) {
            return false;
        }

        File destPath = new File(destFilePath);
        if (destPath.exists()) {
            return true;
        }
        String destP = destPath.getParent();
        File destDir = new File(destP);
        if (!destDir.exists()) {
            destDir.mkdirs();
        }
        String destFileName = destPath.getName();

        File dest = new File(destP, destFileName);

        InputStream input = null;
        OutputStream output = null;

        try {
            input = new FileInputStream(sourceFile);
            output = new FileOutputStream(dest);
            byte[] buf = new byte[1024];
            int bytesRead;
            while ((bytesRead = input.read(buf)) > 0) {
                output.write(buf, 0, bytesRead);
            }
            return true;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (output != null) {
                    output.close();
                }
                if (input != null) {
                    input.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    public List<String> getRecordFiles(String path) {
        File pathFile = new File(path);
        if (!pathFile.exists()) {
            return null;
        }
        File[] files = pathFile.listFiles();
        if (files == null) {
            return null;
        }
        List<String> recordFiles = new ArrayList<>();
        for (int i = 0; i < files.length; i++) {
            recordFiles.add(files[i].getPath());
        }
        return recordFiles;
    }
}
