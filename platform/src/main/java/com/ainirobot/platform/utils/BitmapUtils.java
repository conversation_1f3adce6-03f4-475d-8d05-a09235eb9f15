package com.ainirobot.platform.utils;

import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileDescriptor;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Hashtable;

import com.ainirobot.platform.utils.DimenUtils;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;

/**
 * Created by huang<PERSON><PERSON> on 2017/12/27.
 */

public class BitmapUtils {

    public static Bitmap bytesToBitmap(byte[] data) {
        if (data == null || data.length == 0) {
            return null;
        }
        return BitmapFactory.decodeByteArray(data, 0, data.length);
    }

    public static byte[] bitmapToBytes(Bitmap bitmap) {
        if (bitmap == null || bitmap.getByteCount() == 0) {
            return null;
        }
        int count = bitmap.getByteCount();
        ByteBuffer buffer = ByteBuffer.allocate(count);
        return buffer.array();
    }

    public static byte[] bitmap2Bytes(Bitmap bitmap) {
        if (bitmap == null || bitmap.getByteCount() == 0) {
            return null;
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream);
        return outputStream.toByteArray();
    }

    public static Bitmap drawableToBitmap(Drawable drawable, Bitmap.Config config) {
        int width = drawable.getIntrinsicWidth();
        int height = drawable.getIntrinsicHeight();
        Bitmap.Config cfg = config == null ? Bitmap.Config.RGB_565 : config;
        Bitmap bitmap = Bitmap.createBitmap(width, height, cfg);
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, width, height);
        drawable.draw(canvas);
        return bitmap;
    }

    public static Drawable bitmapToDrawable(Bitmap bitmap) {
        return new BitmapDrawable(bitmap);
    }

    public static Bitmap resizeBitmap(Bitmap bitmap, int reqWidth, int reqHeight) {
        if (bitmap == null) {
            return null;
        }
        if (reqHeight <= 0 || reqWidth <= 0) {
            return null;
        }
        int oldWidth = bitmap.getWidth();
        int oldHeight = bitmap.getHeight();
        float scaleWidth = (float) reqWidth / (float) oldWidth;
        float scaleHeight = (float) reqHeight / (float) oldHeight;
        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleHeight);
        return Bitmap.createBitmap(bitmap, 0, 0, oldWidth, oldHeight, matrix, true);
    }

    public static int calculateInSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        if (options == null) {
            return 1;
        }
        int inSampleSize = 1;
        int outWidth = options.outWidth;
        int outHeight = options.outHeight;

        if ((outHeight > reqHeight && outWidth > reqWidth) && outHeight > 0 && outWidth > 0) {
            int halfWidth = outWidth / 2;
            int halfHeight = outHeight / 2;
            while ((halfHeight / inSampleSize) >= reqHeight &&
                    (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2;
            }
        }
        return inSampleSize;
    }

    public static String mergeBitmap(String sourcePath1, String sourcePath2, String sourcePath3) {
        String parentPath = Environment.getExternalStorageDirectory().getPath() + "/self_picture";

        File parentPathFile = new File(parentPath);
        if (!parentPathFile.exists()) {
            parentPathFile.mkdirs();
        }

        File file1 = new File(sourcePath1);
        File file2 = new File(sourcePath2);

        if (file1.exists() && file2.exists()) {
            Bitmap bitmap1 = BitmapFactory.decodeFile(file1.getPath());
            Bitmap bitmap2 = BitmapFactory.decodeFile(file2.getPath());

            bitmap1 = resizeBitmap(bitmap1, 1200, 1920);
            bitmap2 = resizeBitmap(bitmap2, 1200, 1920);

            if (bitmap1 == null || bitmap2 == null) {
                return "";
            }

            Bitmap bitmap = mergeThumbnailBitmap(bitmap1, bitmap2);
            File saveFile = new File(parentPath, sourcePath3);
            boolean isSaveSuccess = saveBitmap(bitmap, saveFile);

            return isSaveSuccess ? saveFile.getPath() : "";
        }

        return "";
    }

    public static Bitmap decodeBitmapFromResource(Resources rs, int resId, int reqWidth, int reqHeight) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeResource(rs, resId, options);
        options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight);
        options.inJustDecodeBounds = false;
        return BitmapFactory.decodeResource(rs, resId, options);
    }

    public static Bitmap decodeBitmapFromFd(FileDescriptor fd, int reqWidth, int reqHeight) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFileDescriptor(fd, null, options);
        options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight);
        options.inJustDecodeBounds = false;
        return BitmapFactory.decodeFileDescriptor(fd, null, options);
    }

    public static Bitmap decodeBitmapFromFile(String filePath, int reqWidth, int reqHeight) {
        if (TextUtils.isEmpty(filePath)) {
            return null;
        }
        File file = new File(filePath);
        if (!file.exists()) {
            return null;
        }
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(filePath, options);
        options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight);
        options.inJustDecodeBounds = false;
        return BitmapFactory.decodeFile(filePath, options);
    }

    /**
     * create two-dimension code
     *
     * @param url
     * @param width
     * @param height
     * @return
     */
    public static Bitmap createQRImage(String url, final int width, final int height) {
        try {

            if (url == null || "".equals(url) || url.length() < 1) {
                return null;
            }
            Hashtable<EncodeHintType, String> hints = new Hashtable<EncodeHintType, String>();
            hints.put(EncodeHintType.CHARACTER_SET, "utf-8");

            BitMatrix bitMatrix = new QRCodeWriter().encode(url,
                    BarcodeFormat.QR_CODE, width, height, hints);
            int[] pixels = new int[width * height];

            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    if (bitMatrix.get(x, y)) {
                        pixels[y * width + x] = 0xff000000;
                    } else {
                        pixels[y * width + x] = 0xffffffff;
                    }
                }
            }
            Bitmap bitmap = Bitmap.createBitmap(width, height,
                    Bitmap.Config.ARGB_8888);
            bitmap.setPixels(pixels, 0, width, 0, 0, width, height);
            return bitmap;
        } catch (WriterException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * overlay bitmap
     *
     * @param bit1
     * @param bit2
     * @return overlay bitmap2 to bitmap1 return new  Bitmap
     */
    public static Bitmap joinBitmap(Bitmap bit1, Bitmap bit2, float left, float top) {
        Bitmap bitmap = Bitmap.createBitmap(bit1.getWidth(), bit1.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.drawBitmap(bit1, 0, 0, null);
        canvas.drawBitmap(bit2, left, top, null);
        return bitmap;
    }

    public static Bitmap getRotateBitmap(Bitmap b, float rotateDegree) {
        Matrix matrix = new Matrix();
        matrix.postRotate((float) rotateDegree);
        Bitmap rotaBitmap = Bitmap.createBitmap(b, 0, 0, b.getWidth(), b.getHeight(), matrix, false);
        return rotaBitmap;
    }

    public static boolean saveBitmap(Bitmap b, File jpegName) {


        try {
            FileOutputStream fout = new FileOutputStream(jpegName);
            BufferedOutputStream bos = new BufferedOutputStream(fout);
            b.compress(Bitmap.CompressFormat.JPEG, 100, bos);
            bos.flush();
            bos.close();

            return true;
        } catch (IOException e) {
            // TODO Auto-generated catch block
            Log.i("CameraDeviceManager", "saveBitmap:ʧ��");
            e.printStackTrace();
            return false;
        }
    }

    public static Bitmap clipBitmap(Bitmap source,int width, int height) {
        int left = source.getWidth() / 2 - width / 2;
        int top = source.getHeight() / 2 - height / 2;
        Bitmap bitmap = Bitmap.createBitmap(source, left, top, width, height, null, false);
        source.recycle();
        return bitmap;
    }

    public static Bitmap mergeThumbnailBitmap(Bitmap firstBitmap, Bitmap secondBitmap) {
        Bitmap bitmap = Bitmap.createBitmap(firstBitmap.getWidth(), firstBitmap
                .getHeight(), firstBitmap.getConfig());
        Canvas canvas = new Canvas(bitmap);
        float w = firstBitmap.getWidth();
        float h = firstBitmap.getHeight();
        float halfWidth = w / 2 - secondBitmap.getWidth() / 2;
        float halfHeight = h / 2 - secondBitmap.getHeight() / 2;
        Paint paint = new Paint();
        canvas.drawBitmap(firstBitmap, 0, 0, null);
        canvas.drawBitmap(secondBitmap, halfWidth, halfHeight, paint);
        return bitmap;
    }

    public static BitmapDrawable createImage(Bitmap bitmap) {
        float screenWidth = DimenUtils.getScreenWidthPixels();
        float screenHeight = DimenUtils.getScreenHeightPixels();

        float bitmapWidth = bitmap.getWidth();
        float bitmapHeight = bitmap.getHeight();
        float scale = screenWidth / bitmapWidth;

        Matrix matrix = new Matrix();
        matrix.setScale(scale, scale);
        Bitmap resizedBitmap = Bitmap.createBitmap(bitmap, 0, 0,
                bitmap.getWidth(), bitmap.getHeight(), matrix, true);
        if(bitmap != null && !bitmap.isRecycled()){
            bitmap = null;
        }
        Bitmap mBitmap = Bitmap.createBitmap((int) screenWidth,
                (int) screenHeight, Bitmap.Config.RGB_565);

        Canvas mCanvas = new Canvas(mBitmap);
        Paint BitmapPaint = new Paint(Paint.FILTER_BITMAP_FLAG);
        mCanvas.drawColor(0xff000000);
        if (!resizedBitmap.isRecycled()) {
            mCanvas.drawBitmap(resizedBitmap, screenWidth / 2 - bitmapWidth
                            * scale / 2, screenHeight / 2 - bitmapHeight * scale / 2,
                    BitmapPaint);
        }
        mCanvas.save();

        BitmapDrawable drawable = new BitmapDrawable(mBitmap);
        if(resizedBitmap != null && !resizedBitmap.isRecycled()){
            resizedBitmap = null;
        }
        return drawable;
    }

    public static Bitmap drawableToBitmap(Drawable drawable) {
        Bitmap bitmap = null;
        if (drawable instanceof BitmapDrawable) {
            BitmapDrawable bitmapDrawable = (BitmapDrawable) drawable;
            if (bitmapDrawable.getBitmap() != null) {
                return bitmapDrawable.getBitmap();
            }
        }
        if (drawable.getIntrinsicWidth() <= 0 || drawable.getIntrinsicHeight() <= 0) {
            bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.RGB_565);
        } else {
            bitmap = Bitmap.createBitmap(drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight(), Bitmap.Config.RGB_565);
        }

        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
        drawable.draw(canvas);
        return bitmap;
    }
}
