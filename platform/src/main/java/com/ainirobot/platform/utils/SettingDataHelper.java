package com.ainirobot.platform.utils;

import android.content.ContentValues;
import android.content.Context;
import android.database.ContentObserver;
import android.database.Cursor;
import android.net.Uri;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

public class SettingDataHelper {
    private static final String TAG = SettingDataHelper.class.getSimpleName();
    private static final String URI = "content://com.ainirobot.coreservice.robotsettingprovider/setting";
    private static final String KEY_ID = "id";
    private static final String KEY_VALUE = "value";

    /**
     * provide from core
     */
    public static final String BOOT_APP_PACKAGE_NAME = "boot_app_package_name";
    public static final String BOSS_AVATAR_ON_MOBILE = "boss_avatar_on_mobile";
    public static final String BUILD_NEW_MAP_ON_MOBILE = "build_new_map_on_mobile";
    public static final String CHARGING_PILE_CONFIGURED = "charging_pile_configured";
    public static final String ROBOT_AUTO_BACK_RECEPTION = "robot_auto_back_reception";
    public static final String ROBOT_CHAT_REPLY = "robot_chat_reply";
    public static final String ROBOT_DROPDOWN_BAR_PASSWORD = "robot_dropdown_bar_password";
    public static final String ROBOT_FOCUS_FOLLOW = "robot_focus_follow";
    public static final String ROBOT_MASK_DETECTION = "robot_mask_detection";
    public static final String ROBOT_REGULAR_CHARGE_TIME_LIST = "robot_regular_charge_time_list";
    public static final String ROBOT_OBSTACLES_AVOID = "robot_obstacles_avoid";
    public static final String ROBOT_PREVENT_COLLISION = "robot_prevent_collision";
    public static final String ROBOT_SMALL_ACTION = "robot_samll_action";
    public static final String ROBOT_SETTING_ALLOW_AUTO_SITU_SERVICE = "robot_setting_allow_auto_situ_service";
    public static final String ROBOT_SETTING_AUTO_CHARGE = "robot_setting_auto_charge";
    public static final String ROBOT_SETTING_CRUISE_ANGULAR_SPEED = "robot_setting_cruise_angular_speed";
    public static final String ROBOT_SETTING_CRUISE_LINEAR_SPEED = "robot_setting_cruise_linear_speed";
    public static final String ROBOT_SETTING_DISABLE_VOICE_CONTROL_VOLUME = "robot_setting_disable_voice_control_volume";
    public static final String ROBOT_SETTING_GREET_ANGULAR_SPEED = "robot_setting_greet_angular_speed";
    public static final String ROBOT_SETTING_GREET_LINEAR_SPEED = "robot_setting_greet_linear_speed";
    public static final String ROBOT_SETTING_GUIDE_ANGULAR_SPEED = "robot_setting_guide_angular_speed";
    public static final String ROBOT_SETTING_GUIDE_LINEAR_SPEED = "robot_setting_guide_linear_speed";
    public static final String ROBOT_SETTING_HIDE_MANUFACTURE_INFORMATION = "robot_setting_hide_manufacture_information";
    public static final String ROBOT_SETTING_LEAD_ANGULAR_SPEED = "robot_setting_lead_angular_speed";
    public static final String ROBOT_SETTING_LEAD_LINEAR_SPEED = "robot_setting_lead_linear_speed";
    public static final String ROBOT_SETTING_NAV_ANGULAR_SPEED = "robot_setting_nav_angular_speed";
    public static final String ROBOT_SETTING_NAV_LINEAR_SPEED = "robot_setting_nav_linear_speed";
    public static final String ROBOT_SETTING_OTA_UPDATE = "robot_setting_ota_update";
    public static final String ROBOT_SETTING_SHIPPING_MODE = "robot_setting_shipping_mode";
    public static final String ROBOT_SETTING_SITU_SERVICE_RUNNING_TIME = "robot_setting_situ_service_running_time";
    public static final String ROBOT_SETTING_SITU_SERVICE_STATUS = "robot_setting_situ_service_status";
    public static final String ROBOT_SETTING_SITU_SERVICE_TIME = "robot_setting_situ_service_time";
    public static final String ROBOT_SETTING_SOUND_ANGEL_CENTER = "robot_setting_sound_angel_center";
    public static final String ROBOT_SETTING_SOUND_ANGEL_RANGE = "robot_setting_sound_angel_range";
    public static final String ROBOT_SETTING_SPEECH_SPEED = "robot_setting_speech_speed";
    public static final String ROBOT_SETTING_SPOKE_MAN = "robot_setting_spoke_man";
    public static final String ROBOT_SETTING_VAD_END = "robot_setting_vad_end";
    public static final String ROBOT_SETTING_WAITING_TIME = "robot_setting_waiting_time";
    public static final String ROBOT_SETTINGS_CHARGING_ENVIRONMENT = "robot_settings_charging_environment";
    public static final String ROBOT_SETTINGS_OBSTACLES_AVOID_DISTANCE = "robot_settings_obstacles_avoid_distance";
    public static final String ROBOT_SHOW_AD = "robot_show_ad";
    public static final String ROBOT_SHOW_AD_AND_ROTATE = "robot_show_ad_and_rotate";
    public static final String ROBOT_USABLE_WHEN_CHARGING = "robot_usable_when_charging";
    public static final String SETTINGS_GLOBAL_SWITCH_FOLLOW_STYLE = "settings_global_switch_follow_style";
    public static final String SLEEP_LIST = "sleep_list";
    public static final String SWITCH_ALLOW_CHAT_WHEN_INTERPRET = "switch_allow_chat_when_interpret";
    public static final String SWITCH_START_APP_PASSWORD = "switch_start_app_password";
    public static final String VERSION_UPGRADE_ON_MOBILE = "version_upgrade_on_mobile";
    public static final String ROBOT_LANGUAGE = "robot_language";
    public static final String ROBOT_TIME_ZONE_CODE = "robot_time_zone_code";
    public static final String ROBOT_SETTING_SYSTEM_ENV = "robot_setting_system_environment";
    public static final String ROBOT_SETTING_SYSTEM_ADB_ALWAYS_OPEN = "robot_setting_system_adb_always_open";
    public static final String ROBOT_SETTING_SPEAKER_ROLE = "robot_setting_speaker_role";
    public static final String ROBOT_SETTING_PRODUCT_TYPE = "robot_product_type";
    public static final String ROBOT_SETTING_ENABLE_ASR_SWITCH = "robot_enable_asr_switch";
    public static final String ROBOT_SETTING_PORTAL_OPK = "robot_portal_opk";
    public static final String ROBOT_SETTINGS_CLOUD_SERVER_ZONE = "robot_settings_cloud_server_zone";
    public static final String ROBOT_STATIC_MODE_SWITCH = "robot_static_mode_switch"; //静止模式开关
    public static final String ROBOT_SETTING_VOICE_MODE = "robot_setting_voice_mode"; //三指下拉语音模式
    public static final String ROBOT_SETTING_AMBIENT_LIGHT_SWITCH = "robot_setting_ambient_light_switch";

    /**
     * provide from system
     */
    public static final String GLOBAL_ADMIN_FULL_NAME = "admin_full_name";
    public static final String GLOBAL_ADMIN_MOBILE = "admin_mobile";
    public static final String GLOBAL_CORP_NAME = "corp_name";
    public static final String GLOBAL_CORP_UUID = "corp_uuid";
    public static final String GLOBAL_DEVICE_PROVISIONED = Settings.Global.DEVICE_PROVISIONED;
    public static final String GLOBAL_HOME_LAUNCHER_COMPONENT = "robot_setting_home_launcher_component";
    public static final String GLOBAL_REGULAR_CHARGE_TIME_SPAN_MAX_COUNT = "robot_regular_charge_time_span_max_count";
    public static final String GLOBAL_ROBOT_NAME = "robot_name";
    public static final String GLOBAL_ROBOT_SAMPLE = "robot_sample";
    public static final String GLOBAL_ROBOT_UUID = "robot_uuid";
    public static final String GLOBAL_SHUT_DOWN_PASSWORD = "robot_shut_down_password";
    public static final String GLOBAL_MEAL_DELIVERY_THREE_FLOORS = "robot_meal_delivery_three_floors";
    public static final String ROBOT_SETTING_QUICK_SETTING_BAR_SUPPORT_METHOD = "robot_settings_quick_setting_bar_support_method";
    public static final String SYSTEM_SCREEN_BRIGHTNESS = Settings.System.SCREEN_BRIGHTNESS;
    public static final String SYSTEM_SCREEN_BRIGHTNESS_MODE = Settings.System.SCREEN_BRIGHTNESS_MODE;
    public static final int SYSTEM_SCREEN_BRIGHTNESS_MODE_AUTOMATIC = Settings.System.SCREEN_BRIGHTNESS_MODE_AUTOMATIC;
    public static final int SYSTEM_SCREEN_BRIGHTNESS_MODE_MANUAL = Settings.System.SCREEN_BRIGHTNESS_MODE_MANUAL;
    public static final String SYSTEM_VOLUME_MUSIC_SPEAKER = "volume_music_speaker";
    public static final String ACTION_MANAGE_OVERLAY_PERMISSION = Settings.ACTION_MANAGE_OVERLAY_PERMISSION;
    public static final String ACTION_MANAGE_WRITE_SETTINGS = Settings.ACTION_MANAGE_WRITE_SETTINGS;

    public static enum CloudServerZone {
        DEFAULT("default"),
        US("us");

        String value;

        private CloudServerZone(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }


    private static SettingDataHelper mInstance;
    private static Context mContext;

    public static synchronized SettingDataHelper getInstance() {
        if (mInstance == null) {
            mInstance = new SettingDataHelper();
        }
        return mInstance;
    }

    public void setContext(Context context) {
        mContext = context;
        Log.i(TAG, "set context");
    }

    private SettingDataHelper() {
        Log.i(TAG, "create");
    }

    public int getRobotInt(String key) {
        String value = getRobotString(key);
        if (TextUtils.isEmpty(value)) {
            return -1;
        } else {
            return Integer.valueOf(value);
        }
    }

    public float getRobotFloat(String key) {
        String value = getRobotString(key);
        if (TextUtils.isEmpty(value)) {
            return -1;
        } else {
            return Float.valueOf(value);
        }
    }

    public long getRobotLong(String key) {
        String value = getRobotString(key);
        if (TextUtils.isEmpty(value)) {
            return -1;
        } else {
            return Long.valueOf(value);
        }
    }

    public String getRobotString(String key) {
        String result  = "";
        Cursor cursor;
        if(mContext != null){
            cursor = mContext.getContentResolver().query(Uri.parse(URI), new String[]{KEY_VALUE},
                    KEY_ID + "=?", new String[]{key}, null);
            if (cursor != null && cursor.moveToNext()) {
                result = cursor.getString(cursor.getColumnIndex(KEY_VALUE));
                cursor.close();
            }
        }
        if (!TextUtils.isEmpty(result)) {
            return result;
        } else {
            // RobotSettingApi 数据库未成功存入, 但是存入了Global
            result = Settings.Global.getString(mContext.getContentResolver(), key);
            if (TextUtils.isEmpty(result)) {
                result = "";
            }
        }
        return result;
    }

    public int getGlobalInt(String key, int defaultValue) {
        return Settings.Global.getInt(mContext.getContentResolver(), key, defaultValue);
    }

    public String getGlobalString(String key) {
        return Settings.Global.getString(mContext.getContentResolver(), key);
    }

    public int getSecureInt(String key, int defaultValue) {
        return Settings.Secure.getInt(mContext.getContentResolver(), key, defaultValue);
    }

    public String getSecureString(String key) {
        return Settings.Secure.getString(mContext.getContentResolver(), key);
    }

    public int getSystemInt(String key, int defaultValue) {
        return Settings.System.getInt(mContext.getContentResolver(), key, defaultValue);
    }

    public float getSystemFloat(String key, float defaultValue) {
        return Settings.System.getFloat(mContext.getContentResolver(), key, defaultValue);
    }

    public void putRobotInt(String key, int value) {
        putRobotString(key, String.valueOf(value));
    }

    public void putRobotFloat(String key, float value) {
        putRobotString(key, String.valueOf(value));
    }

    public void putRobotLong(String key, long value) {
        putRobotString(key, String.valueOf(value));
    }

    public void putRobotString(String key, String value) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(KEY_VALUE, value);
        try {
            mContext.getContentResolver().update(Uri.parse(URI), contentValues,
                    KEY_ID + "=?", new String[]{key});
        } catch (IllegalArgumentException e) {
            Log.d(TAG, "unSupport uri");
        }
    }

    public void putGlobalInt(String key, int value) {
        Settings.Global.putInt(mContext.getContentResolver(), key, value);
    }

    public void putSecureInt(String key, int value) {
        Settings.Secure.putInt(mContext.getContentResolver(), key, value);
    }

    public void putSecureString(String key, String value) {
        Settings.Secure.putString(mContext.getContentResolver(), key, value);
    }

    public void putSystemInt(String key, int value) {
        Settings.System.putInt(mContext.getContentResolver(), key, value);
    }

    public void putSystemFloat(String key, float value) {
        Settings.System.putFloat(mContext.getContentResolver(), key, value);
    }

    public Uri getRobotUriFor(String key) {
        return Uri.withAppendedPath(Uri.parse(URI), key);
    }

    public Uri getGlobalUriFor(String key) {
        return Settings.Global.getUriFor(key);
    }

    public void registerRobotContentObserver(String key, ContentObserver observer) {
        mContext.getContentResolver().registerContentObserver(
                getRobotUriFor(key), false, observer);
    }

    public void registerSystemContentObserver(String key, ContentObserver observer) {
        mContext.getContentResolver().registerContentObserver(
                Settings.System.getUriFor(key), false, observer);
    }

    public void registerGlobalContentObserver(String key, ContentObserver observer) {
        mContext.getContentResolver().registerContentObserver(
                Settings.Global.getUriFor(key), false, observer);
    }

    public boolean systemCanWrite() {
        return Settings.System.canWrite(mContext);
    }

    public boolean systemCanDrawOverlays() {
        return Settings.canDrawOverlays(mContext);
    }

    public boolean isOverSeaProduct() {
        System.out.println("SettingDataHelper ROBOT_SETTING_PRODUCT_TYPE "+ this.getRobotString(this.ROBOT_SETTING_PRODUCT_TYPE));
        System.out.println("SettingDataHelper ROBOT_SETTING_ENABLE_ASR_SWITCH "+ this.getRobotInt(this.ROBOT_SETTING_ENABLE_ASR_SWITCH));
        return TextUtils.equals(this.getRobotString(this.ROBOT_SETTING_PRODUCT_TYPE), "saiph_oversea");
    }

    public String getRobotPortalOpkID() {
        System.out.println("SettingDataHelper ROBOT_SETTING_PORTAL_OPK "+ this.getRobotString(this.ROBOT_SETTING_PORTAL_OPK));
        return this.getRobotString(this.ROBOT_SETTING_PORTAL_OPK);
    }

    public String getCloudServerZone(){
        return this.getRobotString(this.ROBOT_SETTINGS_CLOUD_SERVER_ZONE);
    }

    /**
     * 获取产品类型
     * @return
     */
    public String getRobotProductType(){
        return this.getRobotString(this.ROBOT_SETTING_PRODUCT_TYPE);
    }

    /**
     * 获取静止模式开关
     * @return
     */
    public String getStaticModeSwitch(){
        return this.getRobotString(this.ROBOT_STATIC_MODE_SWITCH);
    }

    public int getRobotSettingVoiceMode(){
        return this.getRobotInt(this.ROBOT_SETTING_VOICE_MODE);
    }

    public int getRobotSettingAmbientLightSwitch(){
        return this.getRobotInt(this.ROBOT_SETTING_AMBIENT_LIGHT_SWITCH);
    }
}

