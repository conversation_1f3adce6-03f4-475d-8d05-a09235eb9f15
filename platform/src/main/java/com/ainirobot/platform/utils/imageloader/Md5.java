package com.ainirobot.platform.utils.imageloader;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.security.MessageDigest;

public class Md5 {

    private static final String hexDigIts[] = { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d",
            "e", "f" };

    private static final int CACHE_SIZE = 1024*2;

    public static String MD5Encode(String origin) {
        return MD5Encode(origin, "UTF-8");
    }

    /**
     * MD5加密
     *
     * @param origin
     *            字符
     * @param charsetname
     *            编码
     * @return
     */
    public static String MD5Encode(String origin, String charsetname) {
        String resultString = null;
        try {
            resultString = origin;
            MessageDigest md = MessageDigest.getInstance("MD5");
            if (null == charsetname || "".equals(charsetname)) {
                resultString = byteArrayToHexString(md.digest(resultString.getBytes("UTF-8")));
            } else {
                resultString = byteArrayToHexString(md.digest(resultString.getBytes(charsetname)));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultString;
    }


    /**
     * 生成文件md5字符串
     * @param filePath 文件路径
     * @return md5字符串
     */
    public static String generateFileMD5(String filePath) {
        String md5 = "";
        File file = new File(filePath);
        if (file.exists()) {
            try {
                MessageDigest messageDigest = MessageDigest.getInstance("MD5");
                InputStream in = new FileInputStream(file);
                byte[] cache = new byte[CACHE_SIZE];
                int nRead;
                while ((nRead = in.read(cache)) != -1) {
                    messageDigest.update(cache, 0, nRead);
                }
                in.close();
                byte data[] = messageDigest.digest();
                md5 = byteArrayToHexString(data);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return md5;
    }

    private static String byteArrayToHexString(byte b[]) {
        StringBuffer resultSb = new StringBuffer();
        for (int i = 0; i < b.length; i++) {
            resultSb.append(byteToHexString(b[i]));
        }
        return resultSb.toString();
    }

    private static String byteToHexString(byte b) {
        int n = b;
        if (n < 0) {
            n += 256;
        }
        int d1 = n / 16;
        int d2 = n % 16;
        return hexDigIts[d1] + hexDigIts[d2];
    }

}
