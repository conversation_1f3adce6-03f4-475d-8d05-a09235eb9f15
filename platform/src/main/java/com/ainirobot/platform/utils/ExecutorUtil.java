/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.utils;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;


public class ExecutorUtil {

    private ExecutorUtil() {
    }

    private static Singleton<Handler> UI_HANDLER = new Singleton<Handler>() {
        @Override
        protected Handler create() {
            return new Handler(Looper.getMainLooper());
        }
    };

    public static Handler getUiHandler() {
        return UI_HANDLER.get();
    }

    private static Singleton<Handler> BG_HANDLER = new Singleton<Handler>() {
        @Override
        protected Handler create() {
            HandlerThread handlerThread = new HandlerThread("ModuleApp.Bg", android.os.Process.THREAD_PRIORITY_DEFAULT);
            handlerThread.start();
            return new Handler(handlerThread.getLooper());
        }
    };

    public static Handler getBgHandler() {
        return BG_HANDLER.get();
    }


    private static abstract class Singleton<T> {

        private volatile T mInstance;

        /**
         * 创建
         *
         * @return T
         */
        protected abstract T create();

        public final T get() {
            if (mInstance == null) {
                synchronized (this) {
                    if (mInstance == null) {
                        mInstance = create();
                    }
                }
            }
            return mInstance;
        }
    }
}
