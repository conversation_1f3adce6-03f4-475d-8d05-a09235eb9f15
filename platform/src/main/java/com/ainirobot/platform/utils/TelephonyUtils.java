package com.ainirobot.platform.utils;

import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.util.Log;

import com.ainirobot.platform.BaseApplication;

public class TelephonyUtils {

    private static final String TAG = TelephonyUtils.class.getSimpleName();

    private static final String SIM_STATE_UNKNOWN = "unknown";
    private static final String SIM_STATE_ABSENT = "absent";
    private static final String SIM_STATE_PIN_REQUIRED = "pinRequired";
    private static final String SIM_STATE_PUK_REQUIRED = "pukRequired";
    private static final String SIM_STATE_NETWORK_LOCKED = "networkLocked";
    private static final String SIM_STATE_READY = "ready";
    private static final int VALUE_SIM_STATE_NOT_READY = 6;
    private static final String SIM_STATE_NOT_READY = "notReady";
    private static final int VALUE_SIM_STATE_DROP_CARD = 10;
    private static final String SIM_STATE_DROP_CARD = "dropCard";
    private static final String TYPE_NONE = "none";
    private static final String TYPE_MOBILE = "mobile";
    private static final String TYPE_WIFI = "wifi";

    public static String getSimState() {
        TelephonyManager tm = (TelephonyManager) BaseApplication
                .getContext().getSystemService(Service.TELEPHONY_SERVICE);
        if (tm == null) {
            Log.e(TAG, "getSimState telephonyManager null");
            return SIM_STATE_UNKNOWN;
        }
        return TelephonyUtils.getSimStateStr(tm.getSimState());
    }

    public static String getActiveNetworkType() {
        ConnectivityManager connMgr = (ConnectivityManager) BaseApplication
                .getContext().getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connMgr == null) {
            Log.e(TAG, "getActiveNetworkType connectivityManager null");
            return TYPE_NONE;
        }

        NetworkInfo info = connMgr.getActiveNetworkInfo();
        if (info == null) {
            return TYPE_NONE;
        }
        Log.d(TAG, "getActiveNetworkType getType: " + info.getType());
        switch (info.getType()) {
            case ConnectivityManager.TYPE_MOBILE:
                return TYPE_MOBILE;
            case ConnectivityManager.TYPE_WIFI:
                return TYPE_WIFI;
            default:
                return TYPE_NONE;
        }
    }

    public static String getMobileSubType() {
        ConnectivityManager connMgr = (ConnectivityManager) BaseApplication
                .getContext().getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connMgr == null) {
            Log.e(TAG, "getMobileSubType connectivityManager null");
            return "unknown";
        }
        NetworkInfo networkInfo = connMgr.getNetworkInfo(ConnectivityManager.TYPE_MOBILE);
        Log.d(TAG, "getMobileSubType networkInfo subType: " + networkInfo.getSubtypeName()
                + ", " + networkInfo.getSubtype());
        return getMobileSubTypeStr(networkInfo.getSubtype());
    }

    public static boolean isMobileConnect() {
        ConnectivityManager connMgr = (ConnectivityManager) BaseApplication
                .getContext().getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connMgr == null) {
            Log.e(TAG, "isMobileConnect connectivityManager null");
            return false;
        }
        NetworkInfo networkInfo = connMgr.getNetworkInfo(ConnectivityManager.TYPE_MOBILE);
        return networkInfo.isConnected();
    }

    public static void setAirPlaneModeByRadio(boolean enable) {
        Log.d(TAG, "setAirPlaneModeByRadio enable: " + enable);
        Settings.Global.putInt(BaseApplication.getContext().getContentResolver(),
                Settings.Global.AIRPLANE_MODE_ON, enable ? 1 : 0);
        Intent intent = new Intent(Intent.ACTION_AIRPLANE_MODE_CHANGED);
        intent.putExtra("state", enable);
        BaseApplication.getContext().sendBroadcast(intent);
    }

    private static String getMobileSubTypeStr(int networkType) {
        Log.d(TAG, "getMobileSubTypeStr networkType: " + networkType);
        switch (networkType) {
            case TelephonyManager.NETWORK_TYPE_GPRS:
            case TelephonyManager.NETWORK_TYPE_EDGE:
            case TelephonyManager.NETWORK_TYPE_CDMA:
            case TelephonyManager.NETWORK_TYPE_1xRTT:
            case TelephonyManager.NETWORK_TYPE_IDEN:
                return "2G";
            case TelephonyManager.NETWORK_TYPE_UMTS:
            case TelephonyManager.NETWORK_TYPE_EVDO_0:
            case TelephonyManager.NETWORK_TYPE_EVDO_A:
            case TelephonyManager.NETWORK_TYPE_HSDPA:
            case TelephonyManager.NETWORK_TYPE_HSUPA:
            case TelephonyManager.NETWORK_TYPE_HSPA:
            case TelephonyManager.NETWORK_TYPE_EVDO_B:
            case TelephonyManager.NETWORK_TYPE_EHRPD:
            case TelephonyManager.NETWORK_TYPE_HSPAP:
                return "3G";
            case TelephonyManager.NETWORK_TYPE_LTE:
                return "4G";
            default:
                return "unknown";
        }
    }

    private static String getSimStateStr(int simState) {
        Log.d(TAG, "getSimStateStr state: " + simState);
        String stateStr;
        switch (simState) {
            case TelephonyManager.SIM_STATE_UNKNOWN:
                stateStr = SIM_STATE_UNKNOWN;
                break;
            case TelephonyManager.SIM_STATE_ABSENT:
                stateStr = SIM_STATE_ABSENT;
                break;
            case TelephonyManager.SIM_STATE_PIN_REQUIRED:
                stateStr = SIM_STATE_PIN_REQUIRED;
                break;
            case TelephonyManager.SIM_STATE_PUK_REQUIRED:
                stateStr = SIM_STATE_PUK_REQUIRED;
                break;
            case TelephonyManager.SIM_STATE_NETWORK_LOCKED:
                stateStr = SIM_STATE_NETWORK_LOCKED;
                break;
            case TelephonyManager.SIM_STATE_READY:
                stateStr = SIM_STATE_READY;
                break;
            case VALUE_SIM_STATE_NOT_READY:
                stateStr = SIM_STATE_NOT_READY;
                break;
            case VALUE_SIM_STATE_DROP_CARD:
                stateStr = SIM_STATE_DROP_CARD;
                break;
            default:
                stateStr = simState + "";
                break;
        }
        return stateStr;
    }
}
