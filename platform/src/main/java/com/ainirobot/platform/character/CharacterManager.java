/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.character;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.PlatformDef;
import com.ainirobot.platform.bean.CharacterInfo;
import com.ainirobot.platform.data.FeatureConfig;
import com.ainirobot.platform.react.reactnative.character.ReactCharacter;
import com.ainirobot.platform.utils.ModuleProperties;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CharacterManager {

    private static final String TAG = "CharacterManager";

    private static CharacterManager mInstance = new CharacterManager();

    private final HashMap<CharacterInfo, Character> mCharacters;

    /**
     * 默认角色信息
     */
    private CharacterInfo mDefault;

    /**
     * 是否启用RN平台（默认启用）
     */
    private String platform = CharacterInfo.PLATFORM_NATIVE;

    private CharacterManager() {
        mCharacters = new HashMap<>();
        //TODO: 从配置中读取是否支持RN平台
    }

    public static CharacterManager getInstance() {
        return mInstance;
    }

    public boolean registerCharacter(String name, Character character) {
        Log.i(TAG, "registerCharacter: name=" + name);
        boolean registerResult = registerCharacter(name, CharacterInfo.PLATFORM_NATIVE, character);
        Log.i(TAG, "registerCharacter: registerResult:" + registerResult);
        return registerResult;
    }

    /**
     * 注册OPK信息
     *
     * @param appId
     * @return
     */
    public boolean registerRNCharacter(String appId) {
        if (TextUtils.isEmpty(appId)) {
            return false;

        }
        if (isRegistered(appId, CharacterInfo.PLATFORM_RN)) {
            return true;
        }
        ReactCharacter character = new ReactCharacter(appId);
        return registerCharacterFromRN(character.getName(), character);
    }

    public boolean registerCharacterFromRN(String name, Character character) {
        Log.d(TAG, "Register character, name : " + name
                + "  platform : " + CharacterInfo.PLATFORM_RN);
        boolean registerRnResult = registerCharacter(name, CharacterInfo.PLATFORM_RN, character);
        Log.i(TAG, "registerCharacterFromRN: registerRnResult=" + registerRnResult);
        return registerRnResult;
    }

    /**
     * unregister character and delete character value in database
     *
     * @param name     character name
     * @param platform platform name {@link CharacterInfo#PLATFORM_RN}
     *                 or {@link CharacterInfo#PLATFORM_NATIVE}
     * @return true-unregister success false-fail
     */
    public boolean unRegisterCharacter(String name, String platform) {
        Log.i(TAG, "unRegisterCharacter: name=" + name + " platform=" + platform);
        boolean success;
        synchronized (mCharacters) {
            CharacterInfo characterInfo = new CharacterInfo(name, platform);
            //            mCharacterData.delete(name);
            success = mCharacters.remove(characterInfo) != null;
        }
        return success;
    }

    /**
     * obtain target platform all characters
     *
     * @param platform platform name {@link CharacterInfo#PLATFORM_RN}
     *                 or {@link CharacterInfo#PLATFORM_NATIVE}
     * @return target platform character list
     * @see CharacterInfo
     */
    public List<Character> getCharactersByPlatform(String platform) {
        List<Character> characters = new ArrayList<>();
        for (Map.Entry<CharacterInfo, Character> entry : mCharacters.entrySet()) {
            CharacterInfo info = entry.getKey();
            if (info != null && info.getPlatform() != null
                    && info.getPlatform().equals(platform)) {
                if (entry.getValue() != null) {
                    characters.add(entry.getValue());
                }
            }
        }
        return characters;
    }

    /**
     * 注册角色
     *
     * @param name      角色名称
     * @param platform  角色实现平台
     * @param character 角色实例
     * @return 如果当前角色名称已存在且实例不为null返回false, 否则返回true
     */
    private boolean registerCharacter(String name, String platform, Character character) {
        synchronized (mCharacters) {
            CharacterInfo info = new CharacterInfo(name, platform);
            mCharacters.put(info, character);
            return true;
        }
    }

    /**
     * check character is registered
     *
     * @param name     character id
     * @param platform platform name {@link CharacterInfo#PLATFORM_RN}
     *                 or {@link CharacterInfo#PLATFORM_NATIVE}
     * @return true - registered false - otherwise
     */
    public boolean isRegistered(String name, String platform) {
        CharacterInfo info = new CharacterInfo(name, platform);
        return mCharacters.containsKey(info);
    }


    /**
     * 根据名称获取角色
     * 如果启用RN平台则优先获取RN平台实现的角色，RN未实现获取Native实现
     *
     * @param name 角色名称
     * @return Character
     */
    public Character getCharacter(String name) {
        synchronized (mCharacters) {
            Character character = getCharacter(name, platform);
            if (character == null) {
                switch (platform) {
                    case CharacterInfo.PLATFORM_NATIVE:
                        character = getCharacter(name, CharacterInfo.PLATFORM_RN);
                        break;

                    case CharacterInfo.PLATFORM_RN:
                        character = getCharacter(name, CharacterInfo.PLATFORM_NATIVE);
                        break;

                    default:
                        break;
                }
            }
            return character;
        }
    }

    public Character getCharacter(int code) {
        for (Character character : mCharacters.values()) {
            if (code == character.getCharacterCode()) {
                return character;
            }
        }
        return null;
    }

    /**
     * 设置默认角色
     *
     * @param character Character
     */
    public void setDefault(Character character) {
        CharacterInfo info = getCharacterInfo(character);
        if (info == null) {
            return;
        }

        if (!character.isNeedToBeDefault()) {
            Log.d(TAG, "setDefault not need set default, name: " + info.getName());
            return;
        }

        if (mDefault != null) {
            mDefault.setDefault(false);
        }

        info.setDefault(true);
        //将当前角色的名称，设置到ROBOT_SETTING_CURRENT_CHARACTER中
        String name = info.getName();
        Log.i(TAG, "setDefault: characterName=" + name);
        SettingsUtil.putString(BaseApplication.getContext(), SettingsUtil.ROBOT_SETTING_CURRENT_CHARACTER, name);
        if (CharacterInfo.PLATFORM_NATIVE.equals(info.getPlatform())) {
            //不是rn角色则设置成空
            SettingsUtil.putString(BaseApplication.getContext(), SettingsUtil.ROBOT_SETTING_OPK_INFO, "");
        }
        ModuleProperties.saveDefaultCharacter(info.getName());
        Log.i(TAG, "setDefault: platform=" + info.getPlatform());
        setDefaultCharacterInfo(info);
        updatePlatform(info.getPlatform());
    }

    public void clearDefault() {
        if (mDefault != null) {
            mDefault.setDefault(false);
            mDefault = null;
        }
    }

    public boolean switchPlatform(String platform) {
        if (updatePlatform(platform)) {
            Log.i(TAG, "switchPlatform: success");
            setDefaultCharacterInfo(null);
            return true;
        }
        Log.i(TAG, "switchPlatform: fail");
        return false;
    }

    private boolean updatePlatform(String platform) {
        Log.d(TAG, "Switch platform, platform : "
                + platform + "  current : "
                + this.platform);
        boolean result = ModuleProperties.updatePlatform(platform);
        Log.i(TAG, "update settings platform result= " + result);
        if (TextUtils.equals(this.platform, platform)) {
            return false;
        }
        this.platform = platform;
        return true;
    }

    public boolean resetPlatform() {
        return switchPlatform(CharacterInfo.PLATFORM_NATIVE);
    }

    private void setDefaultCharacterInfo(CharacterInfo info) {
        this.mDefault = info;
    }

    public boolean isPlatformSupport(String intent) {
        if (TextUtils.isEmpty(intent)) {
            return false;
        }

        switch (intent) {
            case PlatformDef.SET_WORK_MODE:
                return CharacterInfo.PLATFORM_NATIVE.equals(this.platform);

            default:
                return true;
        }
    }

    /**
     * 获取默认角色
     *
     * @return Character
     */
    public Character getDefault() {
        Character character = null;
        Log.d(TAG, "Get default mDefault : " + mDefault + ", platform: " + platform);
        if (mDefault == null) {
            if (CharacterInfo.PLATFORM_RN.equals(platform)) {
                character = getCharacter("", CharacterInfo.PLATFORM_RN);
            }
        } else {
            //为和云端保持一致，同步下云端角色信息
            syncRemoteCharacter();
            character = mCharacters.get(mDefault);
        }

        if (character == null) {
            Log.d(TAG, "Get default character failed, use native default");
            updatePlatform(CharacterInfo.PLATFORM_NATIVE);
            character = getRemoteCharacter();
        }
        Log.d(TAG, "Get default character : " + character);
        return character == null ? getDefaultByPlatform(CharacterInfo.PLATFORM_NATIVE) : character;
    }

    /**
     * get default character by platform
     *
     * @param platform platform name
     * @return platform default character
     */
    public Character getDefaultByPlatform(String platform) {
        for (CharacterInfo characterInfo : mCharacters.keySet()) {
            Character character = mCharacters.get(characterInfo);
            if (character != null && characterInfo.getPlatform().equals(platform)
                    && character.isDefault()) {
                return character;
            }
        }
        return getCharacter("", platform);
    }

    /**
     * 从数据库读取所有角色信息
     */
    public void loadCharacterInfo() {
        String defaultName = ModuleProperties.loadDefaultCharacter();
        Log.d(TAG, "Current default character : " + defaultName);
        for (CharacterInfo info : mCharacters.keySet()) {
            Log.i(TAG, "loadCharacterInfo: characterName=" + info.getName());
            if (info.getName().equals(defaultName)) {
                Log.d(TAG, "loadCharacterInfo default : " + info.getName());
                updatePlatform(info.getPlatform());
                setDefaultCharacterInfo(info);
            }
        }
    }

    /**
     * 同步云端角色
     */
    private void syncRemoteCharacter() {
        //目前RN环境不支持云端切换
        if (CharacterInfo.PLATFORM_RN.equals(platform)) {
            return;
        }

        Character character = getRemoteCharacter();
        Log.d(TAG, "Sync remote character : " + character);
        if (character != null) {
            setDefault(character);
        }
    }

    private Character getRemoteCharacter() {
        int code = FeatureConfig.getCurrentCharacterCode();
        Log.d(TAG, "Get remote character : " + code);
        if (code == Integer.MIN_VALUE) {
            return null;
        }
        return getCharacter(code);
    }

    private Character getCharacter(String name, @CharacterInfo.Platform String platform) {
        Log.i(TAG, "getCharacter: name=" + name + " platform=" + platform);
        if (TextUtils.isEmpty(platform)) {
            platform = CharacterInfo.PLATFORM_NATIVE;
        }

        if (TextUtils.isEmpty(name)) {
            for (CharacterInfo characterInfo : mCharacters.keySet()) {
                if (characterInfo.getPlatform().equals(platform)) {
                    return mCharacters.get(characterInfo);
                }
            }
        }
        CharacterInfo info = new CharacterInfo(name, platform);
        return mCharacters.get(info);
    }

    public CharacterInfo getCharacterInfo(Character character) {
        for (Map.Entry<CharacterInfo, Character> entry : mCharacters.entrySet()) {
            if (entry.getValue() == character) {
                return entry.getKey();
            }
        }
        return null;
    }

    public Character getSpecialCharacterByStartIntent(String intent) {
        if (intent != null) {
            for (Character character : mCharacters.values()) {
                List<String> intents = character.getStartIntents();
                if (intents != null) {
                    for (String i : intents) {
                        if (intent.equals(i)) {
                            return character;
                        }
                    }
                }
            }
        }
        return null;
    }
}
