
/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.character;

import com.ainirobot.coreservice.client.speech.SkillCallback;

import java.util.List;

public interface Character {

    String FIRST_BOOT = "firstBoot";
    String OPK_DATA = "opkData";
    String LAST_CHARACTER = "lastCharacter";

    /**
     *
     * @param params start character request param,format-JSON
     * @return true-success, false-fail
     */
    boolean start(String params);

    /**
     * 处理指令
     *
     * @param reqId  request id
     * @param intent 指令标识
     * @param text   指令对应文本
     * @param params 指令参数
     * @return Request已处理，返回true
     */
    boolean handleRequest(int reqId, String intent, String text, String params);

    /**
     * 处理Hardware异常
     *
     * @param function Hardware id
     * @param type     异常类型
     * @param message  异常详细信息
     * @return 成功处理返回true
     */
    boolean handleHWException(int function, String type, String message);

    /**
     * 获取语音回调
     *
     * @return
     */
    SkillCallback getSkillCallback();

    /**
     * 处理被系统挂起事件
     */
    void handleSuspend();

    /**
     * 处理控制权恢复事件
     */
    void handleRecovery();

    /**
     * 处理API连接事件
     */
    void handleApiConnection();

    /**
     * 处理API断连事件
     */
    void handleApiDisconnection();

    /**
     * 停止当前角色运行
     *
     * @param timeout 停止超时时间
     * @return 成功返回true
     */
    boolean stop(long timeout);

    /**
     * uninstall character
     * <p>
     * NOTE:ban uninstall current character
     *
     * @param params uninstall request param ,format-JSON
     * @return true-uninstall success, false-fail
     */
    boolean uninstall(String params);


    /**
     * get this character info
     *
     * @return Character info,format-JSON
     */
    String getCharacterInfo();

    /**
     * get character code
     *
     * @return Character code
     */
    int getCharacterCode();

    /**
     * get character start intents
     * @return List<String>
     */
    List<String> getStartIntents();

    /**
     * is default character in special platform
     * @return boolean
     */
    boolean isDefault();

    /**
     * is need to be default character in special platform
     * @return boolean
     */
    boolean isNeedToBeDefault();

    /**
     * 设置角色事件监听
     *
     * @param listener
     */
    void setCharacterListener(CharacterListener listener);

    interface CharacterListener {
        void onFinished();
    }
}
