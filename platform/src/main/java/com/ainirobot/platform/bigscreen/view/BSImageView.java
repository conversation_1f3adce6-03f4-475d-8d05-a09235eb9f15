package com.ainirobot.platform.bigscreen.view;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.drawable.ClipDrawable;
import android.graphics.drawable.Drawable;
import android.net.Uri;

import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.Gravity;
import android.view.animation.LinearInterpolator;

import com.ainirobot.platform.R;
import com.ainirobot.platform.react.server.control.RNServerManager;

import java.io.File;
import java.util.Timer;
import java.util.TimerTask;

public class BSImageView extends androidx.appcompat.widget.AppCompatImageView {
    private static final String TAG = "BSImageView";
    private static final int DEFAULT_ANIMATION_TIME = 0;

    private Timer timer;
    private ITimeoutListener mTimeoutListener;
    private volatile boolean mIsAnimating;

    public BSImageView(Context context) {
        this(context, null);
    }

    public BSImageView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BSImageView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        // do something for initialization.
    }

    public void setTimeoutListener(ITimeoutListener listener) {
        mTimeoutListener = listener;
    }

    public void showImage(String filePath, int period, boolean needBackground) {
        if (period > DEFAULT_ANIMATION_TIME) {
            changeImage(Drawable.createFromPath(filePath), needBackground);
        } else {
            setImageURI(Uri.fromFile(new File(filePath)));
        }

        cancelTimer();
        timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                cancelTimer();
                if (mTimeoutListener != null) {
                    mTimeoutListener.onImageTimeout();
                }
            }
        }, period);
    }

    public void showDefaultImage(boolean needBackground) {
        boolean isSaiphDp = RNServerManager.getInstance().getApiServer().isSaiphDp();
        if (isSaiphDp){
            changeImage(getResources().getDrawable(R.drawable.big_screen_default_image_saiph, getContext().getTheme()), needBackground);
        }else {
            changeImage(getResources().getDrawable(R.drawable.big_screen_default_image, getContext().getTheme()), needBackground);
        }
    }

    public void showDefaultImage(boolean needBackground, String path) {
        changeImage(Drawable.createFromPath(path), needBackground);
    }

    private void cancelTimer() {
        if (timer != null) {
            timer.purge();
            timer.cancel();
            timer = null;
        }
    }

    public interface ITimeoutListener {
        void onImageTimeout();

        void onImageAnimationEnd();
    }

    public void changeImage(Drawable drawable, boolean needBackground) {
        if (drawable == null) {
            Log.d(TAG, "changeImage: drawable == null");
            return;
        }

        if (getDrawable() != null && needBackground) {
            setBackground(getDrawable());
        } else {
            setBackground(null);
        }

        final ClipDrawable imageDrawable = new ClipDrawable(drawable, Gravity.TOP | Gravity.START, ClipDrawable.HORIZONTAL);
        setImageDrawable(imageDrawable);
        imageDrawable.setLevel(0);

        Log.d(TAG, "changeImage: mIsAnimating = " + mIsAnimating);
        if (!mIsAnimating) {
            mIsAnimating = true;
            ValueAnimator animator = ValueAnimator.ofInt(0, 10000);
            animator.setDuration(DEFAULT_ANIMATION_TIME);
            animator.setInterpolator(new LinearInterpolator());
            animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    int progress = (Integer) animation.getAnimatedValue();
                    imageDrawable.setLevel(progress);
                }
            });
            animator.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {

                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    mIsAnimating = false;
                    if (mTimeoutListener != null) {
                        mTimeoutListener.onImageAnimationEnd();
                    }
                }

                @Override
                public void onAnimationCancel(Animator animation) {

                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });
            animator.start();
        } else {
            imageDrawable.setLevel(10000);
        }
    }
}
