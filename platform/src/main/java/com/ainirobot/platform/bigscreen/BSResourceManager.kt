package com.ainirobot.platform.bigscreen

import android.content.Context
import android.graphics.drawable.Drawable
import androidx.core.content.res.ResourcesCompat
import com.ainirobot.coreservice.client.ProductInfo
import com.ainirobot.platform.R

object BSResourceManager {

    private var resourceList: ArrayList<BSResource> = ArrayList()
    private var defImagePath: String? = null
    private var index: Int = 0;

    /**
     * 获取默认图片
     */
    fun getDefaultImage(): Drawable? {
        return Drawable.createFromPath(defImagePath)
    }

    fun get(): BSResource? {
        if (index >= resourceList.size) {
            return null
        }
        return resourceList.get(index)
    }

    fun next(): BSResource? {
        index++
        if (index >= resourceList.size) {
            index = 0
        }
        return get()
    }

    fun removeResource(resource: BSResource) {
        resourceList.remove(resource)
    }

    private fun getDrawable(context: Context, id: Int): Drawable? {
        return ResourcesCompat.getDrawable(context.resources, id, context.theme)
    }

    fun loadResource() {
        //TODO: 加载资源
    }
}

/**
 * 大屏播放资源
 */
data class BSResource(
    var resourceType: Int = 0,
    var filePath: String? = null,
    var needSound: Boolean = false,
    var duration: Int = 10
) : Cloneable {

    companion object {
        const val TYPE_IMAGE = 1
        const val TYPE_VIDEO = 2
    }
}