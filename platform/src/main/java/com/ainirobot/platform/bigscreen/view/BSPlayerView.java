package com.ainirobot.platform.bigscreen.view;

import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.SurfaceTexture;
import android.media.MediaPlayer;
import android.os.Build;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Surface;
import android.view.TextureView;

import java.io.File;


@TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH)
public class BSPlayerView extends TextureView /*implements VastReceiver.ReceiverListener*/ {

    private static final String TAG = "Mp4Viewer";

    // Stuff we need for playing and showing a video
    private BSMediaPlayer BSMediaPlayer;

    public BSPlayerView(final Context context) {
        super(context);
        initVideoView();
    }

    public BSPlayerView(final Context context,
                        final AttributeSet attrs) {
        super(context, attrs);
        initVideoView();
    }

    public BSPlayerView(Context context,
                        AttributeSet attrs,
                        int defStyle) {
        super(context, attrs, defStyle);
        initVideoView();
    }

    private void initVideoView() {
        BSMediaPlayer = new BSMediaPlayer(getContext());
        setSurfaceTextureListener(surfaceTextureListener);
        setBackgroundColor(getResources().getColor(android.R.color.transparent));
        setFocusable(false);
    }

    @Override
    public void setBackgroundColor(int color) {
        int targetSdkVersion = getContext().getApplicationInfo().targetSdkVersion;
        if (targetSdkVersion <= 23) {
            super.setBackgroundColor(color);
        }
    }


    public void setAutoPlay(boolean autoPlay) {
        if (BSMediaPlayer != null) {
            BSMediaPlayer.setAutoPlay(autoPlay);
        }
    }

    public void setDuration(int duration) {
        if (BSMediaPlayer != null) {
            BSMediaPlayer.setDuration(duration);
        }
    }

    public boolean setVideoPath(String path) {
        if (TextUtils.isEmpty(path)) {
            return false;
        }

        File videoFile = new File(path);
        if (!videoFile.exists()) {
            return false;
        }

        try {
            BSMediaPlayer.setVideoPath(path);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public void reset() {
        BSMediaPlayer.setTargetState(BSMediaPlayer.STATE_IDLE);
    }

    public void start() {
        BSMediaPlayer.setTargetState(BSMediaPlayer.STATE_STARTED);
    }

    public void seekTo(int position) {
        BSMediaPlayer.seekTo(position);
    }

    public void pause() {
        BSMediaPlayer.setTargetState(BSMediaPlayer.STATE_PAUSED);
    }

    public void stop() {
        BSMediaPlayer.setTargetState(BSMediaPlayer.STATE_STOPPED);
    }

    public void release() {
        BSMediaPlayer.setTargetState(BSMediaPlayer.STATE_RELEASED);
    }

    public int getCurrentPosition() {
        return BSMediaPlayer.getCurrentPosition();
    }

    public int getTargetState() {
        return BSMediaPlayer.getTargetState();
    }

    public void setSupportAudio(boolean support) {
        BSMediaPlayer.setSupportAudio(support);
    }

    public void setVolume(float left, float right) {
        BSMediaPlayer.setVolume(left, right);
    }


    public void setStateListener(BSMediaPlayer.Mp4StateListener listener) {
        BSMediaPlayer.setMp4StateListener(listener);
    }

    public void setProgressListener(BSMediaPlayer.Mp4ProgressListener listener) {
        BSMediaPlayer.setMp4ProgressListener(listener);
    }

    public void setErrorListener(MediaPlayer.OnErrorListener errorListener) {
        BSMediaPlayer.setMediaPlayerErrorListener(errorListener);
    }

    private SurfaceTextureListener surfaceTextureListener = new SurfaceTextureListener() {
        @Override
        public void onSurfaceTextureAvailable(SurfaceTexture surfaceTexture,
                                              int width,
                                              int height) {
            if (BSMediaPlayer != null) {
                BSMediaPlayer.setSurface(new Surface(surfaceTexture));
            }
        }

        @Override
        public void onSurfaceTextureSizeChanged(SurfaceTexture surface,
                                                int width,
                                                int height) {

        }

        @Override
        public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {
            if (BSMediaPlayer != null) {
                BSMediaPlayer.setSurface(null);
            }
            return false;
        }

        @Override
        public void onSurfaceTextureUpdated(SurfaceTexture surface) {

        }
    };
}
