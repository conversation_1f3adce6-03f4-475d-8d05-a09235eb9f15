package com.ainirobot.platform.bigscreen

import android.content.Context
import android.media.MediaPlayer
import android.text.TextUtils
import android.util.Log
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.bigscreen.view.BSImageView
import com.ainirobot.platform.bigscreen.view.BSMediaPlayer
import com.ainirobot.platform.bigscreen.view.BSPlayerView
import com.ainirobot.platform.react.reactnative.component.uicomponent.external.view.ExternalDisplay
import com.ainirobot.platform.utils.SystemUtils
import java.lang.ref.WeakReference

/**
 * 大屏本地控制
 * 根据资源配置，自动播放
 */
object BSLocalController {

    private const val TAG = "BSLocalController"

    private lateinit var contextRef: WeakReference<Context>

    private lateinit var videoView: BSPlayerView
    private lateinit var imageView: BSImageView

    fun init(context: Context) {
        Log.d(TAG, "Init : " + SystemUtils.isBigShowEnabled())
        if (!SystemUtils.isBigShowEnabled() &&
            !BaseApplication.getApplication().isSupportBigScreen
        ) {
            return
        }
        contextRef = WeakReference(context.applicationContext)
        initExternalDisplay(context)
        BSResourceManager.loadResource()
        initVideoView(context)
        initImageView(context)
    }

    fun start() {
        val resource = BSResourceManager.get()
        resource?.let { playResource(it) }
    }

    fun stop() {
        if (this::videoView.isInitialized) {
            videoView.stop()
            videoView.release()
        }
        ExternalDisplay.hide()
    }

    private fun initVideoView(context: Context) {
        videoView = BSPlayerView(context)
        videoView.setErrorListener(MediaPlayer.OnErrorListener { _, _, _ ->
            playNext()
            return@OnErrorListener true
        })
        videoView.setStateListener { state ->
            if (state == BSMediaPlayer.STATE_PLAYBACK_COMPLETED) {
                playNext()
            }
        }
        videoView.setProgressListener { _, currentPosition ->
            if (currentPosition > 50) {
                ExternalDisplay.show(videoView)
            }
        }
        videoView.setVolume(1f, 1f)
        videoView.setSupportAudio(false)
    }

    private fun initImageView(context: Context) {
        imageView = BSImageView(context)
        imageView.setTimeoutListener(object : BSImageView.ITimeoutListener {
            override fun onImageTimeout() {
                playNext()
            }

            override fun onImageAnimationEnd() {
                ExternalDisplay.show(imageView)
                videoView.release()
            }
        })
    }

    private fun initExternalDisplay(context: Context) {
        ExternalDisplay.init(context)
        val drawable = BSResourceManager.getDefaultImage()
        drawable?.let { ExternalDisplay.setDefaultImage(it) }
        ExternalDisplay.show()
    }

    private fun playNext() {
        val resource = BSResourceManager.next()
        resource?.let { playResource(it) }
    }

    private fun playResource(resource: BSResource) {
        if (TextUtils.isEmpty(resource.filePath)) {
            BSResourceManager.removeResource(resource)
            playNext()
            return
        }

        when (resource.resourceType) {
            BSResource.TYPE_IMAGE -> {
                imageView.showImage(resource.filePath, resource.duration * 1000, true)
                ExternalDisplay.show(imageView)

            }
            BSResource.TYPE_VIDEO -> {
                videoView.reset()
                videoView.setSupportAudio(resource.needSound)
                videoView.setVideoPath(resource.filePath)
                videoView.start()
            }
            else -> {
                BSResourceManager.removeResource(resource)
                playNext()
            }
        }
    }
}