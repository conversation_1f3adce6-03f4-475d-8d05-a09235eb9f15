package com.ainirobot.platform.bigscreen.view;

import android.annotation.TargetApi;
import android.content.Context;
import android.media.MediaPlayer;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.text.TextUtils;
import android.util.Log;
import android.view.Surface;

import com.ainirobot.platform.utils.FileUtils;

import java.io.FileInputStream;
import java.util.HashMap;

/**
 * Created by <PERSON> on 2015/7/17.
 */
public class BSMediaPlayer {
    private static final String TAG = "BSMediaPlayer";

    // playback state
    public static final int STATE_IDLE = 0;
    public static final int STATE_INITIALIZED = 1;
    // We will make media player running on a non-ui thread,
    // all the state change is synchronized in this thread,
    // so we don't need preparing state.
    public static final int STATE_PREPARED = 2;
    public static final int STATE_STARTED = 3;
    public static final int STATE_PAUSED = 4;
    public static final int STATE_PLAYBACK_COMPLETED = 5;
    public static final int STATE_STOPPED = 6;
    public static final int STATE_RELEASED = 7;
    public static final int STATE_ERROR = 8;

    private Context context;
    private MediaPlayer mediaPlayer;
    private Mp4StateMachine stateMachine;

    private String videoPath;

    private Mp4StateListener mp4StateListener;
    private Mp4ProgressListener mp4ProgressListener;
    private MediaPlayer.OnErrorListener mediaPlayerErrorListener;

    private boolean supportAudio = true;
    private boolean autoPlay;
    private boolean looping;
    private float leftVolume;
    private float rightVolume;
    private int duration;


    public BSMediaPlayer(Context context) {
        this.context = context;
        mediaPlayer = new MediaPlayer();
        stateMachine = new Mp4StateMachine();
        autoPlay = true;
        looping = false;//auto replay
        leftVolume = 0.5f;
        rightVolume = 0.5f;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public void setAutoPlay(boolean autoPlay) {
        this.autoPlay = autoPlay;
    }

    public void setVideoPath(String path) {
        if (TextUtils.isEmpty(path)) {
            return;
        }

        videoPath = path;
        if (autoPlay && stateMachine.getTargetState() == STATE_IDLE) {
            setTargetState(STATE_STARTED);
            stateMachine.moveToState(STATE_IDLE);
        } else {
            stateMachine.fightForTarget();
        }
    }

    public void setTargetState(int state) {
        stateMachine.setTargetState(state);
    }

    public int getTargetState() {
        return stateMachine.getTargetState();
    }

    public void setSurface(Surface surface) {
        stateMachine.setSurface(surface);
    }

    public void setMp4StateListener(Mp4StateListener listener) {
        mp4StateListener = listener;
    }

    public void setMp4ProgressListener(Mp4ProgressListener listener) {
        mp4ProgressListener = listener;
    }

    private void notifyStateChanged(final int state) {
        if (mp4StateListener != null) {
            mp4StateListener.onStateInUiThread(state);
        }
    }

    private void notifyProgress(final int totalLength, final int currentPosition) {
        if (mp4ProgressListener != null) {
            mp4ProgressListener.onProgressInUiThread(totalLength, currentPosition);
        }
    }

    public void setSupportAudio(boolean support) {
        this.supportAudio = support;
    }

    public void setVolume(float left,
                          float right) {
        if (left < 0) {
            leftVolume = 0;
        } else if (left > 1) {
            leftVolume = 1;
        } else {
            leftVolume = left;
        }

        if (right < 0) {
            rightVolume = 0;
        } else if (right > 1) {
            rightVolume = 1;
        } else {
            rightVolume = right;
        }

        if (mediaPlayer != null) {
            try {
                mediaPlayer.setVolume(left, right);
            } catch (Exception e) {
            }
        }
    }

    public void seekTo(int playTime) {
        try {
            if (mediaPlayer != null) {
                mediaPlayer.seekTo(playTime);
            }
        } catch (Exception e) {
        }
    }

    public int getCurrentPosition() {
        if (mediaPlayer != null) {
            Log.d(TAG, "getCurrentPosition: mediaPlayer = " + mediaPlayer);
            return mediaPlayer.getCurrentPosition();
        } else {
            return 0;
        }
    }

    public void setMediaPlayerErrorListener(MediaPlayer.OnErrorListener mediaPlayerErrorListener) {
        this.mediaPlayerErrorListener = mediaPlayerErrorListener;
    }

    public interface Mp4StateListener {
        void onStateInUiThread(int state);
    }

    public interface Mp4ProgressListener {
        void onProgressInUiThread(int totalLength, int currentPosition);
    }

    /**
     * 维护MediaPlayer状态迁移的状态机;
     * <p/>
     * 没有使用系统的StateMachine的原因是：
     * 1, 从他代码看，他的状态是以树的形式进行维护切换的，目前media player的状态是图的形式，
     * 如果要使用它的状态机，我们需要把它拆分成多棵树，有点费事。
     * 2，考虑到扩展性和调试方便性，可能还是我们自己写一个状态机可能会简单点。
     */
    private class Mp4StateMachine {
        private HashMap<Integer, Mp4State> stateHashMap;
        private Mp4Thread mp4Thread;
        private int currentState;
        private int targetState;
        private Surface surface;

        private Runnable progressRunnable = new Runnable() {
            @Override
            public void run() {
                int totalLength = duration;
                int currentPosition = getCurrentPosition();
                notifyProgress(totalLength, currentPosition);

                if (currentState == STATE_STARTED) {
                    if (currentPosition < 100) {
                        mp4Thread.postDelayedTask(this, 50);
                    } else {
                        mp4Thread.postDelayedTask(this, 1000);
                    }
                } else {
                    mp4Thread.removeTask(this);
                }
            }
        };

        void handlerProgress(boolean start) {
            if (start) {
                mp4Thread.postTask(progressRunnable);
            } else {
                mp4Thread.removeTask(progressRunnable);
            }
        }

        private int getCurrentState() {
            return currentState;
        }

        private void setCurrentState(final int state) {
            mp4Thread.postTask(new Runnable() {
                @Override
                public void run() {
                    currentState = state;
                }
            });
        }

        private int getTargetState() {
            return targetState;
        }

        public void setTargetState(final int state) {
            mp4Thread.postTask(new Runnable() {
                @Override
                public void run() {
                    targetState = state;
                    fightForTarget();
                }
            });
        }

        public void setSurface(final Surface s) {
            mp4Thread.postTask(new Runnable() {
                @Override
                public void run() {
                    surface = s;
                    if (surface == null) {
                        moveToState(STATE_ERROR);
                    } else {
                        fightForTarget();
                    }
                }
            });
        }

        public Surface getSurface() {
            return surface;
        }

        public Mp4StateMachine() {
            stateHashMap = new HashMap<>();
            mp4Thread = new Mp4Thread();
            currentState = STATE_IDLE;
            targetState = STATE_IDLE;
        }

        private void moveToState(final int id) {
            mp4Thread.postTask(new Runnable() {
                @Override
                public void run() {
                    Mp4State state = getState(id);
                    if (state != null) {
                        if (state.run()) {
                            state.next();
                        } else {
                            // do nothing
                        }
                    }
                }
            });
        }

        public void fightForTarget() {
            mp4Thread.postTask(new Runnable() {
                @Override
                public void run() {
                    Mp4State state = getState(currentState);
                    if (state != null) {
                        state.next();
                    }
                }
            });
        }

        private Mp4State getState(int id) {
            Mp4State state;
            if (stateHashMap.containsKey(id)) {
                state = stateHashMap.get(id);
            } else {
                switch (id) {
                    case STATE_IDLE: {
                        state = new Mp4Idle();
                        break;
                    }

                    case STATE_INITIALIZED: {
                        state = new Mp4Initialized();
                        break;
                    }

                    case STATE_PREPARED: {
                        state = new Mp4Prepared();
                        break;
                    }

                    case STATE_STARTED: {
                        state = new Mp4Started();
                        break;
                    }

                    case STATE_PAUSED: {
                        state = new Mp4Paused();
                        break;
                    }

                    case STATE_PLAYBACK_COMPLETED: {
                        state = new Mp4PlaybackComplete();
                        break;
                    }

                    case STATE_STOPPED: {
                        state = new Mp4Stopped();
                        break;
                    }

                    case STATE_RELEASED: {
                        state = new Mp4Released();
                        break;
                    }

                    case STATE_ERROR: {
                        state = new Mp4Error();
                        break;
                    }

                    default: {
                        state = null;
                        break;
                    }
                }

                if (state != null) {
                    stateHashMap.put(id, state);
                }
            }

            return state;
        }

        public void releaseSurface() {
            if (surface != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
                    surface.release();
                }
                surface = null;
            }
        }
    }

    private class Mp4Thread {
        private static final String TAG = "Mp4Thread_";
        private HandlerThread handlerThread;
        private Handler handler;

        private Mp4Thread() {
            handlerThread = new HandlerThread(TAG + System.currentTimeMillis());
            handlerThread.start();
            handler = new Handler(handlerThread.getLooper());
        }

        private boolean isMp4Thread() {
            return handlerThread.getId() == Thread.currentThread().getId();
        }

        private void postTask(Runnable runnable) {
            if (isMp4Thread()) {
                runnable.run();
            } else {
                handler.post(runnable);
            }
        }

        private void postDelayedTask(Runnable runnable, int delay) {
            handler.postDelayed(runnable, delay);
        }

        private void removeTask(Runnable runnable) {
            handler.removeCallbacks(runnable);
        }
    }

    private abstract class Mp4State {
        public abstract String name();

        public abstract boolean run();

        public abstract boolean next();

        public void updateState(int state) {
            stateMachine.setCurrentState(state);
            notifyStateChanged(state);

            if (state == STATE_STARTED) {
                stateMachine.handlerProgress(true);
            } else {
                stateMachine.handlerProgress(false);
            }

            // 如果出错了，就把player资源释放了
            if (state == STATE_ERROR) {
                releaseMediaPlayer();
            }
        }

        public void toNextState(int state) {
            if (stateMachine != null) {
                stateMachine.moveToState(state);
            }
        }

        public synchronized void releaseMediaPlayer() {
            Log.d(TAG, "releaseMediaPlayer: ");
            if (mediaPlayer != null) {
                mediaPlayer.stop();
                mediaPlayer.reset();
                mediaPlayer.release();
                mediaPlayer = null;
            }
        }
    }

    private class Mp4Idle extends Mp4State {

        @Override
        public String name() {
            return "STATE_IDLE";
        }

        @Override
        public boolean run() {
            if (mediaPlayer == null) {
                mediaPlayer = new MediaPlayer();
            }

            if (stateMachine.getCurrentState() != STATE_IDLE) {
                mediaPlayer.reset();
                updateState(STATE_IDLE);
            }

            return true;
        }

        @Override
        public boolean next() {
            switch (stateMachine.getTargetState()) {
                case STATE_IDLE: {
                    break;
                }

                case STATE_INITIALIZED:
                case STATE_PREPARED:
                case STATE_STARTED:
                case STATE_PAUSED:
                case STATE_PLAYBACK_COMPLETED:
                case STATE_STOPPED: {
                    toNextState(STATE_INITIALIZED);
                    break;
                }

                case STATE_RELEASED: {
                    toNextState(STATE_RELEASED);
                    break;
                }
            }

            return true;
        }
    }

    private class Mp4Initialized extends Mp4State {

        @Override
        public String name() {
            return "STATE_INITIALIZED";
        }

        @TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH)
        @Override
        public boolean run() {
            Surface surface = stateMachine.getSurface();
            if (mediaPlayer != null &&
                    stateMachine.getCurrentState() == STATE_IDLE &&
                    surface != null &&
                    FileUtils.checkFileExist(videoPath)) {
                try {
                    mediaPlayer.setVolume(0, 0);
//                    if (supportAudio && (leftVolume > 0 || rightVolume > 0)) {
//                        mediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
//                        mediaPlayer.setVolume(leftVolume, rightVolume);
//                    } else {
//                        mediaPlayer.setVolume(0, 0);
//                    }

                    mediaPlayer.setLooping(looping);
                    mediaPlayer.setOnErrorListener(new MediaPlayer.OnErrorListener() {
                        @Override
                        public boolean onError(MediaPlayer mp,
                                               int what,
                                               int extra) {
                            stateMachine.mp4Thread.postTask(new Runnable() {
                                @Override
                                public void run() {
                                    updateState(STATE_ERROR);
                                }
                            });

                            try {
                                if (mediaPlayerErrorListener != null) {
                                    mediaPlayerErrorListener.onError(mp, what, extra);
                                }
                            } catch (Throwable e) {
                                Log.d(TAG, "media player onError: " + e.getMessage());
                            }

                            return false;
                        }
                    });
                    mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                        @Override
                        public void onCompletion(MediaPlayer mp) {
                            Log.d(TAG, "onCompletion: mp = " + mp);
                            stateMachine.mp4Thread.postTask(new Runnable() {
                                @Override
                                public void run() {
                                    updateState(STATE_PLAYBACK_COMPLETED);
                                }
                            });
                            notifyProgress(duration, duration);
                        }
                    });

                    mediaPlayer.setSurface(surface);

                    FileInputStream fileInputStream = new FileInputStream(videoPath);
                    mediaPlayer.setDataSource(fileInputStream.getFD());
                    fileInputStream.close();

//                    mediaPlayer.setDataSource(context, Uri.fromFile(new File(videoPath)));
                    updateState(STATE_INITIALIZED);
                    return true;
                } catch (Exception e) {
                    e.printStackTrace();
                    updateState(STATE_ERROR);
                    return false;
                }
            } else {
                return false;
            }
        }

        @Override
        public boolean next() {
            switch (stateMachine.getTargetState()) {
                case STATE_IDLE: {
                    toNextState(STATE_IDLE);
                    break;
                }

                case STATE_INITIALIZED: {
                    break;
                }

                case STATE_PREPARED:
                case STATE_STARTED:
                case STATE_PAUSED:
                case STATE_PLAYBACK_COMPLETED:
                case STATE_STOPPED: {
                    toNextState(STATE_PREPARED);
                    break;
                }

                case STATE_RELEASED: {
                    toNextState(STATE_RELEASED);
                    break;
                }
            }

            return true;
        }
    }

    private class Mp4Prepared extends Mp4State {

        @Override
        public String name() {
            return "STATE_PREPARED";
        }

        @Override
        public boolean run() {
            if (mediaPlayer != null && (stateMachine.getCurrentState() == STATE_INITIALIZED || stateMachine.getCurrentState() == STATE_STOPPED)) {
                try {
                    mediaPlayer.prepare();
                    // 从vastModel中读取,若读取数据错误，读取mediaplayer的时长
                    if (duration <= 0) {
                        duration = mediaPlayer.getDuration();
                        // 若获取的值大于1天
                        if (duration >= 24 * 3600 * 1000) {
                            duration = 0;
                        }
                        Log.d(TAG, "media player getDuration " + duration);
                    }

                    updateState(STATE_PREPARED);
                    return true;
                } catch (Exception e) {
                    e.printStackTrace();
                    updateState(STATE_ERROR);
                    return false;
                }
            } else {
                return false;
            }
        }

        @Override
        public boolean next() {
            switch (stateMachine.getTargetState()) {
                case STATE_IDLE:
                case STATE_INITIALIZED: {
                    toNextState(STATE_IDLE);
                    break;
                }

                case STATE_PREPARED: {
                    break;
                }

                case STATE_STARTED:
                case STATE_PAUSED:
                case STATE_PLAYBACK_COMPLETED: {
                    toNextState(STATE_STARTED);
                    break;
                }

                case STATE_STOPPED: {
                    toNextState(STATE_STOPPED);
                    break;
                }

                case STATE_RELEASED: {
                    toNextState(STATE_RELEASED);
                    break;
                }
            }

            return true;
        }
    }

    private class Mp4Started extends Mp4State {

        @Override
        public String name() {
            return "STATE_STARTED";
        }

        @Override
        public boolean run() {
            if (mediaPlayer != null && (stateMachine.getCurrentState() == STATE_PREPARED || stateMachine.getCurrentState() == STATE_PAUSED || stateMachine.getCurrentState() == STATE_PLAYBACK_COMPLETED)) {
                mediaPlayer.start();
                updateState(STATE_STARTED);
                return true;
            } else {
                if (stateMachine.getCurrentState() == STATE_STARTED) {
                    return true;
                }
                updateState(STATE_ERROR);
                return false;
            }
        }

        @Override
        public boolean next() {
            switch (stateMachine.getTargetState()) {
                case STATE_IDLE:
                case STATE_INITIALIZED: {
                    toNextState(STATE_IDLE);
                    break;
                }

                case STATE_PREPARED:
                case STATE_STOPPED: {
                    toNextState(STATE_STOPPED);
                    break;
                }

                case STATE_STARTED:
                case STATE_PLAYBACK_COMPLETED: {
                    break;
                }

                case STATE_PAUSED: {
                    toNextState(STATE_PAUSED);
                    break;
                }

                case STATE_RELEASED: {
                    toNextState(STATE_RELEASED);
                    break;
                }
            }

            return true;
        }
    }

    private class Mp4Paused extends Mp4State {

        @Override
        public String name() {
            return "STATE_PAUSED";
        }

        @Override
        public boolean run() {
            if (mediaPlayer != null && stateMachine.getCurrentState() == STATE_STARTED) {
                mediaPlayer.pause();
                updateState(STATE_PAUSED);
                return true;
            } else {
                updateState(STATE_ERROR);
                return false;
            }
        }

        @Override
        public boolean next() {
            switch (stateMachine.getTargetState()) {
                case STATE_IDLE:
                case STATE_INITIALIZED: {
                    toNextState(STATE_IDLE);
                    break;
                }

                case STATE_PREPARED:
                case STATE_STOPPED: {
                    toNextState(STATE_STOPPED);
                    break;
                }

                case STATE_STARTED:
                case STATE_PLAYBACK_COMPLETED: {
                    toNextState(STATE_STARTED);
                    break;
                }

                case STATE_PAUSED: {
                    break;
                }

                case STATE_RELEASED: {
                    toNextState(STATE_RELEASED);
                    break;
                }
            }

            return true;
        }
    }

    private class Mp4PlaybackComplete extends Mp4State {

        @Override
        public String name() {
            return "STATE_PLAYBACK_COMPLETED";
        }

        @Override
        public boolean run() {
            if (mediaPlayer != null && stateMachine.getCurrentState() == STATE_STARTED) {
                updateState(STATE_PLAYBACK_COMPLETED);
                return true;
            } else {
                updateState(STATE_ERROR);
                return false;
            }
        }

        @Override
        public boolean next() {
            switch (stateMachine.getTargetState()) {
                case STATE_IDLE:
                case STATE_INITIALIZED: {
                    toNextState(STATE_IDLE);
                    break;
                }

                case STATE_PREPARED:
                case STATE_STOPPED: {
                    toNextState(STATE_STOPPED);
                    break;
                }

                case STATE_STARTED:
                case STATE_PAUSED: {
                    toNextState(STATE_STARTED);
                    break;
                }

                case STATE_PLAYBACK_COMPLETED: {
                    break;
                }

                case STATE_RELEASED: {
                    toNextState(STATE_RELEASED);
                    break;
                }
            }

            return true;
        }
    }

    private class Mp4Stopped extends Mp4State {

        @Override
        public String name() {
            return "STATE_STOPPED";
        }

        @Override
        public boolean run() {
            if (mediaPlayer != null && (stateMachine.getCurrentState() == STATE_PREPARED || stateMachine.getCurrentState() == STATE_STARTED || stateMachine.getCurrentState() == STATE_PAUSED || stateMachine.getCurrentState() == STATE_PLAYBACK_COMPLETED)) {
                mediaPlayer.stop();
                updateState(STATE_STOPPED);
                return true;
            } else {
                updateState(STATE_ERROR);
                return false;
            }
        }

        @Override
        public boolean next() {
            switch (stateMachine.getTargetState()) {
                case STATE_IDLE:
                case STATE_INITIALIZED: {
                    toNextState(STATE_IDLE);
                    break;
                }

                case STATE_STARTED:
                case STATE_PAUSED:
                case STATE_PLAYBACK_COMPLETED:
                case STATE_PREPARED: {
                    toNextState(STATE_PREPARED);
                    break;
                }

                case STATE_STOPPED: {
                    break;
                }

                case STATE_RELEASED: {
                    toNextState(STATE_RELEASED);
                    break;
                }
            }
            return true;
        }
    }

    private class Mp4Released extends Mp4State {

        @Override
        public String name() {
            return "STATE_RELEASED";
        }

        @Override
        public boolean run() {
            if (stateMachine.getCurrentState() != STATE_RELEASED) {
                releaseMediaPlayer();
                updateState(STATE_RELEASED);
            }

            return true;
        }

        @Override
        public boolean next() {
            switch (stateMachine.getTargetState()) {
                case STATE_IDLE:
                case STATE_INITIALIZED:
                case STATE_PREPARED:
                case STATE_STARTED:
                case STATE_PAUSED:
                case STATE_PLAYBACK_COMPLETED:
                case STATE_STOPPED: {
                    toNextState(STATE_IDLE);
                    break;
                }

                case STATE_RELEASED: {
                    break;
                }
            }

            return true;
        }
    }

    private class Mp4Error extends Mp4State {

        @Override
        public String name() {
            return "STATE_ERROR";
        }

        @Override
        public boolean run() {
            if (stateMachine.getCurrentState() != STATE_ERROR) {
                updateState(STATE_ERROR);
            }

            return true;
        }

        @Override
        public boolean next() {
            switch (stateMachine.getTargetState()) {
                case STATE_IDLE:
                case STATE_INITIALIZED:
                case STATE_PREPARED:
                case STATE_STARTED:
                case STATE_PAUSED:
                case STATE_PLAYBACK_COMPLETED:
                case STATE_STOPPED:
                case STATE_RELEASED: {
                    toNextState(STATE_IDLE);
                    break;
                }
            }

            return true;
        }
    }
}
