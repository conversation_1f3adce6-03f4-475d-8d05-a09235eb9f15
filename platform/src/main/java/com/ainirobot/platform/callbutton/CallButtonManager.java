package com.ainirobot.platform.callbutton;

import android.os.Environment;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.call.CallButton;
import com.ainirobot.call.CallClient;
import com.ainirobot.call.ResultListener;
import com.ainirobot.call.proto.ButtonInfo;
import com.ainirobot.call.proto.ButtonMapping;
import com.ainirobot.call.proto.Condition;
import com.ainirobot.call.proto.Result;
import com.ainirobot.call.proto.RobotInfo;
import com.ainirobot.call.proto.State;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.utils.FileHelper;
import com.ainirobot.platform.utils.GsonUtil;

import java.util.Map;

public class CallButtonManager {

    private static final String TAG = CallButtonManager.class.getSimpleName();
    public final static String CALL_BUTTON_SERVER_IP_KEY = "call_button_server_ip_key";
    public final static String CALL_BUTTON_CONNECT_STATE = "call_button_connect_state";
    private final static String BUTTON_MAP_DATA_PATH = Environment.getExternalStorageDirectory() + "/robot/buttonmapdata.txt";
    private static volatile CallButtonManager sCallButtonManager;

    private String mServerIp = "";
    private String mConnectState = "0";

    private MessageListener listener;

    private CallButton.Reply<Condition> sCallPrepareReply;
    private CallButton.Reply<?> sCallReply;
    private CallButton.Reply<?> sCancelReply;
    private CallButton.Reply<?> sButtonMappingChangeReply;

    public static CallButtonManager getInstance() {
        if (sCallButtonManager == null) {
            synchronized (CallButtonManager.class) {
                if (sCallButtonManager == null) {
                    sCallButtonManager = new CallButtonManager();
                }
            }
        }
        return sCallButtonManager;
    }

    public void init() {
        connect();
    }

    public void connect() {
        String serverIp = RobotSettingApi.getInstance().getRobotString(CallButtonManager.CALL_BUTTON_SERVER_IP_KEY);
        if (!TextUtils.isEmpty(serverIp)) {
            this.connect(serverIp);
        }
    }

    public void connect(String serverIp) {
        mServerIp = serverIp;
        RobotSettingApi.getInstance().setRobotString(CallButtonManager.CALL_BUTTON_SERVER_IP_KEY, serverIp);

        RobotInfo.Builder builder = RobotInfo.newBuilder();
        builder.setSn(RobotSettings.getSystemSn());
        ButtonMapping buttonMapping = getCurrentMappingData();
        if (buttonMapping != null) {
            builder.setButtonMappingId(buttonMapping.getId());
            Log.d(TAG, " connect buttonMappingId=" + buttonMapping.getId());
        }
        RobotInfo robotInfo = builder.build();

        Log.d(TAG, " connect serverIp=" + serverIp);

        mConnectState = "2";
        RobotSettingApi.getInstance().setRobotString(CallButtonManager.CALL_BUTTON_CONNECT_STATE, "2");
        CallButton.connect(BaseApplication.getContext(), robotInfo, serverIp, new CallClient.ConnectListener() {
            @Override
            public void onConnected() {
                Log.d(TAG, " connect onConnected");
                mConnectState = "1";
                RobotSettingApi.getInstance().setRobotString(CallButtonManager.CALL_BUTTON_CONNECT_STATE, "1");
                setEventListener();
            }

            @Override
            public void onDisconnected() {
                Log.d(TAG, " connect onDisconnected");
                mConnectState = "0";
                RobotSettingApi.getInstance().setRobotString(CallButtonManager.CALL_BUTTON_CONNECT_STATE, "0");
            }
        });
    }

    public void disconnect() {
        CallButton.disconnect();
    }

    public void registerListener(MessageListener listener) {
        this.listener = listener;
    }

    public void setEventListener() {
        CallButton.setEventListener(new CallButton.EventListener() {
            @Override
            public void callPrepare(ButtonInfo button, CallButton.Reply<Condition> reply) {
                try {
                    Log.d(TAG, "callPrepare " + GsonUtil.toJson(button));
                    sCallPrepareReply = reply;
                    if (listener != null) {
                        listener.onMessageReceived("callPrepare", GsonUtil.toJson(button));
                    }
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void call(ButtonInfo button, CallButton.Reply<?> reply) {
                try {
                    Log.d(TAG, "call " + GsonUtil.toJson(button));
                    sCallReply = reply;
                    if (listener != null) {
                        listener.onMessageReceived("call", GsonUtil.toJson(button));
                    }
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void cancel(ButtonInfo button, CallButton.Reply<?> reply) {
                Log.d(TAG, "cancel " + GsonUtil.toJson(button));
                try {
                    sCancelReply = reply;
                    if (listener != null) {
                        listener.onMessageReceived("cancel", GsonUtil.toJson(button));
                    }
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void buttonMappingChange(ButtonMapping mapping, CallButton.Reply<?> reply) {
                Log.d(TAG, "buttonMappingChange " + GsonUtil.toJson(mapping));
                try {
                    sButtonMappingChangeReply = reply;
                    if (listener != null) {
                        listener.onMessageReceived("buttonMappingChange", GsonUtil.toJson(mapping));
                    }
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
                FileHelper.saveToFile(BUTTON_MAP_DATA_PATH, GsonUtil.toJson(mapping));
                reply.onResult(Result.SUCCESS, "");
            }

            @Override
            public void buttonPress(String message) {
                Log.d(TAG, "buttonPress " + message);
                try {
                    if (listener != null) {
                        listener.onMessageReceived("buttonPress", message);
                    }
                } catch (RemoteException e) {
                    e.printStackTrace();
                }

            }

        });
    }

    public void replyCallPrepare(String result, String msg, String data) {
        Condition.Builder condition = Condition.newBuilder();
        condition.setDistance(Float.parseFloat(data));
        if (sCallPrepareReply != null) {
            sCallPrepareReply.onResult(getResultType(result), msg, condition.build());
        }
    }

    public void replyCall(String result, String msg) {
        if (sCallReply != null) {
            sCallReply.onResult(getResultType(result), msg);
        }
    }

    public void replyCancel(String result, String msg) {
        if (sCancelReply != null) {
            sCancelReply.onResult(getResultType(result), msg);
        }
    }

    public void replyButtonMappingChange(String result, String msg) {
        if (sButtonMappingChangeReply != null) {
            sButtonMappingChangeReply.onResult(getResultType(result), msg);
        }
    }

    public ButtonMapping getCurrentMappingData() {
        if (TextUtils.isEmpty(getCurrentDataMapping())) {
            return null;
        }
        ButtonMapping buttonMapping = GsonUtil.fromJson(getCurrentDataMapping(), ButtonMapping.class);
        return buttonMapping;
    }

    public String getCurrentDataMapping() {
        if (!FileHelper.isExist(BUTTON_MAP_DATA_PATH)) {
            return "";
        }
        return FileHelper.loadJsonFromFile(BUTTON_MAP_DATA_PATH);
    }

    public String getMapName() {
        return RobotApi.getInstance().getMapName();
    }

    private Result getResultType(String result) {
        switch (result) {
            case "unspecified":
            default:
                return Result.RESULT_UNSPECIFIED;
            case "success":
                return Result.SUCCESS;
            case "canceled":
                return Result.CANCELED;
            case "reject":
                return Result.REJECT;
            case "failed":
                return Result.FAILED;
            case "noHandled":
                return Result.NO_HANDLER;
            case "timeout":
                return Result.TIMEOUT;
            case "block":
                return Result.BLOCK;
            case "unrecognized":
                return Result.UNRECOGNIZED;
        }

    }

    public void setRobotState(String state) {
        Log.d(TAG, "setRobotState " + state);
        switch (state) {
            case "busy":
                CallButton.setRobotState(State.BUSY);
                break;
            case "bind":
                CallButton.setRobotState(State.BIND);
                break;
            case "idle":
            default:
                CallButton.setRobotState(State.IDLE);
                break;
        }

    }

    public void syncButtonMapping(Map<String, String> buttonMap) {
        ButtonMapping.Builder mappingBuilder = ButtonMapping.newBuilder();
        mappingBuilder.setId(RobotSettings.getSystemSn() + "_" + System.currentTimeMillis());
        mappingBuilder.setMapName(RobotApi.getInstance().getMapName());
        mappingBuilder.putAllValue(buttonMap);
        ButtonMapping buttonMapping = mappingBuilder.build();

        Log.d(TAG, "syncButtonMapping " + GsonUtil.toJson(buttonMapping));
        FileHelper.saveToFile(BUTTON_MAP_DATA_PATH, GsonUtil.toJson(buttonMapping));

        CallButton.syncButtonMapping(buttonMapping, 3000, new ResultListener() {
            @Override
            public void onResult(Result result, String message) {
                Log.d(TAG, "Sync button mapping : " + result.name());
            }
        });
    }

    public void unbindButton(String buttonId, String locationName) {
        ButtonInfo.Builder buttonInfoBuilder = ButtonInfo.newBuilder();
        buttonInfoBuilder.setId(buttonId);
        buttonInfoBuilder.setLocationName(locationName);
        buttonInfoBuilder.setMapName(RobotApi.getInstance().getMapName());
        ButtonInfo buttonInfo = buttonInfoBuilder.build();

        Log.d(TAG, "unbindButton " + GsonUtil.toJson(buttonInfo));
        CallButton.unbindButton(buttonInfo, 3000, new ResultListener() {
            @Override
            public void onResult(Result result, String message) {
                Log.d(TAG, "unbindButton : " + result.name());
            }
        });

    }
}
