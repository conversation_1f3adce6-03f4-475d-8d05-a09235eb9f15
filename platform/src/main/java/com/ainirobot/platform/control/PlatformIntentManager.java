package com.ainirobot.platform.control;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.platform.PlatformDef;
import com.ainirobot.platform.bean.CharacterInfo;
import com.ainirobot.platform.character.Character;
import com.ainirobot.platform.character.CharacterManager;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

/**
 * platform intent manager to handle platform intent
 *
 * @version V1.0.0
 * @date 2019/7/2 20:05
 */
public class PlatformIntentManager {

    private static final String TAG = "PlatformIntentManager";
    private static final int PLATFORM_INTENT_SUCCESS = 0;
    private static final int PLATFORM_INTENT_ERROR = -3000;
    private static final String ERROR_GET_OPK_LIST_FAIL = "obtain opk list fail";
    private static final String PARAM_KEY_MSG = "msg";
    private static final String PARAM_KEY_REQ_ID = "reqId";
    private static final String PARAM_KEY_TYPE = "type";
    private static final String PARAM_KEY_RESULT = "result";
    private static final String PARAM_KEY_ERROR_MSG = "errmsg";
    private static final String PARAM_KEY_RN_APP_LIST = "rnapp_list";
    private static final String PARAM_KEY_NAME = "name";

    private static volatile PlatformIntentManager mInstance;

    private PlatformIntentManager() {
    }

    public static PlatformIntentManager getInstance() {
        if (mInstance == null) {
            synchronized (PlatformIntentManager.class) {
                if (mInstance == null) {
                    mInstance = new PlatformIntentManager();
                }
            }
        }
        return mInstance;
    }

    /**
     * obtain all rn opk info
     *
     * @param reqId request id
     */
    public void getRnOpkList(int reqId) {
        JSONObject data = new JSONObject();
        try {
            List<Character> characters = CharacterManager.getInstance().getCharactersByPlatform(CharacterInfo.PLATFORM_RN);
            Log.i(TAG, "getRnOpkList: size="+characters.size()+" characters=" + characters);
            JSONArray array = new JSONArray();
            for (Character character : characters) {
                String info = character.getCharacterInfo();
                if(TextUtils.isEmpty(info)) {
                    Log.w(TAG, "getRnOpkList: characterInfo is empty");
                    continue;
                }
                array.put(new JSONObject(info));
            }
            data.put(PARAM_KEY_RN_APP_LIST, array);
            notifyToServer(reqId, PlatformDef.LIST_RN_OPK
                    , PLATFORM_INTENT_SUCCESS, "", data);
        } catch (JSONException e) {
            Log.e(TAG, "getRnOpkList: json format exception");
            notifyToServer(reqId, PlatformDef.LIST_RN_OPK
                    , PLATFORM_INTENT_ERROR, ERROR_GET_OPK_LIST_FAIL, data);
        }
    }

    /**
     * uninstall local opk pkg
     *
     * @param reqId  request id
     * @param params uninstall opk param
     */
    public void uninstallRn(int reqId, String params) {
        if (TextUtils.isEmpty(params)) {
            Log.i(TAG, "uninstallRn: params is empty");
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(params);
            String appId = jsonObject.optString(PARAM_KEY_NAME);
            Character character = CharacterManager.getInstance().getCharacter(appId);
            JSONObject uninstallParam = new JSONObject();
            uninstallParam.put(PARAM_KEY_REQ_ID, reqId);
            Log.i(TAG, "uninstallRn: uninstall param：" + uninstallParam);
            if (character == null) {
                Log.e(TAG, "uninstallRn: character don't exit,name=" + appId);
                return;
            }
            character.uninstall(uninstallParam.toString());
        } catch (JSONException e) {
            Log.e(TAG, "uninstallRn: params=" + params);
        }
    }

    /**
     * overload
     */
    public void notifyToServer(int reqId, String type, int result, String errorMsg) {
        notifyToServer(reqId, type, result, errorMsg, null);
    }

    /**
     * response server rn cmd
     *
     * @param reqId  request id
     * @param type   command type
     * @param result cmd handle result
     * @param data   data param
     */
    private void notifyToServer(int reqId, String type, int result, String errorMsg, JSONObject data) {
        try {
            JSONObject json = new JSONObject();
            json.put(PARAM_KEY_REQ_ID, reqId);
            json.put(PARAM_KEY_TYPE, type);
            json.put(PARAM_KEY_RESULT, result);
            json.put(PARAM_KEY_MSG, data);
            json.put(PARAM_KEY_ERROR_MSG, errorMsg);
            Log.i(TAG, "notifyToServer: param:" + json);
            if (RobotApi.getInstance().isApiConnectedService()) {
                RobotApi.getInstance().sendStatusReport(Definition.STATUS_PROCESS_STATE, json.toString());
                Log.i(TAG, "notifyToServer finish");
            }
        } catch (JSONException e) {
            Log.e(TAG, "notifyToServer: json format exception");
        }
    }
}
