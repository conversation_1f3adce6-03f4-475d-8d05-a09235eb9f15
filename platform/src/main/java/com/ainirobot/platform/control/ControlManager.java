/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.control;

import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.PlatformDef;
import com.ainirobot.platform.bean.CharacterInfo;
import com.ainirobot.platform.bean.IntentInfo;
import com.ainirobot.platform.bi.annotation.OpkOpStatus;
import com.ainirobot.platform.bi.annotation.OpkOpType;
import com.ainirobot.platform.bi.annotation.SpeechResponse;
import com.ainirobot.platform.bi.wrapper.ReportControl;
import com.ainirobot.platform.bi.wrapper.manager.bi.impl.OpkOperationPoint;
import com.ainirobot.platform.character.Character;
import com.ainirobot.platform.character.Character.CharacterListener;
import com.ainirobot.platform.character.CharacterManager;
import com.ainirobot.platform.data.IntentManager;
import com.ainirobot.platform.react.reactnative.character.ReactCharacter;
import com.ainirobot.platform.speech.SpeechApi;
import com.ainirobot.platform.utils.LocalUtils;
import com.ainirobot.platform.utils.SpeechUtils;

import org.json.JSONException;
import org.json.JSONObject;

public final class ControlManager {
    private static final ControlManager sController = new ControlManager();

    /**
     * 消息类型
     */
    private static final int MSG_REQUEST = 0;
    private static final int MSG_HW_EXCEPTION = 1;
    private static final int MSG_SUSPEND = 2;
    private static final int MSG_RECOVERY = 3;
    private static final int MSG_API_CONNECTION = 4;
    private static final int MSG_API_DISCONNECTION = 5;

    private static final int PLATFORM_PARAMS_ERROR = -2;
    private static final int PLATFORM_SWITCH_FAIL = -3;
    private static final int PLATFORM_SWITCH_CHARACTER_START_FAIL = -4;
    private static final int PLATFORM_NO_DEFAULT_CHARACTER = -1;

    private static final int PLATFORM_INTENT_SUCCESS = 0;
    private static final int PLATFORM_INTENT_ERROR = -3000;
    private static final int SWITCH_CHARACTER_SUCCESS = 0;
    private static final int ERROR_SWITCH_CHARACTER_FAIL = -3000;
    private static final int ERROR_CHARACTER_NOT_EXIT = -3001;
    private static final int ERROR_IS_CURRENT_CHARACTER = -3002;
    private static final int ERROR_IS_CURRENT_PLATFORM = -3003;
    private static final String ERROR_MSG_CHARACTER_NOT_EXIT = "character not exit";
    private static final String ERROR_MSG_IS_CURRENT_CHARACTER = "is current character";
    private static final String ERROR_MSG_SWITCH_CHARACTER_FAIL = "switch character fail";
    private static final String ERROR_MSG_CLOSE_CHARACTER_FAIL = "close character fail";
    private static final String ERROR_MSG_IS_CURRENT_PLATFORM = "close character fail,current platform is native platform";

    private static final String CLOSE_RN_FAIL_PLATFORM = "close rn fail,current platform is native";
    private static final String CLOSE_RN_FAIL_CHARACTER = "close rn fail,start native default character fail";

    private static final String OB_OPK_SWITCH = "ob_opk_switch";
    private static final String REQ_ID = "req_id";
    private static final String REQ_INTENT = "req_intent";
    private static final String REQ_TEXT = "req_text";
    private static final String REQ_PARAMS = "req_params";
    private static final String FUNCTION = "function";
    private static final String TYPE = "type";
    private static final String MESSAGE = "message";
    public static final String TAG = "ControlManager";
    private final Object CHARACTER_LOCK = "character";
    private static final String NATIVE_DEFAULT_CHARACTER = "common";
    /**
     * 角色退出默认超时
     */
    private static final long TIMEOUT = 500;
    private final Handler mHandler;

    /**
     * 当前角色
     */
    private static Character mCharacter;
    private CharacterManager mCharacterManager;
    private boolean mIsFirstStart = true;

    private CharacterListener mCharacterListener = new CharacterListener() {
        @Override
        public void onFinished() {
            //角色主动退出后，切换到默认角色
            if (mCharacterManager.getDefault() == mCharacter) {
                Log.d(TAG, "onFinished default is current, first set default null");
                mCharacterManager.clearDefault();
            }
            Character character = mCharacterManager.getDefault();
            Log.d(TAG, "On character stop, switch to : " + character
                    + ", isFirstStart: " + mIsFirstStart);
            if (character != null) {
                mCharacterManager.setDefault(character);
                SpeechApi.getInstance().setCallBack(character.getSkillCallback());
                character.setCharacterListener(mCharacterListener);
                startCharacter(character, null);
                mIsFirstStart = false;
                if (!RobotApi.getInstance().isActive()) {
                    Log.i(TAG, "onFinished: don't active handleSuspend");
                    character.handleSuspend();
                }
                mCharacter = character;
            }
        }
    };

    private ControlManager() {
        mCharacterManager = CharacterManager.getInstance();

        HandlerThread mThread = new HandlerThread("ControlManager");
        mThread.start();

        mHandler = new Handler(mThread.getLooper()) {
            @Override
            public void handleMessage(Message msg) {
                onNewMessage(msg);
            }
        };
    }

    private void onNewMessage(Message msg) {
        switch (msg.what) {
            case MSG_REQUEST:
                onNewRequest(msg.getData());
                break;

            case MSG_HW_EXCEPTION:
                onHWException(msg.getData());
                break;

            case MSG_SUSPEND:
                onSuspend();
                break;

            case MSG_RECOVERY:
                onRecovery();
                break;

            case MSG_API_CONNECTION:
                onApiConnection();
                break;

            case MSG_API_DISCONNECTION:
                onApiDisconnection();
                break;

            default:
                break;
        }
    }

    private void onApiConnection() {
        Log.d(TAG, "On api connection");
        if (!RobotApi.getInstance().isActive()) {
            Log.d(TAG, "Current not in control");
            return;
        }
        synchronized (CHARACTER_LOCK) {
            if (mCharacter == null) {
                //第一次连接启动默认角色
                if (!switchDefaultCharacter()) {
                    Log.w(TAG, "onApiConnection: start default character fail");
                    switchNativeDefaultCharacter();
                }
            } else {
                mCharacter.handleRecovery();
            }
        }
        //功能已废弃
//        RobotApi.getInstance().enableAvatarFaceData();
    }

    private void onApiDisconnection() {
        Log.d(TAG, "On api disconnection");
        synchronized (CHARACTER_LOCK) {
            mCharacter.handleApiDisconnection();
        }
    }

    private void onNewRequest(Bundle data) {
        int reqId = data.getInt(REQ_ID);
        String intent = data.getString(REQ_INTENT);
        String text = data.getString(REQ_TEXT);
        String params = data.getString(REQ_PARAMS);
        Log.d(TAG, "onNewRequest intent : " + intent + ", text : " + text + ", param : " + params);

        boolean isPlatformIntent = IntentManager.getInstance().isPlatformIntent(intent);
        if (isPlatformIntent && intent != null) {
            Log.d(TAG, "platform intent : intent=" + intent + " param=" + params);
            handlePlatformReq(reqId, intent, params);
            if (RobotApi.getInstance().isApiConnectedService()) {
                RobotApi.getInstance().finishModuleParser(reqId, true);
            }
            return;
        }

        //所有指令优先派发给当前角色处理
        if (mCharacter != null
                && mCharacter.handleRequest(reqId, intent, text, params)) {
            Log.d(TAG, "The request has been processed by current character");
            //state report
            SpeechUtils.checkSpeechResponse(params, SpeechResponse.SPEECH_RESPONSE);
            return;
        }

        boolean isSwitchCharacter = IntentManager.getInstance().isSwitchCharacter(intent);
        if (isSwitchCharacter) {
            int result = -1;
            if (CharacterManager.getInstance().isPlatformSupport(intent)) {
                result = switchCharacter(intent, params);
            } else {
                Log.d(TAG, "platform not support : " + intent + "    " + mCharacter);
            }
            Log.d(TAG, "Switch character : " + result);
            notifyToServer(reqId, result == SWITCH_CHARACTER_SUCCESS ? 0 : -1);
            if (RobotApi.getInstance().isApiConnectedService()) {
                RobotApi.getInstance().finishModuleParser(reqId, result == SWITCH_CHARACTER_SUCCESS);
            }
            return;
        }

        Character specialCharacter = CharacterManager.getInstance()
                .getSpecialCharacterByStartIntent(intent);
        if (specialCharacter != null) {
            boolean result = switchCharacter(specialCharacter);
            notifyToServer(reqId, result ? 0 : -1);
            if (RobotApi.getInstance().isApiConnectedService()) {
                RobotApi.getInstance().finishModuleParser(reqId, false);
            }
            return;
        }

        //state report
        SpeechUtils.checkSpeechResponse(params, SpeechResponse.SPEECH_NOT_RESPONSE);
        if (RobotApi.getInstance().isApiConnectedService()) {
            RobotApi.getInstance().finishModuleParser(reqId, false);
        }
    }

    private void handlePlatformReq(int reqId, String intent, String params) {
        Log.i(TAG, "handlePlatformReq: intent=" + intent + " params=" + params);
        switch (intent) {
            case PlatformDef.LAUNCH_RN:
                reportOpkOpCmd(params, OpkOpType.LAUNCH_RN, OpkOpStatus.RECEIVE_CMD, "");
                boolean result = launchRn(reqId, intent, params);
                if (result) {
                    Log.i(TAG, "handlePlatformReq: save ob operation");
                    saveLaunchRnOp(params);
                }
                break;
            case PlatformDef.CLOSE_RN:
                reportOpkOpCmd(params, OpkOpType.CLOSE_RN, OpkOpStatus.RECEIVE_CMD, "");
                boolean resetPlatFormResult = mCharacterManager.resetPlatform();
                if (!resetPlatFormResult) {
                    Log.i(TAG, "handlePlatformReq: reset platform fail");
                    reportOpkOpCmd(params, OpkOpType.CLOSE_RN, OpkOpStatus.CLOSE_RN_FAIL, CLOSE_RN_FAIL_PLATFORM);
                    PlatformIntentManager.getInstance().notifyToServer(reqId, intent,
                            ERROR_IS_CURRENT_PLATFORM
                            , ERROR_MSG_IS_CURRENT_PLATFORM);
                    break;
                }
                boolean isSuccess = switchDefaultCharacter();
                int resultCode = isSuccess ? PLATFORM_INTENT_SUCCESS : PLATFORM_INTENT_ERROR;
                String errorMsg = isSuccess ? "" : ERROR_MSG_CLOSE_CHARACTER_FAIL;
                PlatformIntentManager.getInstance().notifyToServer(reqId, intent, resultCode, errorMsg);
                if (!isSuccess) {
                    reportOpkOpCmd(params, OpkOpType.CLOSE_RN, OpkOpStatus.CLOSE_RN_FAIL,
                            CLOSE_RN_FAIL_CHARACTER);
                } else {
                    LocalUtils.putStringSettings(BaseApplication.getApplication(), OB_OPK_SWITCH, NATIVE_DEFAULT_CHARACTER);
                    reportOpkOpCmd(params, OpkOpType.CLOSE_RN, OpkOpStatus.CLOSE_RN_SUCCESS,
                            CLOSE_RN_FAIL_CHARACTER);
                }
                break;
            case PlatformDef.LIST_RN_OPK:
                PlatformIntentManager.getInstance().getRnOpkList(reqId);
                break;
            case PlatformDef.UNINSTALL_RN:
                PlatformIntentManager.getInstance().uninstallRn(reqId, params);
                break;
            case PlatformDef.SET_WORK_MODE:
            case PlatformDef.SWITCH_APP_PLATFORM:
                //compatible old version switch_platform command
                notifyToServer(reqId, switchPlatform(params));
                break;
            default:
                Log.w(TAG, "handlePlatformReq: unKnow rnIntent:" + intent);
                break;
        }
    }

    private void saveLaunchRnOp(String params) {
        try {
            JSONObject jsonObject = new JSONObject(params);
            String opkAppId = jsonObject.optString("name");
            LocalUtils.putStringSettings(BaseApplication.getApplication(), OB_OPK_SWITCH, opkAppId);
        } catch (JSONException e) {
            Log.w(TAG, "saveLaunchRnOp: json format fail");
        }
    }

    private void reportOpkOpCmd(String params, @OpkOpType int type, @OpkOpStatus int status,
                                String errorMessage) {
        if (TextUtils.isEmpty(params)) {
            Log.w(TAG, "reportOpkOpCmd: param is error " + params);
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(params);
            String appId = jsonObject.optString("name");
            String msgId = jsonObject.optString("msgId");
            appId = TextUtils.isEmpty(appId) ? "" : appId;
            msgId = TextUtils.isEmpty(msgId) ? "" : msgId;
            ReportControl.getInstance().reportMsg(new OpkOperationPoint(msgId, appId, type,
                    status, errorMessage));
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private boolean launchRn(int reqId, String intent, String params) {
        String data = formatParams(reqId, params);
        if (data == null) {
            reportResult(reqId, intent, params, ERROR_SWITCH_CHARACTER_FAIL);
            return false;
        }

        //兼容插件的处理策略，先尝试发送给当前OPK处理，处理不了再走正常流程
        if (mCharacter instanceof ReactCharacter) {
            boolean isSucc = ((ReactCharacter) mCharacter).switchScene(data);
            Log.d(TAG, "Switch opk : " + isSucc);
            if (isSucc) {
                reportResult(reqId, intent, params, SWITCH_CHARACTER_SUCCESS);
                return true;
            }
        }

        Log.i(TAG, "switchRnCharacter: start params" + data);
        int result = switchCharacter(intent, params, data);
        Log.i(TAG, "switchRnCharacter: resultCode=" + result);

        reportResult(reqId, intent, params, result);
        return result == SWITCH_CHARACTER_SUCCESS;
    }

    private String formatParams(int reqId, String params) {
        if (TextUtils.isEmpty(params)) {
            return null;
        }

        try {
            JSONObject json = new JSONObject(params);
            json.put("reqId", reqId);
            return json.toString();
        } catch (JSONException e) {
            Log.i(TAG, "switchRnCharacter: json format exception");
            e.printStackTrace();
        }
        return null;
    }

    private void reportResult(int reqId, String intent, String params, int result) {
        int code = result;
        String errorMsg = "";
        switch (result) {
            case SWITCH_CHARACTER_SUCCESS:
                code = OpkOpStatus.LAUNCHER_SUCCESS;
                break;
            case ERROR_CHARACTER_NOT_EXIT:
                code = OpkOpStatus.LAUNCHER_FAIL;
                errorMsg = ERROR_MSG_CHARACTER_NOT_EXIT;
                break;
            case ERROR_IS_CURRENT_CHARACTER:
                code = OpkOpStatus.LAUNCHER_FAIL;
                errorMsg = ERROR_MSG_IS_CURRENT_CHARACTER;
                break;
            default:
                code = OpkOpStatus.LAUNCHER_FAIL;
                errorMsg = ERROR_MSG_SWITCH_CHARACTER_FAIL;
                break;
        }

        //上报埋点
        reportOpkOpCmd(params, OpkOpType.LAUNCH_RN, code, errorMsg);

        //上报服务端执行结果
        PlatformIntentManager.getInstance().notifyToServer(reqId, intent, result, errorMsg);
    }

//    private boolean switchRnCharacter(int reqId, String rnIntent, String params) {
//        if (TextUtils.isEmpty(params)) {
//            PlatformIntentManager.getInstance().notifyToServer(reqId, rnIntent
//                    , ERROR_SWITCH_CHARACTER_FAIL
//                    , ERROR_MSG_SWITCH_CHARACTER_FAIL);
//            return false;
//        }
//        JSONObject json;
//        try {
//            json = new JSONObject(params);
//            json.put("reqId", reqId);
//        } catch (JSONException e) {
//            Log.i(TAG, "switchRnCharacter: json format exception");
//            PlatformIntentManager.getInstance().notifyToServer(reqId, rnIntent
//                    , ERROR_SWITCH_CHARACTER_FAIL
//                    , ERROR_MSG_SWITCH_CHARACTER_FAIL);
//            return false;
//        }
//        Log.i(TAG, "switchRnCharacter: start params" + json);
//        int resultCode = switchCharacter(rnIntent, params, json.toString());
//        Log.i(TAG, "switchRnCharacter: resultCode=" + resultCode);
//        String errorMsg = "";
//        int result = OpkOpStatus.LAUNCHER_SUCCESS;
//        switch (resultCode) {
//            case SWITCH_CHARACTER_SUCCESS:
//                break;
//            case ERROR_CHARACTER_NOT_EXIT:
//                result = OpkOpStatus.LAUNCHER_FAIL;
//                errorMsg = ERROR_MSG_CHARACTER_NOT_EXIT;
//                break;
//            case ERROR_IS_CURRENT_CHARACTER:
//                result = OpkOpStatus.LAUNCHER_FAIL;
//                errorMsg = ERROR_MSG_IS_CURRENT_CHARACTER;
//                break;
//            default:
//                result = OpkOpStatus.LAUNCHER_FAIL;
//                errorMsg = ERROR_MSG_SWITCH_CHARACTER_FAIL;
//                break;
//        }
//        reportOpkOpCmd(params, OpkOpType.LAUNCH_RN, result, errorMsg);
//        PlatformIntentManager.getInstance()
//                .notifyToServer(reqId, rnIntent, resultCode, errorMsg);
//        return resultCode == SWITCH_CHARACTER_SUCCESS;
//    }

    private void onHWException(Bundle data) {
        Log.d(TAG, "On hardware exception");
        if (mCharacter == null) {
            return;
        }

        int function = data.getInt(FUNCTION);
        String type = data.getString(TYPE);
        String message = data.getString(MESSAGE);
        synchronized (CHARACTER_LOCK) {
            mCharacter.handleHWException(function, type, message);
        }
    }

    private void onSuspend() {
        Log.d(TAG, "On suspend");
        synchronized (CHARACTER_LOCK) {
            if (mCharacter != null) {
                Log.i(TAG, "onSuspend: handleSuspend");
                mCharacter.handleSuspend();
            }
        }
    }

    /**
     *
     */
    private void onRecovery() {
        Log.d(TAG, "On recovery");
        synchronized (CHARACTER_LOCK) {
            syncServerCmd();
            if (mCharacter == null) {
                if (!switchDefaultCharacter()) {
                    Log.i(TAG, "onRecovery: start default character fail ");
                    switchNativeDefaultCharacter();
                }
                //功能已废弃
//                RobotApi.getInstance().enableAvatarFaceData();
            } else {
                mCharacter.handleRecovery();
            }
        }
    }

    private void syncServerCmd() {
        JSONObject data = new JSONObject();
        try {
            data.put("cmd", PlatformDef.STATUS_SYNC_SERVER_CMD);
            if (RobotApi.getInstance().isApiConnectedService()) {
                Log.i(TAG, "syncServerCmd: " + data);
                RobotApi.getInstance().sendStatusReport(PlatformDef.STATUS_SYNC_SERVER_CMD
                        , data.toString());
            } else {
                Log.w(TAG, "syncServerCmd: service disconnect");
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void switchNativeDefaultCharacter() {
        if (mCharacterManager.resetPlatform()) {
            Log.i(TAG, "switchNativeCharacter: start native default character");
            switchDefaultCharacter();
        }
    }

    private boolean switchDefaultCharacter() {
        Log.d(TAG, "Switch default character");
        Character character = CharacterManager.getInstance().getDefault();
        if (character == null) {
            Log.i(TAG, "switchDefaultCharacter: default character don't exit");
            return false;
        }
        return switchCharacter(character);
    }

    private boolean switchCharacter(Character character) {
        Log.d(TAG, "switchCharacter " + character);
        if (character != null) {
            if (mCharacter != null) {
                Log.i(TAG, "switchCharacter: stop last character");
                mCharacter.setCharacterListener(null);
                mCharacter.stop(TIMEOUT);
            }
            mCharacterManager.setDefault(character);
            SpeechApi.getInstance().setCallBack(character.getSkillCallback());
            character.setCharacterListener(mCharacterListener);
            boolean result = startCharacter(character, null);
            Log.i(TAG, "switchCharacter: start default result :" + result
                    + ", isFirstStart: " + mIsFirstStart);
            if (result) {
                mIsFirstStart = false;
                mCharacter = character;
            }
            return result;
        }
        return false;
    }

    private int switchPlatform(String params) {
        Log.i(TAG, "switchPlatform params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            String platformName = json.optString("name");
            int code = json.optInt("mode");
            Character character = CharacterManager.getInstance().getCharacter(code);
            if (character == null) {
                character = CharacterManager.getInstance().getDefaultByPlatform(platformName);
            }
            if (character == null) {
                Log.i(TAG, "switchPlatform: character don't exit");
                return PLATFORM_NO_DEFAULT_CHARACTER;
            }
            boolean switchPlatformResult = CharacterManager.getInstance().switchPlatform(platformName);
            if (!switchPlatformResult) {
                Log.i(TAG, "switchPlatform: switch fail");
                return PLATFORM_SWITCH_FAIL;
            }
            boolean result = switchCharacter(character);
            if (!result) {
                Log.d(TAG, "Switch platform failed : default character start false");
                //平台切换失败，重置回Native
                CharacterManager.getInstance().resetPlatform();
                switchDefaultCharacter();
                return PLATFORM_SWITCH_CHARACTER_START_FAIL;
            }
            Log.i(TAG, "switchPlatform: platform and character switch success");
            return PLATFORM_INTENT_SUCCESS;
        } catch (JSONException e) {
            e.printStackTrace();
            return PLATFORM_PARAMS_ERROR;
        }
    }

    private int switchCharacter(String intent, String params) {
        return switchCharacter(intent, params, params);
    }

    /**
     * 切换角色
     *
     * @param intent      意图
     * @param params      参数：角色名称 + Type
     * @param startParams 角色启动参数
     * @return
     */
    private int switchCharacter(String intent, String params, String startParams) {
        try {
            Character character = getCharacter(intent, params);
            if (character == null) {
                Log.d(TAG, "Get character failed : " + params);
                return ERROR_CHARACTER_NOT_EXIT;
            }

            Log.d(TAG, "Switch character : " + character.toString() + "   current : " + mCharacter);
            if (character == mCharacter) {
                Log.d(TAG, "Current already running : " + params);
                return ERROR_IS_CURRENT_CHARACTER;
            }

            synchronized (CHARACTER_LOCK) {
                if (mCharacter != null) {
                    mCharacter.setCharacterListener(null);
                    mCharacter.stop(TIMEOUT);
                }

                character.setCharacterListener(mCharacterListener);
                boolean result = startCharacter(character, startParams);
                Log.i(TAG, "switchCharacter: start character result" + result);
                if (result) {
                    CharacterManager.getInstance().setDefault(character);
                    SpeechApi.getInstance().setCallBack(character.getSkillCallback());
                    mCharacter = character;
                    return SWITCH_CHARACTER_SUCCESS;
                } else {
                    Log.d(TAG, "Switch character failed : " + params);
                    if (mCharacter != null) {
                        mCharacter.setCharacterListener(mCharacterListener);
                        startCharacter(mCharacter, startParams);
                    }
                    return ERROR_SWITCH_CHARACTER_FAIL;
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return ERROR_SWITCH_CHARACTER_FAIL;
    }

    private boolean startCharacter(Character character, String params) {
        Log.d(TAG, "startCharacter param: " + params);
        JSONObject jsonObject = new JSONObject();
        boolean result = false;
        try {
            jsonObject.put(Character.FIRST_BOOT, mIsFirstStart);
            jsonObject.put(Character.OPK_DATA, params);
            if (mCharacter != null) {
                CharacterInfo info = CharacterManager.getInstance()
                        .getCharacterInfo(mCharacter);
                if (info != null) {
                    jsonObject.put(Character.LAST_CHARACTER, info.getName());
                }
            }
            result = character.start(jsonObject.toString());
        } catch (JSONException e) {
            Log.e(TAG, "startCharacter exception: " + e.getMessage());
        }
        return result;
    }

    private Character getCharacter(String intent, String params) throws JSONException {
        IntentInfo info = IntentManager.getInstance().getIntentInfo(intent);
        Character character = null;
        if (TextUtils.isEmpty(params)) {
            character = CharacterManager.getInstance().getCharacter(info.getCharacter());
            return character;
        }
        JSONObject json = new JSONObject(params);
        //根据Code获取角色，云端下发的是
        if (json.has(PlatformDef.REQ_MODE_KEY)) {
            int mode = json.optInt(PlatformDef.REQ_MODE_KEY);
            character = CharacterManager.getInstance().getCharacter(mode);
        }

        if (character == null) {
            String name = json.getString("name");
            if (TextUtils.isEmpty(name)) {
                name = info.getCharacter();
            }
            character = CharacterManager.getInstance().getCharacter(name);
        }

        return character;
    }


    private void notifyToServer(int reqId, int result) {
        Log.d(TAG, "notifyToServer isApiConnectedService: " + RobotApi.getInstance().isApiConnectedService());
        try {
            JSONObject json = new JSONObject();
            json.put("reqId", reqId);
            json.put("type", PlatformDef.SET_WORK_MODE);
            json.put("result", result);

            if (RobotApi.getInstance().isApiConnectedService()) {
                RobotApi.getInstance().sendStatusReport(Definition.STATUS_PROCESS_STATE, json.toString());
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理Request指令（包含语音、服务端下发）
     *
     * @param reqId  request id
     * @param intent request指令
     * @param text   语音识别内容
     * @param params 指令参数
     * @return
     */
    public static boolean handleRequest(int reqId, String intent, String text, String params) {
        Message message = sController.mHandler.obtainMessage(MSG_REQUEST);
        Bundle bundle = new Bundle();
        bundle.putInt(REQ_ID, reqId);
        bundle.putString(REQ_INTENT, intent);
        bundle.putString(REQ_TEXT, text);
        bundle.putString(REQ_PARAMS, params);
        message.setData(bundle);
        return sController.mHandler.sendMessage(message);
    }

    /**
     * 处理Hardware异常
     *
     * @param function Hardware id
     * @param type     异常类型
     * @param msg      异常消息
     * @return 消息发送成功true, 否则false
     */
    public static boolean handleHWException(int function, String type, String msg) {
        Message message = sController.mHandler.obtainMessage(MSG_HW_EXCEPTION);
        Bundle bundle = new Bundle();
        bundle.putInt(FUNCTION, function);
        bundle.putString(TYPE, type);
        bundle.putString(MESSAGE, msg);
        message.setData(bundle);
        return sController.mHandler.sendMessage(message);
    }

    /**
     * 被系统挂起事件（充电、急停、OTA等系统任务执行时会触发该事件）
     */
    public static boolean handleSuspend() {
        Log.d(TAG, "Handle suspend");
        return sController.mHandler.sendEmptyMessage(MSG_SUSPEND);
    }

    /**
     * 被系统恢复控制权，可继续执行业务
     */
    public static boolean handleRecovery() {
        Log.d(TAG, "Handle recovery");
        return sController.mHandler.sendEmptyMessage(MSG_RECOVERY);
    }

    /**
     * 处理Api连接事件
     */
    public static boolean handleApiConnection() {
        return sController.mHandler.sendEmptyMessage(MSG_API_CONNECTION);
    }

    /**
     * 处理Api断连事件
     *
     * @return 消息发送成功true, 否则false
     */
    public static boolean handleApiDisconnection() {
        return sController.mHandler.sendEmptyMessage(MSG_API_DISCONNECTION);
    }

    public static boolean isCharacterNull() {
        return mCharacter == null;
    }
}
