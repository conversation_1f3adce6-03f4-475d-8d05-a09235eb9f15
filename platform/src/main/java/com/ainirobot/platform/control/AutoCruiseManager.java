package com.ainirobot.platform.control;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.DataSetObservable;
import android.database.DataSetObserver;
import android.os.AsyncTask;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.util.SparseArray;
import android.util.SparseBooleanArray;

import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.PlatformDef;
import com.ainirobot.platform.bean.AutoCruiseTaskInfo;
import com.ainirobot.platform.utils.SettingsUtils;

import org.jetbrains.annotations.Nullable;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.PriorityQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 */
public class AutoCruiseManager {
    private static final String TAG = "AutoCruiseManager";

    private static volatile AutoCruiseManager sAutoCruiseManager;

    private AutoEngine mAutoEngine;

    private AutoCruiseSettings mAutoCruiseSettings;

    private AutoCruiseManager() {
        init();
    }

    private void init() {
        mAutoEngine = AutoEngine.newInstance();
        mAutoCruiseSettings = AutoCruiseSettings.newInstance();
        mAutoCruiseSettings.registerCruiseSettingsObserver(
                new AutoCruiseSettings.CruiseSettingsObserver() {
                    @Override
                    public void onInvalidated() {
                        Log.d(TAG, "AutoCruiseSettings onInvalidated: CruiseTask list is empty, quit.");

                        quit();
                    }

                    @Override
                    public void onChanged() {
                        Log.d(TAG,
                                "AutoCruiseSettings onChanged: CruiseTask list has changed, reload data.");

                        stop();
                        List<CruiseTask> cruiseTaskList = mAutoCruiseSettings.getCruiseTaskList();
                        for (CruiseTask task : cruiseTaskList) {
                            addCruiseTask(task);
                        }
                        start();
                    }
                });
    }

    public static AutoCruiseManager newInstance() {
        if (sAutoCruiseManager == null) {
            synchronized (AutoCruiseManager.class) {
                if (sAutoCruiseManager == null) {
                    sAutoCruiseManager = new AutoCruiseManager();
                }
            }
        }
        return sAutoCruiseManager;
    }

    private void start() {
        Log.d(TAG, "start: ");
        mAutoEngine.start();
    }

    private void stop() {
        Log.d(TAG, "stop: ");
        mAutoEngine.stop();
    }

    private void quit() {
        Log.d(TAG, "quit: ");
        mAutoEngine.quit();
    }

    public void setup() {
        Log.d(TAG, "setup: ");
        mAutoCruiseSettings.setup();
    }

    private void addCruiseTask(CruiseTask cruiseTask) {
        Log.d(TAG, "addCruiseTask: ");
        mAutoEngine.addCruiseTask(cruiseTask);
    }

    /**
     * get the cruise task currently being executed
     *
     * @return true means the task is active, false is not active
     */
    public boolean getCurrentTaskActiveStatus() {
        return mAutoCruiseSettings.getCurrentTaskActiveStatus();
    }

    /**
     * disable the current cruise task
     */
    public void disableCurrentCruiseTask() {
        mAutoCruiseSettings.disableCurrentCruiseTask();
    }

    private static class AutoCruiseSettings {
        private static final String TAG = "AutoCruiseSettings";

        private static final String ACTION_CRUISE_SETTINGS_CHANGED =
                "ainirobot.action_CRUISE_SEETINGS_CHANGED";

        private List<CruiseTask> mCruiseTaskList;
        private volatile SparseBooleanArray mCruiseTaskActiveStatus;
        private DataSetObservable mDataSetObserverObservable;
        private BroadcastReceiver mRequestCruiseSettingsReceiver;
        private boolean isRegisterCruiseSettingsReceiver;
        private CruiseTask mCurrentTask;

        private AutoCruiseSettings() {
            mDataSetObserverObservable = new DataSetObservable();
            mCruiseTaskList = new ArrayList<>();
            mCruiseTaskActiveStatus = new SparseBooleanArray(1);
            mRequestCruiseSettingsReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(final Context context, final Intent intent) {
                    Log.d(TAG, String.format("AutoCruiseSettings onReceive: %s", intent.getAction()));

                    requestConfiguration();
                }
            };
            isRegisterCruiseSettingsReceiver = false;
        }

        static AutoCruiseSettings newInstance() {
            return new AutoCruiseSettings();
        }

        void setup() {
            if (!isRegisterCruiseSettingsReceiver) {
                Log.d(TAG, "setup: register CruiseSettingsReceiver");

                Context context = BaseApplication.getContext();
                IntentFilter intentFilter = new IntentFilter();
                intentFilter.addAction(Intent.ACTION_DATE_CHANGED);
                intentFilter.addAction(Intent.ACTION_TIME_CHANGED);
                intentFilter.addAction(Intent.ACTION_TIMEZONE_CHANGED);
                intentFilter.addAction(ACTION_CRUISE_SETTINGS_CHANGED);
                context.registerReceiver(mRequestCruiseSettingsReceiver, intentFilter);
                isRegisterCruiseSettingsReceiver = true;
            }

            requestConfiguration();
        }

        private void requestConfiguration() {
            AutoCruiseTaskInfo cruiseInfo = SettingsUtils.getAutoCruiseInfo();
            if (cruiseInfo == null) {
                Log.e(TAG, "requestConfiguration: autoCruiseTaskInfo is null");
                return;
            }

            Log.d(TAG, String.format("requestConfiguration: autoCruiseTaskInfo = %s", cruiseInfo));

            try {
                List<CruiseTask> cruiseTaskList = parseConfiguration(cruiseInfo);
                mCruiseTaskList.clear();
                mCruiseTaskList.addAll(cruiseTaskList);
                if (!mCruiseTaskList.isEmpty()) {
                    mDataSetObserverObservable.notifyChanged();
                } else {
                    mDataSetObserverObservable.notifyInvalidated();
                }
            } catch (Exception e) {
                Log.e(TAG, Log.getStackTraceString(e));
            }
        }

        private List<CruiseTask> parseConfiguration(AutoCruiseTaskInfo autoCruiseTaskInfo) {
            ArrayList<String> startTimeList =
                    new ArrayList<>(autoCruiseTaskInfo.getStartTimeList());
            ArrayList<String> endTimeList = new ArrayList<>(autoCruiseTaskInfo.getEndTimeList());

            if (startTimeList.size() != endTimeList.size()) {
                throw new IllegalArgumentException(String.format(
                        "the autoCruiseTaskInfo start time list(size = %d) and end time list(size = %d) don't match",
                        startTimeList.size(), endTimeList.size()));
            }

            CruiseTask cruiseTask = createCruiseTask();
            List<CruiseTask> tmpCruiseTaskList = new ArrayList<>(startTimeList.size());
            for (String dateStr : startTimeList) {
                long startTime = DateUtils.getRelativeTimeOfDay(dateStr);
                CruiseTask task = (CruiseTask) cruiseTask.clone();
                task.setBeginTime(startTime);
                task.setName(dateStr);
                tmpCruiseTaskList.add(task);
            }

            int index = 0;
            for (String dateStr : endTimeList) {
                long endTime = DateUtils.getRelativeTimeOfDay(dateStr);
                tmpCruiseTaskList.get(index++).setEndTime(endTime);
            }

            List<CruiseTask> cruiseTaskList = new ArrayList<>(tmpCruiseTaskList.size());
            for (CruiseTask task : tmpCruiseTaskList) {
                try {
                    cruiseTaskList.add(reviewCruiseTask(task));
                } catch (CruiseTaskFailureException e) {
                    Log.w(TAG, Log.getStackTraceString(e));
                }
            }

            return cruiseTaskList;
        }

        private CruiseTask reviewCruiseTask(CruiseTask cruiseTask)
                throws CruiseTaskFailureException {
            long beginTime = cruiseTask.getBeginTime();
            long endTime = cruiseTask.getEndTime();
            long currentTime = System.currentTimeMillis();
            if (endTime < currentTime) {
                beginTime = beginTime + DateUtils.getCycleTime();
                endTime = endTime + DateUtils.getCycleTime();
                if (endTime < currentTime) {
                    throw new CruiseTaskFailureException(String.format(
                            "the %s has expired in the next time period(%dms)", cruiseTask,
                            DateUtils.getCycleTime()));
                }
                cruiseTask.setBeginTime(beginTime);
                cruiseTask.setEndTime(endTime);
            }

            return cruiseTask;
        }

        private CruiseTask createCruiseTask() {
            return new CruiseTask("auto_cruise_task") {
                @Override
                public void onStart() {
                    Log.d(TAG, String.format("cruiseTask onStart: %s", toString()));
                    ModuleRequestUtils.startAutoCruise(getBeginTime());
                    updateCruiseTaskStatus(this, true);
                    mCurrentTask = this;
                }

                @Override
                public void onStop() {
                    Log.d(TAG, String.format("cruiseTask onStop: %s", toString()));
                    ModuleRequestUtils.stopAutoCruise(getEndTime());
                    disableCurrentCruiseTask();
                    mCurrentTask = null;
                }

                @Override
                protected void onExpired() {
                    Log.d(TAG, String.format("cruiseTask onExpired: %s", toString()));
                }

                @Override
                protected void onCancel() {
                    Log.d(TAG, String.format("cruiseTask onCancel: %s", toString()));
//                    ModuleRequestUtils.stopAutoCruise(getEndTime());
                    disableCurrentCruiseTask();
                    mCurrentTask = null;
                }
            };
        }

        private boolean checkCruiseTaskStatus(final CruiseTask cruiseTask) {
            int uniqueCode = DateUtils.calculateUniqueCode(cruiseTask.getBeginTime(), cruiseTask.getEndTime());
            return mCruiseTaskActiveStatus.get(uniqueCode, false);
        }

        private void updateCruiseTaskStatus(final CruiseTask task, final boolean isActive) {
            mCruiseTaskActiveStatus.clear();
            if (task != null) {
                int uniqueCode = DateUtils.calculateUniqueCode(task.getBeginTime(), task.getEndTime());
                mCruiseTaskActiveStatus.put(uniqueCode, isActive);
            }
        }

        void disableCurrentCruiseTask() {
            updateCruiseTaskStatus(mCurrentTask, false);
        }

        boolean getCurrentTaskActiveStatus() {
            if (mCurrentTask == null) {
                return false;
            } else {
                return checkCruiseTaskStatus(mCurrentTask);
            }
        }

        List<CruiseTask> getCruiseTaskList() {
            return new ArrayList<>(mCruiseTaskList);
        }

        void registerCruiseSettingsObserver(CruiseSettingsObserver observer) {
            mDataSetObserverObservable.registerObserver(observer);
        }

        @SuppressWarnings("unused")
        void unregisterCruiseSettingsObserver(CruiseSettingsObserver observer) {
            mDataSetObserverObservable.unregisterObserver(observer);
        }

        @SuppressWarnings("unused")
        void quit() {
            Log.d(TAG, "quit: ");

            if (isRegisterCruiseSettingsReceiver) {
                Context context = BaseApplication.getContext();
                context.unregisterReceiver(mRequestCruiseSettingsReceiver);
                isRegisterCruiseSettingsReceiver = false;
            }
        }

        static class CruiseSettingsObserver extends DataSetObserver {
            @Override
            public void onInvalidated() {
                throw new UnsupportedOperationException("you must override this method");
            }

            @Override
            public void onChanged() {
                throw new UnsupportedOperationException("you must override this method");
            }
        }

        static class CruiseTaskFailureException extends Exception {
            private static final long serialVersionUID = -5381913045460527280L;

            CruiseTaskFailureException(final String message) {
                super(message);
            }
        }
    }

    private static class AutoEngine {
        private static final String TAG = "AutoCruiseEngine";

        private static final int MSG_ADD_TASK_FINISH = 0xA;
        private static final int DEFAULT_CAPACITY = 8;
        private static final int AWAIT_STOP_CONDITION_TIMEOUT = 500;

        private PriorityQueue<CruiseTask> mCruiseTaskPriorityQueue;
        private SparseArray<Long> mCruiseTaskFilter;
        private AtomicBoolean isEngineQuit;
        private AtomicBoolean isStartEngine;
        private AtomicBoolean isRecallTask;
        private AtomicBoolean isRunningTask;
        private AtomicBoolean isEmptyConditionLock;

        private Lock mLock;
        private Condition mStopCondition;
        private Condition mEmptyCondition;
        private Condition mWaitCondition;
        private Condition mDurationCondition;
        private SparseArray<String> mConditionDictionary;

        private Handler mEngineHandler;

        private AutoEngine() {
            initDataSet();
            initLock();
            initHandler();
        }

        private void initDataSet() {
            mCruiseTaskPriorityQueue = new PriorityQueue<>(DEFAULT_CAPACITY,
                    new Comparator<CruiseTask>() {
                        @Override
                        public int compare(CruiseTask o1, CruiseTask o2) {
                            return Long.compare(o1 != null ? o1.beginTime : 0,
                                    o2 != null ? o2.beginTime : 0);
                        }
                    });
            mCruiseTaskFilter = new SparseArray<>(DEFAULT_CAPACITY);
        }

        private void initLock() {
            isEngineQuit = new AtomicBoolean(false);
            isStartEngine = new AtomicBoolean(false);
            isRecallTask = new AtomicBoolean(false);
            isRunningTask = new AtomicBoolean(false);
            isEmptyConditionLock = new AtomicBoolean(false);

            mLock = new ReentrantLock();
            mStopCondition = mLock.newCondition();
            mEmptyCondition = mLock.newCondition();
            mWaitCondition = mLock.newCondition();
            mDurationCondition = mLock.newCondition();

            mConditionDictionary = new SparseArray<>(4);
            mConditionDictionary.put(mStopCondition.hashCode(), "StopCondition");
            mConditionDictionary.put(mEmptyCondition.hashCode(), "EmptyCondition");
            mConditionDictionary.put(mWaitCondition.hashCode(), "WaitCondition");
            mConditionDictionary.put(mDurationCondition.hashCode(), "DurationCondition");
        }

        private void initHandler() {
            mEngineHandler = new Handler(Looper.getMainLooper(), new Handler.Callback() {
                @Override
                public boolean handleMessage(Message msg) {
                    if (msg.what == MSG_ADD_TASK_FINISH) {
                        Log.d(TAG, String.format("add CruiseTask finish : isEmptyConditionLock = %b",
                                isEmptyConditionLock.get()));

                        if (isEmptyConditionLock.get()) {
                            AutoEngine.this.notifyCondition(mEmptyCondition);
                        }
                    }
                    return false;
                }
            });
        }

        static AutoEngine newInstance() {
            return new AutoEngine();
        }

        void start() {
            Log.d(TAG, "start AutoEngine: cruise");

            if (isStartEngine.get()) {
                Log.e(TAG, "AutoEngine has already started!");
                return;
            }

            isStartEngine.set(true);
            isEngineQuit.set(false);

            AsyncTask.execute(new Runnable() {
                @Override
                public void run() {
                    while (!isEngineQuit.get()) {
                        CruiseTask cruiseTask = AutoEngine.this.fetchCruiseTask();
                        if (cruiseTask == null) {
                            AutoEngine.this.waitingForEmpty();
                            continue;
                        }
                        AutoEngine.this.scheduleCruiseTask(cruiseTask);
                    }
                    isStartEngine.set(false);

                    Log.d(TAG, "AutoEngine quit");
                }
            });
        }

        void stop() {
            Log.d(TAG, "stop: ");

            clear();
            stopInternally();
        }

        void quit() {
            Log.d(TAG, "quit: ");

            isEngineQuit.set(true);
            notifyCondition(mEmptyCondition);
            clear();
            stop();
        }

        void clear() {
            mCruiseTaskPriorityQueue.clear();
            mCruiseTaskFilter.clear();

            Log.d(TAG, String.format("clear: todo CruiseTask list size = %d, filter list size = %d",
                    mCruiseTaskPriorityQueue.size(), mCruiseTaskFilter.size()));
        }

        void addCruiseTask(CruiseTask cruiseTask) {
            if (cruiseTask == null) {
                throw new NullPointerException(
                        String.format("cruiseTask is null, todo cruise task list : %s",
                                Arrays.toString(mCruiseTaskPriorityQueue.toArray())));
            }

            mEngineHandler.removeMessages(MSG_ADD_TASK_FINISH);
            addCruiseTaskInternally(cruiseTask);
            mEngineHandler.sendEmptyMessageDelayed(MSG_ADD_TASK_FINISH, 100);
        }

        @Nullable
        private CruiseTask fetchCruiseTask() {
            Log.i(TAG, String.format("to do CruiseTask list = %s",
                    Arrays.toString(mCruiseTaskPriorityQueue.toArray())));

            CruiseTask cruiseTask = mCruiseTaskPriorityQueue.poll();
            removeTaskFilter(cruiseTask);

            return cruiseTask;
        }

        private void removeTaskFilter(CruiseTask task) {
            if (task == null) {
                return;
            }

            int taskUniqueCode = DateUtils.calculateUniqueCode(task.getBeginTime(), task.getEndTime());
            mCruiseTaskFilter.remove(taskUniqueCode);
        }

        private void waitingForEmpty() {
            notifyCondition(mStopCondition);
            isEmptyConditionLock.set(true);
            awaitCondition(mEmptyCondition);
            isEmptyConditionLock.set(false);
        }

        private void scheduleCruiseTask(final CruiseTask cruiseTask) {
            Log.d(TAG, String.format("scheduleCruiseTask: fetch %s", cruiseTask));

            long currentTimeMillis = System.currentTimeMillis();
            long needWaitTime = cruiseTask.getBeginTime() - currentTimeMillis;
            if (needWaitTime < 0) {
                if (cruiseTask.getEndTime() < currentTimeMillis) {
                    cruiseTask.onExpired();
                    return;
                }
            } else {
                Log.e(TAG, String.format("%s need wait start time %dms", cruiseTask.getName(),
                        needWaitTime));
            }

            isRunningTask.set(true);
            awaitConditionTimeout(mWaitCondition, needWaitTime);

            if (recallCruiseTask(cruiseTask)) {
                Log.d(TAG, String.format("scheduleCruiseTask: recall task : %s", cruiseTask));
                return;
            }

            cruiseTask.onStart();

            currentTimeMillis = System.currentTimeMillis();
            long durationTime = cruiseTask.getEndTime() - currentTimeMillis;
            awaitConditionTimeout(mDurationCondition, durationTime);

            if (recallCruiseTask(cruiseTask)) {
                Log.d(TAG, String.format("scheduleCruiseTask: recall CruiseTask : %s", cruiseTask));
                return;
            }
            cruiseTask.onStop();
            isRunningTask.set(false);
        }

        private boolean recallCruiseTask(final CruiseTask task) {
            if (isRecallTask.compareAndSet(true, false)) {
                task.onCancel();
                isRunningTask.set(false);
                return true;
            }
            return false;
        }

        private void addCruiseTaskInternally(CruiseTask task) {
            int taskUniqueCode = DateUtils.calculateUniqueCode(task.getBeginTime(), task.getEndTime());
            if (filterTask(taskUniqueCode, task.getEndTime())) {
                Log.e(TAG, String.format("filter duplicate or invalid CruiseTask : %s", task));
                return;
            }

            Log.d(TAG, String.format("addCruiseTaskInternally: %s", task));

            mCruiseTaskPriorityQueue.offer(task);
            long endTime = task.getEndTime();
            mCruiseTaskFilter.put(taskUniqueCode, endTime);

            Log.d(TAG, String.format("all CruiseTak: %s",
                    Arrays.toString(mCruiseTaskPriorityQueue.toArray())));
        }

        private boolean filterTask(final int taskUniqueCode, final long endTime) {
            Long conflictEndTime = mCruiseTaskFilter.get(taskUniqueCode);
            if (conflictEndTime == null) {
                return false;
            }
            return conflictEndTime == endTime;
        }

        private void stopInternally() {
            if (isRunningTask.get()) {
                isRecallTask.set(true);
            }

            notifyCondition(mWaitCondition);
            notifyCondition(mDurationCondition);
            awaitConditionTimeout(mStopCondition, AWAIT_STOP_CONDITION_TIMEOUT);
        }

        private void awaitCondition(final Condition condition) {
            Log.d(TAG,
                    String.format("awaitCondition: condition = %s", getConditionName(condition)));

            try {
                mLock.lock();
                condition.await();
            } catch (InterruptedException e) {
                Log.e(TAG, Log.getStackTraceString(e));
            } finally {
                mLock.unlock();
            }
        }

        private void awaitConditionTimeout(final Condition condition, final long timeout) {
            if (timeout > 0) {
                Log.d(TAG, String.format("awaitConditionTimeout: condition = %s, timeout = %dms",
                        getConditionName(condition), timeout));

                try {
                    mLock.lock();
                    condition.await(timeout, TimeUnit.MILLISECONDS);
                } catch (InterruptedException e) {
                    Log.e(TAG, Log.getStackTraceString(e));
                } finally {
                    mLock.unlock();
                }
            }
        }

        private void notifyCondition(final Condition condition) {
            Log.d(TAG, String.format("notifyCondition: %s", getConditionName(condition)));

            try {
                mLock.lock();
                condition.signal();
            } finally {
                mLock.unlock();
            }
        }

        private String getConditionName(final Condition condition) {
            return mConditionDictionary.get(condition.hashCode());
        }
    }

    private static abstract class CruiseTask implements Cloneable {
        private String name;
        private long beginTime;
        private long endTime;

        CruiseTask(String name) {
            this(name, 0, 0);
        }

        CruiseTask(final String name, final long beginTime, final long endTime) {
            this.name = name;
            this.beginTime = beginTime;
            this.endTime = endTime;
        }

        void setName(final String name) {
            this.name = name;
        }

        void setBeginTime(final long beginTime) {
            this.beginTime = beginTime;
        }

        void setEndTime(final long endTime) {
            this.endTime = endTime;
        }

        String getName() {
            return name;
        }

        long getBeginTime() {
            return beginTime;
        }

        long getEndTime() {
            return endTime;
        }

        @Override
        public Object clone() {
            CruiseTask cruiseTask = null;
            try {
                cruiseTask = (CruiseTask) super.clone();
            } catch (CloneNotSupportedException e) {
                Log.e(TAG, Log.getStackTraceString(e));
            }
            return cruiseTask;
        }

        @Override
        public String toString() {
            String beginTimeStr = DateUtils.toTimeFormat(beginTime);
            String endTimeStr = DateUtils.toTimeFormat(endTime);
            return "CruiseTask{"
                    + "name='"
                    + name
                    + '\''
                    + ", beginTime="
                    + beginTimeStr
                    + "("
                    + beginTime
                    + ")"
                    + ", endTime="
                    + endTimeStr
                    + "("
                    + endTime
                    + ")"
                    + '}';
        }

        protected abstract void onStart();

        protected abstract void onStop();

        protected abstract void onExpired();

        protected abstract void onCancel();
    }

    private static class ModuleRequestUtils {

        static void startAutoCruise(final long beginTime) {
            Log.d(TAG, "ModuleRequestUtils startAutoCruise: ");

            ControlManager.handleRequest(0, PlatformDef.CRUISE_AUTO_START,
                    PlatformDef.CRUISE_AUTO_START_PATTERN, null);
        }

        static void stopAutoCruise(final long endTime) {
            Log.d(TAG, "ModuleRequestUtils stopAutoCruise: ");

            ControlManager.handleRequest(0, PlatformDef.CRUISE_AUTO_EXIT,
                    PlatformDef.CRUISE_AUTO_EXIT_PATTERN, null);
        }
    }

    private static class DateUtils {

        private static final String RELATIVE_ZERO = "00:00";

        private static final long CYCLE_TIME = 86400000L;

        private static final SimpleDateFormat sTimeFormatter;
        private static final SimpleDateFormat sDateFormatter;

        static {
            sTimeFormatter = new SimpleDateFormat("HH:mm", Locale.getDefault());
            sDateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
        }

        private static long getZeroTimeOfDay() {
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(System.currentTimeMillis());
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            return calendar.getTimeInMillis();
        }

        private static long getRelativeZeroTime() {
            return parseTime(RELATIVE_ZERO);
        }

        static long parseTime(String timeStr) {
            try {
                Date date = sTimeFormatter.parse(timeStr);
                return date.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }

            return -1;
        }

        static long getRelativeTimeOfDay(String time) {
            long relativeTime = parseTime(time);
            long relativeZero = getRelativeZeroTime();
            long relativeInterval = relativeTime - relativeZero;
            long zeroTime = getZeroTimeOfDay();
            return zeroTime + relativeInterval;
        }

        static String toTimeFormat(long time) {
            synchronized (sTimeFormatter) {
                return sTimeFormatter.format(new Date(time));
            }
        }

        static String toDateFormat(long time) {
            synchronized (sDateFormatter) {
                return sDateFormatter.format(new Date(time));
            }
        }

        static long getCycleTime() {
            return CYCLE_TIME;
        }

        static int calculateUniqueCode(long beginTime, final long endTime) {
            return String.format("%s%s", toDateFormat(beginTime), toDateFormat(endTime)).hashCode();
        }
    }
}
