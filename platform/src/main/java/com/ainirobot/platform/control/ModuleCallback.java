/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.platform.control;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.BatteryManager;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.module.ModuleCallbackApi;
import com.ainirobot.platform.PlatformDef;
import com.ainirobot.platform.data.FeatureConfig;
import com.ainirobot.platform.data.HwStateManager;
import com.ainirobot.platform.data.RobotInfo;

public class ModuleCallback extends ModuleCallbackApi {

    private static final String TAG = ModuleCallback.class.getSimpleName();
    private static final String BATTERY_STATUS_ACTION = "com.ainirobot.platform.BATTERY_STATUS_CHANGED";

    private Context mContext;
    private BatteryStatusReceiver mBatteryReceiver;
    private boolean mIsCharging = false;

    public ModuleCallback(Context context) {
        mContext = context;
        IntentFilter batteryFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        mBatteryReceiver = new BatteryStatusReceiver();
        mContext.registerReceiver(mBatteryReceiver, batteryFilter);
    }

    @Override
    public boolean onSendRequest(int reqId, String reqType, String reqText, String reqParam)
            throws RemoteException {
        Log.d(TAG, "New request: " + " type is:" + reqType + " text is:" + reqText + " reqParam = " + reqParam);
        ControlManager.handleRequest(reqId, reqType, reqText, reqParam);
        return true;
    }

    /**
     * Handle message received from HW service
     *
     * @param function
     * @param type
     * @param message
     * @throws RemoteException
     */
    @Override
    public void onHWReport(int function, String type, String message)
            throws RemoteException {
        ControlManager.handleHWException(function, type, message);
    }

    @Override
    public void onSuspend() throws RemoteException {
        Log.d(TAG, "onSuspend");
        ControlManager.handleSuspend();
        Log.d(TAG, "Suspend finished");
    }

    @Override
    public void onRecovery() throws RemoteException {
        Log.d(TAG, "onRecovery");
        //重新加载信息
        RobotInfo.reload();
        //清除导览结束点
        RobotInfo.resetBackPoint();
        HwStateManager.getInstance().init();
        ControlManager.handleRecovery();
        Log.d(TAG, "Recovery finished");
    }

    public class BatteryStatusReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (!RobotApi.getInstance().isActive()) {
                return;
            }
            int charging = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1);
            boolean isCharging = charging != 0;
            RobotInfo.setCharging(isCharging);

            // 获取电量信息
            int level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0);
            int scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, 100);
            int batteryPct = (level * 100) / scale;

            // 发送广播到RN进程
            Intent batteryIntent = new Intent(BATTERY_STATUS_ACTION);
            batteryIntent.putExtra("batteryLevel", batteryPct);
            batteryIntent.putExtra("isCharging", isCharging);
            context.sendBroadcast(batteryIntent);

            if (mIsCharging != isCharging) {
                boolean isAllowChargingNavi = FeatureConfig.isAllowChargingNavi();
                boolean isAllowChargingChat = FeatureConfig.isAllowChargingChat();
                Log.d(TAG, "On charge state change, isCharge: " + mIsCharging
                        + ", isAllowChargingChat: " + isAllowChargingChat
                        + ", isCharacterNull: " + ControlManager.isCharacterNull());
                if (isCharging && ControlManager.isCharacterNull()) {
                    Log.d(TAG, "On charge state change, Current has not switch character");
                    return;
                }
                mIsCharging = isCharging;
                if (isCharging) {
                    Log.d(TAG, "On charge state change, " +
                            "isAllowChargingNavi=" + isAllowChargingNavi +
                            ", isAllowChargingChat=" + isAllowChargingChat);
                    //从业务上来说，充电中导航和充电中聊天相互没有影响（移动电源版本使用，目前支持的时区：ja、ko、tw）
                    if(isAllowChargingNavi){
                        Log.d(TAG, "On charge state change, Support charging navi");
                        //充电中可导航不弹出大电池的逻辑是在coreservice中处理的，所以这里不需要掉disableBattery
                        ControlManager.handleRequest(Definition.DEBUG_REQ_ID,
                                PlatformDef.START_CHARGING, null, null);
                        return;
                    }
                    if (!isAllowChargingChat) {
                        Log.d(TAG, "On charge state change, Not support charging chat");
                        RobotApi.getInstance().resetSystemStatus();
                        return;
                    } else {
                        Log.d(TAG, "On charge state change, Support charging chat");
                        RobotApi.getInstance().disableBattery();
                    }
                    ControlManager.handleRequest(Definition.DEBUG_REQ_ID,
                            PlatformDef.START_CHARGING, null, null);
                } else {
                    ControlManager.handleRequest(Definition.DEBUG_REQ_ID,
                            PlatformDef.STOP_CHARGING, null, null);
                }
            }
        }
    }
}
