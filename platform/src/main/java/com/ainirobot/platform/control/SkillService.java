package com.ainirobot.platform.control;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import androidx.annotation.Nullable;
import android.util.Log;

import com.ainirobot.coreservice.client.ApiListener;
import com.ainirobot.coreservice.client.speech.SkillApi;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.nlp.state.NlpStateManager;
import com.ainirobot.platform.speech.SpeechApi;

import java.util.Timer;
import java.util.TimerTask;

public class SkillService extends Service {

    private static final String TAG = SkillService.class.getSimpleName();
    private static final long RECONNECT_INTERVAL = 5000;

    private Timer timer;
    private final SkillApi skillApi = new SkillApi();

    @Override
    public void onCreate() {
        Log.i(TAG, "onCreate");
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        int result = super.onStartCommand(intent, flags, startId);
        Log.i(TAG, "onStartCommand this result=" + result);
        connectSpeechServer();
        return START_NOT_STICKY;
    }

    private void connectSpeechServer() {
        ApiListener listener = new ApiListener() {
            @Override
            public void handleApiDisabled() {

            }

            @Override
            public void handleApiConnected() {
                SpeechApi.getInstance().setSpeechApi(skillApi);
                NlpStateManager.Companion.get().resetNlpLauncher();
            }

            @Override
            public void handleApiDisconnected() {
                Log.e(TAG, "handleApiDisconnected");
//                skillApi.connectApi(BaseApplication.getContext());
                reconnect();
            }
        };
        skillApi.addApiEventListener(listener);
        skillApi.connectApi(BaseApplication.getContext());
    }

    private void reconnect() {
        cancelTimer();
        timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.e(TAG, "Reconnect to speech");
                if (skillApi.isApiConnectedService()) {
                    Log.e(TAG, "Already connected to speech");
                    cancelTimer();
                    return;
                }
                skillApi.connectApi(BaseApplication.getContext());
            }
        }, 0, RECONNECT_INTERVAL);
    }

    private void cancelTimer() {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
    }
}
