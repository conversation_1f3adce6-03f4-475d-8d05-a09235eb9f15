package com.ainirobot.platform.control

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.text.TextUtils
import android.util.Log
import com.ainirobot.platform.react.server.control.RNServerManager

const val TAG = "AppMessengerService"

class AppMessengerService : Service() {

    override fun onBind(intent: Intent): IBinder? {
        val packageName = intent.getStringExtra("package")
        Log.i(TAG, "onBind intent:$intent packageName:$packageName")
        return if (TextUtils.isEmpty(packageName)) {
            null
        } else {
            var appMessengerProxy =
                    RNServerManager.getInstance().nlpApkControlServer.getAppMessengerProxy(packageName)
            if(null == appMessengerProxy){
                appMessengerProxy = IAppMessengerProxy(packageName)
                RNServerManager.getInstance().nlpApkControlServer.putAppMessengerProxy(appMessengerProxy)
                RNServerManager.getInstance().nlpApkControlServer.onServiceConnected(packageName)
            }
            appMessengerProxy
        }
    }

    override fun onRebind(intent: Intent?) {
        super.onRebind(intent)
        val packageName = intent?.getStringExtra("package")
        Log.i(TAG, "onRebind $packageName")
        packageName?.apply {
            var appMessengerProxy =
                    RNServerManager.getInstance().nlpApkControlServer.getAppMessengerProxy(packageName)
            if(appMessengerProxy == null){
                appMessengerProxy = IAppMessengerProxy(packageName)
                RNServerManager.getInstance().nlpApkControlServer.putAppMessengerProxy(appMessengerProxy)
                RNServerManager.getInstance().nlpApkControlServer.onServiceConnected(packageName)
            }
        }
    }

    override fun onUnbind(intent: Intent?): Boolean {
        val packageName = intent?.getStringExtra("package")
        Log.i(TAG, "onUnbind $packageName")
        packageName?.apply {
            RNServerManager.getInstance().nlpApkControlServer.removeAppMessengerProxy(packageName)
            RNServerManager.getInstance().nlpApkControlServer.onServiceDisconnected(packageName)
        }
        return super.onUnbind(intent)
    }
}

class IAppMessengerProxy(val packageName: String) : IAppMessenger.Stub() {

    private var mRobotMessenger: IRobotMessenger? = null

    override fun setRobotMessenger(robotMessenger: IRobotMessenger?) {
        Log.i(TAG, "setRobotMessenger $robotMessenger")
        this.mRobotMessenger = robotMessenger
        RNServerManager.getInstance().nlpApkControlServer.onRobotMessengerReady(packageName)
    }

    override fun onTriggerCommand(command: String) {
        Log.i(TAG, "onTriggerCommand command:$command")
        RNServerManager.getInstance().nlpApkControlServer.onTriggerCommand(packageName, command)
    }

    fun onRobotMessage(message: String?): Int {
        Log.i(TAG, "onRobotMessage:$message")
        if (null == this.mRobotMessenger) {
            return -2
        } else {
            try {
                this.mRobotMessenger!!.onRobotMessage(message)
            } catch (e: Exception) {
                e.printStackTrace()
                return -3
            }
            return 0
        }
    }
}