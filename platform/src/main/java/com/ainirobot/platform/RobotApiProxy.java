package com.ainirobot.platform;


import android.content.Context;
import android.content.Intent;
import android.os.RemoteException;
import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.Definition.TrackMode;
import com.ainirobot.coreservice.client.Invoker;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.actionbean.CruiseRouteBean;
import com.ainirobot.coreservice.client.actionbean.HeadTurnBean;
import com.ainirobot.coreservice.client.actionbean.LeadingParams;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.actionbean.RobotStandbyBean;
import com.ainirobot.coreservice.client.actionbean.StartCreateMapBean;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.PersonInfoListener;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

public class RobotApiProxy {
    private final Object LOCK = new Object();
    private final Object COMMAND_LOCK = new Object();
    private static final String TAG = "RobotApiProxy";
    private Gson mGson;

    /**
     * Api回调的最小超时时间
     */
    private static final long MIN_TIMEOUT = 50;

    /**
     * Api回调的最大超时时间
     */
    private static final long MAX_TIMEOUT = 2 * 60 * 1000;

    private RobotApi mApi;
    private int mReqId;
    private final ConcurrentHashMap<String, List<ActionCallback>> mActionCallbacks;
    private final List<Callback> mCommandCallbacks;
    private final Invoker mInvoker;

    public RobotApiProxy(Invoker invoker) {
        this.mInvoker = invoker;
        this.mApi = RobotApi.getInstance();
        this.mActionCallbacks = new ConcurrentHashMap<>();
        this.mCommandCallbacks = new ArrayList<>();
        this.mGson = new Gson();
    }

    /**
     * 停止Api调用
     * <p>
     * 等待Api调用stop后的回调，底层确认已stop后才能结束
     *
     * <P><B>NOTE:</B> 只做Api停止回调的等待，不会主动调用stop操作，具体的stop操作请在上层onStop内调用</p>
     *
     * @param timeout 超时时间，超过该时间仍然收不到回调，自动退出，最小超时200ms，最大2minutes
     * @return 成功返回true，失败false
     */
    public boolean stop(long timeout) {
        Log.d(TAG, "Api stop : " + mInvoker);
        boolean result = checkAction();
        if (!result) {
            throw new RuntimeException("Action not stopped");
        }

        Log.d(TAG, "Api stop check action :  " + result);

        synchronized (COMMAND_LOCK) {
            for (Callback callback : mCommandCallbacks) {
                callback.stop();
            }
            mCommandCallbacks.clear();
        }
        Log.d(TAG, "Api stop : command clear");

        synchronized (LOCK) {
            Log.d(TAG, "Api stop : " + mActionCallbacks.size() + " " + timeout);
            if (mActionCallbacks.isEmpty()) {
                return true;
            }

            try {
                if (timeout < MIN_TIMEOUT) {
                    timeout = MIN_TIMEOUT;
                }

                if (timeout > MAX_TIMEOUT) {
                    timeout = MAX_TIMEOUT;
                }

                LOCK.wait(timeout);
                mActionCallbacks.clear();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    public void setReqid(int reqId) {
        mReqId = reqId;
    }

    public int startLead(LeadingParams params, ActionListener listener) {
        return mApi.startLead(mReqId, params, createActionCallback("lead", listener));
    }

    public int stopLead(boolean isResetHW) {
        stopAction("lead");
        return mApi.stopLead(mReqId, isResetHW);
    }

    public int startFocusFollow(int personId,
                                long lostTimer, float maxDistance, ActionListener listener) {
        return startFocusFollow(personId, lostTimer, maxDistance, true, listener);
    }

    public int startFocusFollow(int personId, long lostTimer,
                                float maxDistance, boolean isAllowMoveBody, ActionListener listener) {
        return mApi.startFocusFollow(mReqId, personId,
                lostTimer, maxDistance, isAllowMoveBody, createActionCallback("focusFollow", listener));
    }

    public int stopFocusFollow() {
        stopAction("focusFollow");
        return stopFocusFollow(false);
    }

    public int stopFocusFollow(boolean isResetHW) {
        stopAction("focusFollow");
        return mApi.stopFocusFollow(mReqId, isResetHW);
    }

    public int startSmartFocusFollow(long lostTimer, float maxDistance, ActionListener listener) {
        return mApi.startSmartFocusFollow(mReqId, lostTimer, maxDistance,  createActionCallback("smartFocusFollow", listener));
    }

    public int stopSmartFocusFollow() {
        stopAction("smartFocusFollow");
        return mApi.stopSmartFocusFollow(mReqId);
    }

    public int startBodyFollowAction(int personId, ActionListener listener) {
        return mApi.startBodyFollowAction(mReqId, personId, listener);
    }

    public int stopBodyFollowAction() {
        return mApi.stopBodyFollowAction(mReqId);
    }

    public int switchTrackTarget(int angle, CommandListener listener) {
        return mApi.switchTrackTarget(mReqId, angle, createCallback(listener));
    }

    public int startReceptionRegister(int detection, String imageSrc, String name,
                                      String taskId, int has_image, CommandListener listener) {
        return mApi.receptionRegister(mReqId, detection, imageSrc, name, taskId, has_image, createCallback(listener));
    }

    public int startNavigation(String destination,
                               double coordinateDeviation, long time, ActionListener listener) {
        return mApi.startNavigation(mReqId, destination,
                coordinateDeviation, time, createActionCallback("navigation", listener));
    }

    public int startNavigation(String destination, double coordinateDeviation, long time,
                               double linearSpeed, double angularSpeed, ActionListener listener) {
        return mApi.startNavigation(mReqId, destination, coordinateDeviation, time, linearSpeed,
                angularSpeed, createActionCallback("navigation", listener));
    }

    public int startNavigation(String destination, double coordinateDeviation, long time,
                               double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, ActionListener listener) {
        return mApi.startNavigation(mReqId, destination, coordinateDeviation, time, linearSpeed,
                angularSpeed, isAdjustAngle, createActionCallback("navigation", listener));
    }

    public int startNavigation(String destination, double coordinateDeviation, long time,
                               double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange,
                               ActionListener listener) {
        return mApi.startNavigation(mReqId, destination, coordinateDeviation, time, linearSpeed,
                angularSpeed, isAdjustAngle, destinationRange, createActionCallback("navigation", listener));
    }

    public int startNavigation(String destination, double coordinateDeviation, long time,
                               double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange,
                               int wheelOverCurrentRetryCount,
                               ActionListener listener) {
        return mApi.startNavigation(mReqId, destination, coordinateDeviation, time, linearSpeed,
                angularSpeed, isAdjustAngle, destinationRange, wheelOverCurrentRetryCount,
                createActionCallback("navigation", listener));
    }

    public int startNavigation(String destination, double coordinateDeviation, long time,
                               double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange,
                               int wheelOverCurrentRetryCount, long multipleWaitTime,
                               ActionListener listener) {
        return mApi.startNavigation(mReqId, destination, coordinateDeviation, time, linearSpeed,
                angularSpeed, isAdjustAngle, destinationRange, wheelOverCurrentRetryCount,
                multipleWaitTime, createActionCallback("navigation", listener));
    }

    public int startNavigation(String destination, double coordinateDeviation, long time,
                               double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange,
                               int wheelOverCurrentRetryCount, long multipleWaitTime,
                               int priority, int startModeLevel, int brakeModeLevel, ActionListener listener) {
        return mApi.startNavigation(mReqId, destination, coordinateDeviation, time, linearSpeed,
                angularSpeed, isAdjustAngle, destinationRange, wheelOverCurrentRetryCount,
                multipleWaitTime, priority, startModeLevel, brakeModeLevel, createActionCallback("navigation", listener));
    }

    public int startNavigation(String destination, double coordinateDeviation, double obsDistance,
                               long time, double linearSpeed, double angularSpeed, boolean isAdjustAngle,
                               double destinationRange, int wheelOverCurrentRetryCount, long multipleWaitTime,
                               int priority, double linearAcceleration, double angularAcceleration,
                               int runStopCmdParam, int startModeLevel, int brakeModeLevel,
                               double posTolerance, double angleTolerance, ActionListener listener) {
        return mApi.startNavigation(mReqId, destination, coordinateDeviation, obsDistance, time,
                linearSpeed, angularSpeed, isAdjustAngle, destinationRange, wheelOverCurrentRetryCount,
                multipleWaitTime, priority, linearAcceleration, angularAcceleration, runStopCmdParam,
                startModeLevel, brakeModeLevel, posTolerance, angleTolerance, createActionCallback("navigation", listener));
    }

    public int startNavigation(Pose pose, double coordinateDeviation, boolean isAdjustAngle,
                               long time, double linearSpeed, double angularSpeed,
                               ActionListener listener) {
        return mApi.startNavigation(mReqId, pose, coordinateDeviation, isAdjustAngle, time,
                linearSpeed, angularSpeed, createActionCallback("navigation", listener));
    }

    public int startNavigation(Pose pose, double coordinateDeviation, boolean isAdjustAngle,
                               long time, double linearSpeed, double angularSpeed,
                               int wheelOverCurrentRetryCount,
                               int priority, ActionListener listener) {
        return mApi.startNavigation(mReqId, pose, coordinateDeviation, isAdjustAngle, time,
                linearSpeed, angularSpeed, wheelOverCurrentRetryCount, priority,
                createActionCallback("navigation", listener));
    }

    public int startNavigation(Pose pose, double coordinateDeviation, double obsDistance,
                               boolean isAdjustAngle, long time, double linearSpeed, double angularSpeed,
                               int wheelOverCurrentRetryCount, int priority, double linearAcceleration,
                               double angularAcceleration, int runStopCmdParam, int startModeLevel,
                               int brakeModeLevel, double posTolerance, double angleTolerance, ActionListener listener) {
        return mApi.startNavigation(mReqId, pose, coordinateDeviation, obsDistance, isAdjustAngle, time, 0,
                linearSpeed, angularSpeed, wheelOverCurrentRetryCount, priority, linearAcceleration, angularAcceleration,
                runStopCmdParam, startModeLevel, brakeModeLevel, posTolerance, angleTolerance,
                createActionCallback("navigation", listener));
    }

    public int startNavigationBack(String destinationJson, double linearSpeed, double angularSpeed, ActionListener listener) {
        return mApi.startNavigationBack(mReqId, destinationJson, linearSpeed, angularSpeed,
                createActionCallback("navigationback", listener));
    }

    public int stopNavigation() {
        stopAction("navigation");
        return mApi.stopNavigation(mReqId);
    }

    /**
     * 停止导航时是否执行NavigationAction 的getStopCommands的指令
     * @param isCancelStopCmd
     * @return
     */
    public int stopNavigation(boolean isCancelStopCmd) {
        stopAction("navigation");
        return mApi.stopNavigation(mReqId, isCancelStopCmd);
    }

    public int stopNavigationBack() {
        stopAction("navigationback");
        return mApi.stopNavigationBack(mReqId);
    }

    public int startCruise(List<Pose> route, int startPoint,
                           List<Integer> dockingPoints, ActionListener listener) {
        return mApi.startCruise(mReqId, route, startPoint,
                dockingPoints, createActionCallback("cruise", listener));
    }

    public int startCruise(List<Pose> route, int startPoint, List<Integer> dockingPoints,
                           double linearSpeed, double angularSpeed, ActionListener listener) {
        return mApi.startCruise(mReqId, route, startPoint, dockingPoints,
                linearSpeed, angularSpeed, createActionCallback("cruise", listener));
    }

    public int startCruise(List<Pose> route, int startPoint, List<Integer> dockingPoints,
                           double linearSpeed, double angularSpeed, long multiTimeout,
                           ActionListener listener) {
        return mApi.startCruise(mReqId, route, startPoint, dockingPoints,
                linearSpeed, angularSpeed, multiTimeout, createActionCallback("cruise", listener));
    }


    public int stopCruise() {
        stopAction("cruise");
        return mApi.stopCruise(mReqId);
    }

    public int startRegister(String personName, long timeout,
                             int tryCount, long secondDelay, String welcomeContent, ActionListener listener) {
        return mApi.startRegister(mReqId, personName, timeout,
                tryCount, secondDelay, welcomeContent, createActionCallback("register", listener));
    }

    public int stopRegister() {
        stopAction("register");
        return mApi.stopRegister(mReqId);
    }

    public int wakeUp(float angle, ActionListener listener) {
        return mApi.wakeUp(mReqId, angle, createActionCallback("wakeup", listener));
    }

    public int stopWakeUp() {
        stopAction("wakeup");
        return mApi.stopWakeUp(mReqId);
    }

    /**
     * start pose navigation
     *
     * @param destination navigation destination
     * @param time        How long does it take to fail to think
     * @param needRotate  Is need rotate head to -270
     * @return
     */
    public int startPoseNavigation(String destination, double coordinateDeviation
            , long time, double linearSpeed, double angularSpeed
            , boolean needRotate, ActionListener listener) {

        return mApi.startPoseNavigation(mReqId, destination, coordinateDeviation, time,
                linearSpeed, angularSpeed, needRotate, createActionCallback("poseNavi", listener));
    }

    /**
     * stop pose navigation
     *
     * @return
     */
    public int stopPoseNavigation() {
        stopAction("poseNavi");
        return mApi.stopPoseNavigation(mReqId);
    }

    public int startGetAllPersonInfo(PersonInfoListener listener) {
        return mApi.startGetAllPersonInfo(mReqId, listener);
    }

    public int stopGetAllPersonInfo(PersonInfoListener listener) {
        return mApi.stopGetAllPersonInfo(mReqId, listener);
    }

    public boolean finishModuleParser(int reqId, boolean ifParsedSucc) {
        if (!mApi.isApiConnectedService()) {
            Log.w(TAG, "finishModuleParser: finish module parser fail,reqId=" + reqId);
            return false;
        }
        return mApi.finishModuleParser(reqId, ifParsedSucc);
    }

    public boolean finishModuleParser(int reqId, boolean ifParsedSucc, String response) {
        return mApi.finishModuleParser(reqId, ifParsedSucc, response);
    }

    public int goBackward(float speed, CommandListener listener) {
        return mApi.goBackward(mReqId, speed, createCallback(listener));
    }

    public int goBackward(float speed, float distance, CommandListener listener) {
        return mApi.goBackward(mReqId, speed, distance, createCallback(listener));
    }

    public int goForward(float speed, CommandListener listener) {
        return mApi.goForward(mReqId, speed, createCallback(listener));
    }

    public int goForward(float speed, float distance, CommandListener listener) {
        return mApi.goForward(mReqId, speed, distance, createCallback(listener));
    }

    public int goForwardWithAvoid(float speed, float distance, boolean avoid, CommandListener listener) {
        return mApi.goForward(mReqId, speed, distance, avoid, createCallback(listener));
    }

    public int turnLeft(float speed, CommandListener listener) {
        return mApi.turnLeft(mReqId, speed, createCallback(listener));
    }

    public int turnLeft(float speed, float angle, CommandListener listener) {
        return mApi.turnLeft(mReqId, speed, angle, createCallback(listener));
    }

    public int turnRight(float speed, CommandListener listener) {
        return mApi.turnRight(mReqId, speed, createCallback(listener));
    }

    public int turnRight(float speed, float angle, CommandListener listener) {
        return mApi.turnRight(mReqId, speed, angle, createCallback(listener));
    }

    public int turnBack(float speed, CommandListener listener) {
        return mApi.turnBack(mReqId, speed, createCallback(listener));
    }

    public int rotate(float speed, CommandListener listener) {
        return mApi.rotate(mReqId, speed, createCallback(listener));
    }

    public int rotateInPlace(int direction, float speed, float angle, final CommandListener listener) {
//        return mApi.rotateInPlace(mReqId, direction, speed, angle, createCallback(listener));
        return 0;
    }

    public int stopMove(CommandListener listener) {
        return mApi.stopMove(mReqId, createCallback(listener));
    }

    public int setLocation(String param, CommandListener listener) {
        return mApi.setLocation(mReqId, param, createCallback(listener));
    }

    public int setPoseLocation(String param, CommandListener listener) {
        return mApi.setPoseLocation(mReqId, param, createCallback(listener));
    }

    public int setPoseEstimate(String param, CommandListener listener) {
        return mApi.setPoseEstimate(mReqId, param, createCallback(listener));
    }

    public int setNavigationConfig(String param, CommandListener listener) {
        return mApi.setNavigationConfig(mReqId, param, createCallback(listener));
    }

    public int getNavigationConfig(CommandListener listener) {
        return mApi.getNavigationConfig(mReqId, createCallback(listener));
    }

    public int startCreatingMap(CommandListener listener) {
        Log.d(TAG, "Use param StartCreateMapBean to startCreatingMap");
        return -1;
    }

    public int startCreatingMap(StartCreateMapBean startCreateMapBean, CommandListener listener) {
        return mApi.startCreatingMap(mReqId, startCreateMapBean, createCallback(listener));
    }

    public int stopCreatingMap(String mapName, CommandListener listener) {
        return mApi.stopCreatingMap(mReqId, mapName, createCallback(listener));
    }

    public int switchMap(String param, CommandListener listener) {
        return mApi.switchMap(mReqId, param, createCallback(listener));
    }

    public int removeMap(String param, CommandListener listener) {
        return mApi.removeMap(mReqId, param, createCallback(listener));
    }

    public int removeLocation(String param, CommandListener listener) {
        return mApi.removeLocation(mReqId, param, createCallback(listener));
    }

    public int getPlaceList(CommandListener listener) {
        return mApi.getPlaceList(mReqId, createCallback(listener));
    }

    public int getPlaceListWithName(CommandListener listener) {
        return mApi.getPlaceListWithName(mReqId, createCallback(listener));
    }

    public int getPlaceListWithNameList(String param, CommandListener listener) {
        return mApi.getPlaceListWithNameList(mReqId, param, createCallback(listener));
    }

    public int getPlace(String param, CommandListener listener) {
        return mApi.getPlace(mReqId, param, createCallback(listener));
    }

    public int getMultiFloorConfigAndPose(CommandListener listener) {
        return mApi.getMultiFloorConfigAndPose(mReqId, createCallback(listener));
    }

    public int goPosition(String position, CommandListener listener) {
        return mApi.goPosition(mReqId, position, createCallback(listener));
    }

    public int goPosition(String position, String velocity, CommandListener listener) {
        return mApi.goPosition(mReqId, position, velocity, listener);
    }

    public int goPosition(String position, String velocity, boolean isAdjustAngle,
                          double destinationRange, int priority, CommandListener listener) {
        return mApi.goPosition(mReqId, position, velocity, isAdjustAngle, destinationRange, priority, listener);
    }

    public int stopGoPosition() {
        return mApi.stopGoPosition(mReqId);
    }

    public int getLocation(String param, CommandListener listener) {
        return mApi.getLocation(mReqId, param, createCallback(listener));
    }

    public int turnToTargetDirection(Pose pose, double linearSpeed, double angularSpeed, boolean turnLeft, CommandListener listener) {
        return mApi.turnToTargetDirection(mReqId, pose, linearSpeed, angularSpeed, turnLeft, listener);
    }

    public int stopTurnToTargetDirection() {
        return mApi.stopTurnToTargetDirection(mReqId);
    }

    public int isRobotInLocation(String name, double range, CommandListener listener) {
        try {
            JSONObject json = new JSONObject();
            json.put(Definition.JSON_NAVI_TARGET_PLACE_NAME, name);
            json.put(Definition.JSON_NAVI_COORDINATE_DEVIATION, range);
            return mApi.isRobotInlocations(mReqId, json.toString(), listener);
        } catch (JSONException je) {
            je.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int isRobotEstimate(CommandListener listener) {
        return mApi.isRobotEstimate(mReqId, createCallback(listener));
    }

    public int saveRobotEstimate(CommandListener listener) {
        return mApi.saveRobotEstimate(mReqId, createCallback(listener));
    }

    public int isInNavigation(CommandListener listener) {
        return mApi.isInNavigation(mReqId, createCallback(listener));
    }

    public int resetHead(CommandListener listener) {
        return mApi.resetHead(mReqId, createCallback(listener));
    }

    public int resetHeadAngle(String params, CommandListener listener) {
        return mApi.resetHeadAngle(mReqId, "", params, createCallback(listener));
    }

    public int navMoveRotation(String text, String params, CommandListener listener) {
        return mApi.navMoveRotation(mReqId, text, params, createCallback(listener));
    }

    public int switchCamera(String mode, CommandListener listener) {
        return mApi.switchCamera(mReqId, mode, createCallback(listener));
    }

    public int moveHead(String hmode, String vmode, int hangle, int vangle, CommandListener listener) {
        return mApi.moveHead(mReqId, hmode, vmode, hangle, vangle, createCallback(listener));
    }

    public int moveHead(String hmode, String vmode, int hangle, int vangle, int hMaxSpeed, int vMaxSpeed, CommandListener listener) {
        return mApi.moveHead(mReqId, hmode, vmode, hangle, vangle, hMaxSpeed, vMaxSpeed, createCallback(listener));
    }

    public int turnHead(HeadTurnBean bean, CommandListener listener) {
        return mApi.turnHead(mReqId, bean, createCallback(listener));
    }

    public int stopTurnHead(CommandListener listener) {
        return mApi.stopTurnHead(mReqId, createCallback(listener));
    }

    public int getHeadStatus(CommandListener listener) {
        return mApi.getHeadStatus(mReqId, createCallback(listener));
    }

    public int setTrackTarget(String name, int id, TrackMode mode, CommandListener listener) {
        return mApi.setTrackTarget(mReqId, name, id, mode, createCallback(listener));
    }

    public int stopTrack(CommandListener listener) {
        return mApi.stopTrack(mReqId, createCallback(listener));
    }

    public int getPictureById(int id, int count, CommandListener listener) {
        return mApi.getPictureById(mReqId, id, count, createCallback(listener));
    }

    public int remoteRegister(String name, List<String> pictures, String welcomeContent, CommandListener listener) {
        return mApi.remoteRegister(mReqId, name, pictures, welcomeContent, createCallback(listener));
    }

    public int remoteDetect(String personId, List<String> pictures, CommandListener listener) {
        return mApi.remoteDetect(mReqId, personId, pictures, createCallback(listener));
    }

    public int remoteModifyDetectName(String userId, String modifiedName, String welcomeContent, CommandListener listener) {
        return mApi.remoteModifyDetectName(mReqId, userId, modifiedName, welcomeContent, createCallback(listener));
    }

    public int getPersonInfoFromNet(String personId, List<String> pictures, CommandListener listener) {
        return mApi.getPersonInfoFromNet(mReqId, personId, pictures, createCallback(listener));
    }

    public int getChargeStatus(CommandListener listener) {
        return mApi.getChargeStatus(mReqId, createCallback(listener));
    }

//    public int setStartChargePoseAction(long timeout, ActionListener listener) {
//        return mApi.setStartChargePoseAction(mReqId, timeout,
//                createActionCallback("setChargePose", listener));
//    }

    public int startNaviToAutoChargeAction(long timeout, ActionListener listener) {
        return mApi.startNaviToAutoChargeAction(mReqId, timeout,
                createActionCallback("startAutoCharge", listener));
    }

    public int startNaviToAutoChargeAction(long timeout, double distance,
                                           long avoidTime, ActionListener listener) {
        return mApi.startNaviToAutoChargeAction(mReqId, timeout, distance,
                avoidTime, createActionCallback("startAutoCharge", listener));
    }

    public int startNaviToAutoChargeAction(long timeout, double distance,
                                           long avoidTime, long multiWaitTime,
                                           ActionListener listener) {
        return mApi.startNaviToAutoChargeAction(mReqId, timeout, distance,
                avoidTime, multiWaitTime, createActionCallback("startAutoCharge", listener));
    }

    public int stopAutoChargeAction(boolean isResetHW) {
        stopAction("startAutoCharge");
        return mApi.stopAutoChargeAction(mReqId, isResetHW);
    }

    public int stopSetChargePileAction(boolean isResetHW) {
        return mApi.stopSetChargePileAction(mReqId, isResetHW);
    }

    public int switchChargeMode() {
        return mApi.switchChargeMode(mReqId);
    }

    public int startCharge() {
        return mApi.startCharge(mReqId);
    }

    public int getEmergencyStatus(CommandListener listener) {
        return mApi.getEmergencyStatus(mReqId, createCallback(listener));
    }

    public int setLambColor(int target, int color) {
        return mApi.setLambColor(mReqId, target, color);
    }

    public int setLambAnimation(int target, int start, int end,
                                int startTime, int endTime, int repeat, int onTime, int freeze) {
        return mApi.setLambAnimation(mReqId, target, start, end,
                startTime, endTime, repeat, onTime, freeze);
    }

    public String registerStatusListener(String type, StatusListener listener) {
        if (mInvoker.isAlive()) {
            return mApi.registerStatusListener(type, listener);
        } else {
            Log.e(TAG, "Invoker is not Alive");
            return null;
        }
    }

    public boolean unregisterStatusListener(StatusListener listener) {
        return mApi.unregisterStatusListener(listener);
    }

    public boolean startStatusSocket(String type, int socketPort) {
        return mApi.startStatusSocket(type, socketPort);
    }

    public boolean closeStatusSocket(String type, int socketPort) {
        return mApi.closeStatusSocket(type, socketPort);
    }

    public void sendStatusReport(String type, String data) {
        mApi.sendStatusReport(type, data);
    }

    public int startInspection(long time,
                               boolean isReInspection, ActionListener listener) {
        return mApi.startInspection(mReqId, time,
                isReInspection, createActionCallback("inspection", listener));
    }

    public int getHWStatus(int function) throws RemoteException {
        return mApi.getHWStatus(function);
    }

    public int getMapName(CommandListener listener) {
        return mApi.getMapName(mReqId, createCallback(listener));
    }

    public String isAlreadyInElevator() {
        return mApi.isAlreadyInElevator();
    }

    public int getPosition(CommandListener listener) {
        return mApi.getPosition(mReqId, createCallback(listener));
    }

    public int setMapInfo(String param, CommandListener listener) {
        return mApi.setMapInfo(mReqId, param, createCallback(listener));
    }

    public int remoteRequestQrcode(CommandListener listener) {
        return mApi.remoteRequestQrcode(mReqId, createCallback(listener));
    }

    public int remoteCheckVerify(String code, CommandListener listener) {
        return mApi.remoteCheckVerify(mReqId, code, createCallback(listener));
    }

    public int remotePhoneCheckVerify(String code, CommandListener listener) {
        return mApi.remoteCheckPhoneVerify(mReqId, code, createCallback(listener));
    }

    public int remoteRequestSkill(CommandListener listener) {
        return mApi.remoteRequestSkill(mReqId, createCallback(listener));
    }

    public int remotePostMsg(String taskId, String status, CommandListener listener) {
        return mApi.remotePostMsg(mReqId, taskId, status, createCallback(listener));
    }

    public int remotePostEmergencyMsg(String status) {
        return mApi.remotePostEmergencyMsg(mReqId, status);
    }

    public int remoteRequestGuestInfo(String id, String type, String startTime, String endTime, CommandListener listener) {
        return mApi.remoteRequestGuestInfo(mReqId, id, type, startTime, endTime, createCallback(listener));
    }

    public int remoteRegister(String name, String id, List<String> pictures, CommandListener listener) {
        return mApi.remoteRegister(mReqId, name, id, pictures, createCallback(listener));
    }

    public int remoteBindStatus(CommandListener listener) {
        return mApi.remoteBindStatus(mReqId, createCallback(listener));
    }

    public int remoteWakeUpTimes(CommandListener listener) {
        return mApi.remoteWakeUpTimes(mReqId, createCallback(listener));
    }

    public int getBatteryTimeRemaining(CommandListener listener) {
        return mApi.getBatteryTimeRemaining(mReqId, createCallback(listener));
    }

    public int getChargeTimeRemaining(CommandListener listener) {
        return mApi.getChargeTimeRemaining(mReqId, createCallback(listener));
    }

    public int sendBatteryTimeRemaining(String msg) {
        return mApi.sendBatteryTimeRemaining(mReqId, msg);
    }

    public int sendChargingTimeRemaining(String msg) {
        return mApi.sendChargingTimeRemaining(mReqId, msg);
    }

    public int resetEstimate(int retryCount, CommandListener listener) {
        return mApi.resetEstimate(mReqId, retryCount, createCallback(listener));
    }

    public int remotePostPrepared(CommandListener listener) {
        return mApi.remotePostPrepared(mReqId, createCallback(listener));
    }

    public int postSetPlaceToServer(String params) {
        return mApi.postSetPlaceToServer(mReqId, params);
    }

    public int resumeSpecialPlaceTheta(String placeName, CommandListener listener) {
        return mApi.resumeSpecialPlaceTheta(mReqId, placeName, createActionCallback("resumeSpecialPlaceTheta", listener));
    }

    public int stopResumeSpecialPlaceTheta() {
        stopAction("resumeSpecialPlaceTheta");
        return mApi.stopResumeSpecialPlaceThetaAction(mReqId);
    }

    public int getCanRotateSupport(CommandListener listener) {
        return mApi.getCanRotateSupport(mReqId, createCallback(listener));
    }

    public int getAskWayList(CommandListener listener) {
        return mApi.getAskWayList(mReqId, createCallback(listener));
    }

    public void disableBattery() {
        mApi.disableBattery();
    }

    public void resetSystemStatus() {
        mApi.resetSystemStatus();
    }

    public int resetPoseEstimate(CommandListener listener) {
        return mApi.resetPoseEstimate(mReqId, createCallback(listener));
    }

    public boolean isActive() {
        return mApi.isActive();
    }

    /**
     * 讲解员——评分——语音
     *
     * @param params
     * @param listener
     * @return
     */
    public int scoreRecordingTask(String params, CommandListener listener) {
        return mApi.scoreRecordingTask(mReqId, params, createCallback(listener));
    }

    public int postCruiseStatus(String params) {
        return mApi.postCruiseStatus(mReqId, params);
    }

    public int setNaviCruiseRoute(String params, CommandListener listener) {
        return mApi.setNaviCruiseRoute(mReqId, params, createCallback(listener));
    }

    public int getNaviCruiseRoute(String params, CommandListener listener) {
        return mApi.getNaviCruiseRoute(mReqId, params, createCallback(listener));
    }

    public int startVisionAlgorithm(CommandListener listener) {
        return mApi.startVision(mReqId, createCallback(listener));
    }

    public int stopVisionAlgorithm(CommandListener listener) {
        return mApi.stopVision(mReqId, createCallback(listener));
    }

    @Deprecated
    public int startBackupVision(CommandListener listener) {
        return mApi.startBackupVision(mReqId, createCallback(listener));
    }
    
    @Deprecated
    public int stopBackupVision(CommandListener listener) {
        return mApi.stopBackupVision(mReqId, createCallback(listener));
    }

    public int setChassisRelocation(int mode, Pose pose, CommandListener listener) {
        return mApi.setChassisRelocation(mReqId, mode, pose, createCallback(listener));
    }

    public int isRobotHasVision(CommandListener listener) {
        return mApi.isRobotHasVision(mReqId, createCallback(listener));
    }

    public void sendStatusReport(int reqId, String action, String msg, boolean succeed, String status) {
        JsonObject obj = new JsonObject();
        obj.addProperty("reqId", reqId);
        obj.addProperty("type", action);
        obj.addProperty("value", succeed);
        if (!TextUtils.isEmpty(msg)) {
            try {
                JsonElement msgJson = new JsonParser().parse(msg);
                if (msgJson != null && msgJson.isJsonObject()) {
                    obj.add("msg", msgJson);
                } else {
                    obj.addProperty("msg", msg);
                }
            } catch (JsonSyntaxException e) {
                obj.addProperty("msg", "jsonerr");
            }
        } else {
            obj.addProperty("msg", "none");
        }
        obj.addProperty("status", status);
        mApi.sendStatusReport(Definition.STATUS_PROCESS_STATE, mGson.toJson(obj));
    }

    public void startReposition() {
        Context context = BaseApplication.getContext();
        Intent visionIntent = new Intent();
        visionIntent.setAction(Definition.ACTION_REPOSITION);
        visionIntent.putExtra(Definition.REPOSITION_VISION, true);
        context.sendBroadcast(visionIntent);
    }

    public void sendStatusToServer(int reqId, String type) {
        JsonObject obj = new JsonObject();
        obj.addProperty("reqId", reqId);
        obj.addProperty("type", type);
        obj.addProperty("result", 0);
        mApi.sendStatusReport(Definition.STATUS_PROCESS_STATE, new Gson().toJson(obj));
    }

    /**
     * remote charge pile pose when set charge from WeiXin program
     *
     * @param reqId
     * @param status   start/finish/running
     * @param msg      String err msg/JsonString position
     * @param result   boolean true/false
     * @param listener
     * @return
     */
    public int remoteChargePile(int reqId, String status, String msg, boolean result, CommandListener listener) {
        return mApi.remoteChargePile(reqId, status, msg, result, listener);
    }

    /**
     * report to result of auto_charge from WeiXin program
     *
     * @param reqId
     * @param status
     * @param result
     * @param listener
     * @return
     */
    public int remoteFirstCharge(int reqId, String status, boolean result, CommandListener listener) {
        return mApi.remoteFirstCharge(reqId, status, result, listener);
    }

    public void setLanguage(String language) {
        mApi.setLanguage(language);
    }

    public int getLanguageList(CommandListener listener) {
        return mApi.getLanguageList(mReqId, listener);
    }

    public int robotStandby(ActionListener listener) {
        return mApi.robotStandby(mReqId, createActionCallback("robotStandby", listener));
    }

    public int robotStandby(RobotStandbyBean bean, ActionListener listener) {
        return mApi.robotStandby(mReqId, bean, createActionCallback("robotStandby", listener));
    }

    public int robotStandbyEnd() {
        stopAction("robotStandby");
        return mApi.robotStandbyEnd(mReqId);
    }

    public int checkIfHasObstacle(int reqId, double startAngle, double endAngle, double distance, CommandListener listener) {
        return mApi.checkIfHasObstacle(reqId, startAngle, endAngle, distance, listener);
    }

    public int hasObstacleInArea(double startAngle, double endAngle, double minDistance,
                                 double maxDistance, CommandListener listener) {
        return mApi.hasObstacleInArea(mReqId, startAngle, endAngle, minDistance, maxDistance, listener);
    }

    public boolean getRobotChargingStatus() {
        return mApi.getChargeStatus();
    }

    public int getRobotBatteryLevel() {
        return mApi.getBatteryLevel();
    }

    public String getInspectResult() {
        return mApi.getInspectResult();
    }

    public String getVersion() {
        return mApi.getVersion();
    }

    public boolean isRobotEstimate() {
        return mApi.isRobotEstimate();
    }

    public boolean isChargePileExits() {
        return mApi.isChargePileExits();
    }

    public boolean isSupportHeadReverse() {
        return mApi.isSupportHeadReverse();
    }

    public boolean isRobotInlocations(@NonNull String name, double range) {
        return mApi.isRobotInlocations(name, range);
    }

    public boolean isInReceptionLocation() {
        return mApi.isInReceptionLocation();
    }

    public double getPlaceDistance(String placeName) {
        return mApi.getPlaceDistance(placeName);
    }

    public List<Pose> getPlaceList() {
        return mApi.getPlaceList();
    }

    public Pose getSpecialPose(String placeName) {
        return mApi.getSpecialPose(placeName);
    }

    public Pose getCurrentPose() {
        return mApi.getCurrentPose();
    }

    public CruiseRouteBean getNaviCruiseRoute() {
        return mApi.getNaviCruiseRoute();
    }

    public String getMapName() {
        return mApi.getMapName();
    }

    public boolean updateRobotStatus(int status) {
        return mApi.updateRobotStatus(status);
    }

    public void getDoorStatus(int reqId, int type, CommandListener listener) {
        mApi.getDoorStatus(reqId, type, listener);
    }

    public void setLockEnable(int reqId, int type, int bord, boolean enable) {
        mApi.setLockEnable(reqId, type, bord, enable);
    }

    public void getRobotStatus(String type, StatusListener listener) {
        mApi.getRobotStatus(type, listener);
    }

    public void getMultiRobotSettingConfig(CommandListener listener) {
        mApi.getMultiRobotSettingConfig(mReqId, listener);
    }

    public int leaveChargingPile(float speed, float distance, CommandListener listener){
        return mApi.leaveChargingPile(mReqId,speed,distance,createCallback(listener));
    }

    public int judgeInChargingPile(float coordinateDeviation, CommandListener listener){
        return mApi.judgeInChargingPile(mReqId,coordinateDeviation,createCallback(listener));
    }

    public int calculateNavigationDistance(List<String> goalsName, CommandListener listener) {
        return mApi.getNaviPathInfoToGoals(mReqId, goalsName, createCallback(listener));
    }

    /**
     * 托盘摄像头 启动单次识别
     * @param listener
     * @return
     */
    public int uvcSingleClassify(CommandListener listener){
        return mApi.uvcSingleClassify(mReqId, listener);
    }

    /**
     * 托盘摄像头 启动持续识别
     * @param listener
     * @return
     */
    public int uvcContinueClassify(CommandListener listener){
        return mApi.uvcContinueClassify(mReqId, listener);
    }

    /**
     * 托盘摄像头 停止持续识别
     * @param listener
     * @return
     */
    public int uvcStopContinueClassify(CommandListener listener) {
        return mApi.uvcStopContinueClassify(mReqId, listener);
    }


    /**
     * Pro底盘的灯带控制
     * @param effect 　参见Definition定义　ZCB2UARTLED_GREENBREATH, ZCB2UARTLED_ALLOFF等
     * @return
     */
    public void setBottomLedEffect(int effect, CommandListener listener) {
        mApi.setBottomLedEffect(mReqId, effect, listener);
    }

    public boolean isUseAutoEffectLed(){
        return mApi.isUseAutoEffectLed();
    }

    /**
     * Pro托盘三层氛围灯的控制
     * @param trayLedEffect must be defined in Definition.
     * like TRAYLEDS_TRAY01ON , TRAYLEDS_TRAY01OFF , TRAYLEDS_TRAY02ON , TRAYLEDS_TRAY02OFF ,
     *  TRAYLEDS_TRAY03ON , TRAYLEDS_TRAY03OFF , TRAYLEDS_ALLON , TRAYLEDS_ALLOFF
     */
    public void setTrayLightEffect(int trayLedEffect , CommandListener listener){
        mApi.setTrayLightEffect(mReqId , trayLedEffect, listener);
    }

    public void setTrayLedEffect(int trayLedEffect , CommandListener listener){
        mApi.setTrayLedEffect(mReqId , trayLedEffect, listener);
    }

    public boolean isHasProTrayLED(){
        return mApi.isHasTrayLight();
    }

    /**
     * 电动门控制
     * @param ctrlCmd must be defined in Definition , only support one of below commands :
     CAN_DOOR_DOOR1_DOOR2_OPEN; CAN_DOOR_DOOR1_DOOR2_CLOSE ;
     CAN_DOOR_DOOR3_DOOR4_OPEN; CAN_DOOR_DOOR3_DOOR4_CLOSE;
     CAN_DOOR_ALL_OPEN; CAN_DOOR_ALL_CLOSE;
     * 发送指令前，先通过mApi.registerStatusListener(STATUS_CAN_ELECTRIC_DOOR_CTRL,listener)
     * 实时获取控制指令的结果状态值
     * @param listener
     * @return
     */
    public void setElectricDoorCtrl(int ctrlCmd , CommandListener listener){
        mApi.setElectricDoorCtrl(mReqId , ctrlCmd ,listener);
    }

    /**
     * 电动门状态查询，　各个门对应的查询结果状态值，defined in Definition:
     * CAN_DOOR_STATUS_OPEN = 51;
     * CAN_DOOR_STATUS_CLOSE = 68;
     * CAN_DOOR_STATUS_TIMEOUT = 85;
     * CAN_DOOR_STATUS_FG2LOW = 238;
     * CAN_DOOR_STATUS_RUNNING = 102;
     * @param listener
     */
    public void getElectricDoorStatus(CommandListener listener){
        mApi.getElectricDoorStatus(mReqId , listener);
    }

    public void startMultiRobotNavigation(String destination, Definition.AdvNaviStrategy advNaviStrategy, List<String> standbyDesList, String navigationParams, ActionListener listener) {
        mApi.startMultiRobotNavigation(mReqId, destination, advNaviStrategy, standbyDesList, navigationParams, createActionCallback("multiRobotNavigation", listener));
    }

    public void startElevatorNavigation(String destination, int floor, ActionListener listener) {
        mApi.startElevatorNavigation(mReqId, destination, floor, createActionCallback("elevatorNavigation", listener));
    }

    public void startElevatorNavigation(String destination, int floor, boolean isAdjustAngle, ActionListener listener) {
        mApi.startElevatorNavigation(mReqId, destination, floor, isAdjustAngle, createActionCallback("elevatorNavigation", listener));
    }

    public void startElevatorNavigation(String destination, int floor, boolean isAdjustAngle, double linearSpeed, double angularSpeed, boolean isReversePoseTheta, ActionListener listener) {
        mApi.startElevatorNavigation(mReqId, destination, floor, isAdjustAngle, linearSpeed, angularSpeed,  isReversePoseTheta, createActionCallback("elevatorNavigation", listener));
    }

    public void stopMultiRobotNavigation() {
        stopAction("multiRobotNavigation");
        mApi.stopAdvanceNavigation(mReqId);
    }

    public void stopElevatorNavigation() {
        stopAction("elevatorNavigation");
        mApi.stopAdvanceNavigation(mReqId);
    }

    public void getGatePassingRoute(String tempGateDestination, CommandListener listener) {
        mApi.getGatePassingRoute(mReqId, tempGateDestination, createCallback(listener));
    }

    public double getPlaceOrPoseDistance(String placeName, Pose destPose) {
        return mApi.getPlaceOrPoseDistance(placeName, (Pose)null, (String)null, destPose);
    }

    public void startGateNavgation(Pose firstPose, Pose secondPose, String destination, double linearSpeed, double angularSpeed, ActionListener listener) {
        mApi.startGateNavgation(mReqId, firstPose, secondPose, destination, linearSpeed, angularSpeed, createActionCallback("gateNavigation", listener));
    }

    private Callback createCallback(CommandListener listener) {
        Callback callback = new Callback(listener);
        synchronized (COMMAND_LOCK) {
            mCommandCallbacks.add(callback);
        }
        return callback;
    }

    private ActionCallback createActionCallback(String name, ActionListener listener) {
        ActionCallback callback = new ActionCallback(name, listener);
//        mActionCallbacks.putIfAbsent(name, callback);
        Log.d(TAG, "Add action callback : " + name + "  " + callback);
        if (mActionCallbacks.containsKey(name)) {
            mActionCallbacks.get(name).add(callback);
        } else {
            List<ActionCallback> list = new CopyOnWriteArrayList<>();
            list.add(callback);
            mActionCallbacks.putIfAbsent(name, list);
        }
        return callback;
    }

    private boolean checkAction() {
        List<String> runningAction = new ArrayList<>();
        for (Entry<String, List<ActionCallback>> entry : mActionCallbacks.entrySet()) {
            for (ActionCallback cb : entry.getValue()) {
                if (!cb.isActionStopped) {
                    runningAction.add(cb.getName());
                }
            }
        }

        Log.d(TAG, "Action not stopped : " + runningAction);
        return runningAction.isEmpty();
    }

    private void stopAction(String name) {
        synchronized (LOCK) {
            if (mActionCallbacks.containsKey(name)) {
                for (ActionCallback cb : mActionCallbacks.get(name)) {
                    cb.stopAction();
                }
            }
        }
    }

    private void onActionFinish(String actionName, ActionCallback callback) {
        synchronized (LOCK) {
            Log.d(TAG, "On action finish : " + actionName + "  " + callback);
            if (mActionCallbacks.containsKey(actionName)) {
                mActionCallbacks.get(actionName).remove(callback);
                if (mActionCallbacks.get(actionName).isEmpty()) {
                    mActionCallbacks.remove(actionName);
                }
            }
            if (mActionCallbacks.isEmpty()) {
                Log.d(TAG, "Action finish complete");
                LOCK.notifyAll();
            }
        }
    }

    private void onCommandFinish(Callback callback) {
        synchronized (COMMAND_LOCK) {
            mCommandCallbacks.remove(callback);
        }
    }

    private class ActionCallback extends Callback {
        private String mName;
        private boolean isActionStopped = false;

        ActionCallback(String actionName, @NonNull ActionListener listener) {
            super(listener);
            mName = actionName;
        }

        @Override
        public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
            onActionFinish(mName, this);
            super.onError(errorCode, errorString, extraData);
        }

        @Override
        public void onResult(int result, String responseString, String extraData) {
            onActionFinish(mName, this);
            super.onResult(result, responseString, extraData);
        }

        void stopAction() {
            this.isActionStopped = true;
        }

        public String getName() {
            return mName;
        }
    }

    private class Callback extends CommandListener {
        protected volatile ActionListener mListener;

        Callback(@NonNull ActionListener listener) {
            this.mListener = listener;
        }

        @Override
        public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
            if (mInvoker.isAlive() && mListener != null) {
                mListener.onError(errorCode, errorString, extraData);
            }
            onCommandFinish(this);
        }

        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            if (mInvoker.isAlive() && mListener != null) {
                try {
                    mListener.onStatusUpdate(status, data, extraData);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }

        @Override
        public void onResult(int result, String responseString, String extraData) {
            if (mInvoker.isAlive() && mListener != null) {
                try {
                    mListener.onResult(result, responseString, extraData);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
            onCommandFinish(this);
        }

        @Override
        public void onError(int errorCode, String errorString) throws RemoteException {
            if (mInvoker.isAlive() && mListener != null) {
                mListener.onError(errorCode, errorString);
            }
            onCommandFinish(this);
        }

        @Override
        public void onStatusUpdate(int status, String data) {
            if (mInvoker.isAlive() && mListener != null) {
                try {
                    mListener.onStatusUpdate(status, data);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }

        @Override
        public void onResult(int result, String responseString) {
            if (mInvoker.isAlive() && mListener != null) {
                try {
                    mListener.onResult(result, responseString);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
            onCommandFinish(this);
        }

        public void stop() {
            this.mListener = null;
        }
    }
}
