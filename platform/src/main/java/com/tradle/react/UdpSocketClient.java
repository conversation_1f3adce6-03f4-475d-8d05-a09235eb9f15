package com.tradle.react;

import static com.tradle.react.UdpSenderTask.OnDataSentListener;

import android.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;

import com.facebook.react.bridge.Promise;

import java.io.IOException;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.MulticastSocket;
import java.net.SocketAddress;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


/**
 * Client class that wraps a sender and a receiver for UDP data.
 */
public final class UdpSocketClient implements UdpReceiverTask.OnDataReceivedListener, OnDataSentListener {
    private static final String TAG = "UdpSocketClient";
    private final OnDataReceivedListener mReceiverListener;
    private final OnRuntimeExceptionListener mExceptionListener;

    private ExecutorService executor = Executors.newSingleThreadExecutor();

    private UdpReceiverTask mReceiverTask;

    private final Map<UdpSenderTask, Promise> mPendingSends;
    private DatagramSocket mSocket;
    private boolean mIsMulticastSocket = false;

    public static final String DATA_TYPE_STRING = "string";
    public static final String DATA_TYPE_HEX = "hex";

    public UdpSocketClient(OnDataReceivedListener receiverListener, OnRuntimeExceptionListener exceptionListener) {
        this.mReceiverListener = receiverListener;
        this.mExceptionListener = exceptionListener;
        this.mPendingSends = new ConcurrentHashMap<>();
    }

    /**
     * Checks to see if client part of a multi-cast group.
     *
     * @return boolean true IF the socket is part of a multi-cast group.
     */
    public boolean isMulticast() {
        return mIsMulticastSocket;
    }

    /**
     * Binds to a specific port or address.  A random port is used if the address is {@code null}.
     *
     * @param port    local port to bind to
     * @param address local address to bind to
     * @throws IOException
     * @throws IllegalArgumentException if the SocketAddress is not supported
     * @throws SocketException          if the socket is already bound or a problem occurs during
     *                                  binding.
     */
    public void bind(Integer port, @Nullable String address) throws IOException {
        if (mSocket != null || mReceiverTask != null) {
            throw new IllegalStateException("Socket is already bound");
        }
        SocketAddress socketAddress;
        if (!TextUtils.isEmpty(address)) {
            socketAddress = new InetSocketAddress(InetAddress.getByName(address), port);
        } else {
            socketAddress = new InetSocketAddress(port);
        }

        mSocket = new MulticastSocket(socketAddress);
        mSocket.setReuseAddress(true);

        // begin listening for data in the background
        mReceiverTask = new UdpReceiverTask(mSocket, this);
        new Thread(mReceiverTask).start();
    }

    /**
     * Adds this socket to the specified multicast group. Rebuilds the receiver task with a
     * MulticastSocket.
     *
     * @param address the multicast group to join
     * @throws UnknownHostException
     * @throws IOException
     * @throws IllegalStateException if socket is not bound.
     */
    public void addMembership(String address) throws UnknownHostException, IOException, IllegalStateException {
        if (null == mSocket || !mSocket.isBound()) {
            throw new IllegalStateException("Socket is not bound.");
        }

        ((MulticastSocket) mSocket).joinGroup(InetAddress.getByName(address));
        mIsMulticastSocket = true;
    }

    /**
     * Removes this socket from the specified multicast group.
     *
     * @param address the multicast group to leave
     * @throws UnknownHostException
     * @throws IOException
     */
    public void dropMembership(String address) throws UnknownHostException, IOException {
        ((MulticastSocket) mSocket).leaveGroup(InetAddress.getByName(address));
        mIsMulticastSocket = false;
    }

    /**
     * Creates a UdpSenderTask, and transmits udp data in the background.
     *
     * @param data byte array housed in a String.
     * @param dType       data type, either "string" or "hex"
     * @param port         destination port
     * @param address      destination address
     * @param promise      callback for results
     * @throws UnknownHostException
     * @throws IOException
     * @throws IllegalStateException if socket is not bound.
     */
    public void send(String data, String dType, Integer port, String address, @Nullable Promise promise)
            throws UnknownHostException, IllegalStateException, IOException {
        if (null == mSocket || !mSocket.isBound()) {
            throw new IllegalStateException("Socket is not bound.");
        }

        byte[] bytes = getBytesFromData(data, dType);
        if(bytes == null) {
            throw new IllegalArgumentException("Data type not supported");
        }
        printByteArray(bytes);

        UdpSenderTask task = new UdpSenderTask(mSocket, this, new InetSocketAddress(InetAddress.getByName(address), port), bytes);

        if (promise != null) {
            synchronized (mPendingSends) {
                mPendingSends.put(task, promise);
            }
        }

        executor.submit(task);
    }

    /**
     * 打印字节数组
     */
    private void printByteArray(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b));
        }
        Log.d(TAG, "Sending message: " + sb.toString());
    }

    /**
     * 把数据转换为字节数组
     * @param data 数据
     * @param dataType 数据类型，支持 string 和 hex
     * @return
     */
    private byte[] getBytesFromData(String data, String dataType) {
        byte[] bytes = null;
        if (dataType.equals(DATA_TYPE_STRING)) {
            bytes = data.getBytes();
        } else if (dataType.equals(DATA_TYPE_HEX)) {
            bytes = hexStringToByteArray(data);
        }
        return bytes;
    }

    /**
     * 将十六进制字符串转换为字节数组的辅助函数
     */
    private byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    }

    /**
     * Sets the socket to enable broadcasts.
     */
    public void setBroadcast(boolean flag) throws SocketException {
        if (mSocket != null) {
            mSocket.setBroadcast(flag);
        }
    }

    /**
     * Shuts down the receiver task, closing the socket.
     */
    public void close() {
        // stop the receiving task
        if (mReceiverTask != null && mReceiverTask.isRunning()) {
            mReceiverTask.terminate();
        }

        // stop pending send tasks
        executor.shutdownNow();

        // close the socket
        if (mSocket != null && !mSocket.isClosed()) {
            mSocket.close();
        }

        mSocket = null;
        mReceiverTask = null;
    }

    /**
     * Retransmits the data back a level, attaching {@code this}
     */
    @Override
    public void didReceiveData(String data, String host, int port) {
        mReceiverListener.didReceiveData(this, data, host, port);
    }

    /**
     * Retransmits the error back a level, attaching {@code this}
     */
    @Override
    public void didReceiveError(String message) {
        mReceiverListener.didReceiveError(this, message);
    }

    /**
     * Retransmits the exception back a level, attaching {@code this}
     */
    @Override
    public void didReceiveRuntimeException(RuntimeException exception) {
        mExceptionListener.didReceiveException(exception);
    }

    /**
     * Transmits success to the javascript layer, if a callback is present.
     */
    @Override
    public void onDataSent(UdpSenderTask task) {
        Promise callback;

        synchronized (mPendingSends) {
            callback = mPendingSends.get(task);
            mPendingSends.remove(task);
        }

        if (callback != null) {
            callback.resolve("send success");
        }
    }

    /**
     * Transmits an error to the javascript layer, if a callback is present.
     */
    @Override
    public void onDataSentError(UdpSenderTask task, String error) {
        Promise promise;

        synchronized (mPendingSends) {
            promise = mPendingSends.get(task);
            mPendingSends.remove(task);
        }

        if (promise != null) {
            promise.reject(UdpErrorCodes.sendError.name(), error);
        }
    }

    /**
     * Retransmits the exception back a level, attaching {@code this}
     */
    @Override
    public void onDataSentRuntimeException(UdpSenderTask task, RuntimeException exception) {
        mExceptionListener.didReceiveException(exception);
        synchronized (mPendingSends) {
            mPendingSends.remove(task);
        }
    }

    /**
     * Callback interface for runtime exceptions.
     */
    public interface OnRuntimeExceptionListener {
        void didReceiveException(RuntimeException exception);
    }

    /**
     * Callback interface data received events.
     */
    public interface OnDataReceivedListener {
        void didReceiveData(UdpSocketClient client, String data, String host, int port);

        void didReceiveError(UdpSocketClient client, String message);
    }
}
