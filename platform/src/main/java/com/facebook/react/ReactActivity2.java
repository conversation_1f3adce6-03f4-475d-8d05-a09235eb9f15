package com.facebook.react;

import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import android.view.KeyEvent;

import com.facebook.react.modules.core.DefaultHardwareBackBtnHandler;
import com.facebook.react.modules.core.PermissionAwareActivity;
import com.facebook.react.modules.core.PermissionListener;

import javax.annotation.Nullable;

/***
 ** <AUTHOR>
 ** @email <EMAIL>
 ** @date 2019-06-14
 **/
public class ReactActivity2 extends AppCompatActivity implements DefaultHardwareBackBtnHandler, PermissionAwareActivity {
    private final ReactActivityDelegate2 mDelegate = this.createReactActivityDelegate();

    protected ReactActivity2() {
    }

    @Nullable
    protected String getMainComponentName() {
        return null;
    }

    protected ReactActivityDelegate2 createReactActivityDelegate() {
        return new ReactActivityDelegate2(this, this.getMainComponentName());
    }

    protected void onReactNativeHostDestory() {
        mDelegate.onUnmountReactRootView();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.mDelegate.onCreate(savedInstanceState);
    }

    protected void onActivityCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    protected void onReactCreate(Bundle saveInstanceState) {
        this.mDelegate.onCreate(saveInstanceState);
    }

    protected void onActivityResume() {
        super.onResume();
    }

    protected void onReactResume() {
        this.mDelegate.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        this.mDelegate.onPause();
    }

    protected void skipReactPause() {
        this.mDelegate.skipReactPause();
    }

    @Override
    protected void onResume() {
        super.onResume();
        this.mDelegate.onResume();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        this.mDelegate.onDestroy();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        this.mDelegate.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        return this.mDelegate.onKeyDown(keyCode, event) || super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        return this.mDelegate.onKeyUp(keyCode, event) || super.onKeyUp(keyCode, event);
    }

    @Override
    public boolean onKeyLongPress(int keyCode, KeyEvent event) {
        return this.mDelegate.onKeyLongPress(keyCode, event) || super.onKeyLongPress(keyCode, event);
    }

    @Override
    public void onBackPressed() {
        if (!this.mDelegate.onBackPressed()) {
            super.onBackPressed();
        }

    }

    @Override
    public void invokeDefaultOnBackPressed() {
        super.onBackPressed();
    }

    @Override
    public void onNewIntent(Intent intent) {
        if (!this.mDelegate.onNewIntent(intent)) {
            super.onNewIntent(intent);
        }

    }

    @Override
    public void requestPermissions(String[] permissions, int requestCode, PermissionListener listener) {
        this.mDelegate.requestPermissions(permissions, requestCode, listener);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        this.mDelegate.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    protected final ReactNativeHost getReactNativeHost() {
        return this.mDelegate.getReactNativeHost();
    }

    protected final ReactInstanceManager getReactInstanceManager() {
        return this.mDelegate.getReactInstanceManager();
    }

    protected final void loadApp(String appKey) {
        this.mDelegate.loadApp(appKey);
    }
}