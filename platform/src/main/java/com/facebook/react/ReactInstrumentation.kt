package com.facebook.react

import android.icu.text.SimpleDateFormat
import android.os.Build
import com.facebook.jni.HybridData
import com.facebook.proguard.annotations.DoNotStrip
import com.facebook.react.bridge.ReactContext
import java.io.File
import java.util.*

class ReactInstrumentation private constructor(context: ReactContext) {

    companion object {
        private var instrumentation: ReactInstrumentation? = null

        fun init(context: ReactContext) {
            if (instrumentation == null) {
                instrumentation = ReactInstrumentation(context)
            }
        }

        fun dumpMemInfo(dir: String) {
            instrumentation?.dumpMemInfo(dir)
        }

    }

    //mHybridData名称不能改，C++层会通过该字段获取C++层对象的地址
    @DoNotStrip
    private var mHybridData: HybridData

    init {
        mHybridData = initHybrid(context.javaScriptContextHolder.get())
    }

    fun dumpMemInfo(dir: String) {
        val dirFile = File(dir)
        if (!dirFile.exists()) {
            dirFile.mkdirs()
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            val fileName = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault()).format(Date())
            createSnapshotToFile(File(dir, "$fileName.heapsnapshot").absolutePath)
        }
    }

    private external fun initHybrid(jsContext: Long): HybridData

    private external fun createSnapshotToFile(path: String)

}