#include "ReactInstrumentation.h"

#include <jni.h>
#include <jsi/jsi.h>
#include <jsi/instrumentation.h>
#include <fbjni/fbjni.h>
#include <string>

using namespace facebook;

using TSelf = jni::local_ref<ReactInstrumentation::jhybriddata>;

TSelf ReactInstrumentation::initHybrid(
    jni::alias_ref<HybridClass::jhybridobject> jThis, jlong jsContext) {
    return makeCxxInstance(jThis, (jsi::Runtime *) jsContext);
}

void ReactInstrumentation::registerNatives() {
    registerHybrid({
        makeNativeMethod("initHybrid", ReactInstrumentation::initHybrid),
        makeNativeMethod("createSnapshotToFile", ReactInstrumentation::createSnapshotToFile)
    });
}

void ReactInstrumentation::createSnapshotToFile(std::string path) {
    __android_log_print(ANDROID_LOG_DEBUG,
        TAG,
        "React native create snapshot to file : %s",
        path.c_str());
    runtime_->instrumentation().createSnapshotToFile(path);
}