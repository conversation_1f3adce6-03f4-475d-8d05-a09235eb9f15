#pragma once

#include <jni.h>
#include <fbjni/fbjni.h>
#include <jsi/jsi.h>
#include <string>

using namespace facebook;

/**
 * React
 */
class ReactInstrumentation
    : public jni::HybridClass<ReactInstrumentation> {
  public:
    static auto constexpr
        kJavaDescriptor = "Lcom/facebook/react/ReactInstrumentation;";
    static auto constexpr
        TAG = "ReactInstrumentation";

    static jni::local_ref<jhybriddata> initHybrid(jni::alias_ref<jhybridobject> jthis,
        jlong jsContext);

    static void registerNatives();
    void createSnapshotToFile(std::string path);

  private:
    friend HybridBase;
    jni::global_ref<ReactInstrumentation::javaobject> javaPart_;
    jsi::Runtime *runtime_;

    explicit ReactInstrumentation(
        jni::alias_ref<ReactInstrumentation::jhybridobject> jthis, jsi::Runtime *rt) :
        javaPart_(jni::make_global(jthis)), runtime_(rt) {

    }
};