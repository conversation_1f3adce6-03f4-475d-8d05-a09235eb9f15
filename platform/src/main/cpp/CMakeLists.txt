cmake_minimum_required(VERSION 3.18.1)

project("RobotPlatform")

set(BUILD_DIR ${CMAKE_SOURCE_DIR}/../../../build)
set(PACKAGE_NAME "PlatformJSI")

add_library(
        ${PACKAGE_NAME}
        SHARED
        OnLoad.cpp
        ReactInstrumentation.cpp
)

#fbjni头文件目录
file(GLOB LIBFBJIN_HEADERS_DIR "${BUILD_DIR}/fbjni-*-headers*")

find_package(ReactAndroid REQUIRED CONFIG)

#添加头文件搜索路径
target_include_directories(
        ${PACKAGE_NAME}
        PRIVATE
        ${LIBFBJIN_HEADERS_DIR}
)

#RN框架so库目录
file(GLOB LIBRN_DIR "${BUILD_DIR}/orionos-react-native-*/jni/${ANDROID_ABI}")

#搜索fbjni库
find_library(
        FBJNI_LIB
        fbjni
        PATHS ${LIBRN_DIR}
        NO_CMAKE_FIND_ROOT_PATH
)

find_library(
        LOG_LIB
        log
)

target_link_libraries(${PACKAGE_NAME} ${FBJNI_LIB} ${LOG_LIB} ReactAndroid::jsi)