// 全局定义关键变量和函数
const linkList = [];
let isDomProcessed = false;
let isProcessing = false;

// 添加日志函数
function logInfo(message, data = null) {
  const logMessage = data ? `[WebViewJS] ${message}: ${JSON.stringify(data)}` : `[WebViewJS] ${message}`;
  console.log(logMessage);
  // 可选：同时向Android发送日志
  if (typeof Android !== 'undefined' && Android.onMessage) {
    Android.onMessage(`log:${logMessage}`);
  }
}

// 统一收集可点击元素 - 增强版
function collectClickableElements() {
  logInfo('开始收集可点击元素');
  linkList.length = 0; // 清空旧数据
  isProcessing = true;

  // 更全面的可点击元素选择器
  const selector = [
    'a[href]',
    'button',
    '[data-url]',
    '[onclick]',
    '[role="button"]',
    '[role="link"]',
    '[role="tab"]',
    '[role="menuitem"]',
    'input[type="button"]',
    'input[type="submit"]',
    '.btn',
    '.button',
    '[class*="btn"]',
    '[class*="button"]',
    '[tabindex]:not([tabindex="-1"])'
  ].join(',');

  logInfo(`使用选择器: ${selector}`);
  const startTime = performance.now();

  try {
    document.querySelectorAll(selector).forEach((element, idx) => {
      // 收集具有真实交互能力的元素
      if (isElementClickable(element)) {
        // 提取文本内容，去除空白字符
        let text = element.textContent.trim().replace(/[\n\r\t\s]+/g, ' ').trim();

        // 如果元素没有文本但有alt或title或aria-label属性，使用这些作为替代文本
        if (!text) {
          text = element.getAttribute('alt') ||
                element.getAttribute('title') ||
                element.getAttribute('aria-label') ||
                element.getAttribute('name') ||
                element.getAttribute('placeholder') ||
                element.getAttribute('value') ||
                `[元素 ${idx}]`; // 提供默认文本
        }

        if (text) {
          linkList.push({
            element,
            text,
            index: linkList.length
          });
        }
      }
    });
  } catch (error) {
    logInfo(`收集元素时出错: ${error.message}`, { stack: error.stack });
    Android.onMessage(`error:收集元素时出错: ${error.message}`);
  }

  const endTime = performance.now();
  logInfo(`收集完成，找到 ${linkList.length} 个可点击元素`, {
    耗时: `${(endTime - startTime).toFixed(2)}ms`,
    第一个元素: linkList.length > 0 ? linkList[0].text : '无'
  });

  // 网站特殊处理逻辑
  specialSiteHandling();

  isProcessing = false;
  isDomProcessed = true;
}

// 判断元素是否可点击 - 增强版
function isElementClickable(element) {
  try {
    // 获取计算样式
    const style = window.getComputedStyle(element);

    // 检查元素和所有父元素的可见性
    let current = element;
    while (current && current !== document.body) {
      const currentStyle = window.getComputedStyle(current);
      if (currentStyle.display === 'none' ||
          currentStyle.visibility === 'hidden' ||
          currentStyle.opacity === '0' ||
          parseFloat(currentStyle.opacity) === 0) {
        return false;
      }
      current = current.parentElement;
    }

    // 检查元素尺寸 - 太小的元素可能不是真正可交互的
    const rect = element.getBoundingClientRect();
    if (rect.width < 5 || rect.height < 5) {
      return false;
    }

    // 检查元素是否被禁用
    if (element.disabled || element.getAttribute('aria-disabled') === 'true') {
      return false;
    }

    // 检查是否有点击事件监听器
    const hasClickHandler = typeof element.onclick === 'function' ||
                           element.getAttribute('onclick') ||
                           element.tagName === 'A' ||
                           element.tagName === 'BUTTON';

    return true;
  } catch (error) {
    logInfo(`检查元素可点击性时出错: ${error.message}`);
    return false;
  }
}

// 网站特殊处理
function specialSiteHandling() {
  // 地图特殊处理
  if (window.location.hostname === 'test-agentpoi.orionstar.com' ||
      window.location.hostname.includes('map') ||
      window.location.hostname.includes('amap')) {
    logInfo('检测到地图网站，添加特殊处理');

    // 百度地图
    ['#route-go', '#route-from', '.BMap_bubble_title', '.BMap_bubble_content a'].forEach(selector => {
      try {
        document.querySelectorAll(selector).forEach(element => {
          if (element && isElementClickable(element)) {
            logInfo(`找到地图特殊元素: ${selector}, 文本: ${element.textContent.trim()}`);
            linkList.push({
              element,
              text: element.textContent.trim() || `[地图元素 ${linkList.length}]`,
              index: linkList.length
            });
          }
        });
      } catch (e) {
        logInfo(`处理特殊选择器 ${selector} 时出错: ${e.message}`);
      }
    });
  }
}

// Native消息处理 - 增强版
window.receiveMessage = (message) => {
  logInfo(`收到消息: ${message}`);

  try {
    // 如果正在处理DOM，等待处理完成
    if (isProcessing) {
      logInfo('DOM正在处理中，稍后再试');
      Android.onMessage(`error:DOM正在处理中，请稍后再试`);
      return;
    }

    // 如果DOM尚未处理，先处理DOM
    if (!isDomProcessed) {
      logInfo('DOM尚未处理，开始处理');
      collectClickableElements();
    }

    // 解析消息
    const data = JSON.parse(message);
    logInfo('解析消息', data);

    if (data.type === 'click') {
      // 检查索引是否有效
      if (typeof data.index !== 'number') {
        const error = `无效的索引值: ${data.index}`;
        logInfo(error);
        Android.onMessage(`error:${error}`);
        return;
      }

      // 检查可点击元素列表
      if (linkList.length === 0) {
        const error = '可点击元素列表为空';
        logInfo(error);
        Android.onMessage(`error:${error}`);
        return;
      }

      // 检查索引是否在范围内
      if (data.index < 0 || data.index >= linkList.length) {
        const error = `索引 ${data.index} 超出范围，可点击元素总数: ${linkList.length}`;
        logInfo(error);
        Android.onMessage(`error:${error}`);
        return;
      }

      // 执行点击
      const targetElement = linkList[data.index].element;
      const targetText = linkList[data.index].text;

      logInfo(`执行点击操作，索引: ${data.index}, 文本: ${targetText}`);

      try {
        // 尝试模拟更真实的点击
        const clickEvent = new MouseEvent('click', {
          view: window,
          bubbles: true,
          cancelable: true
        });

        targetElement.dispatchEvent(clickEvent);

        // 如果是链接且没有阻止默认行为，尝试直接跳转
        if (targetElement.tagName === 'A' && targetElement.href && !clickEvent.defaultPrevented) {
          setTimeout(() => {
            if (window.location.href !== targetElement.href) {
              logInfo(`链接点击可能未生效，尝试直接导航到: ${targetElement.href}`);
              window.location.href = targetElement.href;
            }
          }, 300);
        }

        // 报告成功
        Android.onMessage(`click_success:${data.index}`);

        // 点击后，设置一个延迟重新收集元素，因为DOM可能会变化
        setTimeout(() => {
          collectClickableElements();
        }, 1000);
      } catch (clickError) {
        logInfo(`点击执行错误: ${clickError.message}`, { stack: clickError.stack });
        Android.onMessage(`error:点击执行错误: ${clickError.message}`);
      }
    } else if (data.type === 'refresh') {
      // 刷新可点击元素列表
      logInfo('刷新可点击元素列表');
      collectClickableElements();
      Android.onMessage(`refresh_success:找到 ${linkList.length} 个可点击元素`);
    } else if (data.type === 'getElements') {
      // 获取可点击元素列表
      logInfo('获取可点击元素列表');
      const elements = linkList.map((item, idx) => ({
        index: idx,
        text: item.text
      }));
      Android.onMessage(`elements:${JSON.stringify(elements)}`);
    }
  } catch (error) {
    logInfo(`消息处理错误: ${error.message}`, { stack: error.stack });
    Android.onMessage(`error:${error.message}`);
  }
};

// 初始化逻辑 - 保持不变
function initDomObserver() {
  // ... 保持原有代码不变 ...
}

// 启动初始化
logInfo('WebViewJS 脚本开始执行');
initDomObserver();
logInfo('WebViewJS 初始化完成');