// 定义 receiveMessage 函数在全局作用域
function receiveMessage(data) {
    const linkList = [];

    const links = document.querySelectorAll('*');
                links.forEach(link => {
                    const style = window.getComputedStyle(link);
                    if (link.hasAttribute('data-url') || link.tagName === 'A') {
                        const textContent = link.textContent.trim();
                        if (textContent) {
                            // 将可点击标签存储到数组中
                            linkList.push(link);
                        }
                    }
                });
                if (window.location.hostname === 'map.baidu.com') {
                    const elements = document.querySelectorAll('#route-go, #route-from');
                    elements.forEach(element => {
                        console.log('Found element:', element);
                        linkList.push(element);
                    });
                }

    function clickLinkByText(searchText) {
        const link = linkList.find(link => link.textContent.trim() === searchText);
        if (link) {
            link.click();
        }
    }

    function clickLinkByIndex(index) {
        console.info('clickLinkByIndex:'+index);
        if (index >= 0 && index < linkList.length) {
            const link = linkList[index];
            link.click();
        }
    }

    try {
        let msgData = JSON.parse(data); // 从 data 中解析数据
        if (msgData.type == 'click') {
            clickLinkByIndex(msgData.index);
        }
    } catch (e) {
        console.error('Failed to parse message data:', e);
    }
}

// 自执行函数
(function() {
    function traverseDom(element) {
        let result = '';

        // 跳过 <style> 和 <script> 标签
        if (element.tagName === 'STYLE' || element.tagName === 'SCRIPT' || element.tagName === 'NOSCRIPT') {
            return ''; // 直接返回，跳过这些标签及其内容
        }

        // 检查元素的可见性
        const style = getComputedStyle(element);
        const isVisible = style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';

        // 如果元素不可见，则直接返回空字符串
        if (!isVisible) {
            return '';
        }

        // 用于跟踪子内容
        let childContent = '';

        // 遍历子节点
        element.childNodes.forEach(child => {
            if (child.nodeType === Node.TEXT_NODE) {
                const textContent = child.textContent.trim();
                if (textContent) {
                    // 截断文本内容
                    childContent += textContent.length > 50 ? textContent.substring(0, 50) + '...' : textContent;
                }
            } else if (child.nodeType === Node.ELEMENT_NODE) {
                const childResult = traverseDom(child);
                if (childResult) { // 确保递归结果不为空
                    childContent += childResult;
                }
            }
        });

        // 只有在有子内容时才拼接开始标签和结束标签
        if (childContent) {
            if (element.nodeType === Node.ELEMENT_NODE) {
                if (element.tagName === 'DIV' && element.childNodes.length === 1 && element.childNodes[0].nodeType === Node.ELEMENT_NODE && element.childNodes[0].tagName === 'DIV') {
                    // 不拼接开始标签
                } else {
                    result += `<${element.tagName.charAt(0).toLowerCase()}>`; // 拼接开始标签
                }
            }

            result += childContent; // 拼接子内容

            if (element.nodeType === Node.ELEMENT_NODE) {
                if (!(element.tagName === 'DIV' && element.childNodes.length === 1 && element.childNodes[0].nodeType === Node.ELEMENT_NODE && element.childNodes[0].tagName === 'DIV')) {
                    result += `</${element.tagName.charAt(0).toLowerCase()}>`; // 拼接结束标签
                }
            }
        }

        return result; // 返回拼接好的字符串
    }

    setTimeout(() => {
        // 调用并输出最终结果
        const htmlStructure = traverseDom(document.body);
        console.log(htmlStructure);

        // 使用 postMessage 将结果传回 React Native
        Android.onMessage('dom:' + htmlStructure);

        const links = document.querySelectorAll('*');
            links.forEach(link => {
                const style = window.getComputedStyle(link);
                if (link.hasAttribute('data-url') || link.tagName === 'A') {
                    const textContent = link.textContent.trim();
                    if (textContent) {
                        // 将可点击标签存储到数组中
                        linkList.push(link);
                    }
                }
            });
            if (window.location.hostname === 'map.baidu.com') {
                const elements = document.querySelectorAll('#route-go, #route-from');
                elements.forEach(element => {
                    console.log('Found element:', element);
                    linkList.push(element);
                });
            }

        Android.onMessage('anchor:' + convertListToString());
        Android.onExecuteFinish();
    }, 3000);

    const linkList = [];



    function clickLinkByText(searchText) {
        const link = linkList.find(link => link.textContent.trim() === searchText);
        if (link) {
            link.click();
        }
    }

    function clickLinkByIndex(index) {
        if (index >= 0 && index < linkList.length) {
            const link = linkList[index];
            link.click();
        }
    }

    // 将 list 转换为字符串
    function convertListToString() {
        const array = linkList.map((link, index) => {
            return `<a id="${index}">${link.textContent.trim()}</a>`;
        });
        return JSON.stringify(array);
    }

})();
