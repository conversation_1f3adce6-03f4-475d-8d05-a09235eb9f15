// IMsgRegistry.aidl
package com.ainirobot.platform.rn;

// Declare any non-default types here with import statements

import com.ainirobot.platform.rn.listener.IRNMsgListener;

interface IMsgRegistry {
    String getMessage(int type, String appId, String appPath);
    String getMessageContent(String msgId);
    String getDetail(String detailId);
    void deleteDetail(String detailId);
    void increaseDetailPlayTimes(String detailId);
    void registerMsgChanged(int callbackId, int type, String appId, String appPath, IRNMsgListener listener);
    void unregisterMsgChanged(int callbackId);
}
