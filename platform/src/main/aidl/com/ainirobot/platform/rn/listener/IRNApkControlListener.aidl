// INLPApkControlListener.aidl
package com.ainirobot.platform.rn.listener;

// Declare any non-default types here with import statements

interface IRNApkControlListener {
    int getCallbackId();
    void onPageStateChanged(String apkPackage, String state);
    void onTriggerCommand(String apkPackage, String command);
    void onProcessDied(String apkPackagee);
    void onTopActivityChanged(String apkPackage, String activityName);
    void onAppNotResponding(String apkPackage);
    void onProcessVisible(String apkPackage);
    void onProcessInVisible(String apkPackage);
    void onServiceConnected(String apkPackage);
    void onServiceDisconnected(String apkPackage);
    void onRobotMessengerReady(String apkPackage);
    void onApkInstallResult(String apkPackage, boolean success, String errorMsg, String taskId);
}
