// IApiRegistry.aidl
package com.ainirobot.platform.rn;

import com.ainirobot.platform.rn.listener.IRNFinishListener;
import com.ainirobot.platform.rn.listener.IRNStatusListener;

interface IComponentRegistry {
    void start(String componentName,String uid,String param);
    void stop(String componentName, String uid,long timeout);
    void setStatusListener(String componentName,String uid,IRNStatusListener statusListener);
    void setFinishListener(String componentName,String uid,IRNFinishListener finishListener);
    void updateParams(String componentName, String uid,String intent,String param );
    void onCatalystInstanceDestroy(String componentName);
}
