// IApiRegistry.aidl
package com.ainirobot.platform.rn;

import com.ainirobot.platform.rn.listener.IRNApiListener;
import com.ainirobot.platform.rn.listener.IRNEmergencyListener;
import com.ainirobot.platform.rn.listener.IRNFunctionKeyListener;
import com.ainirobot.platform.rn.listener.IRNRobotStatusListener;
import com.ainirobot.platform.rn.listener.IRNAtomizerStatusListener;
import com.ainirobot.platform.rn.listener.IRNRemoteStandByListener;
import com.ainirobot.platform.rn.listener.IRNCollideStatusListener;

interface IApiRegistry {

    void disableEmergency();

    void startTakeOverEmergency(int callbackId, IRNEmergencyListener listener);

    void stopTakeOverEmergency(int callbackId);

    void startTakeOverFunctionKey(int callbackId, IRNFunctionKeyListener listener);

    void stopTakeOverFunctionKey(int callbackId);

    void disableBattery();

    int getPlaceList(IRNApiListener listener);

    int getPlaceListWithName(IRNApiListener listener);

    int getPlace(String param, IRNApiListener listener);

    int isRobotInlocations(String param, IRNApiListener listener);

    int isRobotEstimate(IRNApiListener listener);

    int resetHead(IRNApiListener listener);

    int switchCamera(String mode, IRNApiListener listener);

    int turnHead(String headTurnBean, IRNApiListener listener);

    int stopTurnHead(IRNApiListener listener);

    int getHeadStatus(IRNApiListener listener);

    int textToMp3(String text, String fullFile, String fileName, IRNApiListener listener);

    int stopChargingByApp();

    int canRobotReboot(IRNApiListener listener);

    int getMapName(IRNApiListener listener);

    int getPosition(IRNApiListener listener);

    int remoteWakeUpTimes(IRNApiListener listener);

    int getBatteryTimeRemaining(IRNApiListener listener);

    int getChargeTimeRemaining(IRNApiListener listener);

    int getAskWayList(IRNApiListener listener);

    int startVision(IRNApiListener listener);

    int stopVision(IRNApiListener listener);

    int startBackupVision(IRNApiListener listener);

    int stopBackupVision(IRNApiListener listener);

    int skillDataReport(String param, IRNApiListener listener);

    int setLambColor(int target, int color);

    int setLambAnimation(int target, int start, int end, int startTime, int endTime,
                                    int repeat, int onTime, int freeze);

    boolean isLara();

    boolean isMeissa();

    boolean isMini();

    boolean isSaiphDp();

    int getRobotSn(IRNApiListener listener);

    int robotReboot(String reason);

    int playTrick(String param, IRNApiListener listener);

    int stopPlayTrick();

    int getCanRotateSupport(IRNApiListener listener);

    int turnLeft(float speed, float angle, IRNApiListener listener);

    int turnRight(float speed, IRNApiListener listener);

    int turnRight2(float speed, float angle, IRNApiListener listener);

    int stopMove(IRNApiListener listener);

    void sendStatusReport(String type, String data);

    int getLocation(String param, IRNApiListener listener);

    boolean isActive();

    int goForward(float speed, float distance, IRNApiListener listener);

    int goBackward(float speed, float distance, IRNApiListener listener);

    int rotate(float speed, IRNApiListener listener);

    int startVelocityReport(IRNApiListener listener);

    int stopVelocityReport(IRNApiListener listener);

    int setMaxAcceleration(float xAcc, float yAcc, float zAcc, IRNApiListener listener);

    int motionLine(String direction, float speed, float distance, IRNApiListener listener);

    int motionArc(float distance, float angle, float angularSpeed, float latency, IRNApiListener listener);

    int motionArcWithObstacles(float lineSpeed, float angularSpeed, IRNApiListener listener);

    int getPlaceListByMapName(String mapName, IRNApiListener listener);

    int getPlaceListWithNameList(String param, IRNApiListener listener);

    int goPosition(String position, String velocity, IRNApiListener listener);

    int goPosition2(String position, float linearSpeed, float angularSpeed, IRNApiListener listener);

    int stopGoPosition();

    int getNavigationLineSpeed(IRNApiListener listener);

    int getNavigationAngleSpeed(IRNApiListener listener);

    int moveHead(String hmode, String vmode, int hangle, int vangle, int hMaxSpeed, int vMaxSpeed, IRNApiListener listener);

    int isHeaderConnected(IRNApiListener listener);

    int getChargeStatus(IRNApiListener listener);

    int setStartChargePoseAction(long timeout, IRNApiListener listener);

    int startNaviToAutoChargeAction(long timeout, double distance, long avoidTime, IRNApiListener listener);

    int stopAutoChargeAction(boolean isResetHW);

    int getEmergencyStatus(IRNApiListener listener);

    int startInspection(long time, boolean isReInspection, IRNApiListener listener);

    int stopInspection(boolean isResetHW);

    int resumeSpecialPlaceTheta(String placeName, IRNApiListener listener);

    int stopResumeSpecialPlaceThetaAction();

    int remoteRequestQrcode(IRNApiListener listener);

    int checkIfHasObstacle(double startAngle, double endAngle, double distance, IRNApiListener listener);

    int getBatteryLevel();

    boolean rnHeartBeat();

    int getPersonInfoFromNet(String personId, in List<String> pictures, IRNApiListener listener);

    int getHeadCount(IRNApiListener listener);

    int updatePictureReportConfig(String config, IRNApiListener listener);

    int capScreen(int type, IRNApiListener listener);

    int getCpuTemperature(IRNApiListener listener);

    boolean updateRobotStatus(int status);

    void setLanguage(String language);

    int getLanguageList(IRNApiListener listener);

    void getDoorStatus(int type, IRNApiListener listener);

    void setLockEnable(int type, int bord, boolean enable);

    void scoreRecordingTask(String params,IRNApiListener listener);

    int getVisionResolution(IRNApiListener listener);

    void answerAppResult(String param,IRNApiListener listener);

    void getUserListWithVideo(String params,IRNApiListener listener);

    void inviteCallApp(String params,IRNApiListener listener);

    boolean updateStandbyStatus(boolean standbyStart, String jsonData);

    void getDefWelcomeTTS(IRNApiListener listener);

    int taskModeReport(String taskId, String taskMode, int taskResult, IRNApiListener listener);

    void enableBattery();

    void registerStatusListener(String type, IRNRobotStatusListener listener);

    void unregisterStatusListener(IRNRobotStatusListener listener);

    int execTaskReport(String taskId, String taskType, String execResult, String execData, IRNApiListener listener);

    int moduleCodeConfigUpload(String moduleCode, String description, String configName, String configJson, IRNApiListener listener);

    void takePicture(int type,IRNApiListener listener);

    void uploadCruisePicture(String params, IRNApiListener listener);

    String reportTask(int taskMode, String taskType, String name, String subTaskList);

    void reportTaskEvent(String taskEvent);

    String getCurrentTaskId();

    int taskCommandReport(String cmdId, String cmdType, String execResult, String execData, IRNApiListener listener);

    int taskGroupQueue(String allowTaskType, String taskGroupTypeList, IRNApiListener listener);
    int taskCreate(String parentTaskId, String taskType, String taskList, IRNApiListener listener);
    int taskTake(String taskId, IRNApiListener listener);
    int taskManual(String taskId, IRNApiListener listener);
    int taskCancel(String taskId, String cancelResult, IRNApiListener listener);
    int taskPosQrcode( String qrcodeType, String posName, IRNApiListener listener);

    int getSpecialDishLabels(IRNApiListener listener);
    int shutdown(String reason);
    int getAppToken(String app_type, String app_name, String app_version,IRNApiListener listener);
    int taskSucc(String taskId, IRNApiListener listener);
    int getNaviPathInfo(String startPoseJson, String endPoseJson, IRNApiListener listener);
    int getNaviPathInfoToGoal(in List<String> golaNameList, IRNApiListener listener);
    int getNavigationConfig(IRNApiListener listener);
    int floorPositionList(String floorId,String posName,String padZcbPlateBackTableTaskStatus, IRNApiListener listener);
    int setXDPowderEnable(boolean enable);
    int setXDFanEnable(boolean enable);
    int setXDRank(int rank);
    void monitorDryStatus(int callbackId, IRNAtomizerStatusListener listener);
    void unMonitorDryStatus(int callbackId);
    int goCharging();
    int getMultiFloorConfigAndPose(IRNApiListener listener);
    int getMultiFloorConfigAndCommonPose(IRNApiListener listener);
    int getNaviAngSpeed(String speed,IRNApiListener listener);

    String getLocalAndServerSupportLanguageList();
    void robotStandby();
    void robotStandByEnd();
    int queryRadarStatus(IRNApiListener listener);
    void monitorRemoteStandBy(int callbackId, IRNRemoteStandByListener listener);
    void unMonitorRemoteStandBy(int callbackId);
    int getScreenBrightness();
    boolean setScreenBrightness(int brightness);
    int updateRadarStatus(boolean openRadar,IRNApiListener listener);
    int queryUvcCameraConnectedStatus(IRNApiListener listener);
    void monitorCollideStatus(int callbackId, IRNCollideStatusListener listener);
    void unMonitorCollideStatus(int callbackId);
    void proactiveProblemReport(double occurrenceTime, String issueType, String packageName, String pageName, String description);
    void setElectricDoorCtrl(int ctrlCmd,IRNApiListener listener);
    void getElectricDoorStatus(IRNApiListener listener);
    boolean isSupportElectricDoor();
    void setQueryFeedback(String chatMaxSid, String feedbackResult);
    void registerElectricDoorStateListener(int callbackId, IRNRobotStatusListener listener);
    void unRegisterElectricDoorStateListener(int callbackId);
    boolean isSupportElevator();
    boolean isSupportKKCamera();
    void switchBigScreenMode(String mode);
    void getMotionDistance(IRNApiListener listener);

    boolean isWaiterProCarry();
    int restartRNProcess();

    void reportBackgroundTaskEvent(String taskEvent);
    int cabinetPutGoodsStatus(String taskId, String stationType, String stationId, String stationOrderId, IRNApiListener listener);
    void deliveryRobotTask(String paramJson,IRNApiListener listener);
    void deliveryRobotStatusNotify(String paramJson,IRNApiListener listener);
    void deliveryMonitorDetail(String paramJson,IRNApiListener listener);
    void deliveryRobotComment(String paramJson,IRNApiListener listener);
    void deliveryRemoteCall(String orderInfo,IRNApiListener listener);
    void deliveryReport(String orderInfo,IRNApiListener listener);
    void pauseNavigation(boolean isPause,IRNApiListener listener);
    void switchMap(String mapName,IRNApiListener listener);
    void setFixedEstimate(String param,IRNApiListener listener);
    void installApk(String fullPathName, String taskID);
    String reportRobotCurrentTask(int taskMode, String taskType, String name, String taskId, String subTaskList);
    void startRadarAlign(String poseName, IRNApiListener listener);
    void stopRadarAlign();
    void startRadarAlign2(String poseName, long startAlignTimeout, long retryDelayTime, long navigationTimeout, IRNApiListener listener);
    int getPlacesByType(int typeId, IRNApiListener listener);

    boolean hasHeightLimitCamera();

    //带避停参数的前进接口，avoid为true时，遇到障碍物会避停
    int goForwardWithAvoid(float speed, float distance, boolean avoid, IRNApiListener listener);
    void startNavigationFollow(String followId, int lostFindTimeout, IRNApiListener listener);
    void stopNavigationFollow();
    void detectQrCode(String file, IRNApiListener listener);
    void disableOutsideMapAlarm();
    void enableOutsideMapAlarm();

    void getElevatorStatus(IRNApiListener listener);

    String getLineSpeed();
    void setLineSpeed(String speed, IRNApiListener listener);

    //按角度转动，基础方法，大角度精度优化版本
    int rotateInPlace(int direction, float speed, float angle, IRNApiListener listener);
}
