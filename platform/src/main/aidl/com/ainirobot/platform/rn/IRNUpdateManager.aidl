/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.rn;

import com.ainirobot.platform.rn.listener.IRNUpdateListener;

interface IRNUpdateManager {

    //是否需要更新预置OPK
    boolean isNeedUpdatePresetApp();

    //是否正在进行初始安装
    boolean isInitInstall();

    boolean isUpdating();

    boolean checkUpdate();

    oneway void setUpdateListener(IRNUpdateListener listener);

    oneway void retryUpdate();

    oneway void startUpdate();

    oneway void cancelUpdate();

    oneway void setPresetAppListener(IRNUpdateListener listener);

    //设置是否允许更新
    oneway void setUpdateEnable(boolean enable);

    oneway void setAppConfig(String appId,String key,String value);
}
