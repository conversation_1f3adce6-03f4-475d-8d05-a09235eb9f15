// INLPApkControlRegistry.aidl
package com.ainirobot.platform.rn;
import com.ainirobot.platform.rn.listener.IRNApkControlListener;
// Declare any non-default types here with import statements

interface IRNApkControlRegistry {
   void addListener(String apkPackage, IRNApkControlListener listener);
   void removeListener(String apkPackage);
   void removeAll();
   void forceStopPackage(String apkPackage);
   void setApplicationState(String apkPackage,boolean isEnable);
   int onRobotMessage(String apkPackage,String messsage);
   void grantRuntimePermission(String apkPackage,String permission);
   void revokeRuntimePermission(String apkPackage,String permission);
}
