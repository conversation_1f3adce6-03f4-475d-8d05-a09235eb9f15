// ILightRegistry.aidl
package com.ainirobot.platform.rn;

// Declare any non-default types here with import statements

interface ILightRegistry {

    int playEffect(String effect);

    int playColor(String color);

    int playMultipleColor(String color);

    int playAnimation(String animation);

    int playNavigationAnimation(String animation);

    int playDoorAnimation(String animation);

    int setSceneLedEffect(String effect);
}
