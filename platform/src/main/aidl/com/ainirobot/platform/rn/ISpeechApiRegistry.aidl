// ISpeechApiRegistry.aidl
package com.ainirobot.platform.rn;

import com.ainirobot.platform.rn.listener.IRNTextListener;
import com.ainirobot.platform.rn.listener.IRNToneListener;
import com.ainirobot.platform.rn.listener.IRNSkillServerCheckListener;

interface ISpeechApiRegistry {

    void playText(String text, IRNTextListener listener);

    void playTextWithParams(String text, String ttsParams, IRNTextListener listener);

    void stopTTS();

    void stopTone();

    void cancelAudioOperation();

    void setRecognizeMode(boolean isContinue);

    void setRecognizable(boolean enable);

    void queryByText(String text);

    void setAngleCenterRange(float centerAngle, float rangeAngle);

    int setCustomizeWakeUpWord(String wakeUpWordChinese, String wakeUpWordPinYin, String separator);

    int closeCustomizeWakeUpWord();

    int getPinYinScore(String pinyin, String separator);

    String queryPinYinFromChinese(String chineseWord);

    String queryPinYinMappingTable(String pinyin);

    String queryUserSetWakeUpWord();

    void setSkillServerCheckCallBack(IRNSkillServerCheckListener listener);

    void unRegisterServerCheck();

    void playTone(String type, IRNToneListener listener);

    void resetAngleCenterRange();

    void playToneByLocalPath(String localPath, IRNToneListener listener);

    void setTTSParams(String ttsType, int value);

    void setLangRec(String autoLangJson);

    void setASRParams(String asrType, String value);

    boolean setAsrExtendProperty(String propertyJson);

    void startApp(String appId);

    void moveToForeground(String appId);

    void moveToBack(String appId);

    void destroyApp(String appId);

    void setAppPath(String appId, String path);

    void sendAgentMessage(String type,int code,String message);

    void setAppVersion(String appId, String version);

    void setSyncCustomNlpData(in Map map);

    String setAsyncCustomNlpData(String opt, String data);

    void resetNlpState();

    void setServerApp(in List<String> appList);

    void setNLPDebug(boolean value);

    void downloadTtsAudio(String ttsEntitiesJson);

    String getSpokemanListByLanguage(String lang);

    void closeStreamDataReceived(String paramJson);

    boolean isRecognizeContinue();

    boolean isRecognizable();

    void setRecognizeModeForce(boolean isContinue);

    void setRecognizeModeNew(boolean isContinue, boolean isCloseStreamData);

    void playStreamText(String streamSid, String textSid, String text, IRNTextListener listener);

    void onAgentActionFinish(String action, int code, String message);

    void onAgentActionState(String action, int state, String data);

    void queryByTextWithThinking(String text, boolean isShowThinking);
}
