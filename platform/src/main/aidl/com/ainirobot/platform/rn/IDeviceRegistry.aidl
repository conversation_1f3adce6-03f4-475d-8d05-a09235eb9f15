// IDeviceRegistry.aidl
package com.ainirobot.platform.rn;

import com.ainirobot.platform.rn.listener.IRNConnectivityListener;
import com.ainirobot.platform.rn.listener.IRNSurfaceShareListener;
import com.ainirobot.platform.rn.listener.IRNVolumeListener;

interface IDeviceRegistry {

    void startBluetoothDetect(String type);

    String getWakeupId();

    void generateWakeupId();

    String getCorpUuid();

    String getVoiceCorpId();

    void registerConnectivityChanged(IRNConnectivityListener listener);

    void unregisterConnectivityChanged();

    void toggleAirplaneMode();

    String getCpuUsage();

    String getRobotName();

    int requestImageFrame(inout Surface surface, String param, IRNSurfaceShareListener listener);

    int abandonImageFrame(String param);

    int getStreamVolume(int streamType);

    void setStreamVolume(int streamType, int index, int flags);

    void registerVolumeChanged(int callbackId, IRNVolumeListener listener);

    void unregisterVolumeChanged(int callbackId);

    boolean isSurfaceShareUsed();
}
