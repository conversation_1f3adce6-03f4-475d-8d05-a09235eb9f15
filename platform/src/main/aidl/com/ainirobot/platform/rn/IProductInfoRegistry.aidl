// IProductInfoRegistry.aidl
package com.ainirobot.platform.rn;

interface IProductInfoRegistry {

    boolean isMiniProduct();
    boolean isDeliveryProduct();
    boolean isOverSea();
    boolean isDeliveryOverSea();
    boolean isBaseDeliveryOverSea();
    boolean isLaraForSale();
    boolean isMeissa();
    boolean isMeissa1P5();
    boolean isMeissaPlus();
    boolean isMeissa2();
    boolean isSaiphXD();
    boolean isSaiphBigScreen();
    boolean isSaiphXdOrBigScreen();
    boolean isMiniOverSea();
    boolean isSaiphChargeIr();
    boolean isSaiphRgbdFm1();
    boolean isSaiph();
    boolean isSaiphMall();
    boolean isSaiphPro();
    boolean isAlnilamPro();
    boolean isChargeIrProduct();
    boolean isElevatorCtrlProduct();
    boolean isMiniProductSupportMultiRobot();
    boolean isCarryProduct();
    boolean hasElectricDoor();
}
