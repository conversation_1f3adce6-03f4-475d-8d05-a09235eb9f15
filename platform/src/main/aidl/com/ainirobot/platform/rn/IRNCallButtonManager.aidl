/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.platform.rn;

import com.ainirobot.platform.rn.listener.IRNCallButtonListener;

interface IRNCallButtonManager {

    oneway void setEventListener(IRNCallButtonListener listener);

    oneway void replyCallPrepare(String result, String msg, String data);

    oneway void replyCall(String result, String msg);

    oneway void replyCancel(String result, String msg);

    oneway void replyButtonMappingChange(String result, String msg);

    oneway void setRobotState(String state);

    oneway void connect(String serverIp);

    oneway void disconnect();

    oneway void syncButtonMapping(String buttonMapJson);

    String getCurrentDataMapping();

    String getMapName();

    oneway void unbindButton(String buttonId, String locationName);

}
