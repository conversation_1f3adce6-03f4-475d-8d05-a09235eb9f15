// IRNRobotSettingRegistry.aidl
package com.ainirobot.platform.rn;

import com.ainirobot.platform.rn.listener.IRNRobotSettingListener;

import java.util.List;

interface IRNRobotSettingRegistry {
    int getRobotInt(String key);
    float getRobotFloat(String key);
    String getRobotString(String key);
    void setRobotInt(String key, int value);
    void setRobotFloat(String key, float value);
    void setRobotString(String key, String value);
    void registerRobotSettingListener(IRNRobotSettingListener listener, in List<String> keyList);
    void unRegisterRobotSettingListener(IRNRobotSettingListener listener);
    int getRobotGlobalInt(String key);
}
