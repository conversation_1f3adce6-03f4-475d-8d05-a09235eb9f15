<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="right"
    android:background="@android:color/transparent">
    <!--<androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cslay"
        android:layout_width="wrap_content"
        android:layout_height="27dp"
        android:orientation="horizontal"
        android:background="@android:color/transparent">

        <ImageView
            android:id="@+id/imageView2"
            android:layout_width="20dp"
            android:layout_height="18dp"
            android:layout_alignParentTop="true"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="8dp"
            android:src="@drawable/sound0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/textView2"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.444" />

        <TextView
            android:id="@+id/textView2"
            android:layout_width="46dp"
            android:layout_height="19dp"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:layout_marginBottom="4dp"
            android:text="已静音"
            android:textColor="#fa0303"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>-->
    <ImageView
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/sound0" />

</LinearLayout>