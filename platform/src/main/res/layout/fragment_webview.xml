<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:id="@+id/main">

    <WebView
        android:id="@+id/web_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <com.ainirobot.platform.common.KProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="0px"
        android:layout_height="2dp"
        app:layout_constraintTop_toTopOf="@id/web_view"
        app:layout_constraintLeft_toLeftOf="@id/web_view"
        app:layout_constraintRight_toRightOf="@id/web_view"/>

    <ImageView
        android:id="@+id/close_btn"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:padding="5dp"
        android:layout_margin="3dp"
        android:src="@drawable/ic_close"
        android:background="@drawable/circular_translucent_selector"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/refresh_btn"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:padding="5dp"
        android:layout_marginEnd="16dp"
        android:src="@drawable/ic_refresh"
        android:background="@drawable/circular_translucent_selector"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintTop_toTopOf="@id/close_btn"
        app:layout_constraintRight_toRightOf="parent"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/forward_btn"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:padding="5dp"
        android:layout_marginEnd="8dp"
        android:src="@drawable/ic_forward"
        android:background="@drawable/circular_translucent_selector"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintTop_toTopOf="@id/close_btn"
        app:layout_constraintRight_toLeftOf="@id/refresh_btn"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/back_btn"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:padding="5dp"
        android:layout_marginEnd="8dp"
        android:src="@drawable/ic_back"
        android:background="@drawable/circular_translucent_selector"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintTop_toTopOf="@id/close_btn"
        app:layout_constraintRight_toLeftOf="@id/forward_btn"
        tools:ignore="ContentDescription" />

</androidx.constraintlayout.widget.ConstraintLayout>