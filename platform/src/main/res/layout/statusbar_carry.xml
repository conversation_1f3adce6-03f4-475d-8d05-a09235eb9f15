<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="18dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/transparent">
    <TextView
        android:id="@+id/tv_system_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:textSize="6sp"
        android:textColor="@color/color_222222"
        android:layout_marginEnd="14dp"
        tools:text="12:25"/>

    <LinearLayout
        android:id="@+id/ll_battery"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tv_system_time"
        android:layout_marginEnd="6.4dp"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/icon_battery_charge"
            android:layout_width="11dp"
            android:layout_height="11dp"
            android:layout_marginEnd="1dp"
            android:src="@drawable/carry_battery_drawable"
            tools:src="@drawable/carry_battery_100"/>

        <TextView
            android:id="@+id/tv_battery_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="6sp"
            android:gravity="center"
            android:textColor="@color/color_222222"
            tools:text="100%"/>
    </LinearLayout>

    <ImageView
        android:id="@+id/icon_wifi"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/ll_battery"
        android:layout_marginEnd="6.4dp"
        android:src="@drawable/carry_wifi_drawable"
        tools:src="@drawable/carry_wifi_3"/>

    <ImageView
        android:id="@+id/icon_phone_signal"
        android:layout_width="wrap_content"
        android:layout_height="11dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/icon_wifi"
        android:layout_marginEnd="6.4dp"
        android:src="@drawable/carry_mobile_drawable"
        tools:src="@drawable/carry_network_5g"
        tools:visibility="gone"/>

    <LinearLayout
        android:id="@+id/ll_robot_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/icon_phone_signal"
        android:layout_marginEnd="6.4dp"
        android:visibility="gone"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/robot_icon"
            android:layout_width="11dp"
            android:layout_height="11dp"
            android:layout_marginEnd="1dp"
            android:src="@drawable/carry_status_robot" />

        <TextView
            android:id="@+id/multi_robot_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="6sp"
            android:gravity="center"
            android:textColor="@color/color_222222"
            android:text="0"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
