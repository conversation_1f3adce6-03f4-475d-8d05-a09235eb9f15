<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="18dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/transparent"
    tools:background="@color/black">

    <TextView
        android:id="@+id/tv_system_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:textSize="7sp"
        android:textColor="@color/white"
        android:layout_marginEnd="14dp"
        tools:text="12:25"
        tools:ignore="SmallSp" />

    <TextView
        android:id="@+id/tv_battery_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tv_system_time"
        android:textSize="7sp"
        android:textColor="@color/white"
        android:layout_marginEnd="6.4dp"
        tools:text="100%"/>

    <ImageView
        android:id="@+id/icon_battery_charge"
        android:layout_width="11dp"
        android:layout_height="11dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tv_battery_data"
        android:layout_marginEnd="1dp"
        android:src="@drawable/carry_battery_dark_drawable"
        tools:src="@drawable/carry_battery_charging_100_dark"/>

    <ImageView
        android:id="@+id/icon_wifi"
        android:layout_width="11dp"
        android:layout_height="11dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/icon_battery_charge"
        android:layout_marginEnd="6.4dp"
        android:src="@drawable/carry_wifi_dark_drawable"
        tools:src="@drawable/carry_wifi_dark_3"/>

    <ImageView
        android:id="@+id/icon_phone_signal"
        android:layout_width="wrap_content"
        android:layout_height="11dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/icon_wifi"
        android:layout_marginEnd="6.4dp"
        android:src="@drawable/carry_mobile_dark_drawable"
        tools:src="@drawable/carry_network_dark_5g"/>

    <LinearLayout
        android:id="@+id/ll_robot_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/icon_phone_signal"
        android:layout_marginEnd="6.4dp"
        android:visibility="gone"
        android:gravity="center_vertical">
        <ImageView
            android:id="@+id/robot_icon"
            android:layout_width="11dp"
            android:layout_height="11dp"
            android:layout_marginEnd="1dp"
            android:src="@drawable/carry_status_robot_dark" />

        <TextView
            android:id="@+id/multi_robot_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="6sp"
            android:gravity="center"
            android:textColor="@color/color_222222"
            android:text="0"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>