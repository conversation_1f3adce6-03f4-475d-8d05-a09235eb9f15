<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/cl_content"
        android:layout_width="match_parent"
        android:layout_height="252px"
        android:layout_above="@+id/voice_new_wave">

        <TextView
            android:id="@+id/recognition_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="27dp"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp"
            android:background="@null"
            android:ellipsize="end"
            android:gravity="center_horizontal"
            android:maxLines="1"
            android:textColor="@android:color/white"
            android:textSize="15sp" />


        <com.ainirobot.platform.react.view.GuidedQueryView
            android:id="@+id/recognition_anim_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:visibility="visible" />

    </RelativeLayout>

    <com.ainirobot.platform.react.view.SurfaceAnimation
        android:id="@+id/voice_new_wave"
        android:layout_width="match_parent"
        android:layout_height="52px"
        android:layout_marginTop="200px" />
</merge>