<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:focusable="false"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/eve_watermark"
        android:layout_width="33.3dp"
        android:layout_height="30.7dp"
        android:layout_marginRight="3.3dp"
        android:scaleType="fitXY"
        android:src="@drawable/watermark_img"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>