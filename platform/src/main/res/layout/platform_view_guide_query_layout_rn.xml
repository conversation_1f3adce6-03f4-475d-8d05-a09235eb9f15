<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/ll_sign"
        android:layout_width="match_parent"
        android:layout_height="242px"
        android:background="@drawable/popup_bg_icon"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="210px"
            android:layout_height="160px"
            android:layout_marginTop="31px"
            android:scaleType="fitXY" />

        <TextView
            android:id="@+id/tv_guide_query"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="-10px"
            android:layout_marginRight="100px"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="#2D3235"
            android:textSize="50px"
            android:visibility="visible" />

    </LinearLayout>

    <ImageView
        android:id="@+id/iv_robot_icon"
        android:layout_width="180px"
        android:layout_height="180px"
        android:layout_marginLeft="30px"
        android:layout_marginTop="21px"
        android:background="@drawable/popup_bg_img"
        android:scaleType="fitXY" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="10px"
        android:layout_below="@+id/ll_sign" />
</merge>