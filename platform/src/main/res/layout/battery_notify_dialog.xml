<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="850px"
    android:layout_height="600px"
    android:layout_gravity="center"
    android:background="@drawable/battery_dialog_bg"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="110px"
            android:layout_height="110px"
            android:layout_marginStart="30dp"
            android:src="@drawable/warning" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="6dp"
            android:text="@string/battery_notify_title"
            android:textColor="@color/black"
            android:textSize="16sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/battery_remaining_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:layout_marginStart="30dp"
        android:layout_marginEnd="30dp"
        android:text="@string/battery_notify_sub_title"
        android:textColor="@color/black"
        android:textSize="13sp" />

    <ImageView
        android:id="@+id/battery_divider"
        android:layout_width="800px"
        android:layout_height="1dp"
        android:layout_gravity="center"
        android:layout_marginTop="23dp"
        android:background="#000000"/>

    <TextView
        android:id="@+id/battery_dismiss_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:layout_marginBottom="10dp"
        android:gravity="center_horizontal"
        android:text="@string/has_know"
        android:textColor="#55C3FB"
        android:textSize="14sp" />
</LinearLayout>
