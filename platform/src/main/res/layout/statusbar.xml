<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="end"
    android:background="#00000000"
    android:paddingEnd="25px"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/robot_icon"
        android:layout_width="wrap_content"
        android:layout_height="55px"
        android:layout_alignParentTop="true"
        android:layout_gravity="center"
        android:layout_marginTop="10px"
        android:layout_marginRight="10px"
        android:layout_toStartOf="@+id/multi_robot_number"
        android:src="@drawable/status_robot"
        android:adjustViewBounds="true" />

    <TextView
        android:id="@+id/multi_robot_number"
        android:layout_width="wrap_content"
        android:layout_height="60px"
        android:layout_alignParentTop="true"
        android:textColor="@color/white"
        android:textSize="30px"
        android:shadowColor="@color/black"
        android:layout_toStartOf="@+id/quick_time"
        android:layout_marginTop="25px"
        android:layout_marginRight="10px"
        android:gravity="center|left"
        android:shadowRadius="3"
        android:text="13"/>

    <TextView
        android:id="@+id/quick_time"
        android:layout_width="wrap_content"
        android:layout_height="60px"
        android:layout_alignParentTop="true"
        android:textColor="@color/white"
        android:textSize="30px"
        android:shadowColor="@color/black"
        android:layout_toStartOf="@+id/quick_mobile"
        android:layout_marginTop="25px"
        android:layout_marginRight="10px"
        android:gravity="center|left"
        android:shadowRadius="3"
        android:text="00:00"/>

    <ImageView
        android:id="@+id/quick_wifi"
        android:layout_width="wrap_content"
        android:layout_height="55px"
        android:layout_alignParentTop="true"
        android:layout_gravity="center"
        android:layout_marginTop="10px"
        android:layout_marginRight="10px"
        android:layout_toStartOf="@+id/quick_battery"
        android:src="@drawable/quick_wifi_drawable"
        android:adjustViewBounds="true" />

    <ImageView
        android:id="@+id/quick_mobile"
        android:layout_width="wrap_content"
        android:layout_height="55px"
        android:layout_alignParentTop="true"
        android:layout_gravity="center"
        android:layout_marginTop="10px"
        android:layout_marginRight="10px"
        android:layout_toStartOf="@+id/quick_wifi"
        android:adjustViewBounds="true"
        android:src="@drawable/quick_mobile_drawable" />

    <ImageView
        android:id="@+id/quick_battery"
        android:layout_width="wrap_content"
        android:layout_height="55px"
        android:layout_alignParentTop="true"
        android:layout_marginTop="10px"
        android:layout_marginRight="10px"
        android:adjustViewBounds="true"
        android:layout_gravity="center"
        android:layout_toStartOf="@+id/quick_battery_value"
        android:src="@drawable/quick_battery_drawable"
        android:visibility="visible" />

    <TextView
        android:id="@+id/quick_battery_value"
        android:layout_width="wrap_content"
        android:layout_height="60px"
        android:layout_alignParentTop="true"
        android:layout_marginTop="25px"
        android:layout_marginRight="10px"
        android:gravity="left|center"
        android:text="100%"
        android:textColor="@color/white"
        android:textSize="30px"
        android:shadowColor="@color/black"
        android:layout_toStartOf="@+id/quick_battery_charge"
        android:shadowRadius="3" />

    <TextView
        android:id="@+id/quick_battery_charge"
        android:layout_width="wrap_content"
        android:layout_height="60px"
        android:layout_alignParentTop="true"
        android:textColor="@color/white"
        android:textSize="28px"
        android:shadowColor="@color/black"
        android:shadowRadius="3"
        android:layout_marginTop="25px"
        android:layout_marginRight="8px"
        android:gravity="center|left"
        android:text="@string/in_charging"/>

</LinearLayout>