<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/toast_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/toast_background"
        android:textColor="#ffffff"
        android:gravity="center"
        android:paddingTop="27px"
        android:paddingBottom="27px"
        android:paddingRight="70px"
        android:paddingLeft="70px"
        android:textSize="40px"/>

</androidx.constraintlayout.widget.ConstraintLayout>