<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2017 OrionStar Technology Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->


<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_centerInParent="true"
    android:background="@color/white"
    android:elevation="1dp">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:paddingLeft="50px"
        android:paddingRight="50px"
        android:text="@string/crash_notify"
        android:textColor="@color/mobile_data_confirm_upgrade"
        android:textSize="46px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/crash_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="8dp"
        android:paddingLeft="50px"
        android:paddingRight="50px"
        android:gravity="center"
        android:text="@string/crash_content"
        android:textColor="@color/night_mobile_data"
        android:textSize="38px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.496"
        app:layout_constraintLeft_toLeftOf="@id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"/>

    <View
        android:id="@+id/view_line"
        android:layout_width="match_parent"
        android:layout_height="1.5px"
        android:layout_marginTop="32dp"
        android:background="@color/ota_view_line"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/crash_content"/>

    <TextView
        android:id="@+id/reboot"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/retry_btn_bg"
        android:gravity="center"
        android:paddingTop="30px"
        android:paddingBottom="30px"
        android:text="@string/reboot_app"
        android:textColor="@color/ota_upgrade_now"
        android:textSize="40px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view_line"
        app:layout_constraintVertical_bias="1.0"/>



</androidx.constraintlayout.widget.ConstraintLayout>
