<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="10dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="230dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp">

        <com.ainirobot.platform.react.view.captcha.PictureVertifyView
            android:id="@+id/vertifyView"
            android:layout_width="match_parent"
            android:layout_height="230dp"
            android:scaleType="fitXY" />

        <LinearLayout
            android:id="@+id/accessRight"
            android:layout_width="match_parent"
            android:layout_height="28dp"
            android:layout_gravity="bottom"
            android:background="#7F000000"
            android:orientation="horizontal"
            android:visibility="gone">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:src="@drawable/right" />

            <TextView
                android:id="@+id/accessText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/accessFailed"
            android:layout_width="match_parent"
            android:layout_height="28dp"
            android:layout_gravity="bottom"
            android:background="#7F000000"
            android:orientation="horizontal"
            android:visibility="gone">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:src="@drawable/wrong" />

            <TextView
                android:id="@+id/accessFailedText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />

        </LinearLayout>

        <ImageView
            android:id="@+id/refresh"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="right|top"
            android:layout_margin="5dp"
            android:visibility="gone"
            android:src="@drawable/refresh" />


    </FrameLayout>


    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:layout_marginHorizontal="10dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/corners_bg">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:textColor="#B3333333"
            android:text="@string/slidebar_bg_hint"
            android:textSize="12dp" />

        <com.ainirobot.platform.react.view.captcha.TextSeekbar
            android:id="@+id/seekbar"
            style="@style/MySeekbarSytle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:splitTrack="false"
            android:thumbOffset="0dp" />
    </FrameLayout>

</LinearLayout>