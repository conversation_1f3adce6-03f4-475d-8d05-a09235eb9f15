<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="response_no_app">Sorry, App not found</string>
    <string name="response_no_Activity">Sorry, activity not found</string>
    <string name="sdcard_disabled">SD card not available</string>
    <string name="not_support_video_type">The current video format is not supported</string>
    <string name="adk_questions">Query</string>
    <string name="bubble_view">“%1$s”</string>
    <string name="debug_mode">Debug Mode</string>
    <string name="battery_low_title">Battery is low</string>
    <string name="battery_remaining_text">Battery Remaining %1$s%2$s</string>
    <string name="battery_please_charge">Please charge the robot in time</string>
    <string name="battery_level_percent" translatable="false">%d%%</string>
    <string name="has_know">OK(%d)</string>
    <string name="no_sim_card">No SIM card</string>
    <string name="wlan">WLAN</string>
    <string name="in_charging">Charging</string>
    <string name="confirm">Confirm</string>
    <string name="retry">Retry</string>
    <string name="crash_notify">Crash Notice</string>
    <string name="reboot_app">Reboot App</string>
    <string name="crash_content">%1$s App crashed \nappId: %2$s</string>
    <string name="battery_notify_title">Recharge Notice</string>
    <string name="battery_notify_sub_title">The current battery level is %1$s%2$s. The battery may not last for your usage of today. Please recharge in time.</string>
    <string name="battery_notify_immediate_sub_title">The current battery level is %1$s%2$s. Please recharge in time.</string>
    <string name="data_update_failed_title">App update failed</string>
    <string name="data_update_failed_subTitle">Please make sure to install when the network is clear</string>
    <string name="vertify_access_dafault">Verification passed. Time-consumed: %d milliseconds</string>
    <string name="vertify_failed_default">Verification failed. %d times left</string>
    <string name="slidebar_bg_hint">Press and drag the button to complete the puzzle above</string>
    <string name="vertify_access">Verification passed</string>
    <string name="vertify_failed">Verification failed</string>
    <string name="vertify_access_exceed_max_times">Verification opportunity runs out.</string>
    <string name="radar_expration_please_wait">Radar abnormality, automatically recovering</string>
</resources>
