<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="mobile_type">
        <attr name="state_mobile_type_2g" format="boolean" />
        <attr name="state_mobile_type_3g" format="boolean" />
        <attr name="state_mobile_type_4g" format="boolean" />
        <attr name="state_mobile_type_5g" format="boolean" />
        <attr name="state_mobile_type_no_service" format="boolean" />
        <attr name="state_mobile_type_no_sim_card" format="boolean" />
        <attr name="state_mobile_type_no_data" format="boolean" />
    </declare-styleable>

    <declare-styleable name="battery_type">
        <attr name="state_battery_plugged" format="boolean" />
    </declare-styleable>

    <declare-styleable name="Captcha">
        <attr name="src" format="reference"/>
        <attr name="progressDrawable" format="reference"/>
        <attr name="thumbDrawable" format="reference"/>
        <attr name="mode" format="enum">
            <enum name="mode_bar" value="1"/>
            <enum name="mode_nonbar" value="2"/>
        </attr>
        <attr name="max_fail_count" format="integer"/>
        <attr name="blockSize" format="dimension"/>
    </declare-styleable>
</resources>