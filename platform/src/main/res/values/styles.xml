<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="AppTheme" parent="@android:style/Theme.NoTitleBar.Fullscreen">
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowTranslucentStatus">true</item>
    </style>

    <!--activity animation [end]-->
    <!--OTADialog背景全透明无边框theme -->
    <style name="OTADialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowNoTitle">false</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsFloating">true</item><!--是否浮现在activity之上-->
    </style>

    <style name="swipeToDismissActivity" parent="@android:style/Theme.NoTitleBar.Fullscreen">
        <!--to avoid start activity with animation white screen-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowSwipeToDismiss">true</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="MapConfirmDialog" parent="android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item><!--背景颜色及和透明程度-->
        <item name="android:windowNoTitle">true</item><!--是否去除标题 -->
        <item name="android:windowFrame">@null</item><!--是否去除边框-->
        <item name="android:windowIsFloating">true</item><!--是否浮现在activity之上-->
        <item name="android:backgroundDimEnabled">true</item><!--背景是否变暗-->
    </style>

    <!--Dialog背景全透明无边框theme -->
    <style name="Dialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowNoTitle">false</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsFloating">true</item><!--是否浮现在activity之上-->
    </style>
    <style name="FullScreenDialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>
    <style name="LeadTextStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">sans-serif-light</item>
    </style>

    <style name="greeting_dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item><!--边框-->
        <item name="android:windowIsFloating">true</item><!--是否浮现在activity之上-->
        <item name="android:windowIsTranslucent">false</item><!--半透明-->
        <item name="android:windowNoTitle">true</item><!--无标题-->
        <item name="android:windowBackground">@color/transparent</item><!--背景透明-->
        <item name="android:backgroundDimEnabled">false</item><!--模糊-->
    </style>
    <style name="EmojiGreeting_dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item><!--边框-->
        <item name="android:windowIsFloating">true</item><!--是否浮现在activity之上-->
        <item name="android:windowIsTranslucent">false</item><!--半透明-->
        <item name="android:windowNoTitle">true</item><!--无标题-->
        <item name="android:windowBackground">@color/transparent</item><!--背景透明-->
        <item name="android:backgroundDimEnabled">false</item><!--模糊-->
    </style>

    <style name="GroupPhotoDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item><!--边框-->
        <item name="android:windowIsFloating">true</item><!--是否浮现在activity之上-->
        <item name="android:windowIsTranslucent">false</item><!--半透明-->
        <item name="android:windowNoTitle">true</item><!--无标题-->
        <item name="android:windowBackground">@color/transparent</item><!--背景透明-->
        <item name="android:backgroundDimEnabled">false</item><!--模糊-->
    </style>

    <!-- Base application theme. -->
    <style name="EvaAppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:timePickerDialogTheme">@style/Dialog.Theme</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@color/black</item>
    </style>

    <style name="Dialog.Theme" parent="Theme.AppCompat.Light.Dialog">
        <item name="colorAccent">#198CFF</item>
    </style>

    <style name="quick_settingbar_item_style">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">30px</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">1</item>
        <item name="android:layout_weight">1</item>
        <item name="android:paddingTop">38px</item>
        <item name="android:textColorHint">#FFFFFF</item>
        <item name="android:drawablePadding">5px</item>
    </style>

    <style name="MySeekbarSytle">
        <item name="android:thumb">@drawable/thumb</item>
        <item name="android:progressDrawable">@drawable/po_seekbar</item>
    </style>
</resources>