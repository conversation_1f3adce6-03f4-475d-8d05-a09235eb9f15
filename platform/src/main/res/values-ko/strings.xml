<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="response_no_app">죄송합니다，해당 앱을 찾을 수 없습니다. </string>
    <string name="response_no_Activity">찾을 수 없습니다.</string>
    <string name="sdcard_disabled">SD 카드를 사용할 수 없습니다.</string>
    <string name="not_support_video_type">해당 동영상 형식은 지원되지 않습니다.</string>
    <string name="adk_questions">문제 제기</string>
    <string name="bubble_view">“%1$s”</string>
    <string name="debug_mode">Debug Mode</string>
    <string name="battery_low_title">배터리가 부족합니다</string>
    <string name="battery_remaining_text">현재 배터리가 %1$s%2$s 입니다</string>
    <string name="battery_please_charge">지금 충전기를 연결하세요</string>
    <string name="has_know">확인(%d)</string>
    <string name="no_sim_card">SIM카드 없음</string>
    <string name="wlan">WLAN</string>
    <string name="in_charging">충전 중</string>
    <string name="confirm">확인</string>
    <string name="retry">재시도</string>
    <string name="crash_notify">충돌 알림</string>
    <string name="reboot_app">앱 다시 시작</string>
    <string name="crash_content">%1$s앱이 중단되었습니다\nappId: %2$s</string>
    <string name="battery_notify_title">충전 알림</string>
    <string name="battery_notify_sub_title">로봇의 현재 배터리는 %1$s%2$s입니다. 배터리가 부족하니 충전을 진행 해주시기 바랍니다.</string>
    <string name="battery_notify_immediate_sub_title">로봇 현재 배터리 잔량은 %1$s%2$s입니다. 강제종료되기 전에 충전해주세요.</string>
    <string name="data_update_failed_title">앱 업데이트 실패</string>
    <string name="data_update_failed_subTitle">네트워크가 연결되어있을 때 설치하십시오.</string>
    <string name="vertify_access_dafault">확인되었습니다. 소요 시간: %d 밀리초</string>
    <string name="vertify_failed_default">확인에 실패했습니다. %d 회 남음</string>
    <string name="slidebar_bg_hint">버튼을 누르고 드래그하여 위의 퍼즐을 완성하세요</string>
    <string name="vertify_access">인증 통과</string>
    <string name="vertify_failed">확인 실패</string>
    <string name="vertify_access_exceed_max_times">인증 기회가 소진되었습니다.</string>
    <string name="radar_expration_please_wait">레이더 이상, 자동 복구</string>
</resources>
