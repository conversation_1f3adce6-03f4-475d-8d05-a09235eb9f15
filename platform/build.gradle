apply plugin: 'com.android.library'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-android'

def isRN069 = gradle.startParameter.taskRequests.toString().contains("Rn069")
def enableHermes = isRN069 //RN069版本默认启用Hermes引擎
if (isRN069) {
    println "Build RN069，enableHermes : $enableHermes"
    apply plugin: 'com.facebook.react'
    project.ext.react = [enableHermes: enableHermes] //插件会根据该配置选择对应的引擎so
}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        //noinspection ExpiredTargetSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0"

        if (enableHermes) {
            externalNativeBuild {
                cmake {
                    cppFlags "-fexceptions", "-frtti", "-std=c++1y", "-DONANDROID"
                    abiFilters 'x86', 'armeabi-v7a', 'arm64-v8a'
                    arguments '-DANDROID_STL=c++_shared'
                }
            }
        }
    }

    if (enableHermes) {
        externalNativeBuild {
            cmake {
                path "src/main/cpp/CMakeLists.txt"
            }
        }
    }

    buildTypes {
        debug {
            buildConfigField "String", "RN_VERSION", "\"059\""
            buildConfigField "boolean", "HERMES", "false"
            dependencies {
                fileTreeAar('059') { name ->
                    debugApi(name: name, ext: 'aar')
                }
                debugApi rn059Libs.bundles.rn
                debugApi rn059Libs.bundles.thirdepends
            }
        }

        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "RN_VERSION", "\"059\""
            buildConfigField "boolean", "HERMES", "false"

            dependencies {
                fileTreeAar('059') { name ->
                    releaseApi(name: name, ext: 'aar')
                }
                releaseApi rn059Libs.bundles.rn
                releaseApi rn059Libs.bundles.thirdepends
            }
        }

        rn069 {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules-069.pro'
            buildConfigField "String", "RN_VERSION", "\"069\""
            buildConfigField "boolean", "HERMES", "$enableHermes"

            configurations {
                extractHeaders
                extractJNI
            }

            dependencies {
                println "Build enableHermes : $enableHermes"
                fileTreeAar('069') { name ->
                    if (!name.contains('hermes-engine') || enableHermes) {
                        rn069Api(name: name, ext: 'aar')
                    }

                    if (name.contains('orionos-react-native') && enableHermes) {
                        api(name: name, ext: 'aar')
                        extractJNI(name: name, ext: 'aar')
                    }
                }
                rn069Api rn069Libs.bundles.rn
                rn069Api rn069Libs.bundles.thirdepends
                if (enableHermes) {
                    extractHeaders("com.facebook.fbjni:fbjni:0.2.2:headers")
                }
            }
        }
    }

    lintOptions {
        abortOnError false
    }

    buildFeatures {
        prefab true
    }

    packagingOptions {
        excludes = ["**/libc++_shared.so", "**/libfbjni.so", "**/libjsi.so"]
        exclude "META-INF/**"
    }
}

repositories {
    mavenCentral()
}

/**
 * 遍历添加aar依赖
 * 新版本gradle不再支持直接通过fileTree进行aar导入
 * @param dir
 * @return
 */
def fileTreeAar(String dir, def addDepend) {
    def thirdLibs = rootProject.file("libraries/3rdlibs/$dir")
    thirdLibs.traverse(nameFilter: ~/.*\.aar/) {
        def name = it.getName().replace('.aar', '')
        addDepend(name)
    }
}

dependencies {
    api fileTree(include: ['*.jar'], exclude: ['framework.jar'], dir: 'libs')
    compileOnly fileTree(include: ['framework.jar'], dir: 'libs')
    api (name: 'callapi-release', ext: 'aar')

    //noinspection GradleDependency
    api 'androidx.appcompat:appcompat:1.3.1'
    api 'androidx.cardview:cardview:1.0.0'
    api 'androidx.annotation:annotation:1.4.0'
    api 'androidx.core:core-ktx:1.6.0'
    api 'androidx.media:media:1.3.0'
    api 'androidx.constraintlayout:constraintlayout:2.1.4'
    api 'com.google.android.material:material:1.4.0'
    api 'androidx.recyclerview:recyclerview:1.2.0'
    api 'androidx.dynamicanimation:dynamicanimation:1.0.0'
    api 'androidx.exifinterface:exifinterface:1.3.6'

    api "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.6.10"
    api 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
    api "org.jetbrains.kotlin:kotlin-reflect:1.6.10"
    api 'com.google.protobuf:protobuf-javalite:3.8.0'

    api('com.github.bumptech.glide:glide:3.8.0') {
        exclude group: "com.android.support"
    }

    fileTreeAar('common') { name ->
        api(name: name, ext: 'aar')
    }

    api common.bundles.androidx
    api common.bundles.kotlin
    api common.bundles.fresco
    api common.bundles.okhttp
    api common.bundles.base

    compileOnly project(':libraries:baidumap')
    api project(':emoji')
    api project(':libraries:I18n')

    implementation files('libs/jna.jar')
    implementation files('libs/jxl.jar')
}

task extractAARHeaders {
    doLast {
        configurations.extractHeaders.files.each {
            def file = it.absoluteFile
            copy {
                from zipTree(file)
                into "$buildDir/$file.name"
                include "**/*.h"
            }
        }
    }
}

task extractJNIFiles {
    doLast {
        configurations.extractJNI.files.each {
            def file = it.absoluteFile
            copy {
                from zipTree(file)
                into "$buildDir/$file.name"
                include "jni/**/*"
            }
        }
    }
}

tasks.whenTaskAdded { task ->
    if (!task.name.contains('Clean') &&
            (task.name.contains('externalNative') || task.name.contains('CMake'))) {
        task.dependsOn(extractAARHeaders)
        task.dependsOn(extractJNIFiles)
    }
}